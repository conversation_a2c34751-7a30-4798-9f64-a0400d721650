package com.fptplay.mobile.features.survey

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.View.OnTouchListener
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.core.view.marginBottom
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.paris.utils.setPaddingBottom
import com.fptplay.mobile.R
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.databinding.SurveyQuestionFragmentBinding
import com.xhbadxx.projects.module.domain.entity.fplay.common.SurveyQuestion


@dagger.hilt.android.AndroidEntryPoint
class SurveyQuestionFragment : BaseFragment<SurveyViewModel.SurveyState, SurveyViewModel.SurveyIntent>() {
    override val viewModel: SurveyViewModel by activityViewModels()
    private var _binding: SurveyQuestionFragmentBinding? = null
    private val binding get() = _binding!!
    override val hasEdgeToEdge: Boolean = true
    override val handleBackPressed: Boolean = true
    private var currentQuestionIndex: Int = 0
    private val totalQuestions: Int
        get() = viewModel.dataQuestion?.questions?.size ?: 0
    private var currentQuestion:SurveyQuestion.Question? = null
    private var isKeyboardVisible: Boolean = false

    private val answerAdapter: AnswerAdapter by lazy {
        AnswerAdapter(
            onItemCheck = { answer ->
                if(answer.isSelected) {
                    setEnableButtonContinue(true)
                }else{
                    setEnableButtonContinue(checkAnySelected())
                }
            },
            hideKeyboard = { hide ->
                if (hide) {
                    hideKeyboard()
                }
            }
        )
    }
    private val pushUpViewLisenter = ViewTreeObserver.OnGlobalLayoutListener{
        val rect = Rect()
        binding.root.rootView.getWindowVisibleDisplayFrame(rect)
        val screenHeight = binding.root.rootView.height
        val keypadHeight = screenHeight - rect.bottom
        if (keypadHeight > screenHeight * 0.15) {
            if (!isKeyboardVisible) {
                // Keyboard is open, adjust marginBottom
                val heightDiff = keypadHeight - binding.btnPrevious.height - binding.btnPrevious.marginBottom
                binding.svContainer.setPaddingBottom(heightDiff)
                // Scroll to end
                binding.svContainer.post {
                    val focusedView = binding.root.findFocus()
                    (focusedView?.parent as? View)?.let {
                        val rectFocus = Rect()
                        it.getDrawingRect(rectFocus)
                        binding.svContainer.offsetDescendantRectToMyCoords(it, rectFocus)
                        // Scroll so the entire view is visible
                        binding.svContainer.smoothScrollTo(0, rectFocus.top)
                    }

                    //binding.svContainer.fullScroll(View.FOCUS_DOWN)
                }
                isKeyboardVisible = true
            }
        } else {
            // Keyboard is closed, reset marginBottom
            binding.svContainer.setPaddingBottom(0)
            isKeyboardVisible = false
        }
    }
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = SurveyQuestionFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun backHandler() {
        if(currentQuestionIndex > 0) {
            currentQuestionIndex--
            loadQuestion(currentQuestionIndex)
            return
        }
        //super.backHandler()
    }

    override fun initData() {
        binding.rvListAnswers.layoutManager = LinearLayoutManager(requireContext())
        binding.rvListAnswers.adapter = answerAdapter
        loadQuestion(currentQuestionIndex)
    }

    override fun onStart() {
        super.onStart()
        binding.root.rootView.viewTreeObserver.addOnGlobalLayoutListener(pushUpViewLisenter)
    }

    override fun onStop() {
        super.onStop()
        binding.root.rootView.viewTreeObserver.removeOnGlobalLayoutListener(pushUpViewLisenter)
    }

    override fun bindEvent() {
        binding.llContainer.setOnClickListener { hideKeyboard() }
        binding.btnPrevious.setOnClickListener {
            if (currentQuestionIndex > 0) {
                currentQuestionIndex--
                loadQuestion(currentQuestionIndex)
            }
        }
        binding.btnContinues.setOnClickListener {
            if (currentQuestionIndex < totalQuestions - 1) {
                currentQuestionIndex++
                loadQuestion(currentQuestionIndex)
            } else {
                findNavController().navigate(directions = SurveyQuestionFragmentDirections.actionSurveyQuestionFragmentToSurveyEndFragment())
            }
        }
        binding.root.setOnClickListener {
            // Dismiss keyboard when clicking outside of EditText
            hideKeyboard()
        }
    }
    private fun hideKeyboard() {
        if(!isKeyboardVisible) return
        val focusedView: View? = binding.root.findFocus()
        if (focusedView is EditText) {
            focusedView.clearFocus() // Clear focus
            val imm = context?.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
            imm?.hideSoftInputFromWindow(focusedView.windowToken, 0)
            focusedView.post { //for some case the keyboard is hiding and calling clearFocus() will not work -> because the view is holding focus for keyboard
                focusedView.clearFocus()
            }
        }
    }

    private fun loadQuestion(index:Int){
        viewModel.dataQuestion?.questions?.getOrNull(index)?.apply {
            currentQuestion = this
            binding.tvSurveyTitle.text = this.text
            binding.tvSurveyDescription.text = getString(R.string.survey_question, this.position, totalQuestions.toString())
            if(index == 0) {
                setEnableButtonPrevious(false)
            } else {
                setEnableButtonPrevious(true)
            }
            answerAdapter.updateAnswers(this.answers, this.multiple)
            setEnableButtonContinue(checkAnySelected())
        }
    }
    private fun checkAnySelected(): Boolean {
        return currentQuestion?.answers?.any { it.isSelected } ?: false
    }
    private fun setEnableButtonContinue(enable: Boolean) {
        binding.btnContinues.isEnabled = enable
        val colorRes = if (enable) R.color.white_87 else R.color.white_38
        binding.btnContinues.setTextColor(requireContext().getColor(colorRes))
    }
    private fun setEnableButtonPrevious(enable: Boolean) {
        binding.btnPrevious.isEnabled = enable
        val colorRes = if (enable) R.color.white_87 else R.color.white_38
        binding.btnPrevious.setTextColor(requireContext().getColor(colorRes))
    }

    override fun SurveyViewModel.SurveyState.toUI() {
    }
}
