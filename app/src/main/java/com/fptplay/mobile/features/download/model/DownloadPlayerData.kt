package com.fptplay.mobile.features.download.model

import android.content.Context
import com.fptplay.mobile.R
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject

data class DownloadPlayerData(
    val Id: Int,
    val iconId: Int,
    val title: String,
) : BaseObject() {

    companion object {
        fun getDownloadingData() : List<DownloadPlayerData> {
            val result = mutableListOf<DownloadPlayerData>()
            result.add(DownloadPlayerData(Id = DownloadTaskState.PAUSE, iconId = R.drawable.ic_downloading, title = "Tạm dừng tải"))
            result.add(DownloadPlayerData(Id = DownloadTaskState.DELETE, iconId = R.drawable.ic_recycle_bin, title = "Xóa khỏi nội dung tải xuống"))
            return result
        }
        fun getDownloadingForVodData() : List<DownloadPlayerData> {
            val result = mutableListOf<DownloadPlayerData>()
            result.add(DownloadPlayerData(Id = DownloadTaskState.PAUSE, iconId = R.drawable.ic_downloading, title = "Tạm dừng tải"))
            result.add(DownloadPlayerData(Id = DownloadTaskState.LIST, iconId = R.drawable.ic_download, title = "Danh sách video tải xuống"))
            result.add(DownloadPlayerData(Id = DownloadTaskState.DELETE, iconId = R.drawable.ic_recycle_bin, title = "Xóa khỏi nội dung tải xuống"))
            return result
        }
        fun getDownloadedData() : List<DownloadPlayerData> {
            val result = mutableListOf<DownloadPlayerData>()
            result.add(DownloadPlayerData(Id = DownloadTaskState.LIST, iconId = R.drawable.ic_download, title = "Danh sách video tải xuống"))
            result.add(DownloadPlayerData(Id = DownloadTaskState.DELETE, iconId = R.drawable.ic_recycle_bin, title = "Xóa khỏi nội dung tải xuống"))
            return result
        }
        fun getDownloadedButExpiredData() : List<DownloadPlayerData> {
            val result = mutableListOf<DownloadPlayerData>()
            result.add(DownloadPlayerData(Id = DownloadTaskState.EXTEND, iconId = R.drawable.ic_download_extend, title = "Gia hạn"))
            result.add(DownloadPlayerData(Id = DownloadTaskState.LIST, iconId = R.drawable.ic_download, title = "Danh sách video tải xuống"))
            result.add(DownloadPlayerData(Id = DownloadTaskState.DELETE, iconId = R.drawable.ic_recycle_bin, title = "Xóa khỏi nội dung tải xuống"))
            return result
        }
        fun getDownloadPauseData() : List<DownloadPlayerData> {
            val result = mutableListOf<DownloadPlayerData>()
            result.add(DownloadPlayerData(Id = DownloadTaskState.RESUME, iconId = R.drawable.ic_download, title = "Tiếp tục tải xuống"))
            result.add(DownloadPlayerData(Id = DownloadTaskState.DELETE, iconId = R.drawable.ic_recycle_bin, title = "Xóa khỏi nội dung tải xuống"))
            return result
        }
        fun getDownloadPauseForVodData() : List<DownloadPlayerData> {
            val result = mutableListOf<DownloadPlayerData>()
            result.add(DownloadPlayerData(Id = DownloadTaskState.RESUME, iconId = R.drawable.ic_download, title = "Tiếp tục tải xuống"))
            result.add(DownloadPlayerData(Id = DownloadTaskState.LIST, iconId = R.drawable.ic_download, title = "Danh sách video tải xuống"))
            result.add(DownloadPlayerData(Id = DownloadTaskState.DELETE, iconId = R.drawable.ic_recycle_bin, title = "Xóa khỏi nội dung tải xuống"))
            return result
        }
        fun getDownloadRedownloadData() : List<DownloadPlayerData> {
            val result = mutableListOf<DownloadPlayerData>()
            result.add(DownloadPlayerData(Id = DownloadTaskState.REDOWNLOAD, iconId = R.drawable.ic_download, title = "Tải lại"))
            result.add(DownloadPlayerData(Id = DownloadTaskState.DELETE, iconId = R.drawable.ic_recycle_bin, title = "Xóa khỏi nội dung tải xuống"))
            return result
        }
        fun getDownloadRedownloadForVodData() : List<DownloadPlayerData> {
            val result = mutableListOf<DownloadPlayerData>()
            result.add(DownloadPlayerData(Id = DownloadTaskState.REDOWNLOAD, iconId = R.drawable.ic_download, title = "Tải lại"))
            result.add(DownloadPlayerData(Id = DownloadTaskState.LIST, iconId = R.drawable.ic_download, title = "Danh sách video tải xuống"))
            result.add(DownloadPlayerData(Id = DownloadTaskState.DELETE, iconId = R.drawable.ic_recycle_bin, title = "Xóa khỏi nội dung tải xuống"))
            return result
        }
        fun getDownloadExtendData() : List<DownloadPlayerData> {
            val result = mutableListOf<DownloadPlayerData>()
            result.add(DownloadPlayerData(Id = DownloadTaskState.EXTEND, iconId = R.drawable.ic_download_extend, title = "Gia hạn"))
            result.add(DownloadPlayerData(Id = DownloadTaskState.DELETE, iconId = R.drawable.ic_recycle_bin, title = "Xóa khỏi nội dung tải xuống"))
            return result
        }
        fun getDownloadExtendForVodData() : List<DownloadPlayerData> {
            val result = mutableListOf<DownloadPlayerData>()
            result.add(DownloadPlayerData(Id = DownloadTaskState.EXTEND, iconId = R.drawable.ic_download_extend, title = "Gia hạn"))
            result.add(DownloadPlayerData(Id = DownloadTaskState.LIST, iconId = R.drawable.ic_download, title = "Danh sách video tải xuống"))
            result.add(DownloadPlayerData(Id = DownloadTaskState.DELETE, iconId = R.drawable.ic_recycle_bin, title = "Xóa khỏi nội dung tải xuống"))
            return result
        }
        fun getDeleteFromDownloadData() : List<DownloadPlayerData> {
            val result = mutableListOf<DownloadPlayerData>()
            result.add(DownloadPlayerData(Id = DownloadTaskState.DELETE, iconId = R.drawable.ic_recycle_bin, title = "Xóa khỏi nội dung tải xuống"))
            return result
        }
        fun getStorageForDownloadData(context : Context, availableInStorage : String, availableExStorage : String ) : List<DownloadPlayerData> {
            val result = mutableListOf<DownloadPlayerData>()
            result.add(DownloadPlayerData(Id = DownloadTaskState.STORAGE_IN, iconId = R.drawable.ic_internal_storage, title = context.resources.getString(R.string.text_internal_storage).plus(" (trống ").plus(availableInStorage).plus(")")))
            result.add(DownloadPlayerData(Id = DownloadTaskState.STORAGE_EX, iconId = R.drawable.ic_external_storage, title = context.resources.getString(R.string.text_external_storage).plus(" (trống ").plus(availableExStorage).plus(")")))
            return result
        }
    }
}