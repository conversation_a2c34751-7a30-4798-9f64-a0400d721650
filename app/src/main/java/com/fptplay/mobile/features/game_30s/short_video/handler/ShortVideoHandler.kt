package com.fptplay.mobile.features.game_30s.short_video.handler

import android.content.Intent
import android.view.View.VISIBLE
import android.view.animation.AnimationUtils
import android.widget.Toast

import androidx.core.content.ContextCompat
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.extensions.withSuffix
import com.fptplay.mobile.common.global.GlobalEvent
import com.fptplay.mobile.common.global.GlobalEventListener
import com.fptplay.mobile.databinding.ShortVideoItemBinding
import com.fptplay.mobile.features.game_30s.short_video.adapter.Game30sHashtagAdapter
import com.tear.modules.player.exo.ExoPlayerProxy
import com.tear.modules.player.exo.ExoPlayerView
import com.tear.modules.player.util.IPlayer
import com.xhbadxx.projects.module.domain.entity.fplay.game.game30s.Game30sMemberVideo
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import timber.log.Timber

class ShortVideoHandler(
    private val binding: ShortVideoItemBinding,
//    private var itemEventsListener: IEventShortVideoListener ?= null,
    ) {
//    private val hashtagAdapter by lazy { Game30sHashtagAdapter() }
//    private val isVoted = MutableLiveData<String>()
//    private val playerCallback by lazy {
//        object : IPlayer.IPlayerCallback {
//            override fun onPrepare() {
//                super.onPrepare()
//                binding.ivThumb.show()
//                onShowLoading()
//            }
//            override fun onReady() {
//                super.onReady()
//                onVideoReady()
//                onHideLoading()
//                binding.ivThumb.hide()
//            }
//            override fun onEnd() {
//                super.onEnd()
//                onVideoEnded()
//            }
//            override fun onPause() {
//                super.onPause()
//                binding.ivPause.show()
//                binding.ivPause.animation = AnimationUtils.loadAnimation(binding.root.context,R.anim.trans_in)
//                isPause = true
//            }
//
//            override fun onPlay() {
//                super.onPlay()
//                if(player?.isPlaying() == true){
//                    onHideLoading()
//                }
//                if(isPause) {
//                    binding.ivPause.hide()
//                    binding.ivPause.animation = AnimationUtils.loadAnimation(binding.root.context, R.anim.trans_out)
//                    isPause = false
//                }
//            }
//
//        }
//    }
//
//    init {
//        binding.rvHashtag.adapter = hashtagAdapter
//        setResizeMode(AspectRatioFrameLayout.RESIZE_MODE_FIXED_HEIGHT)
//        binding.flPlayer.setOnClickListener {
//            if (player?.isPlaying() == true) {
//                //Toast.makeText(it.context, "Pause!!", Toast.LENGTH_SHORT).show()
//                player?.pause()
//            } else {
//                player?.play()
//            }
//        }
//        val globalEventListener =
//            object : GlobalEventListener {
//                override fun onEventReceive(data: Any?) {
//                    Timber.d("******onEventReceive $data")
//                    if(data != null && data is String){
//                        lifecycleOwner.lifecycleScope.launch {
//                            flow {
//                                delay(200)
//                                emit(true)
//                            }.flowOn(Dispatchers.IO).collect { isVoted.value = data!! }
//                        }
//                    }
//                }
//            }
//        GlobalEvent.registerEvent(GlobalEvent.VOTE_SHORT_VIDEO_EVENT, globalEventListener)
//    }
//    fun bind(item: Game30sMemberVideo) {
//        bindComponent(item)
//        bindData(item)
//        bindEvent(item)
//    }
//    private fun bindComponent(item: Game30sMemberVideo) {
////        if (player == null) {
////            player = ExoPlayerProxy(binding.root.context, lifecycleOwner.lifecycleScope).apply {
////                setInternalPlayerView(playerView = binding.exoPv)
////                addPlayerCallback(playerCallback)
////            }
////            lifecycleOwner.lifecycle.addObserver(player as DefaultLifecycleObserver)
////        }
//        Logger.d("videos = ${item.videos}")
//        if(!item.videos.isNullOrEmpty()){
//            try {
//   //             player?.prepare(request = IPlayer.Request(autoPlay = false, url = "https://bitdash-a.akamaihd.net/content/MI201109210084_1/m3u8s/f08e80da-bf1d-4e3d-8899-f0f6155f6efa.m3u8"))
//               player?.prepare(request = IPlayer.Request(autoPlay = true, url = item.videos.first().hls))
//            }catch ( ex: Exception){
//                ex.printStackTrace()
//            }
//        }
//        ImageProxy.load(
//            context = binding.root.context,
//            width = binding.root.context.resources.getDimensionPixelSize(R.dimen._22sdp),
//            height =binding.root.context.resources.getDimensionPixelSize(R.dimen._22sdp),
//            target = binding.ivAvatar,
//            url = item.avatar,
//            placeHolderId = R.drawable.image_placeholder,
//            errorDrawableId = R.drawable.image_placeholder
//        )
//        binding.tvUsername.text = item.titleVietnam
//        binding.layoutVote.alpha = VOTE_BUTTON_DISABLE_ALPHA
//        if(item.descriptionVi.isNotBlank()){
//            binding.tvContent.visibility = VISIBLE
//            binding.tvContent.setExpandableText(item.descriptionVi)
//        }
//    }
//    private fun bindData(item: Game30sMemberVideo) {
//       hashtagAdapter.bind(arrayListOf("#Team:${item.hashtag}"))
//        lifecycleOwner.lifecycleScope.launch {
//           // game30sRepository.checkVoted(item.id).collect { Set value for isVoted }
//            // Below code is fake data
//        updateVoteButton(item,isVoted = item.isVoted,isEnded = item.isEnded)
//        }
//    }
//    private fun bindEvent(item: Game30sMemberVideo) {
//        isVoted.observe(lifecycleOwner) {
//            if(!item.isVoted && item.id == isVoted.value) {
//                item.isVoted = item.id == isVoted.value
//                updateVoteButton(item = item, isVoted = item.id == isVoted.value, isEnded = item.isEnded)
//            }
//        }
//        binding.layoutVote.onClickDelay{
//                        lifecycleOwner.lifecycleScope.launch {
//                            if(!item.isEnded){ if(!item.isVoted){
//                            itemEventsListener?.eventVoteListener()
//                        }
//                    }
//                }
//            }
//        binding.layoutShare.setOnClickListener {
//                onShareLink(item.deepLink)
//            }
//    }
//    private fun updateVoteButton(item: Game30sMemberVideo, isVoted: Boolean, isEnded: Boolean) {
//        if (isEnded) {
//            binding.layoutVote.background = ContextCompat.getDrawable(binding.root.context, R.drawable.account_rounded_btn_background_disable)
//            binding.layoutVote.alpha = VOTE_BUTTON_DISABLE_ALPHA
//            binding.ivVote.setImageResource(R.drawable.ic_heart_white)
//            binding.tvVote.text = String.format(
//                binding.root.context.getString(R.string.vote_number),
//                item.votes?.toLong()?.withSuffix()
//            )
//        } else {
//            if (isVoted) {
//                binding.layoutVote.background = ContextCompat.getDrawable(binding.root.context, R.drawable.account_rounded_btn_background_enable)
//                binding.layoutVote.alpha = VOTE_BUTTON_ENABLE_ALPHA
//                binding.ivVote.setImageResource(R.drawable.ic_check_curved)
//                binding.tvVote.setText(R.string.voted)
//            } else {
//                binding.layoutVote.background = ContextCompat.getDrawable(binding.root.context, R.drawable.account_rounded_btn_background_disable)
//                binding.layoutVote.alpha = VOTE_BUTTON_ENABLE_ALPHA
//                binding.ivVote.setImageResource(R.drawable.ic_heart_white)
//                binding.tvVote.setText(R.string.vote_now)
//            }
//        }
//    }
//    private fun onShareLink(url: String) {
//        val sendIntent = Intent().apply {
//            action = Intent.ACTION_SEND
//            putExtra(Intent.EXTRA_TEXT, url)
//            type = "text/plain"
//        }
//        val shareIntent = Intent.createChooser(sendIntent, binding.root.context.getString(R.string.share))
//        binding.root.context?.startActivity(shareIntent)
//    }
//
//
//    private fun setResizeMode(resizeMode: Int) {
////        binding.exoPv.setResizeMode(resizeMode)
//    }
//
//    fun unbind() {
//        isVoted.removeObservers(lifecycleOwner)
//        player?.let {
//            it.pause()
//            it.release()
//            it.removePlayerCallback(playerCallback)
//            lifecycleOwner.lifecycle.removeObserver(it as DefaultLifecycleObserver)
//        }
//        player = null
//    }
//    companion object {
//        private const val VOTE_BUTTON_DISABLE_ALPHA = 0.35f
//        private const val VOTE_BUTTON_ENABLE_ALPHA = 1.0f
//    }
//    public interface  IEventShortVideoListener{
//        fun eventVoteListener()
//    }
}