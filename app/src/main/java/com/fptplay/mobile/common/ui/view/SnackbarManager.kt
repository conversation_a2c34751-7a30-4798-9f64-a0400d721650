package com.fptplay.mobile.common.ui.view

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Context.WINDOW_SERVICE
import android.graphics.Color
import android.graphics.PixelFormat.*
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.BounceInterpolator
import android.view.animation.DecelerateInterpolator
import android.widget.Toast
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.fptplay.mobile.R
import timber.log.Timber
import java.util.concurrent.ConcurrentLinkedQueue
import kotlin.math.abs
class SnackbarManager(private val activity  : Activity) {
    private var currentSnackbarItem: SnackbarItem? = null
    companion object {
        const val DEFAULT_DURATION_MS = 3000L
        const val TAG = "SnackbarManager"
        const val SWIPE_THRESHOLD = 100f
        const val ANIMATION_DURATION = 300L
    }
    init {
        build()
    }
    fun build(){
        Timber.d("tutest === SnackbarManager build")
        val builder = Builder(activity).build()
        currentSnackbarItem = builder
    }
    enum class AnimationType {
        FADE, SLIDE_UP, SLIDE_DOWN, SLIDE_LEFT, SLIDE_RIGHT, BOUNCE, NONE
    }

    enum class Priority {
        LOW, NORMAL, HIGH, CRITICAL
    }

    enum class Position {
        TOP_START, TOP_CENTER, TOP_END,
        CENTER_START, CENTER, CENTER_END,
        BOTTOM_START, BOTTOM_CENTER, BOTTOM_END
    }

    data class SnackbarStyle(
        @ColorInt val backgroundColor: Int = Color.TRANSPARENT,
        @ColorInt val textColor: Int = Color.WHITE,
        @ColorInt val actionTextColor: Int = Color.WHITE,
        @DrawableRes val backgroundDrawable: Int? = null,
        val cornerRadius: Float = 8f,
        val elevation: Float = 6f,
        val maxLines: Int = 2,
        val textSize: Float = 14f,
        val actionTextSize: Float = 14f,
        val marginHorizontal: Int = -1,
        val marginVertical: Int = -1
    )
    data class SnackbarItem(
        val id: String = System.currentTimeMillis().toString(),
        var text: String,
        var iconUrl: String? = null,
        var iconLocal: Int? = null,
        var duration: Long = DEFAULT_DURATION_MS,
        var placeHolderId: Int = R.drawable.ic_error_notification,
        var errorId: Int = R.drawable.ic_error_notification,
        var actionClickListener: (() -> Unit)? = null,
        var isFullWidth: Boolean = false,
        var action: String = "",
        var paddingVertical: Int = -1,
        var priority: Priority = Priority.NORMAL,
        var animationType: AnimationType = AnimationType.SLIDE_UP,
        var position: Position = Position.BOTTOM_CENTER,
        var style: SnackbarStyle = SnackbarStyle(),
        var isDismissible: Boolean = true,
        var onDismissListener: (() -> Unit)? = null,
        var customView: View? = null,
        var isOnlyTimeShow: Boolean = false,
        var isOnly:Boolean = true
    )
    private var lastMessage: String? = null
    private var lastIconLocal: Int? = null
    private var lastIconUrl: String? = null
    private val lock = Any()
    private var isShowing = false
    private var currentAnimator: Animator? = null
    private val snackbarQueue = ConcurrentLinkedQueue<SnackbarItem>()
    private val handler = Handler(Looper.getMainLooper())
    private val snackBarView: CustomSnackbarView by lazy {
        CustomSnackbarView(activity).apply {
            setupSwipeGesture(this)
        }
    }

    private val topPadding by lazy {
        activity.resources.getDimensionPixelSize(R.dimen.margin_top_snackbar)
    }

    private val bottomPadding by lazy {
        activity.resources.getDimensionPixelSize(R.dimen.margin_bottom_snackbar)
    }

    private val defaultLayoutParams by lazy {
        WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_APPLICATION,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_FULLSCREEN,
            TRANSLUCENT
        )
    }

    private val layoutParamsFullWidth by lazy {
        WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_APPLICATION,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_FULLSCREEN,
            TRANSLUCENT
        )
    }

    private var params = defaultLayoutParams
    private var toastMessage: Toast? = null
    private val windowManager by lazy { activity.getSystemService(WINDOW_SERVICE) as? WindowManager }

    class Builder(private val activity: Activity) {
        private var text: String = ""
        private var iconUrl: String? = null
        private var iconLocal: Int? = null
        private var duration: Long = DEFAULT_DURATION_MS
        private var placeHolderId: Int = R.drawable.ic_error_notification
        private var errorId: Int = R.drawable.ic_error_notification
        private var actionClickListener: (() -> Unit)? = null
        private var isFullWidth: Boolean = false
        private var action: String = ""
        private var paddingVertical: Int = -1
        private var priority: Priority = Priority.HIGH
        private var animationType: AnimationType = AnimationType.NONE
        private var position: Position = Position.BOTTOM_CENTER
        private var style: SnackbarStyle = Styles.default(activity)
        private var isDismissible: Boolean = true
        private var onDismissListener: (() -> Unit)? = null
        private var customView: View? = null
        private var isOnlyTimeShow: Boolean = false
        private var isOnly: Boolean = true
        fun text(text: String) = apply { this.text = text }
        fun iconUrl(iconUrl: String?) = apply { this.iconUrl = iconUrl }
        fun iconLocal(iconLocal: Int?) = apply { this.iconLocal = iconLocal }
        fun duration(duration: Long) = apply { this.duration = duration }
        fun placeHolderId(placeHolderId: Int) = apply { this.placeHolderId = placeHolderId }
        fun errorId(errorId: Int) = apply { this.errorId = errorId }
        fun action(action: String, listener: (() -> Unit)? = null) = apply {
            this.action = action
            this.actionClickListener = listener
        }
        fun fullWidth(isFullWidth: Boolean = true) = apply { this.isFullWidth = isFullWidth }
        fun paddingVertical(paddingVertical: Int) = apply { this.paddingVertical = paddingVertical }
        fun priority(priority: Priority) = apply { this.priority = priority }
        fun animation(animationType: AnimationType) = apply { this.animationType = animationType }
        fun position(position: Position) = apply { this.position = position }
        fun style(style: SnackbarStyle) = apply { this.style = style }
        fun dismissible(isDismissible: Boolean = true) = apply { this.isDismissible = isDismissible }
        fun onDismiss(listener: (() -> Unit)?) = apply { this.onDismissListener = listener }
        fun customView(view: View?) = apply { this.customView = view }
        fun onlyTimeShow(isOnlyTimeShow: Boolean = true) = apply { this.isOnlyTimeShow = isOnlyTimeShow }
        fun isOnly(isOnly: Boolean = true) = apply { this.isOnly = isOnly }

        fun build(): SnackbarItem {
            return SnackbarItem(
                text = text,
                iconUrl = iconUrl,
                iconLocal = iconLocal,
                duration = duration,
                placeHolderId = placeHolderId,
                errorId = errorId,
                actionClickListener = actionClickListener,
                isFullWidth = isFullWidth,
                action = action,
                paddingVertical = paddingVertical,
                priority = priority,
                animationType = animationType,
                position = position,
                style = style,
                isDismissible = isDismissible,
                onDismissListener = onDismissListener,
                customView = customView,
                isOnlyTimeShow = isOnlyTimeShow,
                isOnly = isOnly
            )
        }

        fun show(): SnackbarManager {
            val manager = SnackbarManager(activity)
            manager.showSnackbar(build())
            return manager
        }
        fun showWithItem(item: SnackbarItem): SnackbarManager {
            val manager = SnackbarManager(activity)
            manager.showSnackbar(item)
            return manager
        }
    }

    fun showSnackbar(
        text: String,
        iconUrl: String? = "",
        iconLocal: Int? = 0,
        duration: Long = DEFAULT_DURATION_MS,
        placeHolderId: Int = R.drawable.ic_error_notification,
        errorId: Int = R.drawable.ic_error_notification,
        actionClickListener: (() -> Unit)? = null,
        isFullWidth: Boolean = false,
        action: String = "",
        position: Position,
        isOnlyTimeShow: Boolean = false,
        paddingVertical: Int = -1,
        isOnly: Boolean = true
    ) {
        val shouldShow = synchronized(lock) {
            if (isOnlyTimeShow && text == lastMessage && iconLocal == lastIconLocal && iconUrl == lastIconUrl) {
                false
            } else {
                lastIconLocal = iconLocal
                lastIconUrl = iconUrl
                lastMessage = text
                true
            }
        }
        if (!shouldShow) return
        val item = SnackbarItem(
            text = text,
            iconUrl = iconUrl,
            iconLocal = iconLocal,
            duration = duration,
            placeHolderId = placeHolderId,
            errorId = errorId,
            actionClickListener = actionClickListener,
            isFullWidth = isFullWidth,
            action = action,
            paddingVertical = paddingVertical,
            isOnlyTimeShow = isOnlyTimeShow,
            position = position,
            isOnly = isOnly
        )
        showSnackbarInternal(item)
    }

    fun showSnackbar(item: SnackbarItem) {
        synchronized(lock) {
            when (item.priority) {
                Priority.CRITICAL -> {
                    snackbarQueue.clear()
                    hideSnackbar()
                    showSnackbarInternal(item)
                }
                Priority.HIGH -> {
                    val tempQueue = mutableListOf<SnackbarItem>()
                    while (snackbarQueue.isNotEmpty()) {
                        tempQueue.add(snackbarQueue.poll() as SnackbarItem)
                    }
                    snackbarQueue.offer(item)
                    tempQueue.forEach { snackbarQueue.offer(it) }
                    processQueue()
                }
                else -> {
                    snackbarQueue.offer(item)
                    processQueue()
                }
            }
        }
    }

    private fun showSnackbarInternal(item: SnackbarItem) {
        try {
            currentSnackbarItem = item
            isShowing = true
            setupSnackbarView(item)
            params = if (item.isFullWidth) layoutParamsFullWidth else defaultLayoutParams
            params.gravity = getGravityFromPosition(item.position)

            applyCustomStyling(item.style)

            showMessageOverlay(item)

        } catch (ex: Exception) {
            Timber.e(TAG, "Error showing snackbar", ex)
            showToastMessage(item)
        }
    }

    private fun setupSnackbarView(item: SnackbarItem) {
        if (item.customView != null) {
            return
        }
        snackBarView.setText(item.text)
        when {
            !item.iconUrl.isNullOrBlank() -> {
                snackBarView.setIconUrl(item.iconUrl, item.placeHolderId, item.errorId)
            }
            item.iconLocal != null && item.iconLocal != 0 -> {
                snackBarView.setIconLocal(item.iconLocal?:R.drawable.ic_error_notification)
            }
            else -> {
                snackBarView.hideIcon()
            }
        }

        when (getGravityFromPosition(item.position)) {
            Gravity.TOP or Gravity.CENTER -> {
                snackBarView.setPaddingForLayout(
                    0,
                    topPadding,
                    0,
                    0
                )
            }
            else -> {
                snackBarView.setPaddingForLayout(
                    0,
                    0,
                    0,
                    item.paddingVertical.takeIf { it > 0 } ?: bottomPadding
                )
            }
        }
        snackBarView.setAction(item.action, item.actionClickListener)
        snackBarView.setLayoutWidth(isFullWidth = item.isFullWidth)
    }

    private fun getGravityFromPosition(position: Position): Int {
        return when (position) {
            Position.TOP_START -> Gravity.TOP or Gravity.START
            Position.TOP_CENTER -> Gravity.TOP or Gravity.CENTER
            Position.TOP_END -> Gravity.TOP or Gravity.END
            Position.CENTER_START -> Gravity.CENTER_VERTICAL or Gravity.START
            Position.CENTER -> Gravity.CENTER
            Position.CENTER_END -> Gravity.CENTER_VERTICAL or Gravity.END
            Position.BOTTOM_START -> Gravity.BOTTOM or Gravity.START
            Position.BOTTOM_CENTER -> Gravity.BOTTOM or Gravity.CENTER
            Position.BOTTOM_END -> Gravity.BOTTOM or Gravity.END
        }
    }

    private fun applyCustomStyling(style: SnackbarStyle) {
        style.backgroundDrawable?.let { drawable ->
            snackBarView.background = ContextCompat.getDrawable(activity, drawable)
        } ?: run {
            snackBarView.setBackgroundColor(style.backgroundColor)
        }
        snackBarView.elevation = style.elevation
    }

    private fun processQueue() {
        if (!isShowing && snackbarQueue.isNotEmpty()) {
            val nextItem = snackbarQueue.poll()
            nextItem?.let { showSnackbarInternal(it) }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setupSwipeGesture(view: View) {
        var startX = 0f
        var startY = 0f
        var isDragging = false

        view.setOnTouchListener { _, event ->
            val item = currentSnackbarItem
            if (item?.isDismissible != true) return@setOnTouchListener false
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    startX = event.rawX
                    startY = event.rawY
                    isDragging = false
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    val deltaX = event.rawX - startX
                    val deltaY = event.rawY - startY

                    if (abs(deltaX) > SWIPE_THRESHOLD && abs(deltaX) > abs(deltaY)) {
                        isDragging = true
                        view.translationX = deltaX
                        view.alpha = 1f - (abs(deltaX) / (view.width * 0.5f))
                    }
                    true
                }
                MotionEvent.ACTION_UP -> {
                    if (isDragging) {
                        val deltaX = event.rawX - startX
                        if (abs(deltaX) > view.width * 0.3f) {
                            // Dismiss with swipe animation
                            dismissWithSwipe(deltaX > 0)
                        } else {
                            // Return to original position
                            animateToOriginalPosition()
                        }
                    }
                    isDragging = false
                    true
                }
                else -> false
            }
        }
    }

    private fun showToastMessage(item: SnackbarItem) {
        hideSnackbar()
        toastMessage = Toast(activity)
        toastMessage?.apply {
            view = snackBarView
            duration = Toast.LENGTH_LONG
            setGravity(getGravityFromPosition(item.position), 0, 0)
            show()
        }
    }

    private fun dismissWithSwipe(toRight: Boolean) {
        val targetX = if (toRight) snackBarView.width.toFloat() else -snackBarView.width.toFloat()

        ObjectAnimator.ofFloat(snackBarView, "translationX", targetX).apply {
            duration = ANIMATION_DURATION
            interpolator = AccelerateDecelerateInterpolator()
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    hideSnackbar()
                    currentSnackbarItem?.onDismissListener?.invoke()
                }
            })
            start()
        }
    }

    private fun animateToOriginalPosition() {
        ObjectAnimator.ofFloat(snackBarView, "translationX", 0f).apply {
            duration = ANIMATION_DURATION
            interpolator = DecelerateInterpolator()
            start()
        }

        ObjectAnimator.ofFloat(snackBarView, "alpha", 1f).apply {
            duration = ANIMATION_DURATION
            start()
        }
    }

    private fun animateEntry(item: SnackbarItem) {
        when (item.animationType) {
            AnimationType.SLIDE_UP -> {
                snackBarView.translationY = snackBarView.height.toFloat()
                ObjectAnimator.ofFloat(snackBarView, "translationY", 0f).apply {
                    duration = ANIMATION_DURATION
                    interpolator = DecelerateInterpolator()
                    start()
                }
            }
            AnimationType.SLIDE_DOWN -> {
                snackBarView.translationY = -snackBarView.height.toFloat()
                ObjectAnimator.ofFloat(snackBarView, "translationY", 0f).apply {
                    duration = ANIMATION_DURATION
                    interpolator = DecelerateInterpolator()
                    start()
                }
            }
            AnimationType.SLIDE_LEFT -> {
                snackBarView.translationX = snackBarView.width.toFloat()
                ObjectAnimator.ofFloat(snackBarView, "translationX", 0f).apply {
                    duration = ANIMATION_DURATION
                    interpolator = DecelerateInterpolator()
                    start()
                }
            }
            AnimationType.SLIDE_RIGHT -> {
                snackBarView.translationX = -snackBarView.width.toFloat()
                ObjectAnimator.ofFloat(snackBarView, "translationX", 0f).apply {
                    duration = ANIMATION_DURATION
                    interpolator = DecelerateInterpolator()
                    start()
                }
            }
            AnimationType.FADE -> {
                snackBarView.alpha = 0f
                ObjectAnimator.ofFloat(snackBarView, "alpha", 1f).apply {
                    duration = ANIMATION_DURATION
                    start()
                }
            }
            AnimationType.BOUNCE -> {
                snackBarView.translationY = snackBarView.height.toFloat()
                ObjectAnimator.ofFloat(snackBarView, "translationY", 0f).apply {
                    duration = ANIMATION_DURATION * 2
                    interpolator = BounceInterpolator()
                    start()
                }
            }
            AnimationType.NONE -> {

            }
        }
    }

    private val hideSnackBarRunnable = Runnable {
        hideSnackbar()
        lastMessage = null
        lastIconLocal = null
        lastIconUrl = null
    }

    private fun showMessageOverlay(item: SnackbarItem) {
        hideSnackbar()
        try {
            params.apply {
                gravity = getGravityFromPosition(item.position)
            }
            windowManager?.addView(snackBarView, params)
            animateEntry(item)
            snackBarView.postDelayed(hideSnackBarRunnable, item.duration)
        } catch (ex: Exception) {
            Timber.e(TAG, "Error showing message overlay", ex)
            showToastMessage(item)
        }
    }

    fun hideSnackbar() {
        try {
            currentAnimator?.cancel()
            windowManager?.removeView(snackBarView)
            snackBarView.removeCallbacks(hideSnackBarRunnable)
            isShowing = false
            if (currentSnackbarItem?.isOnly ==true){
                currentSnackbarItem = null
            }
            handler.postDelayed({ processQueue() }, 100)
        } catch (ex: Exception) {
            Timber.e(TAG, "Error hiding snackbar", ex)
            toastMessage?.cancel()
        }
    }

    fun clearQueue() {
        snackbarQueue.clear()
    }

    fun getQueueSize(): Int {
        return snackbarQueue.size
    }

    fun isCurrentlyShowing(): Boolean {
        return isShowing
    }

    fun getCurrentItem(): SnackbarItem? {
        return currentSnackbarItem
    }
    fun updateCurrentItem(item: SnackbarItem) {
        currentSnackbarItem = item
    }

    object Styles {
        fun default(activity: Activity) = SnackbarStyle(
            backgroundColor = ActivityCompat.getColor(activity, android.R.color.transparent),
            textColor = Color.WHITE
        )
    }
}

