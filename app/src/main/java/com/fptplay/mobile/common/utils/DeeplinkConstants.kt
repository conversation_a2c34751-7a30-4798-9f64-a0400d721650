package com.fptplay.mobile.common.utils

object DeeplinkConstants {
    const val REQUIRE_RESUME_ACTIVITY = "require-resume-activity"

    const val HOST_FPT_PLAY = "fptplay.vn"
    const val HOST_BETA_FPT_PLAY = "beta-dev.fptplay.vn"
    const val HOST_STAGING_FPT_PLAY = "staging.fptplay.vn"
    const val HOST_DEV_FPT_PLAY = "dev.fptplay.vn"
    const val SCHEME_FPT_PLAY = "fptplay"
    const val HOST_SUPPORT_FPT_PLAY = "hotro.fptplay.vn"
    const val HOST_FIREBASE_DYNAMIC_LINK_STAG = "fplay.page.link"
    const val HOST_FIREBASE_DYNAMIC_LINK_PRO = "fptplay.page.link"

    const val HOST_ADJUST_TRUE_LINK_STAG = "fptplay-staging.go.link"
    const val HOST_ADJUST_TRUE_LINK_PROD = "fptplay.go.link"

    // screen
    const val DEEPLINK__SCREEN__LIVE_TV = "xem-truyen-hinh"
    const val DEEPLINK__SCREEN__HBO_GO = "hbo-go"
    const val DEEPLINK__SCREEN__GALAXY__PLAY = "galaxy-play"
    const val DEEPLINK__SCREEN__VOD = "danh-muc"
    const val DEEPLINK__SCREEN__WATCH_VOD = "xem-video"
    const val DEEPLINK__SCREEN__DETAIL_VOD = "chi-tiet-video"
    const val DEEPLINK__SCREEN__SPORT = "the-thao"
    const val DEEPLINK__SCREEN__SPORT__WATCH_VIDEO = "xem-video"
    const val DEEPLINK__SCREEN__PREMIER__VOD = "cong-chieu"
    const val DEEPLINK__SCREEN__PLAYLIST = "playlist"
    const val DEEPLINK__SCREEN__QUICK_LOGIN = "tv"

    const val DEEPLINK__SCREEN__EVENT = "su-kien"
    const val DEEPLINK__SCREEN__BUY_PACKAGE = "mua-goi"
    const val DEEPLINK__SCREEN__SERVICE = "dich-vu"
    const val DEEPLINK__SCREEN__ACCOUNT = "tai-khoan"
    const val DEEPLINK__SCREEN__REGISTER = "dang-ky"
    const val DEEPLINK__SCREEN__LOGIN = "dang-nhap"
    const val DEEPLINK__SCREEN__NOTIFICATION = "thong-bao"
    const val DEEPLINK__SCREEN__SEARCH = "tim-kiem"
    const val DEEPLINK__SCREEN__REWARD = "loyalty"
    const val DEEPLINK__SCREEN__SUPPORT = "ho-tro"
    const val DEEPLINK__SCREEN__PAGE_IZIOS = "trang"
    const val DEEPLINK__SCREEN__BLOCK_IZIOS = "block"

    const val DEEPLINK__SCREEN__TV__FROM_LANDING_PAGE = "tv-landing-page"
    const val DEEPLINK__SCREEN__VOD__FROM_LANDING_PAGE = "vod-landing-page"
    const val DEEPLINK__SCREEN__EVENT__FROM_LANDING_PAGE = "event-landing-page"
    const val DEEPLINK__SCREEN__EVENT_TV__FROM_LANDING_PAGE = "eventtv-landing-page"

    const val DEEPLINK__SCREEN__GAME__ZONE = "gamezone"
    const val DEEPLINK__SCREEN__PLAY__ZONE = "playzone"
    const val DEEPLINK__SCREEN__SHOP_GAME = "shopgame"

    const val DEEPLINK__SCREEN__DOWNLOAD = "tai-xuong"
//    const val DEEPLINK__SCREEN__MULTI_PROFILE = "multi-profile"
    const val DEEPLINK__SCREEN__PLADIO = "pladio"

    //IZIOS Page
    const val DEEPLINK__MENU__PAGE_IZIOS__HBO_GO = "hbogo"
    const val DEEPLINK__MENU__PAGE_IZIOS__SPORT = "sport"

    // HOME VOD Category
    const val DEEPLINK__MENU__VOD__PHIMBO = "phim-bo"
    const val DEEPLINK__MENU__VOD__PHIMBO__PAGEID = "series"
    const val DEEPLINK__MENU__VOD__PHIMLE = "phim-le"
    const val DEEPLINK__MENU__VOD__PHIMLE__PAGE_ID = "phim_le"
    const val DEEPLINK__MENU__VOD__PHIMRAP = "phim-chieu-rap"
    const val DEEPLINK__MENU__VOD__PHIMRAP__PAGE_ID = "cinema"
    const val DEEPLINK__MENU__VOD__DACSAC = "goi-dac-sac"
    const val DEEPLINK__MENU__VOD__DACSAC__PAGE_ID = "special"
    const val DEEPLINK__MENU__VOD__TVSHOW = "tv-show"
    const val DEEPLINK__MENU__VOD__TVSHOW__PAGE_ID = "tvshow"
    const val DEEPLINK__MENU__VOD__ANIME = "anime"
    const val DEEPLINK__MENU__VOD__ANIME__PAGE_ID = "anime"
    const val DEEPLINK__MENU__VOD__THIEUNHI = "thieu-nhi"
    const val DEEPLINK__MENU__VOD__THIEUNHI__PAGE_ID = "children"
    const val DEEPLINK__MENU__VOD__HAI = "hai"
    const val DEEPLINK__MENU__VOD__HAI__PAGE_ID = "comedy"
    const val DEEPLINK__MENU__VOD__HOCONLINE = "hoc-online"
    const val DEEPLINK__MENU__VOD__HOCONLINE__PAGE_ID = "study"
    const val DEEPLINK__MENU__VOD__MUSIC = "am-nhac"
    const val DEEPLINK__MENU__VOD__MUSIC__PAGE_ID = "music"

    const val DEEPLINK__MENU__EVENT__CHOIHAYCHIA = "choi-hay-chia"

    const val DEEPLINK__MENU__EVENT__GAME30s = "ngoisao30s"
    const val DEEPLINK__SUB_MENU__GAME30s__TEAM = "teams"
    const val DEEPLINK__SUB_MENU__GAME30s__VIDEO_DETAIL = "chi-tiet"
    const val DEEPLINK__SUB_MENU__GAME30s__GAME_RANKING = "bxh"
    const val DEEPLINK__SUB_MENU__GAME30s__GAME_GUIDE = "huong-dan"
    const val DEEPLINK__SUB_MENU__GAME30s__GAME = "game"

    // Buy Package
    const val DEEPLINK__MENU__BUY_PACKAGE__MAX = "max-xmas"
    const val DEEPLINK__MENU__BUY_PACKAGE__VIP = "vip-xmas"
    const val DEEPLINK__MENU__BUY_PACKAGE__SPORT = "sport"
    const val DEEPLINK__MENU__BUY_PACKAGE__KPLUS = "kplus"
    const val DEEPLINK__MENU__BUY_PACKAGE__3G = "3g"

    // Account
    const val DEEPLINK__MENU__ACCOUNT__ACCINFO = "thong-tin-ca-nhan"
    const val DEEPLINK__MENU__ACCOUNT__DEVICE = "quan-li-thiet-bi"
    const val DEEPLINK__MENU__ACCOUNT__PROMOCODE = "ma-kich-hoat"
    const val DEEPLINK__MENU__ACCOUNT__PAIDHISTORY = "lich-su-giao-dich"
    const val DEEPLINK__MENU__ACCOUNT__PAIDPACKAGE = "dich-vu-da-mua"
    const val DEEPLINK__MENU__ACCOUNT__FAVORITE = "yeu-thich"
    const val DEEPLINK__MENU__ACCOUNT__HISTORY_FRIEND = "historyfriend"
    const val DEEPLINK__MENU__ACCOUNT__INVITE_FRIEND = "gioi-thieu-ban-be"
    const val DEEPLINK__MENU__ACCOUNT__MULTI_PROFILE = "multi-profiles"


    // HBO GO
    const val DEEPLINK__MENU__HBO_GO__MENU = "danh-muc"
    const val DEEPLINK__MENU__HBO_GO__LIVE = "kenh-truc-tiep"
    const val DEEPLINK__MENU__HBO_GO__WATCH_VOD = "xem-video"
    const val DEEPLINK__MENU__HBO_GO__DETAIL_VOD = "chi-tiet-video"

    const val DEEPLINK__SUB_MENU__HBO_GO__MENU__ALL = "tat-ca"
    const val DEEPLINK__SUB_MENU__HBO_GO__LIVE__HBO = "hbo"

    const val DEEPLINK__ID__HBO_GO__MENU__PHIMBO = "5ce263b92089bd06e8f89188"
    const val DEEPLINK__ID__HBO_GO__MENU__DIENANH = "5ce264962089bd081c5364d1"
    const val DEEPLINK__ID__HBO_GO__MENU__CHAUA = "5ce279d92089bd0741f89188"
    const val DEEPLINK__ID__HBO_GO__MENU__TAILIEU = "5ce27b1a2089bd081e5364d1"
    const val DEEPLINK__ID__HBO_GO__MENU__THIEUNHI = "5ce27b7c2089bd0729f8918b"

    const val DEEPLINK__NAVIGATE__START = "deeplink__navigate__start"
    const val DEEPLINK__NAVIGATE__STOP = "deeplink__navigate__stop"

    // SUBSCRIPTION
    const val DEEPLINK__MENU__BUY_PACKAGE__SERVICE = "dich-vu"

    //region App link from facebook
    const val APP_LINK_FACE_BOOK_DATA_KEY = "app-link-face-book-data-key"
    const val APP_LINK_FACE_BOOK_NEW_KEY = "app-link-face-book-new-key"
    const val APP_LINK_FACE_BOOK_VIA_SDK_KEY = "app-link-face-book-via-sdk-key"
    //endregion

    //region Fire base dynamic link
    const val FIREBASE_DYNAMIC_LINK_URL_KEY = "firebase-dynamic-link-url-key"
    const val FIREBASE_DYNAMIC_LINK_NEW_KEY = "firebase-dynamic-link-new-key"
    //endregion

    //region Adjust true link
    const val ADJUST_TRUE_LINK_URL_KEY = "adjust-true-link-url-key"
    const val ADJUST_TRUE_LINK_NEW_KEY = "adjust-true-link-new-key"

    const val ADJUST_TRUE_LINK_BROADCAST_INTENT = "com.fplay.activity.adjust_true_link"
    //endregion

    //region Deeplink 30s Data
    const val DEEPLINK__30s__BUNDLE_NAME = "DEEPLINK__30s__BUNDLE_NAME"
    const val DEEPLINK__30s__BUNDLE__SCREEN_KEY = "screen"
    const val DEEPLINK__30s__BUNDLE__TEAM_ID_KEY = "teamId"
    const val DEEPLINK__30s__BUNDLE__GAME_ID_KEY = "gameId"
    const val DEEPLINK__30s__BUNDLE__VIDEO_ID_KEY = "videoId"

    //region Deeplink HipFest
    const val DEEPLINK__SCREEN__HIP_FEST = "hipfest"


    //region Page IZIOS extends arguments
    const val DEEPLINK__PAGE_IZIOS__BLOCK__LEAGUE_RESULT_KEY = "ket-qua"
    const val DEEPLINK__PAGE_IZIOS__BLOCK__LEAGUE_SCHEDULE_KEY = "lich-dau"
    const val DEEPLINK__PAGE_IZIOS__BLOCK__LEAGUE_RANK_KEY = "bang-xep-hang"
    const val DEEPLINK__PAGE_IZIOS__BLOCK__SPORT_SCHEDULE_KEY = "lich-thi-dau"

    const val DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE_NAME = "DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE_NAME"
    const val DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE__SCREEN_KEY = "screen"
    const val DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE__PAGE_ID_KEY = "session"

    //region Sport Share Schedule
    const val DEEPLINK__SPORT__BUNDLE__SCREEN__SCHEDULE = "sport"
    const val DEEPLINK__SPORT__BUNDLE__EVENT = "event"
    const val DEEPLINK__SPORT__BUNDLE__GENRE_EVENT = "event"

    const val DEEPLINK__SPORT__BUNDLE__GENRE = "genre"
    //region MegaZone
    const val DEEPLINK__MEGAZONE = "megazone"
    //region Foxpay
    const val DEEPLINK__FOXPAY = "foxpay"
    //endregion Foxpay

    const val DEEPLINK__PAGE_MEGAZONE__3G ="mien-phi-3g-4g"
    //endregion MegaZone

    //region FPT Play Shop
    const val DEEPLINK__FPTPLAYSHOP = "fptplayshop"
    const val DEEPLINK__FPTPLAYSHOP__LIST__ORDER = "danh-sach-don-hang"
    const val DEEPLINK__FPTPLAYSHOP__DETAIL__ORDER = "chi-tiet-don-hang"
    //endregion

    const val FOXPAY__PHONE__RECHARGE = "naptiendienthoai"
    const val FOXPAY__HOME = "vifoxpay"
    // gamme mdbd

    const val DEEPLINK__MDBD = "mua-day-ban-dinh"
    const val DEEPLINK__LSTSB = "luot-song-tren-san-bong"

    const val DEEPLINK__LOYALTY = "khach-hang-than-thiet"
    const val DEEPLINK__SCREEN__APPLICATION = "ung-dung"

    //moment share
    const val DEEPLINK__SCREEN__MOMENT = "moments"
    const val DEEPLINK__SCREEN__SHORTS_VIDEO = "short-videos"

    // region Pladio
    const val DEEPLINK__MENU__PLADIO__SERIES = "series"
    const val DEEPLINK__MENU__PLADIO__PLAYLIST = "playlist"
    const val DEEPLINK__MENU__PLADIO__ALBUM = "album"
    const val DEEPLINK__MENU__PLADIO__PLAY = "play"

    // endregion Pladio

    //region clevertap payload
    const val DEEPLINK__FROM_CLEVERTAP = "deeplink_from_clevertap"
    const val DEEPLINK__CLEVERTAP_CAMPAIN_ID = "wzrk_id"
    const val DEEPLINK__CLEVERTAP_TITLE = "nt"
    const val DEEPLINK__CLEVERTAP_DESCRIPTION = "nm"
    const val DEEPLINK__CLEVERTAP_LINK = "wzrk_dl"
    //endregion
}