<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:gravity="center"
    android:orientation="vertical">
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_background"
        android:scaleType="centerCrop"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/_22sdp"
            android:layout_height="@dimen/_22sdp"
            android:layout_gravity="end"
            android:layout_marginTop="@dimen/_27sdp"
            android:layout_marginEnd="@dimen/_4sdp"
            android:padding="@dimen/_6sdp"
            android:scaleType="fitXY"
            app:srcCompat="@drawable/ic_x_close" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="@dimen/_236sdp"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_head_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/sf_pro_display_semibold"
                android:textColor="@color/white_38"
                android:textSize="@dimen/_6ssp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Gợi ý cho bạn" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_1sdp"
                android:fontFamily="@font/sf_pro_display_semibold"
                android:textColor="@color/white_87"
                android:textSize="@dimen/_8ssp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_head_title"
                tools:text="Liên kết số điện thoại ádadasdadadasda" />

            <TextView
                android:id="@+id/tv_des"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_1sdp"
                android:fontFamily="@font/sf_pro_display_regular"
                android:textColor="@color/white_60"
                android:textSize="@dimen/_6ssp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_title"
                tools:text="Hãy liên kết số điện thoại của bạn để có thể đăng nhập và sử dụng dịch vụ của FPT Play trên các thiết bị khác như: Thiết bị di động, SmartTV và trình duyệt máy tính." />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_interaction"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/_6sdp"
                android:layout_marginBottom="@dimen/_6sdp"
                android:scaleType="fitCenter"
                app:layout_constraintBottom_toTopOf="@id/btn_go"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_des" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_go"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_20sdp"
                android:layout_marginBottom="@dimen/_13sdp"
                android:background="@drawable/login_button_rectangle_disable"
                android:fontFamily="@font/sf_pro_display_semibold"
                android:textAllCaps="false"
                android:textColor="@color/white_87"
                android:textSize="@dimen/_7ssp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:text="@string/survey_start_survey" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>
</RelativeLayout>
