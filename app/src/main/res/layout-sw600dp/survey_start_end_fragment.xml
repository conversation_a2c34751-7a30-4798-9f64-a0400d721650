<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/transparent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:orientation="vertical"
        android:layout_width="@dimen/_236sdp"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/tv_survey_title"
            tools:text="FPT Play mời bạn tham gia khảo sát"
            android:layout_gravity="center"
            android:gravity="center"
            android:textSize="@dimen/_10ssp"
            android:textColor="@color/white_87"
            android:fontFamily="@font/sf_pro_display_semibold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <androidx.appcompat.widget.AppCompatImageView
            android:layout_gravity="center"
            android:id="@+id/iv_survey_image"
            android:layout_width="@dimen/_99sdp"
            android:layout_height="@dimen/_99sdp"
            android:layout_marginTop="@dimen/_12sdp"
            android:layout_marginVertical="@dimen/_10sdp"
            android:scaleType="fitXY"
            app:srcCompat="@drawable/thumb_survey" />
        <TextView
            android:id="@+id/tv_survey_description"
            tools:text="Trả lời câu hỏi để giúp FPT Play phục vụ bạn tốt hơn"
            android:layout_gravity="center"
            android:textSize="@dimen/_6ssp"
            android:textColor="@color/white_87"
            android:fontFamily="@font/sf_pro_display_semibold"
            android:gravity="center"
            android:layout_width="@dimen/_99sdp"
            android:layout_height="wrap_content"/>
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_start_survey"
        android:layout_width="@dimen/_236sdp"
        android:layout_height="@dimen/_20sdp"
        android:layout_marginBottom="@dimen/_13sdp"
        tools:text="@string/survey_start_survey"
        android:textAllCaps="false"
        android:textSize="@dimen/_7ssp"
        android:textColor="@color/white_87"
        android:fontFamily="@font/sf_pro_display_semibold"
        android:background="@drawable/login_button_rectangle_enable"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
