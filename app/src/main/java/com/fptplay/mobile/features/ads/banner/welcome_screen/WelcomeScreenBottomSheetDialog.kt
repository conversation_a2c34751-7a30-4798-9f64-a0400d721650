package com.fptplay.mobile.features.ads.banner.welcome_screen

import android.app.Dialog

import android.content.res.Configuration
import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.view.*
import androidx.annotation.UiThread
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.drowsyatmidnight.haint.android_banner_sdk.popup_banner.PopupBanner
import com.fptplay.mobile.R
import com.fptplay.mobile.common.ui.bases.BaseFullDialogFragment
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.databinding.AdsWelcomScreenBottomSheetDialogBinding
import com.fptplay.mobile.features.ads.banner.AdsBannerListener
import com.fptplay.mobile.features.ads.AdsViewModel
import com.fptplay.mobile.features.ads.banner.welcome_screen.WelcomeScreenBottomSheetDialogArgs
import com.fptplay.mobile.features.ads.utils.AdsConstants
import com.tear.modules.tracking.model.Infor
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.lang.Exception
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@AndroidEntryPoint
class WelcomeScreenBottomSheetDialog :
    BaseFullDialogFragment<AdsViewModel.AdsState, AdsViewModel.AdsIntent>() {

    @Inject
    lateinit var trackingInfo: Infor
    @Inject
    lateinit var sharedPreferences: SharedPreferences

    private var _binding: AdsWelcomScreenBottomSheetDialogBinding? = null
    private val binding get() = _binding!!

    private val popupBanner by lazy { PopupBanner(requireActivity().applicationContext) }

    private val safeArgs: WelcomeScreenBottomSheetDialogArgs by navArgs()
    private var timeOutCountDownTimer: CountDownTimer? = null
    private var adsSkipCountDownTimer: CountDownTimer? = null
    private var currentTimeoutLoadHtml = 0L

    override val viewModel: AdsViewModel by activityViewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NORMAL, R.style.AdsDialogFullscreen)
    }

//    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
//        val bottomSheetDialog = object : BottomSheetDialog(requireContext(), theme) {
//            override fun onBackPressed() {
//                if(viewModel.currentWelcomeScreenAdsCountdown() <= 0) {
//                    dismissAllowingStateLoss()
//                }
//            }
//        }
//        bottomSheetDialog.apply {
//            requestWindowFeature(Window.FEATURE_NO_TITLE)
//            setCanceledOnTouchOutside(false)
//            setCancelable(false)
//            Timber.tag("tamlog").e("onCreateDialog $bottomSheetDialog")
//            setOnShowListener {
//                val bottomSheet =
//                    bottomSheetDialog.findViewById<FrameLayout>(com.google.android.material.R.id.design_bottom_sheet)
//                Timber.tag("tamlog").e("bottomSheet $bottomSheet")
//                if (bottomSheet != null) {
//                    val behavior = BottomSheetBehavior.from(bottomSheet)
//                    behavior.apply {
//                        skipCollapsed = true
//                        state = BottomSheetBehavior.STATE_EXPANDED
//                        peekHeight = Resources.getSystem().displayMetrics.heightPixels
//                        addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
//                            override fun onStateChanged(bottomSheet: View, newState: Int) {
//                                state = BottomSheetBehavior.STATE_EXPANDED
//                            }
//
//                            override fun onSlide(bottomSheet: View, slideOffset: Float) {}
//                        })
//                    }
//
//                }
//
//            }
//        }
//        return bottomSheetDialog
//    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val bottomSheetDialog = object :  Dialog(requireContext(), theme) {
            @Deprecated("Deprecated in Java")
            override fun onBackPressed() {
                if(viewModel.currentWelcomeScreenAdsCountdown() <= 0) {
                    dismissAllowingStateLoss()
                }
            }
        }
        bottomSheetDialog.apply {
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            setCanceledOnTouchOutside(false)
            setCancelable(false)
            Timber.tag("tamlog").e("onCreateDialog $bottomSheetDialog")
        }
        return bottomSheetDialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = AdsWelcomScreenBottomSheetDialogBinding.inflate(inflater, container, false)
        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Timber.tag("tamlog").e("Welcomescreen onViewCreate")
        sharedPreferences.saveIsShowAdsHomeScreenKey(false) // stop from show ads another time
    }

    override fun onResume() {
        super.onResume()
        val currentTimeAds = viewModel.currentWelcomeScreenAdsCountdown()
        if(currentTimeoutLoadHtml <=0) {
            startAdsSkipCountDownTimer(currentTimeAds)
        }
    }

    override fun onPause() {
        super.onPause()
        timeOutCountDownTimer?.cancel()
        timeOutCountDownTimer = null
        adsSkipCountDownTimer?.cancel()
        adsSkipCountDownTimer = null
        if(currentTimeoutLoadHtml > 0) dismissAllowingStateLoss()

    }

    override fun onDestroy() {
        destroyBanner()
        super.onDestroy()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        timeOutCountDownTimer?.cancel()
        timeOutCountDownTimer = null
        adsSkipCountDownTimer?.cancel()
        adsSkipCountDownTimer = null
        _binding = null
    }



    override fun bindComponent() {
        super.bindComponent()
//        setBottomSheetFullScreen()
        popupBanner.setPopupBannerView(popupBannerView = binding.popupBannerView)
        currentTimeoutLoadHtml = TimeUnit.SECONDS.toMillis(COUNT_DOWN_TIME_LOAD_HTML_IN_SECONDS)

        startTimeoutCountDownTimer(currentTimeoutLoadHtml)

    }

    override fun dismissAllowingStateLoss() {
        super.dismissAllowingStateLoss()
        viewModel.saveWelcomeScreenHtmlContent(null)
        viewModel.saveCurrentWelcomeScreenAdsCountdown(null)

    }

    fun navigateUp() {
        findNavController().navigateUp()
        viewModel.saveWelcomeScreenHtmlContent(null)
        viewModel.saveCurrentWelcomeScreenAdsCountdown(null)
    }

    override fun bindEvent() {
        popupBanner.bannerListener = object : AdsBannerListener(viewLifecycleOwner, trackingInfo) {
            override fun onBannerLoaded(countdown: Int) {
                Timber.tag("tamlog").d("onBannerLoaded $countdown")
                currentTimeoutLoadHtml = 0
                timeOutCountDownTimer?.cancel()
                timeOutCountDownTimer = null
                viewModel.dispatchIntent(AdsViewModel.AdsIntent.ShowCountDownPopupBanner(countdown))
            }

            override fun onClickAds(url: String?, useWebViewInApp: Boolean?) {
                viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                    Timber.d("onClickAds WelcomeScreen: $url - $useWebViewInApp")
                    navigateUp()
                    setFragmentResult(AdsConstants.ON_ADS_CLICK_REQUEST_KEY,
                        bundleOf(
                            AdsConstants.ON_ADS_CLICK_REQUEST_URL to url,
                            AdsConstants.ON_ADS_CLICK_REQUEST_USE_WEB_VIEW to useWebViewInApp
                        )

                    )

                }
            }

            override fun onCloseBanner() {
                Timber.tag("tamlog").d("onCloseBanner")
                dismissAllowingStateLoss()
            }

        }

    }


    override fun bindData() {
        super.bindData()
        val contentHtml = if(viewModel.welcomeScreenHtmlContent().isNotBlank()) {
            viewModel.welcomeScreenHtmlContent()
        } else {
            viewModel.saveWelcomeScreenHtmlContent(safeArgs.contentHtml)
            safeArgs.contentHtml
        }
        if(contentHtml.isBlank()) {
            dismissAllowingStateLoss()
            return
        }
        popupBanner.showBannerWithHtml(contentHtml)

    }

    override fun AdsViewModel.AdsState.toUI() {
        when (this) {
            is AdsViewModel.AdsState.ActionShowCountDownPopupBanner -> {
                startAdsSkipCountDownTimer(this.countdown)
            }
            else -> {}
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
//        if(requireContext().isTablet()) {
//            setBottomSheetFullScreen()
//        }
    }

    // region Commons
    private fun setBottomSheetFullScreen() {
        binding.root.layoutParams.height = getScreenHeightWithoutInsets()
    }

    private fun getScreenHeightWithoutInsets(): Int {
        val curActivity = activity
        val height = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && curActivity != null) {
            val metrics = curActivity.windowManager.currentWindowMetrics
            // Gets all excluding insets
            val windowInsets = metrics.windowInsets
            val insets = windowInsets.getInsetsIgnoringVisibility(
                WindowInsets.Type.navigationBars()
                        or WindowInsets.Type.displayCutout()
            )
            val insetsHeight = insets.top + insets.bottom

            // Legacy size that Display#getSize reports
            val bounds = metrics.bounds
            bounds.height() - insetsHeight
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && curActivity != null) {
            val decorView = curActivity.window.decorView
            val windowInsets = decorView.rootWindowInsets
            if(windowInsets != null) {
                val insetsHeight =
                    windowInsets.systemWindowInsetTop + windowInsets.systemWindowInsetBottom
                decorView.height - insetsHeight
            } else {
                0
            }

        } else if(curActivity != null){
            val rectangle = Rect()
            val window = curActivity.window
            window.decorView.getWindowVisibleDisplayFrame(rectangle)
            val widthPixels = curActivity.resources.displayMetrics.widthPixels
            val heightPixels = curActivity.resources.displayMetrics.heightPixels
            val insetsHeight = rectangle.top + (heightPixels - rectangle.bottom)
            heightPixels - insetsHeight
        } else {
            0
        }
        Timber.d("getScreenHeightWithoutInsets: %s", height)
        return height
    }



    private fun destroyBanner() {
        try {
            popupBanner.apply {
                destroyBanner()
                bannerListener = null
            }
        } catch(e: Exception) {
            Timber.e(e, "destroyBanner error")
        }
    }

    private fun startTimeoutCountDownTimer(timeOutInMillis: Long) {
        timeOutCountDownTimer?.cancel()
        timeOutCountDownTimer = null
        if (timeOutInMillis <= 0) {
            return
        }
        timeOutCountDownTimer = object : CountDownTimer(
            timeOutInMillis,
            TimeUnit.SECONDS.toMillis(COUNT_DOWN_INTERVAL_TIME_IN_SECONDS)
        ) {
            override fun onTick(millisUntilFinished: Long) {
                currentTimeoutLoadHtml = millisUntilFinished
            }

            override fun onFinish() {
                currentTimeoutLoadHtml = 0
                dismissAllowingStateLoss()
            }
        }
        timeOutCountDownTimer?.start()
    }


    @UiThread
    private fun startAdsSkipCountDownTimer(adsCountDown: Int) {
        adsSkipCountDownTimer?.cancel()
        adsSkipCountDownTimer = null
        if (adsCountDown <= 0) {
            showCountdownFinishView()
            return
        }
        adsSkipCountDownTimer = object : CountDownTimer(
            TimeUnit.SECONDS.toMillis(adsCountDown.toLong()),
            TimeUnit.SECONDS.toMillis(COUNT_DOWN_INTERVAL_TIME_IN_SECONDS)
        ) {
            override fun onTick(millisUntilFinished: Long) {
                showCountdownView(millisUntilFinished)
            }

            override fun onFinish() {
                showCountdownFinishView()
            }
        }
        adsSkipCountDownTimer?.start()
    }

    // region handle countdown ads view
    @UiThread
    private fun showCountdownView(timeInMillis: Long) {
        binding.tvCountDown.apply {
            val countdown = TimeUnit.MILLISECONDS.toSeconds(timeInMillis)
            text = context.resources.getString(
                R.string.ads_welcome_screen_skip_countdown_text,
                TimeUnit.MILLISECONDS.toSeconds(timeInMillis)
            )
            setOnClickListener(null)
            show()
            viewModel.saveCurrentWelcomeScreenAdsCountdown(countdown = countdown.toInt())
        }
    }

    @UiThread
    private fun showCountdownFinishView() {
        binding.tvCountDown.apply {
            setText(R.string.ads_welcome_screen_skip_text)
            setOnClickListener {
                dismissAllowingStateLoss()
            }
            show()
            viewModel.saveCurrentWelcomeScreenAdsCountdown(countdown = 0)
        }
    }
    // endregion handle countdown ads view

    // endregion Commons

    companion object {
        const val COUNT_DOWN_INTERVAL_TIME_IN_SECONDS = 1L
        const val COUNT_DOWN_TIME_LOAD_HTML_IN_SECONDS = 2L
    }

}