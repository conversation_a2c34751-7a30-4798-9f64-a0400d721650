package com.fptplay.mobile.features.download

import android.content.Context
import androidx.annotation.OptIn
import androidx.core.net.toUri
import com.fptplay.mobile.features.download.callback.WidevineMediaDrmCallback
import androidx.media3.common.C
import androidx.media3.exoplayer.DefaultRenderersFactory
import androidx.media3.exoplayer.DefaultRenderersFactory.ExtensionRendererMode
import androidx.media3.common.Format
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaItem.ClippingConfiguration
import androidx.media3.common.MediaItem.DrmConfiguration
import androidx.media3.common.MediaMetadata
import androidx.media3.exoplayer.RenderersFactory
import androidx.media3.exoplayer.drm.DefaultDrmSessionManager
import androidx.media3.exoplayer.drm.DrmSession.DrmSessionException
import androidx.media3.exoplayer.drm.DrmSessionEventListener
import androidx.media3.exoplayer.drm.OfflineLicenseHelper
import androidx.media3.exoplayer.offline.DownloadHelper
import androidx.media3.common.util.Assertions
import androidx.media3.common.MimeTypes
import androidx.media3.common.util.UnstableApi
import androidx.media3.common.util.Util
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.datasource.HttpDataSource
import androidx.media3.datasource.cronet.CronetDataSource
import androidx.media3.datasource.cronet.CronetUtil
import com.google.common.primitives.Ints
import com.sigma.packer.SigmaMediaDrm
import org.json.JSONObject
import java.net.CookieHandler
import java.net.CookieManager
import java.net.CookiePolicy
import java.util.UUID
import java.util.concurrent.Executors

@OptIn(UnstableApi::class)
object DRMUtils {

    private const val USE_CRONET_FOR_NETWORKING = true
    private const val USE_DECODER_EXTENSIONS = true

    fun downloadKeyOffsetId(
        userId: String,
        merchant: String,
        session: String,
        format: Format,
        drmConfiguration: DrmConfiguration,
        httpDataSourceFactory: HttpDataSource.Factory
    ): ByteArray? {
        val offlineLicenseHelper = OfflineLicenseHelper(
            DefaultDrmSessionManager.Builder()
                .setUuidAndExoMediaDrmProvider(
                    Assertions.checkNotNull(Util.getDrmUuid("widevine")),
                    SigmaMediaDrm.DEFAULT_PROVIDER
                )
                .setKeyRequestParameters(drmConfiguration.licenseRequestHeaders)
                .setMultiSession(drmConfiguration.multiSession)
                .setPlayClearSamplesWithoutKeys(drmConfiguration.playClearContentWithoutKey)
                .setUseDrmSessionsForClearContent(*Ints.toArray(drmConfiguration.sessionForClearTypes))
                .build(
                    WidevineMediaDrmCallback(
                        drmConfiguration.licenseUri.toString(),
                        drmConfiguration.forceDefaultLicenseUri,
                        httpDataSourceFactory
                    ).apply {
                        setKeyRequestProperty("sigma-custom-data", getCustomData(userId, merchant, session).toString())
                    }
                ),
            DrmSessionEventListener.EventDispatcher()
        )
        try {
            return offlineLicenseHelper.downloadLicense(format)
        } catch (e: DrmSessionException) {
            e.printStackTrace()
        } finally {
            offlineLicenseHelper.release()
        }
        return null
    }

    fun createMediaItem(
        userId: String,
        merchant: String,
        session: String,
        url: String,
        licenseUrl: String
    ): MediaItem {
        val uri = url.toUri()
        val title = ""
        val drmUuid = Util.getDrmUuid("widevine") ?: C.WIDEVINE_UUID
        val builder =
            MediaItem.Builder()
                .setUri(uri)
                .setMimeType(MimeTypes.APPLICATION_MPD)
                .setMediaMetadata(MediaMetadata.Builder().setTitle(title).build())
                .setClippingConfiguration(
                    ClippingConfiguration.Builder()
                        .setStartPositionMs(0)
                        .setEndPositionMs(C.TIME_END_OF_SOURCE)
                        .build()
                )
                .setDrmConfiguration(
                    DrmConfiguration.Builder(drmUuid)
                        .setLicenseUri(licenseUrl)
                        .setMultiSession(true)
                        .setForceDefaultLicenseUri(false)
                        .setLicenseRequestHeaders(getLicenseRequestHeader(drmUuid, userId, merchant, session))
                        .setPlayClearContentWithoutKey(true)
                        .forceSessionsForAudioAndVideoTracks(false)
                        .build()
                )
        return builder.build()
    }


    @Synchronized
    fun getHttpDataSourceFactory(context: Context): HttpDataSource.Factory? {
        var httpDataSourceFactory : HttpDataSource.Factory ?= null
        if (USE_CRONET_FOR_NETWORKING) {
            val cronetEngine = CronetUtil.buildCronetEngine(context.applicationContext)
            cronetEngine?.let {
                httpDataSourceFactory = CronetDataSource.Factory(it, Executors.newSingleThreadExecutor())
            }
        }
        if (httpDataSourceFactory == null) {
            // We don't want to use Cronet, or we failed to instantiate a CronetEngine.
            val cookieManager = CookieManager()
            cookieManager.setCookiePolicy(CookiePolicy.ACCEPT_ORIGINAL_SERVER)
            CookieHandler.setDefault(cookieManager)
            DefaultHttpDataSource.Factory()
        }
        return httpDataSourceFactory
    }

    fun buildRenderersFactory(
        context: Context, preferExtensionRenderer: Boolean
    ): RenderersFactory {
        val extensionRendererMode: @ExtensionRendererMode Int =
            if (USE_DECODER_EXTENSIONS) {
                if (preferExtensionRenderer) DefaultRenderersFactory.EXTENSION_RENDERER_MODE_PREFER
                else DefaultRenderersFactory.EXTENSION_RENDERER_MODE_ON
            } else DefaultRenderersFactory.EXTENSION_RENDERER_MODE_OFF
        return DefaultRenderersFactory(context.applicationContext)
            .setExtensionRendererMode(extensionRendererMode)
    }

    fun getFirstFormatWithDrmInitData(helper: DownloadHelper): Format? {
        for (periodIndex in 0 until helper.periodCount) {
            val mappedTrackInfo = helper.getMappedTrackInfo(periodIndex)
            for (rendererIndex in 0 until mappedTrackInfo.rendererCount) {
                val trackGroups = mappedTrackInfo.getTrackGroups(rendererIndex)
                for (trackGroupIndex in 0 until trackGroups.length) {
                    val trackGroup = trackGroups[trackGroupIndex]
                    for (formatIndex in 0 until trackGroup.length) {
                        val format = trackGroup.getFormat(formatIndex)
                        if (format.drmInitData != null) {
                            return format
                        }
                    }
                }
            }
        }
        return null
    }

    fun getFormatWithDrmInitData(helper: DownloadHelper, index: Int): Format? {
        // TODO
        return null
    }

    private fun getLicenseRequestHeader(uuid: UUID, userId: String, merchant: String, session: String): Map<String, String> {
        // Add standard request properties for supported schemes.
        val contentType =
            if (C.PLAYREADY_UUID == uuid) "text/xml"
            else (if (C.CLEARKEY_UUID == uuid) "application/json" else "application/octet-stream")
        return mapOf(
            "Content-Type" to contentType,
            "sigma-custom-data" to getCustomData(userId, merchant, session).toString()
        )
    }


    private fun getCustomData(userId: String, merchant: String, session: String): JSONObject {
        return JSONObject().apply {
            this.put("userId", userId)
            this.put("sessionId", session)
            this.put("merchantId", merchant)
            this.put("appId", "sigma")
        }
    }
}