package com.fptplay.mobile.features.introduce

import android.annotation.SuppressLint
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide

class ImageSliderAdapter() : RecyclerView.Adapter<ImageSliderAdapter.ImageViewHolder>() {
    private var images: List<String> = emptyList()
    inner class ImageViewHolder(val imageView: ImageView) : RecyclerView.ViewHolder(imageView)
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ImageViewHolder {
        val imageView = ImageView(parent.context).apply {
            layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            scaleType = ImageView.ScaleType.CENTER_CROP
        }
        return ImageViewHolder(imageView)
    }
    override fun onBindViewHolder(holder: ImageViewHolder, position: Int) {
        // Use your image loading library here (e.g., Glide/Picasso)
        Glide.with(holder.imageView.context).load(images[position]).into(holder.imageView)
    }
    override fun getItemCount() = images.size

    @SuppressLint("NotifyDataSetChanged")
    fun setImageList(images: List<String>) {
        this.images = images
        notifyDataSetChanged()
    }
}