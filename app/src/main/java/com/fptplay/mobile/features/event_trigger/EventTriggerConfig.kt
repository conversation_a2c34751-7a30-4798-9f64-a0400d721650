package com.fptplay.mobile.features.event_trigger

import com.fptplay.mobile.features.event_trigger.listener.EventActionListener
import com.xhbadxx.projects.module.util.fplay.SharedPreferences

class EventTriggerConfig {

    private var sharedPreferences: SharedPreferences? = null
    private var eventActionListener: EventActionListener? = null

    fun setSharedPreferences(value: SharedPreferences) : EventTriggerConfig {
        sharedPreferences = value
        return this
    }

    fun getSharedPreferences() : SharedPreferences? {
        return sharedPreferences
    }

    fun setEventActionListener(value: EventActionListener) : EventTriggerConfig {
        eventActionListener = value
        return this
    }

    fun build() : EventTriggerManager {
        val eventTriggerManager = EventTriggerManager().apply {
            setConfig(this@EventTriggerConfig)
            setEventActionListener(eventActionListener)
        }
        return eventTriggerManager
    }
}