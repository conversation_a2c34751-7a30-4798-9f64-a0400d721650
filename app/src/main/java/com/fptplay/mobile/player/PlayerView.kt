package com.fptplay.mobile.player

import android.annotation.SuppressLint
import android.app.KeyguardManager
import android.app.PictureInPictureParams
import android.content.*
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.database.ContentObserver
import android.hardware.SensorManager
import android.os.*
import android.provider.Settings
import android.text.Spanned
import android.text.TextUtils
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.view.OrientationEventListener
import android.view.Surface
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.annotation.UiThread
import androidx.collection.ArrayMap
import androidx.collection.arrayMapOf
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.app.PictureInPictureModeChangedInfo
import androidx.core.util.Consumer
import androidx.core.view.*
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.airbnb.lottie.LottieAnimationView
import com.drowsyatmidnight.haint.android_fplay_ads_sdk.AdsController
import com.drowsyatmidnight.haint.android_fplay_ads_sdk.AdsControllerBuilder
import com.drowsyatmidnight.haint.android_fplay_ads_sdk.AdsListener
import com.drowsyatmidnight.haint.android_fplay_ads_sdk.model.AdsRequestParams
import com.drowsyatmidnight.haint.android_fplay_ads_sdk.model.PlatformType
import com.fplay.ads.logo_instream.LogoInStreamController
import com.fplay.ads.logo_instream.LogoInStreamListener
import com.fplay.ads.logo_instream.PlayerContentCallback
import com.fplay.ads.logo_instream.model.AdsLogoRequestParams
import com.fplay.module.downloader.model.VideoTaskItem
import com.fplay.module.downloader.utils.ImageStorageUtils.localUrl
import com.fptplay.dial.connection.models.transfer_model.KeyCode
import com.fptplay.dial.model.*
import com.fptplay.mobile.BuildConfig
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.application.FptPlayLifecycleObserver
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.ActivityExtensions.hideSystemBar
import com.fptplay.mobile.common.extensions.ActivityExtensions.isInPiPMode
import com.fptplay.mobile.common.extensions.ActivityExtensions.removeSystemBarVisibilityChangeListener
import com.fptplay.mobile.common.extensions.ActivityExtensions.setSystemBarVisibilityChangeListener
import com.fptplay.mobile.common.extensions.ActivityExtensions.showSystemBar
import com.fptplay.mobile.common.extensions.isSubtitle
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.modifyPlayerListener
import com.fptplay.mobile.common.screenshotprotector.ScreenShotProtector
import com.fptplay.mobile.common.ui.bases.BaseActivity
import com.fptplay.mobile.common.ui.view.PlayerVodSeekbarView
import com.fptplay.mobile.common.utils.*
import com.fptplay.mobile.common.utils.DateTimeUtils.areDifferentDaysUsingCalendar
import com.fptplay.mobile.databinding.PlayerViewBinding
import com.fptplay.mobile.features.ads.AdsLogoListener
import com.fptplay.mobile.features.ads.AdsTvcListener
import com.fptplay.mobile.features.ads.tracking_ads.AdsTrackingProxy
import com.fptplay.mobile.features.ads.utils.AdsUtils
import com.fptplay.mobile.features.game_emoji.GameEmojiContentCallback
import com.fptplay.mobile.features.game_emoji.GameEmojiListener
import com.fptplay.mobile.features.game_emoji.GameEmojiVodController
import com.fptplay.mobile.features.livetv_detail.LiveTVDetailViewModel
import com.fptplay.mobile.features.livetv_detail.data.LiveTvPreviewPlayerInfo
import com.fptplay.mobile.features.mega.apps.airline.AirlineActivity
import com.fptplay.mobile.features.mqtt.MqttUtil
import com.fptplay.mobile.features.pairing_control.model.RemotePlayerState
import com.fptplay.mobile.features.premiere.PremiereViewModel
import com.fptplay.mobile.features.premiere.data.EventTvPreviewPlayerInfo
import com.fptplay.mobile.features.sport_interactive.SportInteractiveViewModel
import com.fptplay.mobile.features.sport_interactive.utils.SportInteractiveUtils.canShowSportInteractiveButton
import com.fptplay.mobile.player.PlayerUtils.getIndexOf
import com.fptplay.mobile.player.PlayerUtils.isTheSameUrlRequest
import com.fptplay.mobile.player.PlayerUtils.playerAudioModeName
import com.fptplay.mobile.player.PlayerUtils.updateSituationWarningView
import com.fptplay.mobile.player.PlayerUtils.updateSubtitleLayoutStyle
import com.fptplay.mobile.player.config.PlayerConfig
import com.fptplay.mobile.player.handler.PlayerHandler
import com.fptplay.mobile.player.interfaces.IPlayerControl
import com.fptplay.mobile.player.interfaces.IPlayerStateValidation
import com.fptplay.mobile.player.interfaces.IPlayerUIListener
import com.fptplay.mobile.player.media_session.MediaSessionHandler
import com.fptplay.mobile.player.model.PlayerBottomControlData
import com.fptplay.mobile.player.utils.PlayerConstants
import com.fptplay.mobile.player.utils.PlayerDebugViewData
import com.fptplay.mobile.player.utils.PlayerPiPHelper
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.isEnableAutoScreenRotationInSettings
import com.fptplay.mobile.player.utils.visible
import com.fptplay.mobile.player.views.DismissCallback
import com.fptplay.mobile.services.player.BackgroundPlayerService
import com.fptplay.mobile.vod.VodDetailViewModel
import com.fptplay.mobile.vod.data.BuyPackageGuide
import com.fptplay.mobile.vod.data.VodPreviewPlayerInfo
import androidx.media3.common.C
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.common.Format
import androidx.media3.common.Player
import androidx.media3.common.Timeline
import androidx.media3.common.Tracks
import androidx.media3.exoplayer.analytics.AnalyticsListener
import androidx.media3.exoplayer.DecoderReuseEvaluation
import androidx.media3.common.VideoSize
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.media3.ui.SubtitleView
import com.tear.modules.player.exo.ExoPlayerProxy
import com.tear.modules.player.util.IPlayer
import com.tear.modules.player.util.PlayerControlView
import com.tear.modules.tracking.model.CommonInfor
import com.tear.modules.util.Utils.safeInflate
import com.xhbadxx.projects.module.domain.entity.fplay.common.Stream
import com.xhbadxx.projects.module.domain.entity.fplay.common.UserProfile
import com.xhbadxx.projects.module.domain.entity.fplay.game.gamevod.GameVOD
import com.xhbadxx.projects.module.domain.entity.fplay.live.TvChannelDetail
import com.xhbadxx.projects.module.domain.entity.fplay.live.TvSchedule
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.xhbadxx.projects.module.domain.repository.fplay.DrmRepository
import com.xhbadxx.projects.module.util.common.Util
import com.xhbadxx.projects.module.util.common.Util.fromBase64Default
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.safeInflate as commonSafeInflate
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.max


@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
@AndroidEntryPoint
class PlayerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr), DefaultLifecycleObserver {

//    private val drmStagingMode = !BuildConfig.IS_PRODUCTION
    private val drmStagingMode = false

    private val TAG = this::class.java.simpleName

    //region Binding view
    private val binding = PlayerViewBinding.inflate(LayoutInflater.from(context), this, true)
    //endregion

    private var useController = true

    private var fragmentActivity: FragmentActivity ?= null
    private var viewLifecycleOwner: LifecycleOwner ?= null
    private var screenProtector: ScreenShotProtector? = null
    private var fragmentManager: FragmentManager? = null

    private var inComingRequest: IPlayer.Request? = null
    private var isInComingRequestEnableBackgroundAudio: Boolean = false

    @Inject
    lateinit var sharedPreferences: SharedPreferences
    @Inject
    lateinit var drmRepository: DrmRepository

    @Inject
    lateinit var displayCutoutsHelper: DisplayCutoutsHelper

    @Inject
    lateinit var mediaSessionHandler: MediaSessionHandler

    //region Player
    private var playerConfig: PlayerConfig = PlayerConfig()
    private val basePlayerCallback: IPlayer.IPlayerCallback by lazy { PlayerEvents() }
    private var player: IPlayer ?= null
    private val playerUIEvents : PlayerUIEvents by lazy { PlayerUIEvents() }
    private var playerHandler : PlayerHandler ?= null
    private val playerTrackChangeEvents : Player.Listener by lazy { PlayerTrackChangeEvents() }
    private val playerAnalyticsEvents : PlayerAnalyticsListener by lazy { PlayerAnalyticsListener() }
    private var playerVideoSize: VideoSize ?= null
    private fun lowQualityImage(url: String) = ImageProxy.optimizeUrl(
        url = url,
        width = (Constants.PLAYER_HOVER_IMAGE_WIDTH / 20),
        height = 0
    )
    //endregion

    // Orientation
    private var lastOrientation = Configuration.ORIENTATION_UNDEFINED
    //

    //debug info
    val debugViewData: PlayerDebugViewData by lazy { PlayerDebugViewData()}
    var isShowingDebugView = false

    //region Player Callbacks
    private var playerUIListener: IPlayerUIListener?= null
    private var playerEventsListener: IPlayer.IPlayerCallback?= null
    private var playerDrmCallback: IPlayer.DrmCallback? = null
    private var playerStateValidation: IPlayerStateValidation?= null
    //endregion

    //region Var
    private var isLandscapeMode = false
    private var isFullscreen = false
    private var linkToShare = ""
    private var curResolution = "-1"
    private var videoResizeMode = AspectRatioFrameLayout.RESIZE_MODE_FIT
    private var isSituationWarningShown = false
    //endregion

    //region BackPressHandler
    private val onBackPressCallBack: OnBackPressCallBack by lazy { OnBackPressCallBack() }
    //endregion

    private var screenType: PlayerHandler.ScreenType ?= null

    //region Player Data
    var playerData: PlayerControlView.Data = PlayerControlView.Data()
    //endregion

    private var isPlayingVipTrailer = false

    //region VOD Player Data
    private var isPlayingTrailer: Boolean = false
    private var details: Details ?= null
    private var currentEpisode: Details.Episode ?= null
    private var isVodOffline = false
    //endregion

    private var detailsPremiere : com.xhbadxx.projects.module.domain.entity.fplay.premier.Details ?= null

    private var isPlayingTimeshift = false
    private var tvChannelDetail: TvChannelDetail?= null

    //region Skip intro credits
    private var currentIntroFromMillis = 0
    private var currentStartContentMillis = 0
    private var currentEndContentMillis = 0

    private var isCountDownStopped = true
    private var countDownTimerSkipCredits : CountDownTimer ?= null
    private var handlerCheckSkipCredit : Handler?= null
    private var runnableCheckSkipCredit = Runnable {
        startSkipCredits()
    }

    private var handlerCheckSkipIntro : Handler?= null
    private var runnableCheckSkipIntro = Runnable {
        startSkipIntro()
    }
    private var skipIntroIsRunning = false

    //endregion

    private var isToolTip: Boolean = false
    private var dissmissCallBack: DismissCallback? = null
    fun dismissListener(dissmissCallBack: DismissCallback) {
        this.dissmissCallBack = dissmissCallBack
    }

    //region Pairing Control
    private val pairingConnection by lazy { MainApplication.INSTANCE.pairingConnectionHelper }
    //endregion

    private val isLivePreview: Boolean get() {
        return screenType is PlayerHandler.ScreenType.Live && playerConfig.isPlayPreview
    }

    var isEnableLiveChat : Boolean
        set(enable) {
            binding.playerUI.isEnableLiveChat = enable
        }
        get() {
            return binding.playerUI.isEnableLiveChat
        }

    private var buyPackageGuide: BuyPackageGuide? = null
    private var countDownTimerBuyPackageGuide : CountDownTimer? = null
    private var buyPackageGuideDisplayed: Boolean = true
    private var adsLogoShowing: Boolean = false
    // Background player
    private var haveBackgroundAudio: Boolean = false
    private var isHandlerAudioBackground: Boolean = true
    private var curDurationPlayInBackground = -1L
    private var playerBackgroundEpisodeIndex = -1
    private var binder : BackgroundPlayerService.Binder ?= null

    private var isLimitCcuByMqtt: Boolean = true
    private var codeLimitCcu: String = ""
    private var limitCcuBinding: View? = null
    private val backgroundPlayerServiceConnection = object: ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            try {
                Timber.e("$TAG onServiceConnected playerBackgroundEpisodeIndex = $playerBackgroundEpisodeIndex")
                binder = service as? BackgroundPlayerService.Binder
                stopBackgroundAudioNotificationService()
                if (PlayerUtils.checkRulePlayBAAndBAForPiP(isApiEnableBackgroundAudio = details?.blockContent?.bgAudio == "1", sharedPreferences = sharedPreferences)
                    && isHandlerAudioBackground
                    && canPlayBackground()
                ) {
                    if (playerBackgroundEpisodeIndex != (playerData.episodeIndex ?: 0)) { // reset credit data when change episode
                        resetSkipIntroCreditData()
                    }
                    if (playerBackgroundEpisodeIndex == -1) {
                        playerBackgroundEpisodeIndex = playerData.episodeIndex ?: 0
                    }
                    curDurationPlayInBackground = binder?.getPlayer()?.currentDuration() ?: -1L
                    playerUIListener?.onEpisodeChangedFromBackground(pos = playerBackgroundEpisodeIndex, currentDuration = curDurationPlayInBackground)
                    details?.blockEpisode?.episodes?.let {
                        if (playerBackgroundEpisodeIndex >= 0 && playerBackgroundEpisodeIndex < it.size) {
                            currentEpisode = it[playerBackgroundEpisodeIndex]
                        }
                    }
                    binding.playerUI.post {
                        binding.playerUI.updateCurrentVodIndex(index = playerBackgroundEpisodeIndex)
                    }

                }
                fragmentActivity?.run {
                    intent.putExtra(DeeplinkConstants.REQUIRE_RESUME_ACTIVITY, true)
                    binder?.setForegroundActivityIntent(intent)
                }
            } catch (exception: java.lang.Exception) {
                Timber.e(exception)
            }
        }

        override fun onServiceDisconnected(p0: ComponentName?) {
            Timber.e("$TAG onServiceDisconnected")
        }
    }

    private fun stopBackgroundAudioNotificationService() {
        try {
            if (player != null && haveBackgroundAudio) {
                binder?.movePlaybackToForeground()
                fragmentActivity?.let { LocalBroadcastManager.getInstance(it).unregisterReceiver(onNotificationBroadcastReceiver)}
            } else if (binder?.getPlayer() != null) {
                binder?.movePlaybackToForeground()
                binder?.getPlayer()?.release()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private val onNotificationBroadcastReceiver = object: BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent != null && BackgroundPlayerService.INTENT_NOTIFICATION_CUSTOM_ACTION == intent.action) {
                val customAction = intent.getStringExtra(BackgroundPlayerService.ACTION_KEY)
                if (CheckValidUtil.checkValidString(customAction)) {
                    if (customAction == BackgroundPlayerService.ACTION_VALUE_ERROR) {
                        val errorValue = intent.getStringExtra(BackgroundPlayerService.VALUE_ERROR)
                        if (binder != null && CheckValidUtil.checkValidString(errorValue)) {
                            binder?.showErrorPlayer(errorValue!!)
                        }
                    } else if (customAction == BackgroundPlayerService.VALUE_BACK_GROUND) {
                        haveBackgroundAudio = true
                    } else if(customAction == BackgroundPlayerService.VALUE_FORCE_GROUND) {
                        haveBackgroundAudio = false
                    }
                }
                val newEpisodeIndex = intent.getIntExtra(BackgroundPlayerService.ACTION_EPISODE_INDEX_KEY, playerData.episodeIndex ?: -1) // If no change episode, get current episode
                if (playerBackgroundEpisodeIndex != newEpisodeIndex
                    && (isEnableBackgroundAudio() || PlayerUtils.checkRulePlayBAAndBAForPiP(isApiEnableBackgroundAudio = details?.blockContent?.bgAudio == "1", sharedPreferences = sharedPreferences))
                ) { // reset credit data when change episode
                    resetSkipIntroCreditData()
                }
                playerBackgroundEpisodeIndex = newEpisodeIndex
                playerData.episodeIndex = playerBackgroundEpisodeIndex

                playerUIListener?.onEpisodeChangeInBackground(pos = playerBackgroundEpisodeIndex)

                details?.blockEpisode?.episodes?.let {
                    if (playerBackgroundEpisodeIndex >= 0 && playerBackgroundEpisodeIndex < it.size) {
                        currentEpisode = it[playerBackgroundEpisodeIndex]
                    }
                }

                // PiP
                when (customAction) {
                    BackgroundPlayerService.VALUE_PIP_PLAY_NEW_EPISODE_ACTION_NEXT -> {
                        playNewEpisodeActionNext()
                    }
                    BackgroundPlayerService.VALUE_PIP_PLAY_NEW_EPISODE_ACTION_PREVIOUS -> {
                        playNewEpisodeActionPrevious()
                    }
                }
            }
        }
    }

    private fun appInBackgroundAudio(): Boolean {
        return FptPlayLifecycleObserver.appInBackground && player != null && haveBackgroundAudio
    }

    private fun sendNotificationError(errorMessage: String?) {
        if (appInBackgroundAudio() && fragmentActivity != null) {
            try {
                val intent = Intent(BackgroundPlayerService.INTENT_NOTIFICATION_CUSTOM_ACTION).apply {
                    putExtra(BackgroundPlayerService.ACTION_KEY, BackgroundPlayerService.ACTION_VALUE_ERROR)
                    putExtra(BackgroundPlayerService.VALUE_ERROR, errorMessage)
                }
                fragmentActivity?.let { LocalBroadcastManager.getInstance(it).sendBroadcast(intent)}
            } catch (exception: java.lang.Exception) {
                Timber.e(exception)
            }
        }
    }

    private fun canEnterBackgroundAudioService(): Boolean {
        val navController = fragmentActivity?.findNavHostFragment()?.navController
        return when (navController?.currentDestination?.id) {
            R.id.vod_detail_fragment -> true
            R.id.livetv_detail_fragment -> true
            R.id.premiere_fragment -> true
            //
            R.id.player_report_dialog_fragment -> true
            //
            R.id.downloadOptionBottomSheetDialogFragment -> true
            R.id.downloadFinishBottomSheetDialogFragment -> true
            R.id.download_quality_dialog_fragment_v2 -> true
            R.id.download_setting_dialog_fragment_v2 -> true // tablet
            R.id.download_setting_fragment_v2 -> false // mobile
            R.id.vod_option_dialog_fragment -> true
            //
            R.id.player_option_dialog_fragment -> true
            R.id.option_dialog_fragment -> true
            else -> false
        }
    }

    private fun onCheckBackgroundAudioService(forPiP: Boolean) {
        try {
            Logger.d("$TAG Go to background audio")
            if (player != null && isHandlerAudioBackground && canPlayBackground()) {
                if (binder != null) {

                    binder?.setInitialRequest(if (forPiP) null else inComingRequest)
                    binder?.setPlayer(player as? ExoPlayerProxy)
                    val contentBgUrl = when (screenType) {
                        is PlayerHandler.ScreenType.Vod -> {
                            if (!details?.blockContent?.horizontalImage.isNullOrEmpty())
                                details?.blockContent?.horizontalImage ?: ""
                            else if (!details?.blockEpisode?.episodes.isNullOrEmpty()) {
                                details?.blockEpisode?.episodes?.get(0)?.horizontalImage?:""
                            } else {
                                ""
                            }
                        }
                        is PlayerHandler.ScreenType.Live -> {
                            tvChannelDetail?.let { it.originalLogo } ?: kotlin.run { "" }
                        }
                        is PlayerHandler.ScreenType.Premiere -> {
                            detailsPremiere?.let { it.posterImage } ?: kotlin.run { "" }
                        }
                        else -> ""
                    }
                    binder?.setContentBgUrl(url = contentBgUrl)
                    if (screenType == PlayerHandler.ScreenType.Vod) {
                        details?.blockEpisode?.episodes?.let {
                            val startIndex = if (currentEpisode?.isItemOfPlaylist == true) {
                                val index = it.getIndexOf(currentEpisode)
                                if (index != -1) index else 0
                            } else {
                                playerData.episodeIndex ?: 0
                            }
                            playerData.episodeIndex = startIndex
                            binder?.apply {
                                setEpisodeBgUrls(data = it.map { episode -> episode.horizontalImage })
                                setContentId(id = details?.blockContent?.id ?:"")
                                setPlaylist(playlist = it)
                                setStartIndex(index = startIndex)
                                setDataSource(
                                    MediaSessionHandler.DataSource(
                                        screenType = screenType,
                                        isPlayVipTrailer = isPlayingVipTrailer,
                                        isPlayTimeShift = isPlayingTimeshift
                                    )
                                )
                                setIsBackgroundAudioForPiP(forPiP)
                            }
                        }
                    }
                    fragmentActivity?.let {
                        LocalBroadcastManager.getInstance(it).unregisterReceiver(onNotificationBroadcastReceiver)
                        LocalBroadcastManager.getInstance(it).registerReceiver(onNotificationBroadcastReceiver, IntentFilter(BackgroundPlayerService.INTENT_NOTIFICATION_CUSTOM_ACTION))
                    }
                    binder?.movePlaybackToBackground()
                }
            }
        } catch (exception: java.lang.Exception) {
            Timber.e(exception)
        }
    }

    private fun onStartBackgroundAudioService() {
        try {
            fragmentActivity?.let { BackgroundPlayerService.bind(it, backgroundPlayerServiceConnection) }
        } catch (exception: java.lang.Exception) {
            Timber.e(exception)
        }
    }

    private fun onStopBackgroundAudioService() {
        try {
            fragmentActivity?.unbindService(backgroundPlayerServiceConnection)
        } catch (exception: java.lang.Exception) {
            Timber.e(exception)
        }
    }

    private fun isEnableBackgroundAudio(): Boolean {
        return when (screenType) {
            is PlayerHandler.ScreenType.Vod -> {
                (details?.blockContent?.bgAudio == "1" || isInComingRequestEnableBackgroundAudio) && isHandlerAudioBackground && canPlayBackground() && !playerConfig.isPlayPreview
            }
            is PlayerHandler.ScreenType.Live -> {
                (tvChannelDetail?.bgAudio == "1" || isInComingRequestEnableBackgroundAudio) && isHandlerAudioBackground && canPlayBackground() && !playerConfig.isPlayPreview
            }
            is PlayerHandler.ScreenType.Premiere -> {
                detailsPremiere?.bgAudio == "1" && isHandlerAudioBackground && canPlayBackground() && !playerConfig.isPlayPreview
            }
            else -> false
        }
    }

    //

    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.PlayerView, defStyleAttr, 0)
        try {
            useController = typedArray.getBoolean(R.styleable.PlayerView_useController, true)
        } finally {
            typedArray.recycle()
        }

        doOnAttach {
            initViews()
        }
    }

    override fun onDetachedFromWindow() {
        keepScreenOn = false
        adsController?.destroyAds()
        logoAdsController?.destroyLogoInstream()
        removeObservers()
        resetOrientation()
        releaseBindPlayerPiP()
        onStopBackgroundAudioService()
        context?.contentResolver?.unregisterContentObserver(rotationObserver)
        super.onDetachedFromWindow()
    }

    override fun onConfigurationChanged(newConfig: Configuration?) {
        super.onConfigurationChanged(newConfig)
        configurationChanged(newConfig = MainApplication.INSTANCE.applicationContext.resources.configuration)
    }

    fun configurationChanged(newConfig: Configuration?) {
        // Display cutouts
        displayCutoutsHelper.configurationChanged(newConfig = newConfig)
        //
        if (lastOrientation == newConfig?.orientation) return
        lastOrientation = newConfig?.orientation ?: Configuration.ORIENTATION_UNDEFINED
        when (newConfig?.orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> {
                isLandscapeMode = true
                if (context.isTablet()) {
                    tabletSystemBars()
                } else {
                    isFullscreen = true
                    isLandscapeMode = true
                    fragmentActivity?.hideSystemBar()
                }
            }
            Configuration.ORIENTATION_PORTRAIT -> {
                isLandscapeMode = false
                if (context.isTablet()) {
                    tabletSystemBars()
                } else {
                    isFullscreen = false
                    fragmentActivity?.showSystemBar()
                }
            }
            else -> {}
        }
        binding.playerUI.updateLayouts(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
        playerUIListener?.onFullScreen(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
        // Layout changes
        onLayoutChange()
        //

        // Ad
        newConfig?.let{
            logoAdsController?.configLogoInstreamChangeOrientation(it)
        }
    }

    fun initPlayer(fragmentActivity: FragmentActivity?, viewLifecycleOwner: LifecycleOwner, screenType: PlayerHandler.ScreenType, viewModel: ViewModel, fragmentManager: FragmentManager? = null) {
        Logger.d("----- init player")

        this.screenType = screenType
        this.fragmentActivity = fragmentActivity
        this.viewLifecycleOwner = viewLifecycleOwner
        this.fragmentManager = fragmentManager
        fragmentActivity?.let {activity ->
            this.screenProtector = ScreenShotProtector(activity).also {
                viewLifecycleOwner.lifecycle.addObserver(it)
            }
        }

        handleScreenRotation()
        initDisplayCutouts()
        initSituationWarning()
        bindPlayer(viewModel = viewModel)
        initPlayerPiP()
        bindPlayerUI(screenType = screenType)
        bindEvents()
        onStartBackgroundAudioService()
    }

    fun updateListNextVideo(data: List<Details.RelatedVod>) {
        binding.playerUI.updateListNextVideo(data = data)
    }

    fun updateScreenType(screenType: PlayerHandler.ScreenType) {
        binding.playerUI.updateScreenType(screenType = screenType)
    }

    fun updateIsPlayingTimeShift(isPlayingTimeshift: Boolean) {
        this.isPlayingTimeshift = isPlayingTimeshift
        binding.playerUI.updateIsPlayingTimeShift(isPlayingTimeshift = isPlayingTimeshift)
    }

    fun updateIsVodOffline(isVodOffline: Boolean) {
        this.isVodOffline = isVodOffline
        binding.playerUI.updateIsVodOffline(isVodOffline = isVodOffline)
    }

    fun updatePlayerTitleForOffline(title: String) {
        binding.playerUI.updatePlayerTitleForOffline(title = title)
    }

    fun updatePlayerEpisodeForOffline(episodes: List<VideoTaskItem>) {
        binding.playerUI.updatePlayerEpisodeForOffline(episodes = episodes)
    }

    fun updateIsSupportCast(isSupport: Boolean) {
        binding.playerUI.updateIsSupportCast(isSupport = isSupport)
    }

    /**
     * Set a custom seekbar view for the player. When set, the default seekbar will be hidden
     * and all progress updates will be redirected to the custom seekbar.
     * @param customSeekBarView The custom PlayerVodSeekbarView to use, or null to use default seekbar
     */
    fun setCustomSeekBarView(customSeekBarView: PlayerVodSeekbarView?) {
        binding.playerUI.setCustomSeekBarView(customSeekBarView)
    }

    fun triggerInitCheckSkipIntroAndCredits() {
        initCheckSkipIntroAndCredits()

        //
        binding.playerUI.checkShowSkipIntro()
    }

    fun triggerShowNextRecommendation() {
        if (binding.playerUI.canAutoNextEpisode()) {
            nextEpisode(isAuto = true, isSendEventToRemote = false)
        } else {
            if (binding.playerUI.isNextVideoRecommendationShow()) {
                return
            }
            if (fragmentActivity is AirlineActivity) {
                // Airline Layout => Don't show next video recommendation
            } else {
                // show recommend video, get from the first related list
                if (binding.playerUI.isShowNextVideoRecommendation() != null) {
                    showNextVideoRecommendation(data = binding.playerUI.isShowNextVideoRecommendation()!!)
                }
            }
        }
    }

    fun triggerUpdateShowIntroCredit() {
        // Check if currentEndContent cant get, use default
        player?.run {
            if (currentEndContentMillis == 0 && totalDuration().toInt() > 1000) {
                currentEndContentMillis = totalDuration().toInt() - 1000
            }
        }
        //
        showAndStartSkipIntro()
        startSkipCredits()
    }

    //region Age Restriction

    fun showAgeRestriction(checkPoint: Long, value: String, position: String, advisories: String, duration: Long) {
        binding.playerUI.doShowAgeRestriction(checkPoint, value, position, advisories, duration)
    }
    fun checkReshowAgeRestriction() {
        binding.playerUI.checkResumeAgeRestriction()
    }
    fun hideAgeRestriction() {
        binding.playerUI.doHideAgeRestrictions()
    }
    fun clearAgeRestrictionData() {
        binding.playerUI.clearAgeRestrictionData()
    }

    //endregion

    fun setPlayerUIViewVisible(isVisible: Boolean) {
        binding.playerUI.setPlayerUIViewVisible(isVisible = isVisible)
    }

    fun setPlayerProgressAndLockVisible(isVisible: Boolean) {
        binding.playerUI.setPlayerProgressAndLockVisible(isVisible = isVisible)
    }

    fun setPlayerUIListener(listener: IPlayerUIListener) {
        this.playerUIListener = listener
    }

    fun setPlayerEventsListener(listener: IPlayer.IPlayerCallback?) {
        this.playerEventsListener = listener
    }

    fun setPlayerStateValidation(listener: IPlayerStateValidation?) {
        this.playerStateValidation = listener
    }

    fun getViewStubShowIP() = binding.vtShowIp

    fun getViewStubShowMatrix() = binding.vtShowMatrixIp

    fun getViewStubShowLimitCcu() = binding.vtLimitCcu
    //region Lifecycle
    override fun onCreate(owner: LifecycleOwner) {
        Logger.d("$TAG Lifecycle onCreate")
        super.onCreate(owner)
    }

    override fun onStart(owner: LifecycleOwner) {
        Logger.d("$TAG Lifecycle onStart")
        super.onStart(owner)
    }

    override fun onResume(owner: LifecycleOwner) {
        Logger.d("$TAG Lifecycle onResume")
        keepScreenOn = true

        if (context.isTablet()) {
            isLandscapeMode = MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
            tabletSystemBars()
        } else {
            isFullscreen = MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
            isLandscapeMode = isFullscreen
        }
        binding.playerUI.updateLayouts(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
        playerUIListener?.onFullScreen(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
        // Layout changes
        onLayoutChange()
        //
        super.onResume(owner)
        // Sometime MainApplication.INSTANCE.applicationContext.resources.configuration.orientation && resources.configuration.orientation => value not correctly  => Handle by other way (Ex: View detail is hide)

        // Background playback
        checkRuleEnterBackgroundAudioOrPiP(
            enterBackgroundAudio = {
                if (canEnterBackgroundAudioService() && isEnableBackgroundAudio() && !isLockScreen()) {
                    viewLifecycleOwner?.lifecycle?.addObserver(player as DefaultLifecycleObserver)
                }
            },
            enterPictureInPicture = {
                viewLifecycleOwner?.lifecycle?.addObserver(player as DefaultLifecycleObserver)
                if (fragmentActivity.isInPiPMode()) {
                    goFullscreenBeforeEnterPiP()
                }
            },
            enterBackgroundAudioInPiP = {
                viewLifecycleOwner?.lifecycle?.addObserver(player as DefaultLifecycleObserver)
            },
            doNothing = {}
        )
    }

    private fun isLockScreen(): Boolean {
        val keyguardManager = context.getSystemService(Context.KEYGUARD_SERVICE) as? KeyguardManager
        return keyguardManager?.isKeyguardLocked ?: false
    }

    override fun onPause(owner: LifecycleOwner) {
        keepScreenOn = false
        Logger.d("$TAG Lifecycle onPause")

        // Background playback
        checkRuleEnterBackgroundAudioOrPiP(
            enterBackgroundAudio = {
                if (canEnterBackgroundAudioService() && isEnableBackgroundAudio()) {
                    viewLifecycleOwner?.lifecycle?.removeObserver(player as DefaultLifecycleObserver)
                }
            },
            enterPictureInPicture = {
                if (checkPlayerStateCanEnterPiP() || fragmentActivity.isInPiPMode()) {
                    viewLifecycleOwner?.lifecycle?.removeObserver(player as DefaultLifecycleObserver)
                } else {
                    stopBackgroundAudioNotificationService()
                }
            },
            enterBackgroundAudioInPiP = {
                if (checkPlayerStateCanEnterPiP() || fragmentActivity.isInPiPMode()) {
                    viewLifecycleOwner?.lifecycle?.removeObserver(player as DefaultLifecycleObserver)
                } else {
                    stopBackgroundAudioNotificationService()
                }
            },
            doNothing = {}
        )


        super.onPause(owner)
    }


    override fun onStop(owner: LifecycleOwner) {
        Logger.d("$TAG Lifecycle onStop")
        isFullscreen = false
        isLandscapeMode = false
        binding.playerUI.resetBrightness()
        removeHandlerAndCountDownSkipCredits()
        stopTvcAds()
        stopLogoAds()
        super.onStop(owner)

        // Case: Lock screen when app in foreground
        checkRuleEnterBackgroundAudioOrPiP(
            enterBackgroundAudio = {},
            enterPictureInPicture = {
                CoroutineScope(Dispatchers.IO).launch {
                    kotlin.runCatching {
                        delay(250)
                        withContext(Dispatchers.Main) {
                            try {
                                if (!fragmentActivity.isInPiPMode() && isLockScreen()) {
                                    stop(force = true)
                                    stopBackgroundAudioNotificationService()
                                }
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                        }
                    }.getOrElse { it.printStackTrace() }
                }
            },
            enterBackgroundAudioInPiP = {
                CoroutineScope(Dispatchers.IO).launch {
                    kotlin.runCatching {
                        delay(250)
                        withContext(Dispatchers.Main) {
                            try {
                                if (!fragmentActivity.isInPiPMode() && isLockScreen()) {
                                    stop(force = true)
                                    stopBackgroundAudioNotificationService()
                                }
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                        }
                    }.getOrElse { it.printStackTrace() }
                }
            },
            doNothing = {}
        )
    }

    override fun onDestroy(owner: LifecycleOwner) {
        Logger.d("$TAG Lifecycle onDestroy")
        isFullscreen = false
        player?.modifyPlayerListener(listener = basePlayerCallback, isRegister = false)
        isLandscapeMode = false
        super.onDestroy(owner)
    }

    //endregion

    private fun initViews() {

    }

    //region Handle auto rotate setting
    private fun handleScreenRotation() {
        fragmentActivity?.run {
            if (isEnableAutoScreenRotationInSettings()) {
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR
            }
        }
        context?.contentResolver?.registerContentObserver(Settings.System.getUriFor(Settings.System.ACCELEROMETER_ROTATION), true, rotationObserver)
    }

    private val rotationObserver = object : ContentObserver(Handler(Looper.getMainLooper())) {
        override fun onChange(selfChange: Boolean) {
            fragmentActivity?.run {
                if (context.isTablet()) {
                    requestedOrientation = if (binding.playerUI.playerControllerLocked) {
                        getCurrentScreenOrientation()
                    } else {
                        if (isEnableAutoScreenRotationInSettings()) {
                            ActivityInfo.SCREEN_ORIENTATION_SENSOR
                        } else {
                            ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
                        }
                    }
                } else {
                    if (isEnableAutoScreenRotationInSettings()) {
                        if (!binding.playerUI.playerControllerLocked) {
                            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR
                        }
                    } else {
                        requestedOrientation = if (isFullscreen) {
                            ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
                        } else {
                            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                        }
                    }
                }
            }
        }
    }

    private fun resetOrientation() {
        if (context.isTablet()) {
            fragmentActivity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        } else {
            fragmentActivity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
        //
        sensorEvent?.disable()
        sensorEvent = null
    }
    //endregion

    private fun initDisplayCutouts() {
        displayCutoutsHelper.initialize(rootView = binding.root, windowManager = fragmentActivity?.windowManager, window = fragmentActivity?.window)
        displayCutoutsHelper.setListener(object : DisplayCutoutsHelper.OnRotateDisplayCutouts {
            override fun onChange(padding: List<Int>, rotation: Int) {
                binding.playerUI.updatePaddingDisplayCutouts(padding = padding, rotation = rotation)
            }
        })
    }

    private fun bindPlayer(viewModel: ViewModel) {
        context?.run {
            if (player == null) {
                player = createExoplayerProxy(this)
            }
            if (viewModel is LiveTVDetailViewModel) {
                playerHandler = PlayerHandler(context = this, drmApi = viewModel)
            } else if (viewModel is VodDetailViewModel) {
                playerHandler = PlayerHandler(context = this, drmApi = viewModel)
            } else if (viewModel is PremiereViewModel) {
                playerHandler = PlayerHandler(context = this, drmApi = viewModel)
            }
        }
        player?.apply {
            setInternalPlayerView(playerView = binding.epvPlayer)
            modifyPlayerListener(listener = basePlayerCallback, isRegister = true)
            addDataPlayerControl(data = playerData)
            viewLifecycleOwner?.lifecycle?.addObserver(player as DefaultLifecycleObserver)
        }

        playerHandler?.apply {
            viewLifecycleOwner?.lifecycle?.addObserver(playerHandler as DefaultLifecycleObserver)
            coroutineScope = viewLifecycleOwner?.lifecycleScope
        }

        //DRM
        Log.d("DRMCallback", "Bind DRM Callback")
        playerDrmCallback = PlayerUtils.buildPlayerDrmCallback(viewModel)
        //

        adsController?.apply {
            viewLifecycleOwner?.lifecycle?.addObserver(this)
        }

        // Layout changes
        onLayoutChange()
        //
    }

    private fun bindPlayerUI(screenType: PlayerHandler.ScreenType) {
        player?.run {
            binding.playerUI.initPlayerUI(playerView = this@PlayerView, fragmentActivity = fragmentActivity, player = this, playerUIEvents =  playerUIEvents, lifecycleOwner = viewLifecycleOwner, useController = useController, screenType = screenType)
        }
    }

    private fun bindEvents() {
        viewLifecycleOwner?.lifecycle?.addObserver(this)
        handleBackPressed()

        // Display cutouts
        viewLifecycleOwner?.lifecycle?.addObserver(displayCutoutsHelper)
        //

        // System UI change
        Logger.d("-----run set system bar change listener")
        fragmentActivity?.setSystemBarVisibilityChangeListener {
            Logger.d("-----run system bar visibility change with full screen $isFullscreen")
            if(isFullscreen) {
                fragmentActivity?.hideSystemBar()
            } else {
                fragmentActivity?.showSystemBar()
            }
        }
    }

    private fun onLayoutChange() {
        // Subtitle View
        updateSubtitleView()
        // Situation Warning View
        updateSituationWarningView()
        if (!isFullscreen) {
            hideAndStopBuyPackageGuideInPlayer()
        }
    }

    private fun createExoplayerProxy(context: Context): ExoPlayerProxy {
        return ExoPlayerProxy(
            context = context,
            coroutineScope = viewLifecycleOwner?.lifecycleScope,
            useCronetForNetworking = true,
            requireMinimumResolutionH265 = sharedPreferences.requiredMinimumResolutionH265(),
            requireMinimumResolutionH265HDR = sharedPreferences.requiredMinimumResolutionH265Hdr(),
            requireMinimumResolutionAV1 = sharedPreferences.requiredMinimumResolutionAV1(),
            requireMinimumResolutionVP9 = sharedPreferences.requiredMinimumResolutionVP9(),
            requireMinimumResolutionDolbyVision = sharedPreferences.requiredMinimumResolutionDolbyVision(),
        )
    }

    private fun updateSubtitleView(force: Boolean = false) {
        if (force) {
            onSubtitleLayoutChanged()
        } else {
            try {
                val exoSubtitleView = binding.epvPlayer.findViewById<View>(R.id.v_exo_subtitles)
                viewLifecycleOwner?.lifecycleScope?.launchWhenStarted {
                    if (exoSubtitleView?.isAttachedToWindow == true) {
                        withContext(Dispatchers.IO) {
                            runCatching {
                                delay(300L)
                                withContext(Dispatchers.Main) {
                                    onSubtitleLayoutChanged()
                                }
                            }.getOrElse {
                                it.printStackTrace()
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun onSubtitleLayoutChanged() {
        updateSubtitleLayoutStyle(videoResizeMode, isFullscreen, isLandscapeMode)
        updateSituationWarningView()
    }

    private fun updateSubtitleLayoutStyle(resizeMode: Int, isFullScreen: Boolean, isLandscape: Boolean) {
        if (fragmentActivity.isInPiPMode()) {
            binding.epvPlayer.setFractionalTextSize(SubtitleView.DEFAULT_TEXT_SIZE_FRACTION)
            binding.epvPlayer.setSubtitleBottomPaddingFraction(SubtitleView.DEFAULT_BOTTOM_PADDING_FRACTION)
            return
        } else {
            binding.epvPlayer.updateSubtitleLayoutStyle(
                player,
                fragmentActivity,
                isFullScreen,
                isLandscape,
                resizeMode,
                isSituationWarningShown
            )
        }
    }

    private fun removeObservers() {
        viewLifecycleOwner?.lifecycle?.removeObserver(this)
        viewLifecycleOwner?.lifecycle?.removeObserver(player as DefaultLifecycleObserver)
        viewLifecycleOwner?.lifecycle?.removeObserver(playerHandler as DefaultLifecycleObserver)
        (gameEmojiVodController as? DefaultLifecycleObserver)?.let { viewLifecycleOwner?.lifecycle?.removeObserver(it) }

        // Display cutouts
        viewLifecycleOwner?.lifecycle?.removeObserver(displayCutoutsHelper)
        //
        adsController?.apply {
            viewLifecycleOwner?.lifecycle?.removeObserver(this)
        }
        
        // Remove system bar visibility change listener
        fragmentActivity?.removeSystemBarVisibilityChangeListener()
    }

    fun getPlayer() = player
    fun getPlayerConfig() = playerConfig
    fun isPlayingVipTrailer() = isPlayingVipTrailer
    fun currentDuration() : Long {
        return when (PlayerUtils.getPlayingType()) {
            PlayingType.Local -> {
                player?.currentDuration() ?: 0L
            }
            PlayingType.Cast -> {
                pairingConnection.getRemoteData()?.remotePlayer?.currentDuration ?: 0L
            }
        }
    }
    fun request() = player?.request
    fun bufferDuration() : Long {
        return when (PlayerUtils.getPlayingType()) {
            PlayingType.Local -> {
                player?.bufferDuration() ?: 0L
            }
            PlayingType.Cast -> {
                pairingConnection.getRemoteData()?.remotePlayer?.bufferDuration ?: 0L
            }
        }
    }
    fun totalDuration() : Long {
        return when (PlayerUtils.getPlayingType()) {
            PlayingType.Local -> {
                player?.totalDuration() ?: 0L
            }
            PlayingType.Cast -> {
                pairingConnection.getRemoteData()?.remotePlayer?.totalDuration ?: 0L
            }
        }
    }
    fun isPlaying() : Boolean {
        return when (PlayerUtils.getPlayingType()) {
            PlayingType.Local -> {
                player?.isPlaying() == true
            }
            PlayingType.Cast -> {
                pairingConnection.getRemoteData()?.remotePlayer?.state == RemotePlayerState.PLAY
            }
        }
    }
    fun isLive() = player?.isLive()
    fun isFullscreen() = isFullscreen
    fun isPlayOffline() = isVodOffline
    fun clearRequest() { player?.request = null }
    fun getVideoSize() : String {
        playerVideoSize?.let {
            return "${it.width}x${it.height}"
        } ?: kotlin.run {
            return ""
        }
    }
    fun pause(force: Boolean = false) {
        when (PlayerUtils.getPlayingType()) {
            PlayingType.Local -> {
                player?.pause(force = force)
            }
            PlayingType.Cast -> {
                if (pairingConnection.getRemoteData()?.remotePlayer?.state == RemotePlayerState.PLAY) {
                    when (pairingConnection.getCurrentConnection()) {
                        is FBoxDeviceInfoV2 -> {
                            pairingConnection.sendEventKey(key = KeyCode.PAUSE)
                        }
                        is FSamsungTVDeviceInfo,
                        is FSamsungTVDeviceInfoExternal -> {
                            pairingConnection.sendEventKey(key = KeyCode.PAUSE)
                        }
                        is FAndroidTVDeviceInfo -> {
                            pairingConnection.sendEventTogglePlayer(isPlay = false)
                        }
                        is FAndroidTVDeviceInfoExternal -> {
                            pairingConnection.sendEventKey(key = KeyCode.PAUSE)
                        }
                    }
                }
            }
        }
        binding.playerUI.updateLayouts(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
    }
    fun play(force: Boolean = false) {
        when (PlayerUtils.getPlayingType()) {
            PlayingType.Local -> {
                viewLifecycleOwner?.lifecycleScope?.launchWhenStarted {
                    val canPlay = playerStateValidation?.checkPlayerCanPlay() ?: true
                    if (canPlay) {
                        player?.play(force = force)
                    }
                }
            }
            PlayingType.Cast -> {
                if (pairingConnection.getRemoteData()?.remotePlayer?.state != RemotePlayerState.PLAY) {
                    if (pairingConnection.getRemoteData()?.remotePlayer?.state == RemotePlayerState.PLAY) {
                        when (pairingConnection.getCurrentConnection()) {
                            is FBoxDeviceInfoV2 -> {
                                pairingConnection.sendEventKey(key = KeyCode.PLAY)
                            }
                            is FSamsungTVDeviceInfo,
                            is FSamsungTVDeviceInfoExternal -> {
                                pairingConnection.sendEventKey(key = KeyCode.PLAY)
                            }
                            is FAndroidTVDeviceInfo -> {
                                pairingConnection.sendEventTogglePlayer(isPlay = true)
                            }
                            is FAndroidTVDeviceInfoExternal -> {
                                pairingConnection.sendEventKey(key = KeyCode.PLAY)
                            }
                        }
                    }
                }
            }
        }
        binding.playerUI.updateLayouts(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
    }

    fun setPlaybackSpeed(speed: Float) {
        when (PlayerUtils.getPlayingType()) {
            PlayingType.Local -> {
                player?.setPlaybackSpeed(speed)
                viewLifecycleOwner?.lifecycleScope?.launch {
                    binding.playerUI.updatePlayerSpeed(speed)
                }
            }
            PlayingType.Cast -> {

            }
        }
    }

    fun stopServices() {
        playerHandler?.stopAll(includePlayer = false)
    }

    fun stopPlayerLocal(force : Boolean? = null, isClearRequest: Boolean = false) {
        if (force != null) {
            player?.stop(force = force)
        } else {
            player?.stop()
        }
        if (isClearRequest) {
            clearRequest()
        }
    }
    fun stop(force : Boolean? = null) {
        when (PlayerUtils.getPlayingType()) {
            PlayingType.Local -> {
                if (force != null) {
                    player?.stop(force = force)
                } else {
                    player?.stop()
                }
            }
            PlayingType.Cast -> {
                // Don't send stop, disable function, not using
//                when (pairingConnection.getCurrentConnection()) {
//                    is FBoxDeviceInfoV2 -> {
//                        pairingConnection.sendEventKey(key = KeyCode.STOP)
//                    }
//                    is FSamsungTVDeviceInfo,
//                    is FSamsungTVDeviceInfoExternal -> {
//                        pairingConnection.sendEventKey(key = KeyCode.STOP)
//                    }
//                    is FAndroidTVDeviceInfo -> {
//                        pairingConnection.sendEventTogglePlayer(isPlay = false)
//                    }
//                    is FAndroidTVDeviceInfoExternal -> {
//                        pairingConnection.sendEventKey(key = KeyCode.STOP)
//                    }
//                }

            }
        }

    }
    fun enterFullscreenMode() {
        if (context.isTablet()) {
            isFullscreen = true
            binding.playerUI.updateLayouts(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
            playerUIListener?.onFullScreen(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
            tabletSystemBars()
            // Layout changes
            onLayoutChange()
            //
        } else {
            enterFullscreen()
        }
        // Picture in Picture
        PlayerPiPHelper.storagePlayerMode(isFullscreen = isFullscreen)
    }
    fun exitFullscreenMode() {
        if (context.isTablet()) {
            isFullscreen = false
            binding.playerUI.updateLayouts(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
            playerUIListener?.onFullScreen(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
            tabletSystemBars()
            // Layout changes
            onLayoutChange()
            //
        } else {
            exitFullscreen()
        }
        // Picture in Picture
        PlayerPiPHelper.storagePlayerMode(isFullscreen = isFullscreen)
    }

    fun updateDetailPremiere(details: com.xhbadxx.projects.module.domain.entity.fplay.premier.Details?) {
        updatePremierePlayerData(details = details)
        isPlayingVipTrailer = false

        binding.playerUI.apply {
            updatePlayingVipTrailer(isPlayingVipTrailer = false)
            prepareDataPlayerControl(detailsPremiere = details, dataPlayerControl = playerData)
        }
    }

    fun setInComingRequest(
        request: IPlayer.Request?,
        isRunningBackgroundAudio: Boolean,
        // VOD
        vodDetail: Details ?= null,
        currentEpisode: Details.Episode ?= null,
        userProfile: UserProfile ?= null,
        isPlayingTrailer: Boolean = false,
        playlistName: String = "",
        // TV
        tvDetail: TvChannelDetail ?= null,
        // Premiere
        premiereDetails: com.xhbadxx.projects.module.domain.entity.fplay.premier.Details?= null
    ) {
        Logger.d("$TAG setInComingRequest - bg = $isRunningBackgroundAudio")
        inComingRequest = request
        isInComingRequestEnableBackgroundAudio = isRunningBackgroundAudio
        //
        if (vodDetail != null && currentEpisode != null && userProfile != null) {
            this.details = vodDetail
            this.currentEpisode = currentEpisode
            updateVodPlayerData(userProfile = userProfile, details = vodDetail, episode = currentEpisode, isPlayingTrailer = isPlayingTrailer, playlistName = playlistName)
        }
        //
        tvDetail?.let {
            this.tvChannelDetail = it
            updateLivePlayerData(tvChannelDetail = it)
        }
        //
        premiereDetails?.let {
            this.detailsPremiere = it
            updatePremierePlayerData(details = it)
        }

        // Picture in Picture
        triggerUpdatePictureInPictureParams()
    }

    /**
     * Prepare player for url
     */
    fun preparePlayer(playerConfig: PlayerConfig?= null, url: String) {
        playerConfig?.let { this.playerConfig = it }
        isPlayingVipTrailer = false
        binding.playerUI.apply {
            prepareDataPlayerControl(playerConfig = playerConfig, dataPlayerControl = playerData)
            updatePlayingVipTrailer(isPlayingVipTrailer = false)
        }
        onShowThumb(isShow = false)
        hideAlertLimitCcu()
        player?.run {
            playerHandler?.bindAndPlay(
                request = PlayerHandler.Request(screenType = PlayerHandler.ScreenType.Live),
                player = this,
                playerRequest = IPlayer.Request(
                    url = IPlayer.Request.Url(url = url),
                    clearRequestWhenOnStop = false,
                ),
                stream = Stream(url = url),
            )

            debugViewData.ottType = ""
        }
    }

    /**
     * Prepare player for url
     */
    fun preparePlayerVipTrailer(playerConfig: PlayerConfig?= null, url: String, linkToShare: String, detailVod: Details? = null, detailPremiere: com.xhbadxx.projects.module.domain.entity.fplay.premier.Details? = null) {
        playerConfig?.let { this.playerConfig = it }
        this.linkToShare = linkToShare
        this.details = detailVod
        this.detailsPremiere = detailPremiere
        this.isPlayingVipTrailer = true
        binding.playerUI.apply {
            updatePlayingVipTrailer(isPlayingVipTrailer = true)
            playerData.reset()
            prepareDataPlayerControl(playerConfig = playerConfig, details = details,detailsPremiere = detailPremiere, dataPlayerControl = playerData)
            clearAgeRestrictionData()
        }
        // Skip intro credits
        updateSkipIntroCreditsData(timeStartIntro = 0, timeStartContent = 0, timeEndContent = 0)

        onShowThumb(isShow = false)
        hideAlertLimitCcu()
        player?.run {
            playerHandler?.bindAndPlay(
                request = PlayerHandler.Request(screenType = PlayerHandler.ScreenType.Live),
                player = this,
                playerRequest = IPlayer.Request(
                    url = IPlayer.Request.Url(url = url),
                    clearRequestWhenOnStop = false,
                ),
                stream = Stream(url = url)
            )
        }

        // Picture in Picture
        triggerUpdatePictureInPictureParams()
    }

    fun preparePlayerPreview(playerConfig: PlayerConfig?, vodPreviewData: VodPreviewPlayerInfo, userProfile: UserProfile, details: Details?, episode: Details.Episode, playlistName: String = "", onEvents: PlayerHandler.OnEvents) {
        playerConfig?.let { this.playerConfig = it }
        screenType = PlayerHandler.ScreenType.Vod
        linkToShare = details?.blockContent?.webUrl ?:""
        this.isPlayingVipTrailer = false
        updateVodPlayerData(userProfile = userProfile, details = details, episode = episode, isPlayingTrailer = false, playlistName = playlistName)

        screenProtector?.setProtect(false)
        screenProtector?.disableScreenProtector()

        // Skip intro credits
        updateSkipIntroCreditsData(stream = Stream())
        onShowThumb(isShow = false)
        hideAlertLimitCcu()
        binding.playerUI.apply {
            updatePlayingVipTrailer(isPlayingVipTrailer = false)
            updateCurrentVODData(episode)
            updateOverlayLogo(details?.blockContent?.overlayLogo)
            prepareThumb(episode)
            prepareDataPlayerControl(playerConfig = playerConfig, details = details, dataPlayerControl = playerData)
        }

        player?.run {
            playerHandler?.onEvents = onEvents
            playerHandler?.onCastSessionEvents = null
            playerHandler?.bindAndPlay(
                request = PlayerHandler.Request(
                    screenType = PlayerHandler.ScreenType.Vod,
                    streamId = details?.blockContent?.id ?: "",
                    type = "vod",
                    eventId = details?.blockContent?.id ?: "",
                    bitrateId = episode.autoProfile,
                    isFullHd = Utils.convertStringToInt(sharedPreferences.supportFullHD(), defaultValue = 1),
                ),
                player = this,
                playerRequest = IPlayer.Request(
                    id = episode.id,
                    streamId = episode.autoProfile,
                    url = IPlayer.Request.Url(
                        dolbyVisionUrl = vodPreviewData.urlDashDolbyVision,
                        h265HDR10PlusUrl = vodPreviewData.urlDashH265Hdr10Plus,
                        h265HDR10Url = vodPreviewData.urlDashH265Hdr,
                        h265HlgUrl = vodPreviewData.urlDashH265Hlg,
                        av1Url = vodPreviewData.urlDashAv1,
                        vp9Url = vodPreviewData.urlDashVp9,
                        h265Url = vodPreviewData.urlDashH265,
                        url = vodPreviewData.urlDash
                    ),
                    startPosition = episode.id.toIntOrNull()?.let { episodeIndex ->
                        if (userProfile.getEpisodeIndex() == episodeIndex) userProfile.getStartPosition()
                        else 0
                    } ?: 0,
                    forceUsingStartPosition = userProfile.isForceUsingStartPosition(),
                    delayToPlay = false,
                    clearRequestWhenOnStop = false,
                    autoStreaming = IPlayer.Request.AutoStreaming(
                        resolutionMaxHeight = currentEpisode?.resolution?.maxHeight ?: 0,
                        resolutionMaxWidth = currentEpisode?.resolution?.maxWidth ?: 0,
                    ),
                    autoPlay = vodPreviewData.isAutoPlay
                ),
                stream = Stream(url = vodPreviewData.urlDashDolbyVision.ifBlank { vodPreviewData.urlDashH265Hdr10Plus.ifBlank { vodPreviewData.urlDashH265Hdr.ifBlank { vodPreviewData.urlDashH265Hlg.ifBlank { vodPreviewData.urlDashAv1.ifBlank { vodPreviewData.urlDashVp9.ifBlank { vodPreviewData.urlDashH265.ifBlank { vodPreviewData.urlDash } } } } } } })
            )
        }
    }

    /**
     * Prepare player for vod content
     */
    fun preparePlayerVOD(
        playerConfig: PlayerConfig? = null,
        userProfile: UserProfile,
        details: Details?,
        episode: Details.Episode,
        data: Stream,
        delayToPlay: Boolean,
        isPlayingTrailer: Boolean,
        playlistName: String = "",
        isResetPlayingPosition: Boolean,
        onEvents: PlayerHandler.OnEvents,
        onCastSessionEvents: PlayerHandler.OnCastSessionEvents,
        processShowIp: (Long, Int, Int) -> Unit = { _, _, _ -> },
        processReloadStream: () -> Unit = {},
        processShowMatrixIp: () -> Unit = {},
        sportInteractiveViewModel: SportInteractiveViewModel? = null,
        drmKey: ByteArray?
    ) {

        playerConfig?.let { this.playerConfig = it }
        if (isResetPlayingPosition) seek(0)
        screenType = PlayerHandler.ScreenType.Vod
        linkToShare = details?.blockContent?.webUrl ?:""
        this.isPlayingVipTrailer = false
        updateVodPlayerData(userProfile = userProfile, details = details, episode = episode, isPlayingTrailer = isPlayingTrailer, playlistName = playlistName)

        if(details?.blockContent?.isVerimatrix == true) {
            screenProtector?.setProtect(true)
            screenProtector?.enableScreenProtector()
        } else {
            screenProtector?.setProtect(false)
            screenProtector?.disableScreenProtector()
        }

        // Skip intro credits
        updateSkipIntroCreditsData(stream = data)

        onShowThumb(isShow = false)
        hideAlertLimitCcu()

        binding.playerUI.apply {
            updatePlayingVipTrailer(isPlayingVipTrailer = false)
            updateCurrentVODData(episode)
            updateOverlayLogo(details?.blockContent?.overlayLogo)
            prepareThumb(episode)
            prepareDataPlayerControl(playerConfig = playerConfig, details = details, dataPlayerControl = playerData)
        }

        player?.run {
            playerHandler?.onEvents = onEvents
            playerHandler?.onCastSessionEvents = onCastSessionEvents
            playerHandler?.bindAndPlay(
                request = PlayerHandler.Request(
                    isDrm = details?.blockContent?.isVerimatrix ?: false,
                    provider = details?.blockContent?.sourceProvider ?: "",
                    merchant = data.merchant,
                    screenType = PlayerHandler.ScreenType.Vod,
                    streamId = details?.blockContent?.id ?: "",
                    type = "vod",
                    eventId = details?.blockContent?.id ?: "",
                    bitrateId = episode.autoProfile,
                    isFullHd = Utils.convertStringToInt(sharedPreferences.supportFullHD(), defaultValue = 1),
                    pingEnable = data.pingEnable,
                    pingSession = data.pingSession,
                    pingEncrypt = data.pingEnc,
                    codeLimitCcu = codeLimitCcu,
                    processShowIp = processShowIp,
                    processShowMatrixIp = processShowMatrixIp,
                    processReloadStream = processReloadStream
                ),
                player = this,
                playerRequest = buildVodRequest(
                    playerConfig = playerConfig,
                    userProfile = userProfile,
                    delayToPlay = delayToPlay,
                    stream = data,
                    episode = episode,
                    drmKey = drmKey,
                ),
                stream = data
            )
        }
        if (inComingRequest?.isTheSameUrlRequest(isDrm = details?.blockContent?.isVerimatrix == true, stream = data) == true) {
            setInComingRequest(null, false)
        }
    }

    fun buildVodRequest(
        playerConfig: PlayerConfig?,
        userProfile: UserProfile?,
        delayToPlay: Boolean,
        stream: Stream,
        episode: Details.Episode,
        drmKey: ByteArray?
    ): IPlayer.Request {

        fun buildUrlRequest(): IPlayer.Request.Url {
            return if (details?.blockContent?.isVerimatrix == true)
                IPlayer.Request.Url(
                    dolbyVisionUrl = stream.urlDashDrmDolbyVision,
                    h265HDR10PlusUrl = stream.urlDashDrmH265Hdr10Plus,
                    h265HDR10Url = stream.urlDashDrmH265Hdr,
                    h265HlgUrl = stream.urlDashDrmH265Hlg,
                    h265Url = stream.urlDashDrmH265,
                    av1Url = stream.urlDashDrmAv1,
                    vp9Url = stream.urlDashDrmVp9,
                    url = stream.urlDash
                )
            else
                IPlayer.Request.Url(
                    dolbyVisionUrl = stream.urlDashDolbyVision,
                    h265HDR10PlusUrl = stream.urlDashH265Hdr10Plus,
                    h265HDR10Url = stream.urlDashH265Hdr,
                    h265HlgUrl = stream.urlDashH265Hlg,
                    h265Url = stream.urlDashH265,
                    av1Url = stream.urlDashAv1,
                    vp9Url = stream.urlDashVp9,
                    url = stream.urlDashNoDrm.ifEmpty { stream.urlSub.ifEmpty { stream.url } }
                )
        }

        return IPlayer.Request(
            id = episode.id,
            streamId = episode.autoProfile,
            url = buildUrlRequest(),
            startPosition = episode.id.toIntOrNull()?.let { episodeIndex ->
                if (userProfile?.getEpisodeIndex() == episodeIndex) userProfile.getStartPosition()
                else 0
            } ?: 0,
            forceUsingStartPosition = userProfile?.isForceUsingStartPosition() ?: false,
            drm = run {
                if (stream.merchant == "fptplay"){
                    IPlayer.Request.Drm(
                        userId = sharedPreferences.userId(),
                        merchant = stream.merchant,
                        appId = "sigma",
                        sessionId = stream.sessionId,
                        type = IPlayer.DrmType.SIGMA,
                        stagingMode = drmStagingMode,
                        drmKey = IPlayer.Request.Drm.DrmKey(
                            drmCallback = playerDrmCallback,
                            keyOffId = drmKey
                        ),
                        enableDrmOffline = sharedPreferences.enableCacheDrm()
                    )
                } else {
                    IPlayer.Request.Drm(
                        userId = sharedPreferences.userId(),
                        merchant = stream.merchant,
                        sessionId = stream.sessionId,
                        type = IPlayer.DrmType.CAST_LAB,
                        drmKey = IPlayer.Request.Drm.DrmKey(
                            drmCallback = playerDrmCallback,
                            keyOffId = drmKey
                        ),
                        enableDrmOffline = sharedPreferences.enableCacheDrm()
                    )
                }
            },
            delayToPlay = delayToPlay,
            clearRequestWhenOnStop = false,
            autoStreaming = IPlayer.Request.AutoStreaming(
                resolutionMaxHeight = currentEpisode?.resolution?.maxHeight ?: 0,
                resolutionMaxWidth = currentEpisode?.resolution?.maxWidth ?: 0,
            ),
            headerRequestProperties = Utils.getHeaderForPlayerRequest(stream.streamSession),
            bufferDuration = if (playerConfig?.isAudioMode == true) {
                IPlayer.Request.BufferDuration(
                    minBufferDuration = MainApplication.INSTANCE.appConfig.bufferAudioSecond,
                    maxBufferDuration = MainApplication.INSTANCE.appConfig.bufferAudioSecond
                )
            } else null
        )
    }

    fun setBuyPackageGuide(buyPackageGuide: BuyPackageGuide?) {
        this.buyPackageGuide = buyPackageGuide
    }
    /**
     * Prepare player for offline download vod content
     * @param path: absolute path to file m3u8 (Ex: storage/emulated/0/android/data/com.fplay.activity/files/videos/a323771e504e6f9/a323771e504e6f9_local.m3u8)
     */
    fun preparePlayerOfflineVOD(playerConfig: PlayerConfig?= null, path: String) {
        playerConfig?.let { this.playerConfig = it }
        this.isPlayingVipTrailer = false
        binding.playerUI.apply {
            prepareDataPlayerControl(playerConfig = playerConfig, dataPlayerControl = playerData)
            updatePlayingVipTrailer(isPlayingVipTrailer = false)
        }
        onShowThumb(isShow = false)
        hideAlertLimitCcu()

        updateIsVodOffline(isVodOffline = true)
        player?.prepare(request = IPlayer.Request(url = IPlayer.Request.Url(url = path), clearRequestWhenOnStop = false))
    }

    /**
     * Prepare player for offline download vod content
     * @param folderName: folder contains *.ts file (Ex: a323771e504e6f9)
     * @param fileName: file m3u8 (Ex: a323771e504e6f9_local.m3u8)
     * Get warning list from offline data
     * @param currentItem.warnings
     */
    fun preparePlayerOfflineVOD(playerConfig: PlayerConfig?= null, currentItem: VideoTaskItem, title: String) {
        Timber.tag("tam-sport").d("preparePlayerOfflineVOD")
        playerConfig?.let { this.playerConfig = it}
        onShowThumb(isShow = false)
        updateIsVodOffline(isVodOffline = true)
        this.isPlayingVipTrailer = false
        binding.playerUI.apply {
            prepareDataPlayerControl(playerConfig = playerConfig, dataPlayerControl = playerData)
            updatePlayingVipTrailer(isPlayingVipTrailer = false)
            updateOverlayLogo(currentItem.overlayLogo?.localUrl() ?: currentItem.overlayLogo)
        }
        updatePlayerTitleForOffline(title)
        val fileDir = currentItem.filePath
        fileDir?.let {
            val fileMedia = File(it)
            if(fileMedia.exists()) {
                player?.seek(duration = 0)
                updateSkipIntroCreditsData(currentItem.timeStartIntro,currentItem.timeStartContent,currentItem.timeEndContent)
                if (currentItem.isDrm) {
                    player?.prepare(
                        request = IPlayer.Request(
                            url = IPlayer.Request.Url(url = fileDir),
                            clearRequestWhenOnStop = false,
                            drm = run {
                                IPlayer.Request.Drm(
                                    userId = sharedPreferences.userId(),
                                    merchant = currentItem.merchant ?: "",
                                    appId = "sigma",
                                    sessionId = currentItem.session ?: "",
                                    type = IPlayer.DrmType.SIGMA,
                                    stagingMode = Util.isEnvironmentStaging(),
                                    drmKey = IPlayer.Request.Drm.DrmKey(
                                        drmCallback = playerDrmCallback,
                                        keyOffId = currentItem.keyOffId.fromBase64Default()
                                    ),
                                    enableDrmOffline = true
                                )
                            },
                        )
                    )
                    // TODO: update drm key when it exprired
//                    DownloadUtils.extendKeyOffId(context,currentItem, object: DownloadKeyResult {
//                        override fun downloadKeySuccess(key: String) {
//
//                        }
//
//                        override fun downloadKeyFailed(errCode: Int, errMessage: String) {
//
//                        }
//                    }, trackingProxy, trackingInfo)
                } else {
                    player?.prepare(request = IPlayer.Request(url = IPlayer.Request.Url(url = fileDir), clearRequestWhenOnStop = false))
                }
                binding.playerUI.updateCurrentVODDataOffline(currentItem)
            }
        }
    }

    /**
     * Prepare player for live content
     */
    fun preparePlayerLive(
        playerConfig: PlayerConfig?= null,
        details: TvChannelDetail?,
        autoProfile: String,
        data: Stream,
        isMulticast: Boolean,
        onEvents: PlayerHandler.OnEvents,
        onCastSessionEvents: PlayerHandler.OnCastSessionEvents,
        processShowIp: (Long, Int, Int) -> Unit = { _, _, _ -> },
        processReloadStream: () -> Unit = {},
        processShowMatrixIp: () -> Unit = {},
        sportInteractiveViewModel: SportInteractiveViewModel? = null,
        headers: ArrayMap<String, String> = arrayMapOf<String, String>(),
        drmKey: ByteArray?
    ) {
        Timber.tag("tam-sport").d("preparePlayerLive")
        playerConfig?.let { this.playerConfig = it }
        this.tvChannelDetail = details
        this.isPlayingVipTrailer = false
        screenType = PlayerHandler.ScreenType.Live
        linkToShare = details?.websiteUrl ?:""

        if(details?.isVerimatrix == true) {
            screenProtector?.setProtect(true)
            screenProtector?.enableScreenProtector()
        } else {
            screenProtector?.setProtect(false)
            screenProtector?.disableScreenProtector()
        }

        updateLivePlayerData(tvChannelDetail = details)
        // Skip intro credits
        updateSkipIntroCreditsData(stream = data)

        onShowThumb(isShow = false)
        hideAlertLimitCcu()

        binding.playerUI.apply {
            prepareDataPlayerControl(playerConfig = playerConfig, tvChannelDetail = details, dataPlayerControl = playerData)
            updatePlayingVipTrailer(isPlayingVipTrailer = false)
            updateOverlayLogo(data.overlayLogo)
            clearAgeRestrictionData()
        }

        Timber.tag("LiveTVPlayerFragment").w("preparePlayerLive mechant = ${data.merchant} | url ${if (data.urlSub.isNotEmpty()) data.urlSub else data.url} LLC: ${details?.lowlatency}")
        player?.run {
            playerHandler?.onEvents = onEvents
            playerHandler?.onCastSessionEvents = onCastSessionEvents
            playerHandler?.bindAndPlay(
                request = PlayerHandler.Request(
                    isDrm = details?.isVerimatrix ?: false,
                    provider = details?.sourceProvider ?: "",
                    merchant = data.merchant,
                    screenType = PlayerHandler.ScreenType.Live,
                    streamId = details?.id ?: "",
                    type = "livetv",
                    eventId = details?.id ?: "",
                    bitrateId = autoProfile,
                    isFullHd = Utils.convertStringToInt(sharedPreferences.supportFullHD(), defaultValue = 1),
                    pingEnable = data.pingEnable,
                    pingSession = data.pingSession,
                    pingEncrypt = data.pingEnc,
                    codeLimitCcu = codeLimitCcu,
                    processShowIp = processShowIp,
                    processShowMatrixIp = processShowMatrixIp,
                    processReloadStream = processReloadStream
                ),
                player = this,
                playerRequest = buildLiveTvRequest(
                    autoProfile = autoProfile,
                    isMulticast = isMulticast,
                    stream = data,
                    details = details,
                    headers = headers,
                    drmKey = drmKey,
                ),
                stream = data
            )
        }

        if (inComingRequest?.isTheSameUrlRequest(isDrm = details?.isVerimatrix == true, stream = data) == true) {
            setInComingRequest(null, false)
        }

        // Picture in Picture
        triggerUpdatePictureInPictureParams()
    }

    /**
     * Prepare player for live preview content
     */
    fun preparePlayerLivePreview(
        playerConfig: PlayerConfig?= null,
        details: TvChannelDetail?,
        autoProfile: String,
        liveTvPreviewPlayerInfo: LiveTvPreviewPlayerInfo,
        onEvents: PlayerHandler.OnEvents,
        onCastSessionEvents: PlayerHandler.OnCastSessionEvents,
        processShowIp: (Long, Int, Int) -> Unit = { _, _, _ -> },
        processReloadStream: () -> Unit = {},
        processShowMatrixIp: () -> Unit = {},
        sportInteractiveViewModel: SportInteractiveViewModel? = null,
        headers: ArrayMap<String, String> = arrayMapOf<String, String>(),
        drmKey: ByteArray?
    ) {
        Timber.tag(TAG).d("preparePlayerLivePreview")
        playerConfig?.let { this.playerConfig = it }
        this.tvChannelDetail = details
        screenType = PlayerHandler.ScreenType.Live
        linkToShare = details?.websiteUrl ?:""

        if(details?.isVerimatrix == true) {
            screenProtector?.setProtect(true)
            screenProtector?.enableScreenProtector()
        } else {
            screenProtector?.setProtect(false)
            screenProtector?.disableScreenProtector()
        }

        updateLivePlayerData(tvChannelDetail = details)
        // Skip intro credits
        updateSkipIntroCreditsData(stream = Stream())

        onShowThumb(isShow = false)
        hideAlertLimitCcu()

        binding.playerUI.apply {
            prepareDataPlayerControl(playerConfig = playerConfig, tvChannelDetail = details, dataPlayerControl = playerData)
            updatePlayingVipTrailer(isPlayingVipTrailer = false)
            updateOverlayLogo(liveTvPreviewPlayerInfo.overlayLogo)
            clearAgeRestrictionData()
        }

//        Timber.tag("LiveTVPlayerFragment").w("preparePlayerLive mechant = ${data.merchant} | url ${if (data.urlSub.isNotEmpty()) data.urlSub else data.url} LLC: ${details?.lowlatency}")
        player?.run {
            playerHandler?.onEvents = onEvents
            playerHandler?.onCastSessionEvents = onCastSessionEvents
            playerHandler?.bindAndPlay(
                request = PlayerHandler.Request(
                    isDrm = details?.isVerimatrix ?: false,
                    provider = details?.sourceProvider ?: "",
                    merchant = liveTvPreviewPlayerInfo.merchant,
                    screenType = PlayerHandler.ScreenType.Live,
                    streamId = details?.id ?: "",
                    type = "livetv",
                    eventId = details?.id ?: "",
                    bitrateId = autoProfile,
                    isFullHd = Utils.convertStringToInt(sharedPreferences.supportFullHD(), defaultValue = 1),
                    pingEnable = liveTvPreviewPlayerInfo.pingEnable,
                    pingSession = liveTvPreviewPlayerInfo.pingSession,
                    pingEncrypt = liveTvPreviewPlayerInfo.pingEnc,
                    processShowIp = processShowIp,
                    processShowMatrixIp = processShowMatrixIp,
                    processReloadStream = processReloadStream
                ),
                player = this,
                playerRequest = IPlayer.Request(
                    id = details?.id ?: "",
                    streamId = autoProfile,
                    url = IPlayer.Request.Url(url = liveTvPreviewPlayerInfo.url),
                    isMulticast = false,
                    drm = run {
                        if (liveTvPreviewPlayerInfo.merchant == "fptplay") {
                            IPlayer.Request.Drm(
                                userId = sharedPreferences.userId(),
                                merchant = liveTvPreviewPlayerInfo.merchant,
                                appId = "sigma",
                                sessionId = liveTvPreviewPlayerInfo.session,
                                type = IPlayer.DrmType.SIGMA,
                                stagingMode = drmStagingMode,
                                drmKey = IPlayer.Request.Drm.DrmKey(
                                    drmCallback = playerDrmCallback,
                                    keyOffId = drmKey
                                ),
                                enableDrmOffline = false
                            )
                        } else {
                            IPlayer.Request.Drm(
                                userId = sharedPreferences.userId(),
                                merchant = liveTvPreviewPlayerInfo.merchant,
                                sessionId = liveTvPreviewPlayerInfo.session,
                                type = IPlayer.DrmType.CAST_LAB,
                                drmKey = IPlayer.Request.Drm.DrmKey(
                                    drmCallback = playerDrmCallback,
                                    keyOffId = drmKey
                                ),
                                enableDrmOffline = false
                            )
                        }
                    },
                    lowLatency = details?.lowlatency ?: false,
                    isLive = true,
                    clearRequestWhenOnStop = false,
                    autoStreaming = IPlayer.Request.AutoStreaming(
                        resolutionMaxHeight = details?.resolution?.maxHeight ?: 0,
                        resolutionMaxWidth = details?.resolution?.maxWidth ?: 0,
                    ),
                    headerRequestProperties = headers
                ),
                stream = Stream(url = liveTvPreviewPlayerInfo.url)
            )
        }
    }

    fun buildLiveTvRequest(
        autoProfile: String,
        isMulticast: Boolean,
        stream: Stream,
        details: TvChannelDetail?,
        headers: ArrayMap<String, String>,
        drmKey: ByteArray?,
    ): IPlayer.Request {

        fun buildUrlRequest(): IPlayer.Request.Url {
            return if (details?.isVerimatrix == true)
                IPlayer.Request.Url(
                    dolbyVisionUrl = stream.urlDashDrmDolbyVision,
                    h265HDR10PlusUrl = stream.urlDashDrmH265Hdr10Plus,
                    h265HDR10Url = stream.urlDashDrmH265Hdr,
                    h265HlgUrl = stream.urlDashDrmH265Hlg,
                    h265Url = stream.urlDashDrmH265,
                    av1Url = stream.urlDashDrmAv1,
                    vp9Url = stream.urlDashDrmVp9,
                    url = if(stream.urlDashNoDrm.isNotEmpty())
                        stream.urlDashNoDrm
                    else if (stream.urlSub.isNotEmpty())
                        stream.urlSub
                    else
                        stream.url
                )
            else
                IPlayer.Request.Url(
                    dolbyVisionUrl = stream.urlDashDolbyVision,
                    h265HDR10PlusUrl = stream.urlDashH265Hdr10Plus,
                    h265HDR10Url = stream.urlDashH265Hdr,
                    h265HlgUrl = stream.urlDashH265Hlg,
                    h265Url = stream.urlDashH265,
                    av1Url = stream.urlDashAv1,
                    vp9Url = stream.urlDashVp9,
                    url = if(stream.urlDashNoDrm.isNotEmpty())
                        stream.urlDashNoDrm
                    else if (stream.urlSub.isNotEmpty())
                        stream.urlSub
                    else
                        stream.url
                )
        }

        return IPlayer.Request(
            id = details?.id ?: "",
            streamId = autoProfile,
            url = buildUrlRequest(),
            isMulticast = isMulticast,
            drm = run {
                if (stream.merchant == "fptplay") {
                    IPlayer.Request.Drm(
                        userId = sharedPreferences.userId(),
                        merchant = stream.merchant,
                        appId = "sigma",
                        sessionId = stream.sessionId,
                        type = IPlayer.DrmType.SIGMA,
                        stagingMode = drmStagingMode,
                        drmKey = IPlayer.Request.Drm.DrmKey(
                            drmCallback = playerDrmCallback,
                            keyOffId = drmKey
                        ),
                        enableDrmOffline = sharedPreferences.enableCacheDrm()
                    )
                } else {
                    IPlayer.Request.Drm(
                        userId = sharedPreferences.userId(),
                        merchant = stream.merchant,
                        sessionId = stream.sessionId,
                        type = IPlayer.DrmType.CAST_LAB,
                        drmKey = IPlayer.Request.Drm.DrmKey(
                            drmCallback = playerDrmCallback,
                            keyOffId = drmKey
                        ),
                        enableDrmOffline = sharedPreferences.enableCacheDrm()
                    )
                }
            },
            lowLatency = details?.lowlatency ?: false,
            isLive = true,
            clearRequestWhenOnStop = false,
            autoStreaming = IPlayer.Request.AutoStreaming(
                resolutionMaxHeight = details?.resolution?.maxHeight ?: 0,
                resolutionMaxWidth = details?.resolution?.maxWidth ?: 0,
            ),
            headerRequestProperties = headers,
            audioNameDynamic = details?.audioConfigNames ?: emptyList(),
            audioTrackMappingNameType = details?.audioNameMode?.playerAudioModeName() ?: IPlayer.AudioTrackMappingNameType.DEFAULT
        )
    }

    /**
     * Prepare player for live tv schedule
     */
    fun prepareSchedulePlayer(
        playerConfig: PlayerConfig?= null,
        details: TvChannelDetail?,
        tvSchedule: TvSchedule?,
        data: Stream,
        sportInteractiveViewModel: SportInteractiveViewModel? = null,
        headers: ArrayMap<String, String>
    ) {
        Timber.tag("tam-sport").d("prepareSchedulePlayer")
        playerConfig?.let { this.playerConfig = it }
        screenType = PlayerHandler.ScreenType.Live
        this.isPlayingVipTrailer = false
        binding.playerUI.apply {
            prepareDataPlayerControl(playerConfig = playerConfig, tvChannelDetail = details, dataPlayerControl = playerData)
            updatePlayingVipTrailer(isPlayingVipTrailer = false)
            updateOverlayLogo(data.overlayLogo)
            clearAgeRestrictionData()
        }
        binding.playerUI.apply {
        }
        Timber.tag("LiveTVPlayerFragment").w("prepareSchedulePlayer url ${data.url}")

        onShowThumb(isShow = false)
        hideAlertLimitCcu()

        player?.run {
            playerHandler?.bindAndPlay(
                request = PlayerHandler.Request(
                    isDrm = data.isTimeShiftDrm,
                    merchant = data.merchant,
                    screenType = PlayerHandler.ScreenType.Live,
                    streamId = tvSchedule?.id ?: ""
                ),
                player = this,
                playerRequest = IPlayer.Request(
                    id = tvSchedule?.id ?: "",
                    streamId = if (tvSchedule?.bitrates.isNullOrEmpty()) {
                        ""
                    } else {
                        tvSchedule?.bitrates?.last()?.id ?: ""
                    },
                    url = if (data.isTimeShiftDrm)
                        IPlayer.Request.Url(
                            dolbyVisionUrl = data.urlDashDrmDolbyVision,
                            h265HDR10PlusUrl = data.urlDashDrmH265Hdr10Plus,
                            h265HDR10Url = data.urlDashDrmH265Hdr,
                            h265HlgUrl = data.urlDashDrmH265Hlg,
                            h265Url = data.urlDashDrmH265,
                            av1Url = data.urlDashDrmAv1,
                            vp9Url = data.urlDashDrmVp9,
                            url = data.timeShiftDash
                        )
                    else
                        IPlayer.Request.Url(
                            dolbyVisionUrl = data.urlDashDolbyVision,
                            h265HDR10PlusUrl = data.urlDashH265Hdr10Plus,
                            h265HDR10Url = data.urlDashH265Hdr,
                            h265HlgUrl = data.urlDashH265Hlg,
                            h265Url = data.urlDashH265,
                            av1Url = data.urlDashAv1,
                            vp9Url = data.urlDashVp9,
                            url = data.url
                        ),
                    drm = run {
                        if (data.merchant == "fptplay"){
                            IPlayer.Request.Drm(userId = sharedPreferences.userId(),
                                merchant = data.merchant,
                                appId = "sigma",
                                sessionId = data.sessionId,
                                stagingMode = drmStagingMode,
                                type = IPlayer.DrmType.SIGMA)
                        }else{
                            IPlayer.Request.Drm(userId = sharedPreferences.userId(),
                                merchant = data.merchant,
                                sessionId = data.sessionId,
                                type = IPlayer.DrmType.CAST_LAB)
                        }
                    },
                    clearRequestWhenOnStop = false,
                    headerRequestProperties = headers,
                ),
                stream = data
            )
        }
    }

    /**
     * Prepare player for premiere
     */
    fun preparePlayerPremiere(
        playerConfig: PlayerConfig? = null,
        eventId: String,
        isPremiere: Boolean,
        autoProfile: String,
        details: com.xhbadxx.projects.module.domain.entity.fplay.premier.Details?,
        data: Stream,
        onEvents: PlayerHandler.OnEvents,
        onCastSessionEvents: PlayerHandler.OnCastSessionEvents,
        processShowIp: (Long, Int, Int) -> Unit = { _, _, _ -> },
        processReloadStream: () -> Unit = {},
        processShowMatrixIp: () -> Unit = {},
        headers: ArrayMap<String, String>,
        drmKey: ByteArray?
    ) {
        //Preload end event image for case black screen when change from end event to backdrop image
        GlideApp.with(context)
            .load(lowQualityImage(MainApplication.INSTANCE.appConfig.bgEndEvent))
            .preload(Constants.PLAYER_HOVER_IMAGE_WIDTH, Constants.PLAYER_HOVER_IMAGE_HEIGHT)
        Timber.tag("tam-sport").d("preparePlayerPremiere")
        playerConfig?.let { this.playerConfig = it }
        screenType = PlayerHandler.ScreenType.Premiere
        linkToShare = details?.websiteUrl ?: ""
        this.isPlayingVipTrailer = false
        updatePremierePlayerData(details = details)

        if(details?.isDrm == true) {
            screenProtector?.setProtect(true)
            screenProtector?.enableScreenProtector()
        } else {
            screenProtector?.setProtect(false)
            screenProtector?.disableScreenProtector()
        }

        onShowThumb(isShow = false)
        hideAlertLimitCcu()

        binding.playerUI.apply {
            updatePlayingVipTrailer(isPlayingVipTrailer = false)
            updateOverlayLogo(url = data.overlayLogo)
            prepareDataPlayerControl(playerConfig = playerConfig, detailsPremiere = details, dataPlayerControl = playerData)
        }

        player?.run {
            playerHandler?.onEvents = onEvents
            playerHandler?.onCastSessionEvents = onCastSessionEvents
            playerHandler?.bindAndPlay(
                request = PlayerHandler.Request(
                    isDrm = details?.isDrm ?: false,
                    provider = details?.sourceProvider ?: "",
                    merchant = data.merchant,
                    screenType = if (isPremiere) PlayerHandler.ScreenType.Vod else PlayerHandler.ScreenType.Live,
                    streamId = details?.id ?: "",
                    type = if (isPremiere) "event" else "eventtv",
                    eventId = eventId,
                    bitrateId = autoProfile,
                    isFullHd = Utils.convertStringToInt(sharedPreferences.supportFullHD(), defaultValue = 1),
                    pingEnable = data.pingEnable,
                    pingSession = data.pingSession,
                    pingEncrypt = data.pingEnc,
                    codeLimitCcu = codeLimitCcu,
                    processShowIp = processShowIp,
                    processShowMatrixIp = processShowMatrixIp,
                    processReloadStream = processReloadStream
                ),
                player = this,
                playerRequest = IPlayer.Request(
                    id = details?.id ?: "",
                    streamId = autoProfile,
                    url = if (details?.isDrm == true)
                        IPlayer.Request.Url(
                            dolbyVisionUrl = data.urlDashDrmDolbyVision,
                            h265HDR10PlusUrl = data.urlDashDrmH265Hdr10Plus,
                            h265HDR10Url = data.urlDashDrmH265Hdr,
                            h265HlgUrl = data.urlDashDrmH265Hlg,
                            h265Url = data.urlDashDrmH265,
                            av1Url = data.urlDashDrmAv1,
                            vp9Url = data.urlDashDrmVp9,
                            url = data.urlDash.ifEmpty {
                                if (data.urlDashNoDrm.isNotEmpty())
                                    data.urlDashNoDrm
                                else if (data.urlSub.isNotEmpty())
                                    data.urlSub
                                else
                                    data.url
                            }
                        )
                    else
                        IPlayer.Request.Url(
                            dolbyVisionUrl = data.urlDashDolbyVision,
                            h265HDR10PlusUrl = data.urlDashH265Hdr10Plus,
                            h265HDR10Url = data.urlDashH265Hdr,
                            h265HlgUrl = data.urlDashH265Hlg,
                            h265Url = data.urlDashH265,
                            av1Url = data.urlDashAv1,
                            vp9Url = data.urlDashVp9,
                            url = if(data.urlDashNoDrm.isNotEmpty())
                                data.urlDashNoDrm
                            else if (data.urlSub.isNotEmpty())
                                data.urlSub
                            else
                                data.url
                        ),
                    drm = run {
                        if (data.merchant == "fptplay") {
                            IPlayer.Request.Drm(
                                userId = sharedPreferences.userId(),
                                merchant = data.merchant,
                                appId = "sigma",
                                sessionId = data.sessionId,
                                type = IPlayer.DrmType.SIGMA,
//                                stagingMode = if (isPremiere) false else Util.isEnvironmentStaging(),
                                stagingMode = drmStagingMode,
                                drmKey = IPlayer.Request.Drm.DrmKey(
                                    drmCallback = playerDrmCallback,
                                    keyOffId = drmKey
                                ),
                                enableDrmOffline = sharedPreferences.enableCacheDrm()
                            )
                        } else {
                            IPlayer.Request.Drm(
                                userId = sharedPreferences.userId(),
                                merchant = data.merchant,
                                sessionId = data.sessionId,
                                type = IPlayer.DrmType.CAST_LAB,
                                drmKey = IPlayer.Request.Drm.DrmKey(
                                    drmCallback = playerDrmCallback,
                                    keyOffId = drmKey
                                ),
                                enableDrmOffline = sharedPreferences.enableCacheDrm()
                            )
                        }
                    },
                    lowLatency = details?.lowlatency ?: false,
                    isLive = details?.isPremier == "0",
                    audioNameDynamic = details?.multiAudio,
                    clearRequestWhenOnStop = false,
                    autoStreaming = IPlayer.Request.AutoStreaming(
                        resolutionMaxHeight = details?.resolution?.maxHeight ?: 0,
                        resolutionMaxWidth = details?.resolution?.maxWidth ?: 0,
                    ),
                    headerRequestProperties = headers
                ),
                stream = data
            )
        }
    }

    /**
     * Prepare player for premiere
     */
    fun preparePlayerEventTvPreview(
        playerConfig: PlayerConfig? = null,
        eventId: String,
        isPremiere: Boolean,
        autoProfile: String,
        details: com.xhbadxx.projects.module.domain.entity.fplay.premier.Details?,
        info: EventTvPreviewPlayerInfo,
        onEvents: PlayerHandler.OnEvents,
        onCastSessionEvents: PlayerHandler.OnCastSessionEvents,
        processShowIp: (Long, Int, Int) -> Unit = { _, _, _ -> },
        processReloadStream: () -> Unit = {},
        processShowMatrixIp: () -> Unit = {},
        headers: ArrayMap<String, String>,
        drmKey: ByteArray?
    ) {
        //Preload end event image for case black screen when change from end event to backdrop image
        GlideApp.with(context)
            .load(lowQualityImage(MainApplication.INSTANCE.appConfig.bgEndEvent))
            .preload(Constants.PLAYER_HOVER_IMAGE_WIDTH, Constants.PLAYER_HOVER_IMAGE_HEIGHT)
        Timber.tag("tam-sport").d("preparePlayerPremiere")
        playerConfig?.let { this.playerConfig = it }
        screenType = PlayerHandler.ScreenType.Premiere
        linkToShare = details?.websiteUrl ?: ""
        this.isPlayingVipTrailer = false
        updatePremierePlayerData(details = details)

        if(details?.isDrm == true) {
            screenProtector?.setProtect(true)
            screenProtector?.enableScreenProtector()
        } else {
            screenProtector?.setProtect(false)
            screenProtector?.disableScreenProtector()
        }

        onShowThumb(isShow = false)
        hideAlertLimitCcu()

        binding.playerUI.apply {
            updatePlayingVipTrailer(isPlayingVipTrailer = false)
            updateOverlayLogo(url = info.overlayLogo)
            prepareDataPlayerControl(playerConfig = playerConfig, detailsPremiere = details, dataPlayerControl = playerData)
        }

        player?.run {
            playerHandler?.onEvents = onEvents
            playerHandler?.onCastSessionEvents = onCastSessionEvents
            playerHandler?.bindAndPlay(
                request = PlayerHandler.Request(
                    isDrm = details?.isDrm ?: false,
                    provider = details?.sourceProvider ?: "",
                    merchant = info.merchant,
                    screenType = if (isPremiere) PlayerHandler.ScreenType.Vod else PlayerHandler.ScreenType.Live,
                    streamId = details?.id ?: "",
                    type = if (isPremiere) "event" else "eventtv",
                    eventId = eventId,
                    bitrateId = autoProfile,
                    isFullHd = Utils.convertStringToInt(sharedPreferences.supportFullHD(), defaultValue = 1),
                    pingEnable = info.pingEnable,
                    pingSession = info.pingSession,
                    pingEncrypt = info.pingEnc,
                    processShowIp = processShowIp,
                    processShowMatrixIp = processShowMatrixIp,
                    processReloadStream = processReloadStream
                ),
                player = this,
                playerRequest = IPlayer.Request(
                    id = details?.id ?: "",
                    streamId = autoProfile,
                    url = IPlayer.Request.Url(url = info.url, h265Url = info.urlH265),
                    drm = run {
                        if (info.merchant == "fptplay") {
                            IPlayer.Request.Drm(
                                userId = sharedPreferences.userId(),
                                merchant = info.merchant,
                                appId = "sigma",
                                sessionId = info.session,
                                type = IPlayer.DrmType.SIGMA,
                                stagingMode = drmStagingMode,
                                drmKey = IPlayer.Request.Drm.DrmKey(
                                    drmCallback = playerDrmCallback,
                                    keyOffId = drmKey
                                ),
                                enableDrmOffline = false
                            )
                        } else {
                            IPlayer.Request.Drm(
                                userId = sharedPreferences.userId(),
                                merchant = info.merchant,
                                sessionId = info.session,
                                type = IPlayer.DrmType.CAST_LAB,
                                drmKey = IPlayer.Request.Drm.DrmKey(
                                    drmCallback = playerDrmCallback,
                                    keyOffId = drmKey
                                ),
                                enableDrmOffline = false
                            )
                        }
                    },
                    lowLatency = details?.lowlatency ?: false,
                    isLive = details?.isPremier == "0",
                    audioNameDynamic = details?.multiAudio,
                    clearRequestWhenOnStop = false,
                    autoStreaming = IPlayer.Request.AutoStreaming(
                        resolutionMaxHeight = details?.resolution?.maxHeight ?: 0,
                        resolutionMaxWidth = details?.resolution?.maxWidth ?: 0,
                    ),
                    headerRequestProperties = headers
                ),
                stream = Stream(url = info.url)
            )
        }
    }

    //region Prepare Data -> Pairing Control
    fun preparePlayerVODCast(
        playerConfig: PlayerConfig?= null,
        userProfile: UserProfile,
        details: Details?,
        episode: Details.Episode,
        data: Stream?,
        delayToPlay: Boolean,
        isPlayingTrailer: Boolean,
        playlistName: String,
        isResetPlayingPosition: Boolean,
        onEvents: PlayerHandler.OnEvents,
        processShowIp: (Long, Int, Int) -> Unit = { _, _, _ -> },
        processReloadStream: () -> Unit = {},
        processShowMatrixIp: () -> Unit = {}
    ) {
        playerConfig?.let { this.playerConfig = it }
        if (isResetPlayingPosition) seek(0)
        screenType = PlayerHandler.ScreenType.Vod
        linkToShare = details?.blockContent?.webUrl ?:""
        this.isPlayingVipTrailer = false
        updateVodPlayerData(userProfile = userProfile,  details = details, episode = episode, isPlayingTrailer = isPlayingTrailer, playlistName = playlistName)

        // Skip intro credits
        if (data != null) {
            updateSkipIntroCreditsData(stream = data)
        }

        //onShowThumb(isShow = false)

        binding.playerUI.apply {
            updatePlayingVipTrailer(isPlayingVipTrailer = false)
            updateCurrentVODData(episode)
            updateOverlayLogo(details?.blockContent?.overlayLogo)
            prepareThumb(episode)
            prepareDataPlayerControl(playerConfig = playerConfig, details = details, dataPlayerControl = playerData)
        }
    }

    fun preparePlayerLiveCast(
        playerConfig: PlayerConfig?= null,
        details: TvChannelDetail?,
        autoProfile: String,
        data: Stream,
        isMulticast: Boolean,
        onEvents: PlayerHandler.OnEvents,
        processShowIp: (Long, Int, Int) -> Unit = { _, _, _ -> },
        processReloadStream: () -> Unit = {},
        processShowMatrixIp: () -> Unit = {}
    ) {
        playerConfig?.let { this.playerConfig = it }
        this.tvChannelDetail = details
        this.isPlayingVipTrailer = false
        screenType = PlayerHandler.ScreenType.Live
        linkToShare = details?.websiteUrl ?:""

        updateLivePlayerData(tvChannelDetail = details)
        // Skip intro credits
        updateSkipIntroCreditsData(stream = data)

        //onShowThumb(isShow = false)

        binding.playerUI.apply {
            prepareDataPlayerControl(playerConfig = playerConfig, tvChannelDetail = details, dataPlayerControl = playerData)
            updatePlayingVipTrailer(isPlayingVipTrailer = false)
            updateOverlayLogo(data.overlayLogo)
        }

        // Picture in Picture
        triggerUpdatePictureInPictureParams()
    }

    fun preparePlayerPremiereCast(
        playerConfig: PlayerConfig?= null,
        isPremiere: Boolean,
        autoProfile: String,
        details: com.xhbadxx.projects.module.domain.entity.fplay.premier.Details?,
        data: Stream,
        onEvents: PlayerHandler.OnEvents,
        processShowIp: (Long, Int, Int) -> Unit = { _, _, _ -> },
        processReloadStream: () -> Unit = {},
        processShowMatrixIp: () -> Unit = {}
    ) {
        playerConfig?.let { this.playerConfig = it }
        screenType = PlayerHandler.ScreenType.Premiere
        linkToShare = details?.websiteUrl ?: ""
        this.isPlayingVipTrailer = false
        updatePremierePlayerData(details = details)

        //onShowThumb(isShow = false)

        binding.playerUI.apply {
            updatePlayingVipTrailer(isPlayingVipTrailer = false)
            prepareDataPlayerControl(playerConfig = playerConfig, detailsPremiere = details, dataPlayerControl = playerData)
        }
    }
    //endregion

    private fun initPlayerTrackChangeListener() {
        (player?.internalPlayer() as? ExoPlayer)?.run {
            removeListener(playerTrackChangeEvents)
            addListener(playerTrackChangeEvents)

            removeAnalyticsListener(playerAnalyticsEvents)
            addAnalyticsListener(playerAnalyticsEvents)
        }
    }

    fun sendTrackingPingPlayCcu(actionType: CommonInfor.PingStreamActionType, message: String = "", showFingerprint: Boolean = false, type: String = "teletext"){
        playerHandler?.onEvents?.sendTrackingPingPlayCcu(actionType = actionType, message = message, showFingerprint = showFingerprint, type = type)
    }

    fun seek(duration: Long) {
        when (PlayerUtils.getPlayingType()) {
            PlayingType.Local -> {
                player?.seek(duration = duration)
            }
            PlayingType.Cast -> {
                pairingConnection.sendEventSeek(id = details?.blockContent?.id?:"", refId = details?.blockContent?.refId?:"", indexChapter = currentEpisode?.id ?:"", timeWatched = "${(duration / 1000L)}")
            }
        }

        // Game Emoji
        gameEmojiVodController?.onActionSeek()
        playerUIListener?.onActionSeek(duration)
    }

    fun setResizeMode(resizeMode: Int, position: Int) {
        curResolution = if (position == 0) "-1" else resizeMode.toString()
        binding.epvPlayer.setResizeMode(resizeMode)
        videoResizeMode = resizeMode
        updateSubtitleView(force = true)
        updateSituationWarningView()
    }

    fun changeBitrate(playerRequestId: String, key: String, name: String = ""): Boolean {
        val changeBitrateResult = (player as? ExoPlayerProxy)?.selectBitrateByName(playerRequestId = playerRequestId, key = key) ?: true
        viewLifecycleOwner?.lifecycleScope?.launch {
            binding.playerUI.updatePlayerVideoQualityName(name)
        }

        return changeBitrateResult
    }

    fun changeTrack(track: PlayerControlView.Data.Track) {
        if(track.isSubtitle()) {
            this.playerData.tracks?.filter { it.isSubtitle() }?.forEach { it.isSelected = (it.id == track.id) }
            currentSelectedSubtitleTrack = track
        } else {
            this.playerData.tracks?.filter { it.type == track.type }?.forEach { it.isSelected = (it.name == track.name) }
            currentSelectedAudioTrack = track
        }
        try {
            (player as? ExoPlayerProxy)?.selectSubtitleTrack(data = track)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun onShareLink(url: String) {
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, url)
            type = "text/plain"
        }
        val shareIntent = Intent.createChooser(sendIntent, context.getString(R.string.share))
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            shareIntent.putExtra (Intent.EXTRA_REPLACING, true)
            shareIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
        }
        context?.startActivity(shareIntent)
    }
    fun onShowThumb(isShow: Boolean, url: String = "", detailVod: Details? = null, detailPremiere: com.xhbadxx.projects.module.domain.entity.fplay.premier.Details? = null) {
        when (PlayerUtils.getPlayingType()) {
            PlayingType.Local -> {
                binding.playerUI.apply {
                    onShowThumb(isShow = isShow)
                    if (isShow) {
                        prepareDataPlayerControl(details = detailVod, detailsPremiere = detailPremiere, dataPlayerControl = playerData)
                    }
                }
                if (isShow) {
                    if (url.isNotBlank()) {
                        binding.ivImageHover.show()
                        GlideApp.with(binding.root.context)
                            .load(url)
                            .thumbnail(GlideApp.with(binding.root.context).load(
                                lowQualityImage(url)
                            ))
                            .override(Constants.PLAYER_HOVER_IMAGE_WIDTH, Constants.PLAYER_HOVER_IMAGE_HEIGHT)
                            .into(binding.ivImageHover)
                    }
                } else {
                    binding.ivImageHover.hide()
                }
            }
            PlayingType.Cast -> {
                if (isShow) {
                    if (url.isNotBlank()) {
                        binding.ivImageHover.show()
                        GlideApp.with(binding.root.context)
                            .load(url)
                            .override(Constants.PLAYER_HOVER_IMAGE_WIDTH, Constants.PLAYER_HOVER_IMAGE_HEIGHT)
                            .into(binding.ivImageHover)
                    }
                } else {
                    binding.ivImageHover.hide()
                }
            }
        }

    }

    fun unlockControl() {
        binding.playerUI.unlockControl()
    }

    private fun shouldShowAudioBgImage(requestShow: Boolean, isAudioMode: Boolean, viewShow: ImageView?): Boolean {
        return requestShow && isAudioMode && (viewShow != null)
    }

    fun showBackgroundAudioOverlay(isShow: Boolean) {
        if(shouldShowAudioBgImage(
            requestShow = isShow,
            isAudioMode = playerConfig.isAudioMode,
            viewShow = binding.epvPlayer.posterOverlay()
        )) {
            if (playerConfig.backgroundAudioOverlay.isBlank()){
                ImageProxy.loadLocal(
                    context = binding.root.context,
                    data = R.drawable.placeholder_player_audio_bg_image,
                    width = Constants.PLAYER_HOVER_IMAGE_WIDTH,
                    height = Constants.PLAYER_HOVER_IMAGE_HEIGHT,
                    errorDrawableId = R.drawable.placeholder_player_audio_bg_image,
                    target = binding.epvPlayer.posterOverlay()
                )
            } else {
                if(isPlayOffline()) {
                    ImageProxy.loadLocal(
                        context = binding.root.context,
                        data = playerConfig.backgroundAudioOverlay,
                        width = Constants.PLAYER_HOVER_IMAGE_WIDTH,
                        height = Constants.PLAYER_HOVER_IMAGE_HEIGHT,
                        errorDrawableId = R.drawable.placeholder_player_audio_bg_image,
                        target = binding.epvPlayer.posterOverlay()
                    )
                } else {
                    ImageProxy.load(
                        context = binding.root.context,
                        url = playerConfig.backgroundAudioOverlay,
                        width = Constants.PLAYER_HOVER_IMAGE_WIDTH,
                        height = Constants.PLAYER_HOVER_IMAGE_HEIGHT,
                        errorDrawableId = R.drawable.placeholder_player_audio_bg_image,
                        target = binding.epvPlayer.posterOverlay()
                    )
                }
            }

            binding.epvPlayer.posterOverlay().show()
        } else {
            binding.epvPlayer.posterOverlay().hide()
        }
    }

    //region Skip intro credits
    private fun updateSkipIntroCreditsData(stream: Stream) {
        currentIntroFromMillis = stream.timeStartIntro * 1000
        currentStartContentMillis = stream.timeStartContent * 1000
        currentEndContentMillis = stream.timeEndContent * 1000
        binding.playerUI.updateSkipIntroData(currentIntroFromMillis, currentStartContentMillis, currentEndContentMillis)
    }

    private fun updateSkipIntroCreditsData(timeStartIntro : Int, timeStartContent : Int, timeEndContent : Int) {
        currentIntroFromMillis = timeStartIntro * 1000
        currentStartContentMillis = timeStartContent * 1000
        currentEndContentMillis = timeEndContent * 1000
        binding.playerUI.updateSkipIntroData(currentIntroFromMillis, currentStartContentMillis, currentEndContentMillis)
    }

    private fun resetSkipIntroCreditData() {
        currentIntroFromMillis = 0
        currentStartContentMillis = 0
        currentEndContentMillis = 0
    }

    private fun initCheckSkipIntroAndCredits() {
        if (currentStartContentMillis > 0 || currentEndContentMillis > 0) {
            // init
            initCheckSkipIntro()
            initCheckSkipCredit()
        }
    }

    private fun initCheckSkipIntro() {
        if (handlerCheckSkipIntro == null) {
            Looper.getMainLooper()?.run {
                handlerCheckSkipIntro = Handler(this)
            }
        }
        handlerCheckSkipIntro?.run {
            post(runnableCheckSkipIntro)
        }
    }

    private fun initCheckSkipCredit() {
        binding.playerUI.showNextVideoRecommendation(isShow = false)
        if (handlerCheckSkipCredit == null) {
            Looper.getMainLooper()?.run {
                handlerCheckSkipCredit = Handler(this)
            }
        }
        handlerCheckSkipCredit?.run {
            post(runnableCheckSkipCredit)
        }
    }

    //Set and Get
    fun setShowViewSkipCredit(isShow: Boolean) {
        if (isShow) {
            if (binding.playerUI.isShowNextVideoRecommendation() != null) {
                if (fragmentActivity !is AirlineActivity) {
                    showNextVideoRecommendation(data = binding.playerUI.isShowNextVideoRecommendation()!!)
                }
                return
            } else if (binding.playerUI.canAutoNextEpisode()) {
                binding.playerUI.showSkipCredit(isShow = true)
                return
            }
        }
        binding.playerUI.showSkipCredit(isShow = false)
    }

    private fun showNextVideoRecommendation(data : Details.RelatedVod) {
        binding.playerUI.updateTextWatchNowRecommendation(text = context.getString(R.string.view_now))
        binding.playerUI.updateTextPlayTrailerRecommendation(text = context.getString(R.string.play_trailer))
        binding.playerUI.showNextVideoRecommendation(isShow = true, related = data)
        startCountDownTimerSkipCredits(isNextEpisode = false, related = data)
    }


    private fun runHandlerSkipCredits() {
        Logger.d("$TAG runHandlerSkipCredits")
        val timeToCheckSkipCredits = currentEndContentMillis - currentDuration()
        handlerCheckSkipCredit?.run {
            postDelayed(runnableCheckSkipCredit, if (timeToCheckSkipCredits > 0) timeToCheckSkipCredits else 100)
        }
    }

    private fun startCountDownTimerSkipCredits(isNextEpisode: Boolean, related: Details.RelatedVod? = null) {
        if (countDownTimerSkipCredits == null) {
            countDownTimerSkipCredits = object : CountDownTimer(if (isNextEpisode) 5000 else 5000, 100) {
                    @SuppressLint("SetTextI18n")
                    override fun onTick(millis: Long) {
                        isCountDownStopped = false
                        if (isNextEpisode) {
                            binding.playerUI.updateTextSkipCredit(progress = (((5000 - millis) / 5000.0) * 100) .toInt())
                        } else {
                            related?.run {
                                if (this.trailer == 1) {
                                    binding.playerUI.showRecommendButtonTrailer(isShow = true)
                                    binding.playerUI.updateTextPlayTrailerRecommendation(text = String.format("%s (%s)", context.getString(R.string.play_trailer), "${millis / 1000 + 1}s"))
                                } else {
                                    binding.playerUI.showRecommendButtonTrailer(isShow = false)
                                    binding.playerUI.updateTextWatchNowRecommendation(text = String.format("%s (%s)", context.getString(R.string.view_now), "${millis / 1000 + 1}s"))
                                }
                            }
                        }
                    }

                    override fun onFinish() {
                        isCountDownStopped = true
                        if (isNextEpisode) {
                            binding.playerUI.updateTextSkipCredit(progress = 100)
                            nextEpisode(isAuto = true, isSendEventToRemote = false)
                        } else {
                            binding.playerUI.showNextVideoRecommendation(isShow = false)
                            related?.run {
                                if (this.trailer == 1) {
                                    binding.playerUI.showRecommendButtonTrailer(isShow = true)
                                    binding.playerUI.updateTextPlayTrailerRecommendation(text = String.format("%s (%s)", context.getString(R.string.play_trailer), "0s"))
                                    playerUIListener?.onRecommendPlayTrailer(related = related)

                                } else {
                                    binding.playerUI.showRecommendButtonTrailer(isShow = false)
                                    binding.playerUI.updateTextWatchNowRecommendation(text = String.format("%s (%s)", context.getString(R.string.view_now), "0s"))
                                    playerUIListener?.onRecommendWatchNow(related = related)
                                }
                            }
                        }
                        stopCountDownTimerSkipCredits()
                        removeHandlerAndCountDownSkipCredits()
                    }
                }
            countDownTimerSkipCredits?.start()
            hideAndStopBuyPackageGuideInPlayer()
        }
    }

    // Handler
    private fun startSkipCredits() {
        //remove handler and countdown timer
        removeHandlerAndCountDownSkipCredits()
        //check have skip credits
        if (currentEndContentMillis > 0) {
            if (currentDuration() < totalDuration()) {
                if (currentDuration() > currentEndContentMillis) {
                    //isShowSkipCredits -> show once when have skip credits
                    setShowViewSkipCredit(true)
                    startCountDownTimerSkipCredits(isNextEpisode = true)
                } else {
                    setShowViewSkipCredit(false)
                    runHandlerSkipCredits()
                }
            } else { // Sometime, player callback currentDuration > totalDuration, so we need to start countdown again in order to show next video recommendation. Not apply next episode
                if ((totalDuration() != 0L) && binding.playerUI.isShowNextVideoRecommendation() != null) {
                    if (isCountDownStopped) {
                        if (fragmentActivity !is AirlineActivity) {
                            showNextVideoRecommendation(data = binding.playerUI.isShowNextVideoRecommendation()!!)
                        }
                    }
                }
            }
        }
    }

    private fun removeHandlerAndCountDownSkipCredits() {
        stopCountDownTimerSkipCredits()
        setShowViewSkipCredit(isShow = false)
        handlerCheckSkipCredit?.removeCallbacks(runnableCheckSkipCredit)
    }

    private fun stopCountDownTimerSkipCredits() {
        if (countDownTimerSkipCredits != null) {
            isCountDownStopped = true
            binding.playerUI.updateTextSkipCredit(progress = 0)
            countDownTimerSkipCredits?.cancel()
            countDownTimerSkipCredits = null
        }
    }


    //endregion

    //region Handle Skip Intro
    private fun showAndStartSkipIntro() {
        if (currentIntroFromMillis != 0) { // case skip intro not first
            Handler(Looper.getMainLooper()).postDelayed({
                runHandlerSkipIntro()
                binding.playerUI.checkShowSkipIntro()
            }, currentIntroFromMillis - currentDuration())
        }
        startSkipIntro()
    }

    private fun startSkipIntro() {
        //remove handler and countdown timer
        removeHandlerAndCountDownSkipIntro()
        //check have skip intro
        if (currentDuration() in currentIntroFromMillis until currentStartContentMillis) {
            skipIntroIsRunning = true
            runHandlerSkipIntro()
            hideAndStopBuyPackageGuideInPlayer()
        }
        binding.playerUI.checkShowSkipIntro()
    }

    private fun removeHandlerAndCountDownSkipIntro() {
        handlerCheckSkipIntro?.removeCallbacks(runnableCheckSkipIntro)
        skipIntroIsRunning = false
    }

    private fun runHandlerSkipIntro() {
        val timeToCheckSkipIntro = currentStartContentMillis - currentDuration()
        handlerCheckSkipIntro?.run {
            postDelayed(runnableCheckSkipIntro, if (timeToCheckSkipIntro > 0) timeToCheckSkipIntro else 1000)
        }
    }
    //endregion

    //region Prepare Player Data
    private fun updateLivePlayerData(tvChannelDetail: TvChannelDetail?) {
        tvChannelDetail?.let { tvChannelDetail ->
//            playerData.reset()
            playerData.apply {
                isLive = true
                title = tvChannelDetail.name
                des = tvChannelDetail.description
            }
        } ?: playerData.reset()
    }

    private fun updateVodPlayerData(userProfile: UserProfile, details: Details?, episode: Details.Episode, isPlayingTrailer: Boolean, playlistName: String) {
        this.isPlayingTrailer = isPlayingTrailer
        this.currentEpisode = episode
        this.details = details
        details?.let { details ->
            if (episode.isTrailer == 1) {
                playerData.apply {
                    playerData.reset()
                    title = if (details.blockContent.titleVietnam.isEmpty()) details.blockContent.titleEnglish else details.blockContent.titleVietnam
                    des = if (episode.titleVietnam.isEmpty()) episode.titleEnglish ?:"" else episode.titleVietnam
                }
            } else {
                playerData.apply {
                    isLive = false
                    title = playlistName.ifEmpty {
                        details.blockContent.titleVietnam.ifEmpty { details.blockContent.titleEnglish }
                    }
                    des = kotlin.run {
                        if (playlistName.isNotEmpty()) {
                            details.blockContent.titleVietnam.ifEmpty { details.blockContent.titleEnglish }
                        } else {
                            episode.titleVietnam.ifEmpty { episode.titleEnglish ?: "" }
                        }
                    }
                    if (details.blockEpisode.episodes.size > 1){
                        episodeIndex = userProfile.getEpisodeIndex()
                        if(details.blockEpisode.episodes.firstOrNull()?.isItemOfPlaylist == true) {
                            episodeIndex = max(0, details.blockEpisode.episodes.getIndexOf(episode))
                        }
                        episodes = details.blockEpisode.episodes.map {
                            PlayerControlView.Data.Episode(
                                id = it.id,
                                name = it.titleVietnam,
                                isVip = it.isVip
                            )
                        }
                        episodeGroups = details.blockEpisode.episodeGroups.map {
                            PlayerControlView.Data.EpisodeGroup(
                                id = it.id,
                                name = it.name,
                                indexStart = it.indexFirst,
                                indexLast = it.indexLast
                            )
                        }
                    }
                }
            }
        } ?: playerData.reset()

        // Picture in Picture
        triggerUpdatePictureInPictureParams()
    }

    private fun updatePremierePlayerData(details: com.xhbadxx.projects.module.domain.entity.fplay.premier.Details?) {
        this.detailsPremiere = details
        details?.let { details ->
            playerData.apply {
                isLive = true
                title = details.title
                des = details.description
            }
        } ?: playerData.reset()

        // Picture in Picture
        triggerUpdatePictureInPictureParams()
    }


    private fun getNextEpisode() : Pair<Int, Details.Episode>? {
        val listEpisode = details?.blockEpisode?.episodes ?: listOf()
        val index = listEpisode.getIndexOf(currentEpisode)

        if ((index != -1) && (index + 1 in listEpisode.indices)){
            return Pair(index + 1, listEpisode[index + 1])
        }
        return null
    }

    private fun getNextEpisodeOffline() : Pair<Int, VideoTaskItem>? {
        return binding.playerUI.getNextEpisodeOffline()
    }

    private fun getPreviousEpisode() : Pair<Int, Details.Episode>? {
        val listEpisode = details?.blockEpisode?.episodes ?: listOf()
        val index = listEpisode.getIndexOf(currentEpisode)

        if ((index != -1) && (index - 1 in listEpisode.indices)){
            return Pair(index - 1, listEpisode[index - 1])
        }
        return null
    }

    private fun getExactEpisodeOffline(videoTaskItem: VideoTaskItem?) : Pair<Int, VideoTaskItem>? {
        return binding.playerUI.getExactEpisodeOffline(videoTaskItem)
    }

    private fun getExactEpisode(episode: Details.Episode) : Pair<Int, Details.Episode>? {
        val listEpisode = details?.blockEpisode?.episodes ?: listOf()
        val index = listEpisode.getIndexOf(episode)

        if ((index != -1) && (index in listEpisode.indices)){
            return Pair(index, episode)
        }
        return null
    }

    private fun getPreviousEpisodeOffline() : Pair<Int, VideoTaskItem>? {
        return binding.playerUI.getPreviousEpisodeOffline()
    }
    //endregion


    private fun previousEpisode(isAuto: Boolean) {
        if (screenType == PlayerHandler.ScreenType.Vod) {
            if (isVodOffline) {
                getPreviousEpisodeOffline()?.run {
                    playerUIListener?.onPreviousOffline(first, second, isAuto)
                }
            }
            else {
                getPreviousEpisode()?.run {
                    playerUIListener?.onPrevious(first, second, isAuto)
                    // Game Emoji
                    gameEmojiVodController?.onActionPreviousEpisode()

                }
            }
        }
    }

    private fun nextEpisode(isAuto: Boolean, isSendEventToRemote: Boolean) {
        if (screenType == PlayerHandler.ScreenType.Vod) {
            if (!isPlayingTrailer) {
                if (isVodOffline) {
                    getNextEpisodeOffline()?.run {
                        playerUIListener?.onNextOffline(first, second, isAuto)
                    }
                } else {
                    getNextEpisode()?.run {
                        playerUIListener?.onNext(first, second, isAuto, isSendEventToRemote)
                        // Game Emoji
                        gameEmojiVodController?.onActionNextEpisode()

                    }
                }
            }
        }
    }

    private fun exactEpisode(offlineItem: VideoTaskItem? = null, episode: Details.Episode? = null, isSendEventToRemote: Boolean) {
        if (screenType == PlayerHandler.ScreenType.Vod) {
            if (!isPlayingTrailer) {
                if (isVodOffline) {
                    getExactEpisodeOffline(offlineItem)?.run {
                        playerUIListener?.onPlayItemOffline(second)
                    }
                } else {
                    episode?.let {
                        getExactEpisode(it)?.run {
                            playerUIListener?.onPlayItem(first, second, isSendEventToRemote)
                            // Game Emoji
//                            gameEmojiVodController?.onActionNextEpisode()

                        }
                    }
                }
            }
        }
    }

    private fun playNewEpisodeActionNext() {
        if (screenType == PlayerHandler.ScreenType.Vod) {
            if (!isPlayingTrailer) {
                if (isVodOffline) {
                    // Do nothing
                } else {
                    currentEpisode?.let { currentEpisode ->
                        playerUIListener?.onNext(
                            position = details?.blockEpisode?.episodes?.getIndexOf(currentEpisode) ?: 0,
                            curData = currentEpisode,
                            isAuto = false,
                            isSendEventToRemote = true
                        )
                        // Game Emoji
                        gameEmojiVodController?.onActionNextEpisode()

                    }
                }
            }
        }
    }

    private fun playNewEpisodeActionPrevious() {
        if (screenType == PlayerHandler.ScreenType.Vod) {
            if (!isPlayingTrailer) {
                if (isVodOffline) {
                    // Do nothing
                } else {
                    currentEpisode?.let { currentEpisode ->
                        playerUIListener?.onPrevious(
                            position = details?.blockEpisode?.episodes?.getIndexOf(currentEpisode) ?: 0,
                            curData = currentEpisode,
                            isAuto = false
                        )
                        // Game Emoji
                        gameEmojiVodController?.onActionPreviousEpisode()
                    }
                }
            }
        }
    }

    //region Handle Back Press
    private fun handleBackPressed() {
        fragmentActivity?.run {
            onBackPressedDispatcher.addCallback(this, onBackPressCallBack)
        }
    }
    //endregion

    //region Handle fullscreen
    /**
     * Mobile Mode Only
     * */
    private fun enterFullscreen() {
        fragmentActivity?.run {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
            if (isEnableAutoScreenRotationInSettings()) {
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
                sensorStateChanges = SensorStateChangeActions.WATCH_FOR_LANDSCAPE_CHANGES
                sensorEvent?.enable() ?: kotlin.run {
                    initialiseSensor(true)
                }
            }
        }
    }

    /**
     * Mobile Mode Only
     * */
    private fun exitFullscreen() {
        fragmentActivity?.run {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
            if (isEnableAutoScreenRotationInSettings()) {
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                sensorStateChanges = SensorStateChangeActions.WATCH_FOR_PORTRAIT_CHANGES
                sensorEvent?.enable() ?: kotlin.run {
                    initialiseSensor(true)
                }
            }
        }
    }

    private fun tabletSystemBars() {
        if (context.isTablet()) {
            if (isFullscreen) {
                fragmentActivity?.hideSystemBar()
            } else {
                fragmentActivity?.showSystemBar()
            }
        }
    }
    //endregion

    private fun showCastAlertDialog(message: String? = null, messageOptional: Spanned? = null) {
//        castAlertDialog?.dismissAllowingStateLoss()
//        castAlertDialog = null
//        fragmentActivity?.supportFragmentManager?.let {
//            castAlertDialog = WarningDialog().apply {
//                if (message != null) {
//                    setMessage(message = message)
//                } else if (messageOptional != null) {
//                    setMessage(message = messageOptional)
//                }
//            }
//            castAlertDialog?.show(it, "CastAlertDialog")
//        }
    }

    var currentSelectedAudioTrack: PlayerControlView.Data.Track? = null
    var currentSelectedSubtitleTrack: PlayerControlView.Data.Track? = null
    private fun updateTrackPlayerData() {
        viewLifecycleOwner?.lifecycleScope?.launch {
//            val listTrackInfo = (player as? ExoPlayerProxy)?.fetchSubtitleAndAudio()
            binding.playerUI.updateDataPlayerControl(playerDataControl = playerData)
            //
            updateCurrentTrackSelected()
        }
    }
        
    private fun updateCurrentTrackSelected() {
        // Update Track data
        playerData.tracks?.forEach { track ->
            if (track.isSubtitle()) {
                if (track.isSelected) {
                    currentSelectedSubtitleTrack = track
                }
            } else {
                if (track.isSelected) {
                    currentSelectedAudioTrack = track
                }
            }
        }
    }

    //region Others -> Interfaces, Events...
    inner class PlayerTrackChangeEvents: Player.Listener {

        override fun onTimelineChanged(timeline: Timeline, reason: Int) {
            (player?.internalPlayer() as? ExoPlayer)?.let { exoPlayer ->
                if (exoPlayer.currentMediaItemIndex in 0 until  timeline.windowCount) {
                    val windowCurrent =
                        timeline.getWindow(exoPlayer.currentMediaItemIndex, Timeline.Window())
                    if (windowCurrent.windowStartTimeMs != C.TIME_UNSET && windowCurrent.windowStartTimeMs > 0) {
                        val localTime = windowCurrent.currentUnixTimeMs
                        val nextTime = windowCurrent.windowStartTimeMs + exoPlayer.currentPosition
                        debugViewData.latency =
                            String.format("%.1fs", (localTime - nextTime) / 1000.0)

                    } else {
                        debugViewData.latency = ""
                    }
                } else {
                    debugViewData.latency = ""
                }

                updateDebugView(debugViewData)
            }
        }

        override fun onTracksChanged(tracks: Tracks) {
            super.onTracksChanged(tracks)
            Looper.getMainLooper()?.run { Handler(this).postDelayed({
                playerUIListener?.onTracksInfoChanged()
                updateTrackPlayerData()
            }, 300) }
        }

        override fun onVideoSizeChanged(videoSize: VideoSize) {
            super.onVideoSizeChanged(videoSize)
            if (playerVideoSize?.height != videoSize.height) {
                playerUIListener?.onVideoSizeChanged(videoSize = videoSize)
            }
            playerVideoSize = videoSize
        }
    }

    inner class PlayerAnalyticsListener: AnalyticsListener {
        override fun onVideoInputFormatChanged(
            eventTime: AnalyticsListener.EventTime,
            format: Format,
            decoderReuseEvaluation: DecoderReuseEvaluation?
        ) {
            try{
                val fps = if (format.frameRate != Format.NO_VALUE.toFloat()) "${format.frameRate}" else ""
                val codecs = format.codecs ?: ""
                val res = "${format.height}"
                val bitrate = format.bitrate
                val bitrateInMbps = if (bitrate > 0) String.format("%.2f", (bitrate / 1_000_000.toFloat())) else ""

//                debugViewData.bitrateInMps = bitrateInMbps
                debugViewData.videoInfo.let { videoInfo ->
                    videoInfo.bitrateInMps = bitrateInMbps
                    videoInfo.bitrateInBitValue = if (bitrate >= 0) bitrate.toString() else ""
                    videoInfo.codec = codecs
                    videoInfo.fps = fps
                    videoInfo.res = res
                }
                updateDebugView(debugViewData)

            }catch (ex: Exception) {
                ex.printStackTrace()
            }
        }

        override fun onAudioInputFormatChanged(
            eventTime: AnalyticsListener.EventTime,
            format: Format,
            decoderReuseEvaluation: DecoderReuseEvaluation?
        ) {
            try {
                val codecs = format.codecs ?: ""
                val bitrate = format.bitrate
                val bitrateInMbps = if (bitrate > 0) String.format("%.2f", (bitrate / 1_000_000.toFloat())) else ""

                debugViewData.audioInfo.let { audioInfo ->
                    audioInfo.bitrateInMps = bitrateInMbps
                    audioInfo.bitrateInBitValue = if (bitrate >= 0) bitrate.toString() else ""
                    audioInfo.codec = codecs
                }
                updateDebugView(debugViewData)

            }catch (ex: Exception) {
                ex.printStackTrace()
            }

        }

        override fun onBandwidthEstimate(
            eventTime: AnalyticsListener.EventTime,
            totalLoadTimeMs: Int,
            totalBytesLoaded: Long,
            bitrateEstimate: Long
        ) {
            debugViewData.bitrateInMps = String.format("%.2f Mbps", bitrateEstimate / 1_000_000.toFloat())
            updateDebugView(debugViewData)
        }
    }

    inner class PlayerEvents : IPlayer.IPlayerCallback{
        override fun onBandwidth(message: String) {
            super.onBandwidth(message)
            Logger.d("$TAG onBandwidth")
            playerEventsListener?.onBandwidth(message = message)
            binding.playerUI.onBandwidth(message)

            // Game Emoji
            gameEmojiVodController?.onBandwidth(message)

            // Picture in Picture
            triggerUpdatePictureInPictureParams()
        }

        override fun startBuffering() {
            playerEventsListener?.startBuffering()
            super.startBuffering()
        }

        override fun endBuffering() {
            playerEventsListener?.endBuffering()
            super.endBuffering()
        }

        override fun onBuffering() {
            super.onBuffering()
            Logger.d("$TAG onBuffering")
            playerEventsListener?.onBuffering()
            binding.playerUI.onBuffering()
            // Ads
            logoAdsController?.onContentStartBuffering()

            // Game Emoji
            gameEmojiVodController?.onBuffering()

            // Picture in Picture
            triggerUpdatePictureInPictureParams()
        }

        override fun onEnd() {
            super.onEnd()
            Logger.d("$TAG onEnd")
            playerEventsListener?.onEnd()
            binding.playerUI.onEnd()
            if (binding.playerUI.canAutoNextEpisode()) {
                nextEpisode(isAuto = true, isSendEventToRemote = false)
            } else {
                if (fragmentActivity is AirlineActivity) {
                    // Airline Layout => Don't show next video recommendation
                } else {
                    // show recommend video, get from the first related list
                    if (binding.playerUI.isShowNextVideoRecommendation() != null) {
                        showNextVideoRecommendation(data = binding.playerUI.isShowNextVideoRecommendation()!!)
                    }
                }
            }
            // Ads
            completeTvcAds()
            logoAdsController?.onContentEnd()

            // Game Emoji
            gameEmojiVodController?.onEnd()

            //
            if (playerConfig.isPlayPreview) {
                playerUIListener?.onPreviewPlayCompleted()
            }

            // Picture in Picture
            triggerUpdatePictureInPictureParams()
        }

        override fun onError(code: Int, name: String, detail: String, error403: Boolean, responseCode: Int) {
            super.onError(code, name, detail, error403, responseCode)
            Logger.d("$TAG onError $code $name $detail")

            playerEventsListener?.onError(code = code, name = name, detail = detail, error403 = error403, responseCode = responseCode)
            binding.playerUI.onError(code, name, detail)
            // Ads
            logoAdsController?.onContentError(errorCode = code, errorMessage = name, detailErrorMessage = detail)

            // Picture in Picture
            triggerUpdatePictureInPictureParams()

            // Game Emoji
            gameEmojiVodController?.onError(code, name, detail, error403)
        }

        override fun onErrorBehindInLive(code: Int, name: String, detail: String) {
            super.onErrorBehindInLive(code, name, detail)
            Logger.d("$TAG onErrorBehindInLive")
            playerEventsListener?.onErrorBehindInLive(code = code, name = name, detail = detail)
            // Ads
            logoAdsController?.onContentError(
                errorCode = code,
                errorMessage = name,
                detailErrorMessage = detail
            )

            // Game Emoji
            gameEmojiVodController?.onErrorBehindInLive(code, name, detail)

            // Picture in Picture
            triggerUpdatePictureInPictureParams()
        }

        override fun onErrorCodec(
            code: Int,
            name: String,
            detail: String,
            responseCode: Int,
            isDrm: Boolean,
            codec: IPlayer.CodecType
        ) {
            Logger.d("$TAG onErrorCodec")
            playerEventsListener?.onErrorCodec(code = code, name = name, detail = detail, responseCode = responseCode, isDrm = isDrm, codec = codec)

            // Picture in Picture
            triggerUpdatePictureInPictureParams()
        }

        override fun onError6006(code: Int, name: String, detail: String) {
            Logger.d("$TAG onError6006")
            playerEventsListener?.onError6006(code, name, detail)
            // Picture in Picture
            triggerUpdatePictureInPictureParams()
        }

        override fun onError6006WhenPreview(code: Int, name: String, detail: String, responseCode: Int) {
            playerEventsListener?.onError6006WhenPreview(code, name, detail, responseCode)
        }


        override fun onPrepare() {
            super.onPrepare()
            Logger.d("$TAG onPrepare")
            binding.ivImageHover.hide()
            playerEventsListener?.onPrepare()
            binding.playerUI.onPrepare()

            // Ads
            logoAdsController?.onContentPrepared()

            initPlayerTrackChangeListener()
            // Game Emoji
            gameEmojiVodController?.onPrepare()
            // Audio Mode Background
            showBackgroundAudioOverlay(true)
            //

            //debug data
            debugViewData.clearData()
            when(screenType) {
                PlayerHandler.ScreenType.Live -> debugViewData.updateOTTType(tvChannelDetail)
                PlayerHandler.ScreenType.Vod -> debugViewData.updateOTTType(details)
                PlayerHandler.ScreenType.Premiere -> debugViewData.updateOTTType(detailsPremiere)
                null -> debugViewData.updateOTTType()
            }

            // Picture in Picture
            triggerUpdatePictureInPictureParams()

        }

        override fun onReady() {
            super.onReady()
            Logger.d("$TAG onReady")
            playerEventsListener?.onReady()
            binding.playerUI.onReady()
            binding.playerUI.showPlayerUIView(delay = 500)

            // Skip intro
            initCheckSkipIntroAndCredits()

            when (PlayerUtils.getPlayingType()) {
                PlayingType.Local -> {
                    if (player != null && player?.isPlaying() == true) {
                        // Check if currentEndContent cant get, use default
                        player?.run {
                            if (currentEndContentMillis == 0 && totalDuration().toInt() != 0) {
                                currentEndContentMillis = totalDuration().toInt() - 1
                            }
                        }
                        //
                        showAndStartSkipIntro()
                        startSkipCredits()
                    }
                }
                PlayingType.Cast -> {
                    pairingConnection.getRemoteData()?.remotePlayer?.run {
                        if (this.state == RemotePlayerState.PLAY) {
                            // Check if currentEndContent cant get, use default
                            if (currentEndContentMillis == 0 && totalDuration().toInt() != 0) {
                                currentEndContentMillis = totalDuration().toInt() - 1
                            }
                            //
                            showAndStartSkipIntro()
                            startSkipCredits()
                        }
                    }
                }
            }

            //Ads
            logoAdsController?.onContentEndBuffering(currentDuration())

            // Game Emoji
            gameEmojiVodController?.onReady()

            // Subtitle
            updateSubtitleView(force = true)

            // Picture in Picture
            setupMediaSession()
            triggerUpdatePictureInPictureParams()

            // Background Playback
            checkRuleEnterBackgroundAudioOrPiP(
                enterBackgroundAudio = {
                    if (checkEnableMediaNotification()) {
                        onCheckBackgroundAudioService(forPiP = false)
                    }
                },
                enterPictureInPicture = {
                    if (checkEnableMediaNotification()) {
                        onCheckBackgroundAudioService(forPiP = true)
                    }
                },
                enterBackgroundAudioInPiP = {
                    if (checkEnableMediaNotification()) {
                        onCheckBackgroundAudioService(forPiP = false)
                    }
                },
                doNothing = {}
            )

            setInComingRequest(null, false)
        }

        override fun onStart() {
            super.onStart()
            Logger.d("$TAG onStart")
            playerEventsListener?.onStart()
            binding.playerUI.onStart()

            when (PlayerUtils.getPlayingType()) {
                PlayingType.Local -> {
                    if (player != null && player?.isPlaying() == true) {
                        // Check if currentEndContent cant get, use default
                        player?.run {
                            if (currentEndContentMillis == 0 && totalDuration().toInt() != 0) {
                                currentEndContentMillis = totalDuration().toInt() - 1
                            }
                        }
                        //
                        showAndStartSkipIntro()
                        startSkipCredits()
                    }
                }
                PlayingType.Cast -> {
                    pairingConnection.getRemoteData()?.remotePlayer?.run {
                        if (this.state == RemotePlayerState.PLAY) {
                            // Check if currentEndContent cant get, use default
                            if (currentEndContentMillis == 0 && totalDuration().toInt() != 0) {
                                currentEndContentMillis = totalDuration().toInt() - 1
                            }
                            //
                            showAndStartSkipIntro()
                            startSkipCredits()
                        }
                    }
                }
            }
            //Ads
            logoAdsController?.onContentEndBuffering(currentDuration())
            // Game Emoji
            gameEmojiVodController?.onStart()

            // Picture in Picture
            triggerUpdatePictureInPictureParams()
        }

        override fun onStop() {
            super.onStop()
            Logger.d("$TAG onStop")
            playerEventsListener?.onStop()
            binding.playerUI.onStop()
            //Ads
            logoAdsController?.onContentPrepareToStop()
            // Game Emoji
            gameEmojiVodController?.onStop()
            // Audio Mode Background
            showBackgroundAudioOverlay(false)
            //

            // Picture in Picture
            triggerUpdatePictureInPictureParams()
        }

        override fun onFetchBitrateSuccess(bitrates: ArrayList<IPlayer.Bitrate>) {
            super.onFetchBitrateSuccess(bitrates)
            Logger.d("$TAG onFetchBitrateSuccess")
            playerEventsListener?.onFetchBitrateSuccess(bitrates = bitrates)
            //
            updateCurrentTrackSelected()
        }

        override fun onFetchBitrateAll(
            bitrates: ArrayList<IPlayer.Bitrate>,
            audioTracks: List<PlayerControlView.Data.Track>?
        ) {
            Logger.d("$TAG onFetchBitrateAll")
            playerEventsListener?.onFetchBitrateAll(bitrates = bitrates, audioTracks = audioTracks)
        }

        override fun onResume() {
            super.onResume()
            Logger.d("$TAG onResume")
            playerEventsListener?.onResume()

            // Picture in Picture
            triggerUpdatePictureInPictureParams()
        }

        override fun onPlay() {
            super.onPlay()
            Logger.d("$TAG onPlay")
            playerEventsListener?.onPlay()

            when (PlayerUtils.getPlayingType()) {
                PlayingType.Local -> {
                    if (player != null && player?.isPlaying() == true) {
                        // Check if currentEndContent cant get, use default
                        player?.run {
                            if (currentEndContentMillis == 0 && totalDuration().toInt() != 0) {
                                currentEndContentMillis = totalDuration().toInt() - 1
                            }
                        }
                        //
                        showAndStartSkipIntro()
                        startSkipCredits()
                    }
                }
                PlayingType.Cast -> {}
            }

            // Picture in Picture
            triggerUpdatePictureInPictureParams()
        }

        override fun onPause() {
            super.onPause()
            Logger.d("$TAG onPause")
            playerEventsListener?.onPause()

            // Picture in Picture
            triggerUpdatePictureInPictureParams()
        }

        override fun onRelease() {
            super.onRelease()
            Logger.d("$TAG onRelease")
            playerEventsListener?.onRelease()

            // Picture in Picture
            triggerUpdatePictureInPictureParams()
        }

        override fun onRotationKey() {
            super.onRotationKey()
            Logger.d("$TAG onRotationKey")
            playerEventsListener?.onRotationKey()
        }

        override fun onSeek() {
            super.onSeek()
            Logger.d("$TAG onSeek")
            playerEventsListener?.onSeek()

            // Picture in Picture
            triggerUpdatePictureInPictureParams()
        }

        override fun onTimelineChanged(any: Any?) {
            super.onTimelineChanged(any)
            Logger.d("$TAG onTimelineChanged")
            playerEventsListener?.onTimelineChanged(any)
        }

        override fun onVideoChange(bitrate: String) {
            super.onVideoChange(bitrate)
            Logger.d("$TAG onVideoChange")
            playerEventsListener?.onVideoChange(bitrate)
        }

        override fun onDrmKeysLoaded() {
            super.onDrmKeysLoaded()
            Logger.d("$TAG onDrmKeysLoaded")
            playerEventsListener?.onDrmKeysLoaded()
        }
    }

    inner class PlayerUIEvents: IPlayerControl {
        override fun onBack() {
            fragmentActivity?.onBackPressed()

        }

        override fun onPlayToggle() {
            when (PlayerUtils.getPlayingType()) {
                PlayingType.Local -> {
                    if (player?.isPlaying() == true) {
                        pause()
                        // Ads
                        logoAdsController?.onActionPause(currentDuration())
                        // Game Emoji
                        gameEmojiVodController?.onActionPause()
                    } else {
                        play()
                        // Ads
                        logoAdsController?.onActionResume(currentDuration())
                        // Game Emoji
                        gameEmojiVodController?.onActionPlay()
                    }
                }
                PlayingType.Cast -> {
                    val data = pairingConnection.getRemoteData()?.remotePlayer

                    when (pairingConnection.getCurrentConnection()) {
                        is FBoxDeviceInfoV2 -> {
                            if (data?.state == RemotePlayerState.PLAY) {
                                pairingConnection.sendEventKey(key = KeyCode.PAUSE)
                            } else {
                                pairingConnection.sendEventKey(key = KeyCode.PLAY)
                            }
                        }
                        is FSamsungTVDeviceInfo,
                        is FSamsungTVDeviceInfoExternal -> {
                            if (data?.state == RemotePlayerState.PLAY) {
                                pairingConnection.sendEventKey(key = KeyCode.PAUSE)
                            } else {
                                pairingConnection.sendEventKey(key = KeyCode.PLAY)
                            }
                        }
                        is FAndroidTVDeviceInfo -> {
                            if (data?.state == RemotePlayerState.PLAY) {
                                pairingConnection.sendEventTogglePlayer(isPlay = false)
                            } else {
                                pairingConnection.sendEventTogglePlayer(isPlay = true)
                            }
                        }
                        is FAndroidTVDeviceInfoExternal -> {
                            if (data?.state == RemotePlayerState.PLAY) {
                                pairingConnection.sendEventKey(key = KeyCode.PAUSE)
                            } else {
                                pairingConnection.sendEventKey(key = KeyCode.PLAY)
                            }
                        }
                    }


                }
            }
            playerUIListener?.onPlayToggle()

        }

        override fun onNext() {
            nextEpisode(isAuto = false, isSendEventToRemote = true)

        }

        override fun onPrevious() {
            previousEpisode(isAuto = false)

        }

        override fun onPlayItem(episode: Details.Episode) {
            exactEpisode(episode = episode, isSendEventToRemote = true)
        }

        override fun onSeekNext() {
            when (PlayerUtils.getPlayingType()) {
                PlayingType.Local -> {
                    player?.run {
                        seek(duration = currentDuration() + 10000L)
                        play(true)
                    }
                }
                PlayingType.Cast -> {
                    pairingConnection.sendEventSeek(id = details?.blockContent?.id?:"", refId = details?.blockContent?.refId?:"", indexChapter = currentEpisode?.id ?:"", timeWatched = "${((currentDuration() + 10000L) / 1000L)}")
                }
            }
            // Game Emoji
            gameEmojiVodController?.onActionSeek()
            playerUIListener?.onSeekNext(currentDuration() + 10000L)

        }
        override fun onSeekPrevious() {
            when (PlayerUtils.getPlayingType()) {
                PlayingType.Local -> {
                    player?.run {
                        seek(duration = currentDuration() - 10000L)
                        play(true)
                    }
                }
                PlayingType.Cast -> {
                    pairingConnection.sendEventSeek(id = details?.blockContent?.id?:"", refId = details?.blockContent?.refId?:"", indexChapter = currentEpisode?.id ?:"", timeWatched = "${((currentDuration() - 10000L) / 1000L)}")
                }
            }
            // Game Emoji
            gameEmojiVodController?.onActionSeek()

            playerUIListener?.onSeekPrevious(currentDuration() - 10000L)
        }

        override fun onLockToggle() {
            // Handle screen
            if (context.isTablet()) {
                if (binding.playerUI.playerControllerLocked) {
                    fragmentActivity?.requestedOrientation = getCurrentScreenOrientation()
                } else {
                    fragmentActivity?.run {
                        requestedOrientation = if (isEnableAutoScreenRotationInSettings()) {
                            ActivityInfo.SCREEN_ORIENTATION_SENSOR
                        } else {
                            ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
                        }
                    }
                }
            } else {
                if (binding.playerUI.playerControllerLocked) {
                    if (isFullscreen) {
                        fragmentActivity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
                    } else {
                        fragmentActivity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                    }
                    sensorEvent?.disable()
                }
                else {
                    fragmentActivity?.run {
                        if (isEnableAutoScreenRotationInSettings()) {
                            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR
                        }
                    }
                }
            }
        }

        override fun onMulticam() {
            playerUIListener?.onMulticam()
        }

        override fun onCast() {
            playerUIListener?.onCast()
        }

        override fun onShare() {
            onShareLink(url = linkToShare)
            playerUIListener?.onShare()
        }

        override fun onExpand() {
            playerUIListener?.onExpand(curResolutionId = curResolution)
        }

        override fun onMore(moreData: List<PlayerBottomControlData>) {
            playerUIListener?.onMore(moreData)
        }

        override fun onLiveChatClick() {
            playerUIListener?.onLiveChatClick()
        }

        override fun onSportInteractiveClick() {
            if(isFullscreen) {
                // only handle click on full screen. button should only show on full screen
                if(isLandscapeMode) {
                    // if landscape open sport interactive inside player view
                    playerUIListener?.onSportInteractiveClick()
                } else {
                    // if is portrait, callback for open sport interactive in parent fragment
                    Timber.tag("tam-sport").e("onSportInteractiveClick")
                    playerUIListener?.onSportInteractiveClick()
                }
            }
        }

        override fun onFullScreenToggle() {
            if (context.isTablet()) {
                isFullscreen = !isFullscreen
                binding.playerUI.updateLayouts(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
                playerUIListener?.onFullScreen(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
                tabletSystemBars()

                // Layout changes
                onLayoutChange()
                //
            } else {
                if (!isFullscreen) {
                    enterFullscreen()
                } else {
                    exitFullscreen()
                }
            }
            // Picture in Picture
            PlayerPiPHelper.storagePlayerMode(isFullscreen = isFullscreen)
        }

        override fun onAudioAndSubtitle() {
            when (PlayerUtils.getPlayingType()) {
                PlayingType.Local -> {
                    playerUIListener?.onAudioAndSubtitle(tracks = playerData.tracks)
                }
                PlayingType.Cast -> {
                    playerUIListener?.onAudioAndSubtitle(tracks = pairingConnection.getRemoteData()?.remotePlayer?.tracks)
                }
            }
        }

        override fun onOpenAudioSelection() {
            when (PlayerUtils.getPlayingType()) {
                PlayingType.Local -> {
                    playerUIListener?.onOpenAudioSelection(tracks = playerData.tracks)
                }
                PlayingType.Cast -> {
                    playerUIListener?.onOpenAudioSelection(tracks = pairingConnection.getRemoteData()?.remotePlayer?.tracks)
                }
            }
        }

        override fun onOpenSubtitleSelection() {
            when (PlayerUtils.getPlayingType()) {
                PlayingType.Local -> {
                    playerUIListener?.onOpenSubtitleSelection(tracks = playerData.tracks)
                }
                PlayingType.Cast -> {
                    playerUIListener?.onOpenSubtitleSelection(tracks = pairingConnection.getRemoteData()?.remotePlayer?.tracks)
                }
            }
        }

        override fun onOpenFrameOverlay(type: PlayerConstants.PlayerFrameOverlayType?) {
            playerUIListener?.onOpenFrameOverlay(type)
        }

        override fun onCloseFrameOverlay(type: PlayerConstants.PlayerFrameOverlayType?) {
            playerUIListener?.onCloseFrameOverlay(type)
        }

        override fun onPlayerSpeed() {
            playerUIListener?.onPlayerSpeed()
        }

        override fun onSetting() {
            playerUIListener?.onSetting(bitrates = playerData.bitrates)
        }

        override fun onSeek(position: Long) {
            when (PlayerUtils.getPlayingType()) {
                PlayingType.Local -> {
                    player?.run {
                        seek(duration = position)
                    }
                }
                PlayingType.Cast -> {
                    pairingConnection.sendEventSeek(id = details?.blockContent?.id?:"", refId = details?.blockContent?.refId?:"", indexChapter = currentEpisode?.id ?:"", timeWatched = "${(position / 1000L)}")
                }
            }
            // Game Emoji
            gameEmojiVodController?.onActionSeek()
            playerUIListener?.onActionSeek(position)
        }

        override fun onSkipIntro() {
            when (PlayerUtils.getPlayingType()) {
                PlayingType.Local -> {
                    player?.run {
                        seek(duration = currentStartContentMillis.toLong())
                        play(true)
                    }
                }
                PlayingType.Cast -> {
                    pairingConnection.sendEventSeek(id = details?.blockContent?.id?:"", refId = details?.blockContent?.refId?:"", indexChapter = currentEpisode?.id ?:"", timeWatched = "${(currentStartContentMillis.toLong() / 1000L)}")
                }
            }

            // Game Emoji
            gameEmojiVodController?.onActionSeek()
            playerUIListener?.onActionSeek(currentStartContentMillis.toLong())
        }

        override fun onWatchCredit(isSendEventToRemote: Boolean) {
            setShowViewSkipCredit(isShow = false)
            stopCountDownTimerSkipCredits()
            removeHandlerAndCountDownSkipCredits()
            playerUIListener?.onWatchCredit(isSendEventToRemote = isSendEventToRemote)
        }

        override fun onSkipCredit() {
            nextEpisode(isAuto = true, isSendEventToRemote = true)
            removeHandlerAndCountDownSkipCredits()
        }

        override fun onRecommendClose() {
            stopCountDownTimerSkipCredits()
            removeHandlerAndCountDownSkipCredits()
            playerUIListener?.onRecommendClose()
        }

        override fun onRecommendWatchNow(related: Details.RelatedVod?) {
            stopCountDownTimerSkipCredits()
            removeHandlerAndCountDownSkipCredits()
            playerUIListener?.onRecommendWatchNow(related = related)

        }

        override fun onRecommendPlayTrailer(related: Details.RelatedVod?) {
            stopCountDownTimerSkipCredits()
            removeHandlerAndCountDownSkipCredits()
            playerUIListener?.onRecommendPlayTrailer(related = related)
        }

        override fun onDebugViewStateChange() {
            if(isShowingDebugView) {
                viewLifecycleOwner?.lifecycleScope?.launch(Dispatchers.Main) {
                    hideDebugView()
                }
            } else {
                viewLifecycleOwner?.lifecycleScope?.launch(Dispatchers.Main) {
                    showDebugView()
                }
            }
        }

        override fun onReport() {
            playerUIListener?.onReport()
        }

        override fun onClickBuyPackage() {
            playerUIListener?.onClickBuyPackage()
        }

        override fun hideBuyPackageGuide() {
            hideAndStopBuyPackageGuideInPlayer()
        }
    }

    //endregion

    //region debug view
//    private var _debugViewBinding: ViewStubPlayerDebugInfoBinding? = null
//    private val debugViewBinding get() = _debugViewBinding!!
    private var tvDebug: TextView? = null

    private fun getDebugTextView(): TextView? {
        return if(binding.vtDebugInfo.parent != null) {
            tvDebug = binding.vtDebugInfo.safeInflate() as? TextView
            tvDebug
        } else {
            tvDebug
        }
    }

    private fun showDebugView() {
        isShowingDebugView = true
        getDebugTextView()?.let {
            it.post {
                it.visible()
                it.text = debugViewData.getDebugContent()
            }
        }
    }

    private fun hideDebugView() {
        isShowingDebugView = false
        getDebugTextView()?.gone()
    }

    private fun updateDebugView(data: PlayerDebugViewData) {
        if(isShowingDebugView) {
            getDebugTextView()?.text = data.getDebugContent()
        }
    }
    //endregion

    //region Other
    sealed interface PlayingType{
        object Local : PlayingType
        object Cast : PlayingType
    }
    //endregion


    inner class OnBackPressCallBack : OnBackPressedCallback(true) {
        override fun handleOnBackPressed() {
            Logger.d("$TAG onBackPress")
            if (isFullscreen){ // Logic can back press

                if(binding.playerUI.isShowingFrameOverlay()) {
                    binding.playerUI.closeFrameOverlayView(null)
                    return
                }

                //here handle your backPress in your fragment
                binding.playerUI.playerControllerLocked = false
                if (context.isTablet()) {
                    isFullscreen = !isFullscreen
                    binding.playerUI.updateLayouts(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
                    playerUIListener?.onFullScreen(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
                    tabletSystemBars()
                    // Layout changes
                    onLayoutChange()
                    //
                } else {
                    exitFullscreen()
                }
                //
            } else {
                isEnabled = false //this is important line
                fragmentActivity?.onBackPressed()

                // Logic check on background player
                if (isHandlerAudioBackground) {
                    isHandlerAudioBackground = false
                }
            }
            // Picture in Picture
            PlayerPiPHelper.storagePlayerMode(isFullscreen = isFullscreen)
        }
    }


    // region Ads

    override fun onWindowFocusChanged(hasWindowFocus: Boolean) {
        super.onWindowFocusChanged(hasWindowFocus)
        adsController?.onWindowFocusChange(hasWindowFocus)
    }

    // region TVC Ads
    private var adsController: AdsController? = null
    private var canPlayPrerollAd = true
    private var canPlayInstreamAd = true
    private var prerollPlaying = false
    var tvcAdsListener: AdsTvcListener? = null
    var logoAdsListener: AdsLogoListener? = null
    private val contentPlayerCallback by lazy {
        object : AdsListener.ContentPlayerCallback {
            override fun hiddenPlayer() {
                viewLifecycleOwner?.lifecycleScope?.launch(Dispatchers.Main) {
                    binding.apply {
                        dissmissCallBack?.onDismissClickListener(isToolTip = true)
                        // Pause content player, hide content player and player control
                        epvPlayer.hide()
                        playerUI.hide()
                        pause(force = true)

                        adsView.showInstreamView()
                    }
                    logoAdsController?.onActionPause(currentDuration())

                    // age restriction
                    hideAgeRestriction()

                }

                //
                playerUIListener?.onAdsShow()
            }

            override fun showPlayer(callWhenAdsComplete: Boolean) {
                viewLifecycleOwner?.lifecycleScope?.launch(Dispatchers.Main) {
                    binding.apply {
                        if(codeLimitCcu == MqttUtil.LIMIT_CODE) {
                            limitCcuBinding?.isVisible = true
                            return@launch
                        }
                        dissmissCallBack?.onDismissClickListener(isToolTip = false)
                        // Pause content player, hide content player and player control
                        epvPlayer.show()
                        playerUI.show()
                        play()

                        adsView.hideInstreamView()
                    }
                    logoAdsController?.onActionResume(currentDuration())

                    // age restriction
                    checkReshowAgeRestriction()

                    //
                    playerUIListener?.onAdsHide()

                    // buy package guide
                    if(callWhenAdsComplete) {
                        buyPackageGuideDisplayed = false
                        buyPackageGuide?.let {
                            if (!playerConfig.isPlayPreview) {
                                checkShowBuyPackageGuide(it)
                            }
                        }
                    }
                    //
                }
            }

            override fun getWidth(): Int {
                return try {
                    binding.epvPlayer.width
                } catch (e: Exception) {
                    0
                }

            }

            override fun getHeight(): Int {
                return try {
                    binding.epvPlayer.height
                } catch (e: Exception) {
                    0
                }
            }
        }
    }
    private val adsCallback by lazy {
        object : AdsListener.AdsCallback {
            override fun onPrerollCompleted() {
                tvcAdsListener?.onPrerollCompleted()
                prerollPlaying = false
            }

            override fun onPrerollError() {
                tvcAdsListener?.onPrerollCompleted()
                prerollPlaying = false
            }

            override fun onOutStreamInteractiveLoaded(dataJson: String?, userInteract: Boolean) {
                if(dataJson != null) {
                    tvcAdsListener?.onOutStreamInteractivePopupLoaded(dataJson, userInteract)
                }
            }


            override fun onBannerCompanionLoaded(urlBannerCompanion: String?) {
                // Remove in PlayOs4
            }

            override fun onPromotionButtonClicked(urlDeepLink: String?) {
                // Remove in PlayOs4
            }
        }
    }
    private val contentVideoProgress by lazy {
        object : AdsListener.ContentVideoProgress {
            override fun getVideoDuration(): Long {
                return try {
                    player?.totalDuration() ?: 0L
                } catch(e: Exception) {
                    0L
                }
            }

            override fun getVideoCurrentPosition(): Long {
                return try {
                    player?.currentDuration() ?: 0L
                } catch(e: Exception) {
                    0L
                }
            }
        }
    }

    fun initAdsController(adsTrackingProxy: AdsTrackingProxy) {
        // Init when get detail of vod/live -> reset play ads flag
        canPlayPrerollAd = true
        canPlayInstreamAd = true
        prerollPlaying = false
        if(adsController != null) return
        val adsControllerBuilder = AdsControllerBuilder()
            .setContext(context)
            .setPlatformType(PlatformType.MOBILE)
            .setCustomVideoAdPlayback(binding.adsView.tvcInstreamContainer())
            .setVpaidView(binding.adsView.tvcVpaidContainer())
            .setAdsCallback(adsCallback)
            .setContentPlayerCallback(contentPlayerCallback)
            .setContentVideoProgress(contentVideoProgress)
            .setSkipButton(binding.adsView.skipButton())
            .setAdditionalSkip(MainApplication.INSTANCE.appConfig.adsSkipPlus)
//            .setInteractiveButton(binding.adsView.interactiveButton())
            .setAdsTracking(adsTrackingProxy)
//            .build()
        if(screenType == PlayerHandler.ScreenType.Vod) {
            adsControllerBuilder.setInteractiveButton(binding.adsView.interactiveButton())
        }
        adsController = adsControllerBuilder.build()
    }

    fun playPrerollAds(
        websiteUrl: String,
        contentId: String? = null,
        contentListStructureId: List<String>? = null
    ) {


        val currAdsController = adsController
        Timber.tag("tamlog").d("PlayerView playPrerollAds $adsController $screenType")
        if(canPlayPrerollAd && currAdsController != null) {
            val adsRequestParams = AdsRequestParams(
                uuid = AdsUtils.uniqueUid(sharedPreferences),
                webUrl = websiteUrl,
                useData =  AdsUtils.useMobileData(context),
                userType = AdsUtils.userType(sharedPreferences),
                appVersion = AdsUtils.versionApp(),
                userId = AdsUtils.userId(sharedPreferences),
                profileId = AdsUtils.profileId(sharedPreferences),
                profileType = AdsUtils.profileType(sharedPreferences),
                contentId = contentId,
                categoriesId = contentListStructureId
            )

            when(screenType) {
                PlayerHandler.ScreenType.Vod -> {
                    currAdsController.startAdsPrerollOutStream(adsRequestParams)
                    prerollPlaying = true

                }
                PlayerHandler.ScreenType.Live -> {
                    currAdsController.startAdsPrerollLiveTVOutStream(adsRequestParams)
                    prerollPlaying = true

                }
                else -> {
                    tvcAdsListener?.onPrerollCompleted()
                }
            }
        } else {
            tvcAdsListener?.onPrerollCompleted()
        }

        canPlayPrerollAd = false // Only play preroll one time for this player view until reset
    }

    fun playInstreamAdsVOD(websiteUrl: String, vodId: String? = null, vodListStructureId: List<String>? = null, currentPositionMill: Long? = -1, forcePlayAfterTime: Boolean = false) {
        val currentPosition = currentPositionMill?.let { TimeUnit.MILLISECONDS.toSeconds(currentPositionMill) }
        Timber.tag("tam-ads").i("playInstreamAdsVOD canPlayInstreamAd $canPlayInstreamAd prerollPlaying $prerollPlaying")
        if(!prerollPlaying && canPlayInstreamAd && screenType == PlayerHandler.ScreenType.Vod) {
            // only play instream if preroll not playing
            val playAdsAfterTimeSeconds = if(forcePlayAfterTime) {
                currentPosition ?: -1
            } else {
                -1
            }
            Timber.tag("tam-ads").i("playInstreamAdsVOD forcePlayAfterTime $forcePlayAfterTime -  currentPosition $currentPosition - playAdsAfterTimeSeconds $playAdsAfterTimeSeconds")
            adsController?.let {
                canPlayInstreamAd = false
                val adsRequestParams = AdsRequestParams(
                    uuid = AdsUtils.uniqueUid(sharedPreferences),
                    webUrl = websiteUrl,
                    useData =  AdsUtils.useMobileData(context),
                    userType = AdsUtils.userType(sharedPreferences),
                    appVersion = AdsUtils.versionApp(),
                    userId = AdsUtils.userId(sharedPreferences),
                    profileId = AdsUtils.profileId(sharedPreferences),
                    profileType = AdsUtils.profileType(sharedPreferences),
                    contentId = vodId,
                    categoriesId = vodListStructureId
                )
                it.startAdsVod(
                    adsRequestParams,
                    playAdsAfterTimeSeconds
                )
            }
        }
    }

    fun startInstreamLiveTV(channelId: String,
                            contentId: String? = null,
                            contentListStructureId: List<String>? = null) {
        if(canPlayInstreamAd && screenType == PlayerHandler.ScreenType.Live) {
            val adsRequestParams = AdsRequestParams(
                uuid = AdsUtils.uniqueUid(sharedPreferences),
                channelId = channelId,
                useData =  AdsUtils.useMobileData(context),
                userType = AdsUtils.userType(sharedPreferences),
                appVersion = AdsUtils.versionApp(),
                userId = AdsUtils.userId(sharedPreferences),
                profileId = AdsUtils.profileId(sharedPreferences),
                profileType = AdsUtils.profileType(sharedPreferences),
                contentId = contentId,
                categoriesId = contentListStructureId
            )
            adsController?.startAdsLiveTV(adsRequestParams)
        }
    }

    fun stopTvcAds(resetPrerollAd: Boolean = false, resetInstreamAd: Boolean = true) {
        adsController?.stopAds(true)
        if(resetPrerollAd) {
            // if canPlayerPrerollAd == true && resetPrerollAd = false -> ignore
            canPlayPrerollAd = true
        }
        canPlayInstreamAd = resetInstreamAd
    }

    fun completeTvcAds() {
        adsController?.onCompleted()
    }

    // endregion TVC Ads

    // region Logo Ads
    private var logoAdsController: LogoInStreamController? = null
    private val logoContentCallback by lazy {
        object : PlayerContentCallback {
            override fun getVideoCurrentPosition(): Long? {
                return try {
                    player?.currentDuration() ?: -1L
                } catch(e: Exception) {
                    -1L
                }
            }

        }
    }

    @UiThread
    fun initLogoAdsController() {
        if(logoAdsController != null) return
        binding.playerUI.logoContainer()?.let {
            logoAdsController = LogoInStreamController(applicationContext = context).apply {
                setLogoInStreamContainer(it)
                playerContentCallback = logoContentCallback
                logoInStreamListener = object : LogoInStreamListener {
                    override fun onLogoClicked(landingPage: String?, useDirectBrowser: Boolean?) {
                        Timber.tag("tam-logo").d("onLogoClicked: \n $landingPage")
                        logoAdsListener?.onLogoClicked(landingPage, useDirectBrowser)
                    }

                    override fun onLogoSendInteractive(interactiveData: String?, userInteract: Boolean) {
                        Timber.tag("tam-logo").d("onLogoSendInteractive: \n $ \n - userInteract: $userInteract")
                        if(interactiveData != null) {
                            logoAdsListener?.onLogoSendInteractive(data = interactiveData, userInteract = userInteract)
                        }
                    }


                    override fun onLogoStart() {
                        adsLogoShowing = true
                        logoAdsListener?.onLogoStart()
                    }

                    override fun onLogoStop() {
                        adsLogoShowing = false
                        logoAdsListener?.onLogoStop()
                    }
                }
            }
        }

    }

    fun startLogoAds(websiteUrl: String, autoStartLogo: Boolean = true, vodId: String? = null, vodListStructureId: List<String>? = null) {
        viewLifecycleOwner?.let {
            val adsLogoRequestParams = AdsLogoRequestParams(
                uuid = AdsUtils.uniqueUid(sharedPreferences),
                webUrl = websiteUrl,
                isUseData =  AdsUtils.useMobileData(context),
                userType = AdsUtils.userType(sharedPreferences),
                appVersion = AdsUtils.versionApp(),
                userId = AdsUtils.userId(sharedPreferences),
                profileId = AdsUtils.profileId(sharedPreferences),
                profileType = AdsUtils.profileType(sharedPreferences),
                contentId = vodId,
                categoriesId = vodListStructureId
            )

            logoAdsController?.requestLogoInstream(
                lifeCycleOwner = it,
                adsLogoRequestParams = adsLogoRequestParams,
                isPlayerPlaying = isPlaying() ?: false,
                autoStartLogo = autoStartLogo,
            )
        }
    }

    fun stopLogoAds() {
        logoAdsController?.stopLogoInstream()
    }

    fun pauseLogoAds() {
        logoAdsController?.pauseLogoInstream()
    }

    fun resumeLogoAds() {
        logoAdsController?.resumeLogoInstream()
    }

    // endregion Logo Ads

    // endregion Ads

    // region Game

    // region Game Emoji
    private var gameEmojiVodController : GameEmojiVodController? = null
    private val gameEmojiContentCallback by lazy { object: GameEmojiContentCallback {
        override fun currentPosition(): Long? {
            return try {
                player?.currentDuration() ?: -1L
            } catch(e: Exception) {
                -1L
            }
        }

        override fun isPlaying(): Boolean {
            return try {
                player?.isPlaying() ?: false
            }  catch(e: Exception) {
                false
            }
        }
    } }
    var gameEmojiListener: GameEmojiListener? = null


    @UiThread
    fun initGameEmojiController() {
        if(gameEmojiVodController != null) return
        gameEmojiVodController = GameEmojiVodController(sharedPreferences = sharedPreferences).apply {
            contentCallback = gameEmojiContentCallback
            gameListener = object: GameEmojiListener {
                override fun onStartGame(url: String, dataJson: String) {
                    gameEmojiListener?.onStartGame(url, dataJson)
                }

                override fun onStopGame() {
                    gameEmojiListener?.onStopGame()
                }
            }
        }
        (gameEmojiVodController as? DefaultLifecycleObserver)?.let { viewLifecycleOwner?.lifecycle?.addObserver(it) }

    }

    fun startGameEmoji(data: GameVOD.GameVODData) {
        Timber.tag("tamlog-emoji").e("startGameEmoji ${isPlaying()}")

        gameEmojiVodController?.init(
            data = data
        )
    }

    fun stopGameEmoji() {
        Timber.tag("tamlog-emoji").e("stopGameEmoji")

        gameEmojiVodController?.stopGame()
    }
    // endregion Game Emoji

    // endregion Game

    fun getTrackingBandWith(): String {
        return (player as? ExoPlayerProxy)?.getTrackingBandwidth() ?: "0"
    }

    fun getTrackingBitrate(): String {
        return (player as? ExoPlayerProxy)?.getTrackingBitrate() ?: ""
    }

    //region Screen Rotation Handler
    private var sensorEvent: OrientationEventListener? = null
    private var sensorStateChanges: SensorStateChangeActions? = null
    private enum class SensorStateChangeActions {
        WATCH_FOR_LANDSCAPE_CHANGES,
        SWITCH_FROM_LANDSCAPE_TO_STANDARD,
        WATCH_FOR_PORTRAIT_CHANGES,
        SWITCH_FROM_PORTRAIT_TO_STANDARD
    }

    private fun getCurrentScreenOrientation() : Int {
        val rotation = getCurrentScreenRotation()
        return if (rotation == -1) {
            if (isLandscapeMode) {
                ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
            } else {
                ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
            }
        } else {
            when (rotation) {
                Surface.ROTATION_0 -> { ActivityInfo.SCREEN_ORIENTATION_PORTRAIT }
                Surface.ROTATION_90 -> { ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE }
                Surface.ROTATION_180 -> { ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT }
                Surface.ROTATION_270 -> { ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE }
                else -> {
                    if (isLandscapeMode) {
                        ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
                    } else {
                        ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                    }
                }
            }
        }
    }

    private fun getCurrentScreenRotation() : Int {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) rootView?.display?.rotation ?: -1
        else fragmentActivity?.windowManager?.defaultDisplay?.rotation ?: -1
    }

    private fun initialiseSensor(enable: Boolean) {
        sensorEvent = object : OrientationEventListener(context, SensorManager.SENSOR_DELAY_NORMAL) {
            override fun onOrientationChanged(orientation: Int) {
                /*
                 * This logic is useful when user explicitly changes orientation using player controls, in which case orientation changes gives no callbacks.
                 * we use sensor angle to anticipate orientation and make changes accordingly.
                 */
                if (sensorStateChanges != null) {
                    if (sensorStateChanges == SensorStateChangeActions.WATCH_FOR_LANDSCAPE_CHANGES && ((orientation in 60..120) || (orientation in 240..300))) {
                        sensorStateChanges = SensorStateChangeActions.SWITCH_FROM_LANDSCAPE_TO_STANDARD
                    } else if (sensorStateChanges == SensorStateChangeActions.SWITCH_FROM_LANDSCAPE_TO_STANDARD && (orientation <= 40 || orientation >= 320)) {
                        fragmentActivity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
                        sensorStateChanges = null
                        sensorEvent?.disable()
                    } else if (sensorStateChanges == SensorStateChangeActions.WATCH_FOR_PORTRAIT_CHANGES && ((orientation in 300..359) || (orientation in 0..45))) {
                        sensorStateChanges = SensorStateChangeActions.SWITCH_FROM_PORTRAIT_TO_STANDARD
                    } else if (sensorStateChanges == SensorStateChangeActions.SWITCH_FROM_PORTRAIT_TO_STANDARD && ((orientation in 240..300) || (orientation in 60..130))) {
                        fragmentActivity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
                        sensorStateChanges = null
                        sensorEvent?.disable()
                    }
                }
            }
        }
        if (enable) sensorEvent?.enable()
    }
    //endregion

    //region Pairing Control
    private fun canPlayBackground() : Boolean {
        return when (PlayerUtils.getPlayingType()) {
            PlayingType.Local -> true
            PlayingType.Cast -> false
        }
    }
    //endregion

    // region sport interactive
    fun updateSportInteractiveButton(contentId: String, detailsContent: Any?, viewModel: SportInteractiveViewModel?) {
        checkToShowSportAndInitInteractive(contentId, detailsContent, viewModel, screenType)
    }
    private fun checkToShowSportAndInitInteractive(contentId: String, detailsContent: Any?, viewModel: SportInteractiveViewModel?, screenType: PlayerHandler.ScreenType?) {
        Timber.tag("tam-sport").i("checkToShowSportAndInitInteractive")
        when(screenType) {
            PlayerHandler.ScreenType.Live -> {
                // not show in livetv this phase
                binding.playerUI.updateShowSportInteractiveButton(false)

            }
            PlayerHandler.ScreenType.Premiere -> {
                if((detailsContent as? com.xhbadxx.projects.module.domain.entity.fplay.premier.Details).canShowSportInteractiveButton()) {
                    binding.playerUI.updateShowSportInteractiveButton(true)
                } else {
                    binding.playerUI.updateShowSportInteractiveButton(false)
                }

            }
            PlayerHandler.ScreenType.Vod -> {
                // not show in vod this phase
                binding.playerUI.updateShowSportInteractiveButton(false)
            }
            else -> {
                binding.playerUI.updateShowSportInteractiveButton(false)
            }
        }

    }

    // endregion sport interactive

    //region Situation Warning
    private fun initSituationWarning() {
        when (val situationWarningView = binding.epvPlayer.currentViewSituationalWarning()) {
            is TextView -> {
                (situationWarningView as? TextView)?.run {
                    setTextAppearance(R.style.player_situation_warning)
                    maxLines = 2
                    ellipsize = TextUtils.TruncateAt.END
                    textAlignment = TextView.TEXT_ALIGNMENT_CENTER
                }
            }
            else -> {}
        }
    }

    private fun updateSituationWarningView() {
        binding.epvPlayer.currentViewSituationalWarning()?.let {
            it.updateSituationWarningView(player, fragmentActivity, it, isFullscreen, isLandscapeMode, videoResizeMode)
        }
    }

    fun showSituationWarning(content: String) {
        isSituationWarningShown = true
        when (val situationWarningView = binding.epvPlayer.currentViewSituationalWarning()) {
            is TextView -> {
                val situationTextView = (situationWarningView as? TextView)
                situationTextView?.text = content
                situationTextView.show()
            }
            else -> {}
        }
        //
        updateSituationWarningView()
        updateSubtitleLayoutStyle(videoResizeMode, isFullscreen, isLandscapeMode)
        hideAndStopBuyPackageGuideInPlayer()
    }

    fun hideSituationWarning() {
        isSituationWarningShown = false
        binding.epvPlayer.currentViewSituationalWarning().hide()
        //
        updateSubtitleLayoutStyle(videoResizeMode, isFullscreen, isLandscapeMode)
    }
    //endregion

    //region Picture in picture
    private val pipUserLeaveHintListener = {
        Logger.d("$TAG -> PIP -> pipUserLeaveHintListener -> onReceive -> USER_LEAVE_HINT")
        checkRuleEnterBackgroundAudioOrPiP(
            enterBackgroundAudio = {
                // Do nothing, handled in onReady
            },
            enterPictureInPicture = {
                if (checkPlayerStateCanEnterPiP()) {
                    startPictureInPicture()
                    viewLifecycleOwner?.lifecycle?.removeObserver(player as DefaultLifecycleObserver)
                } else {
                    stopBackgroundAudioNotificationService()
                }
            },
            enterBackgroundAudioInPiP = {
                updatePictureInPictureParams(isAutoEnterEnable = false)
            },
            doNothing = {}
        )
    }

    private val pipModeChangedListener = Consumer<PictureInPictureModeChangedInfo> { pipModeChangedInfo ->
        handlePictureInPictureModeChanged(pipModeChangedInfo)
    }

    private fun handlePictureInPictureModeChanged(info: PictureInPictureModeChangedInfo) {
        if (info.isInPictureInPictureMode) {
            Logger.d("$TAG -> PIP -> handlePictureInPictureModeChanged -> onReceive -> PIP_OPEN")
            playerUIListener?.onPictureInPictureModeChanged(isInPictureInPictureMode = true)
            keepScreenOn = true
            binding.playerUI.updatePiPMode(isPiPMode = true)
            //
            goFullscreenBeforeEnterPiP()
            //
            checkHideUIElementInPipMode()
        } else {
            Logger.d("$TAG -> PIP -> handlePictureInPictureModeChanged -> onReceive -> PIP_CLOSE")
            playerUIListener?.onPictureInPictureModeChanged(isInPictureInPictureMode = false)
            keepScreenOn = false
            binding.playerUI.updatePiPMode(isPiPMode = false)
            //
            pictureInPictureCheckPlayerModeTablet()
            //
            checkShowUIElementInPipMode()
            //
            if (PlayerPiPHelper.isPlayingLive(
                    MediaSessionHandler.DataSource(
                        screenType = screenType,
                        isPlayVipTrailer = isPlayingVipTrailer,
                        isPlayTimeShift = isPlayingTimeshift
                    ))) {
                play(force = true)
            }
            // Pause player, stop background audio when close pip
            if (viewLifecycleOwner?.lifecycle?.currentState == Lifecycle.State.CREATED) {
                pause(force = true)
                stopBackgroundAudioNotificationService()
            }
        }
    }

    private fun goFullscreenBeforeEnterPiP() {
        // Some device OS < 28, enter fullscreen need delay a little bit
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.P) {
            if (!isFullscreen) {
                CoroutineScope(Dispatchers.IO).launch {
                    runCatching {
                        delay(150)
                        withContext(Dispatchers.Main) {
                            pictureInPictureEnterFullscreen()
                        }
                    }.getOrElse {
                        pictureInPictureEnterFullscreen()
                        it.printStackTrace()
                    }
                }
            }
        } else {
            pictureInPictureEnterFullscreen()
        }
    }

    private fun initPlayerPiP() {
        Logger.d("$TAG -> PIP -> bindPlayerPiP")
        fragmentActivity?.let { it ->
            mediaSessionHandler.run {
                initMediaSessionHandler(context = it, listener = mediaSessionPiPListener)
                binding.epvPlayer.doOnLayout { triggerUpdatePictureInPictureParams() }
            }
            //
            (it as? BaseActivity)?.let { activity ->
                activity.addOnUserLeaveHintListener(listener = pipUserLeaveHintListener)
                activity.addOnPictureInPictureChangedListener(listener = pipModeChangedListener)
            }
        }
    }

    private fun setupMediaSession() {
        Logger.d("$TAG -> PIP -> setupMediaSession")
        fragmentActivity?.let {
            mediaSessionHandler.setupPlayerAndBindSession(player as? ExoPlayerProxy, dataSource = MediaSessionHandler.DataSource(
                screenType = screenType,
                isPlayVipTrailer = isPlayingVipTrailer,
                isPlayTimeShift = isPlayingTimeshift
            ))
        }
    }

    private fun triggerUpdatePictureInPictureParams() {
        try {
            checkRuleEnterBackgroundAudioOrPiP(
                enterBackgroundAudio = {
                    updatePictureInPictureParams(isAutoEnterEnable = false)
                },
                enterPictureInPicture = {
                    updatePictureInPictureParams(isAutoEnterEnable = checkPlayerStateCanEnterPiP())
                },
                enterBackgroundAudioInPiP = {
                    updatePictureInPictureParams(isAutoEnterEnable = false)
                },
                doNothing = {
                    updatePictureInPictureParams(isAutoEnterEnable = false)
                }
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    private fun checkRuleEnterBackgroundAudioOrPiP(enterBackgroundAudio: () -> Unit, enterPictureInPicture: () -> Unit, enterBackgroundAudioInPiP: () -> Unit, doNothing: () -> Unit) {
        if (PlayerUtils.getPlayingType() == PlayingType.Local) {
            if (isEnableBackgroundAudio()) {
                Logger.d("$TAG -> PIP -> Will Enter -> Background Audio")
                enterBackgroundAudio.invoke()
            } else if (PlayerPiPHelper.userPackagePlanSupportPictureInPicture(sharedPreferences)) {
                if (PlayerPiPHelper.checkOsVersionSupportPictureInPicture()) {
                    Logger.d("$TAG -> PIP -> Will Enter -> Picture in Picture")
                    enterPictureInPicture.invoke()
                } else {
                    Logger.d("$TAG -> PIP -> Will Enter -> Background Audio (PiP)")
                    enterBackgroundAudioInPiP.invoke()
                }
            } else {
                Logger.d("$TAG -> PIP -> Will Enter -> Nothing")
                doNothing.invoke()
            }
        } else {
            Logger.d("$TAG -> PIP -> Will Enter -> Nothing")
            doNothing.invoke()
        }
    }

    private fun checkPlayerStateCanEnterPiP(): Boolean {
        return isPlaying()
                && PlayerUtils.getPlayingType() == PlayingType.Local
                && !playerConfig.isPlayPreview
                && !isPlayingVipTrailer
                && !isVodOffline
    }

    private fun checkEnableMediaNotification(): Boolean {
        return PlayerUtils.getPlayingType() == PlayingType.Local
                && !playerConfig.isPlayPreview
                && !isPlayingVipTrailer
                && !isVodOffline
    }

    private fun releaseBindPlayerPiP() {
        releaseMediaSessionPiPHandler()
        fragmentActivity?.let {
            (it as? BaseActivity)?.let { activity ->
                activity.removeOnUserLeaveHintListener(listener = pipUserLeaveHintListener)
                activity.removeOnPictureInPictureChangedListener(listener = pipModeChangedListener)
            }
        }
        // Picture in Picture
        PlayerPiPHelper.storagePlayerMode(isFullscreen = isFullscreen)
        stopBackgroundAudioNotificationService()
    }

    private fun releaseMediaSessionPiPHandler() {
        Logger.d("$TAG -> PIP -> unbindMediaSessionPiPHandler")
        mediaSessionHandler.releaseMediaSession()
        updatePictureInPictureParams()
    }

    private val mediaSessionPiPListener = object : MediaSessionHandler.OnMediaSessionListener {

        override fun setSkipToNext() {
            Logger.d("$TAG -> PIP -> mediaSessionPiPListener -> setSkipToNext")
            nextEpisode(isAuto = false, isSendEventToRemote = true)
        }

        override fun setSkipToPrevious() {
            Logger.d("$TAG -> PIP -> mediaSessionPiPListener -> setSkipToPrevious")
            previousEpisode(isAuto = false)
        }

        override fun getPlaylistContent(): List<Details.Episode>? {
            Logger.d("$TAG -> PIP -> mediaSessionPiPListener -> getPlaylistContent")
            currentEpisode?.let {
                if (it.isTrailer == 1) {
                    return listOf(it)
                }
            }
            return details?.blockEpisode?.episodes
        }

        override fun getCurrentIndexPosition(): Int {
            Logger.d("$TAG -> PIP -> mediaSessionPiPListener -> getCurrentPlayingPosition")
            if (screenType == PlayerHandler.ScreenType.Vod) {
                details?.blockEpisode?.episodes?.let {
                    val startIndex = if (currentEpisode?.isItemOfPlaylist == true) {
                        val index = it.getIndexOf(currentEpisode)
                        if (index != -1) index else 0
                    } else {
                        playerData.episodeIndex ?: 0
                    }
                    return startIndex
                }
            }
            return 0
        }

    }

    private fun startPictureInPicture() {
        Logger.d("$TAG -> PIP -> startPictureInPicture")
        try {
            fragmentActivity?.let { activity ->
                if (PlayerPiPHelper.checkPictureInPicturePermission(activity)) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        updatePictureInPictureParams(isAutoEnterEnable = true)?.let {
                            activity.enterPictureInPictureMode(it)
                        }
                    } else { }
                } else {
                    // Todo: Request permission if need
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun updatePictureInPictureParams(isAutoEnterEnable: Boolean = false): PictureInPictureParams? {
        Logger.d("$TAG -> PIP -> updatePictureInPictureParams -> isAutoEnterEnable: $isAutoEnterEnable -> Build.VERSION.SDK_INT = ${Build.VERSION.SDK_INT}")
        try {
            fragmentActivity?.let {
                if (PlayerPiPHelper.checkPictureInPicturePermission(it)) {
                    val aspectRatio = PlayerPiPHelper.getAspectRatioForPiP(playerVideoSize)
                    val visibleRect = PlayerPiPHelper.getVisibleRectForPiP(binding.epvPlayer, playerVideoSize)
                    if (PlayerPiPHelper.isOsSupportAutoEnablePictureInPicture()) {
                        val params = PictureInPictureParams.Builder()
                            .setAspectRatio(aspectRatio)
                            .setSourceRectHint(visibleRect)
                            .setAutoEnterEnabled(isAutoEnterEnable)
                            .build()
                        it.setPictureInPictureParams(params)
                        return params
                    } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        val params = PictureInPictureParams.Builder()
                            .setAspectRatio(aspectRatio)
                            .setSourceRectHint(visibleRect)
                            .build()
                        it.setPictureInPictureParams(params)
                        return params
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }


    private fun pictureInPictureEnterFullscreen() {
        isFullscreen = true
        binding.playerUI.updateLayouts(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
        playerUIListener?.onFullScreen(isFullscreen = isFullscreen, isLandscapeMode = isLandscapeMode)
        tabletSystemBars()
        // Layout changes
        onLayoutChange()
        //
    }

    private fun pictureInPictureCheckPlayerModeTablet() {
        if (fragmentActivity.isTablet()) {
            PlayerPiPHelper.restorePlayerMode(
                enterFullscreen = {
                    enterFullscreenMode()
                },
                exitFullscreen = {
                    exitFullscreenMode()
                }
            )
        } else {
            when (MainApplication.INSTANCE.applicationContext.resources.configuration.orientation) {
                Configuration.ORIENTATION_LANDSCAPE -> {
                    fragmentActivity?.hideSystemBar()
                }
                Configuration.ORIENTATION_PORTRAIT -> {
                    fragmentActivity?.showSystemBar()
                }
                else -> {}
            }
        }
    }

    private fun checkHideUIElementInPipMode() {
        //
        binding.playerUI.apply {
            doHideAgeRestrictions()
            showNextVideoRecommendation(isShow = false)
            showSkipIntro(isShow = false)
            showSkipCredit(isShow = false)
            hideSituationWarning()
        }
    }

    private fun checkShowUIElementInPipMode() {
        showAndStartSkipIntro()
        startSkipCredits()
        binding.playerUI.checkResumeAgeRestriction()
    }
    //endregion

    private fun showAndStartBuyPackageGuide(buyPackageGuide: BuyPackageGuide, showTimeInMillis: Long = 5000, countDownInterval: Long = 1000) {
        if (countDownTimerBuyPackageGuide != null) {
            countDownTimerBuyPackageGuide?.cancel()
            countDownTimerBuyPackageGuide = null
        }
        buyPackageGuideDisplayed = true
        binding.playerUI.showBuyPackageGuide(buyPackageGuide)
        countDownTimerBuyPackageGuide = object : CountDownTimer(showTimeInMillis, countDownInterval) {
            override fun onTick(millisUntilFinished: Long) {}

            override fun onFinish() {
                hideAndStopBuyPackageGuideInPlayer()
            }
        }.start()
    }

    private fun checkShowBuyPackageGuide(buyPackageGuide: BuyPackageGuide) {

        // Verify number remaining to display buy package guide -> verify equal true to continue
        if(!verifyNumberRemainingToShowGuide()) return

        playerUIListener?.onShowBuyPackageGuide(buyPackageGuide)

        checkShowBuyPackageGuideInPlayer(buyPackageGuide)

    }

    private fun checkShowBuyPackageGuideInPlayer(buyPackageGuide: BuyPackageGuide) {

        if (buyPackageGuideDisplayed) return // Displayed -> Gone

        if (binding.playerUI.isVisible() // Showing player controls -> Gone
            || countDownTimerSkipCredits != null // Showing skip Credits -> Gone
            || skipIntroIsRunning // Showing skip intro -> Gone
            || binding.playerUI.isTooltipShowing() // Showing Tooltip -> Gone
            || isSituationWarningShown // Showing situation warning -> Gone
            || adsLogoShowing // Showing ads logo -> Gone
            || !isFullscreen() // Not fullscreen -> Gone
            || binding.playerUI.isAgeRestrictionShowedInBottom()
        ) {
            buyPackageGuideDisplayed = true
            return
        }

        showAndStartBuyPackageGuide(buyPackageGuide)
    }

    private fun hideAndStopBuyPackageGuideInPlayer() {
        if (countDownTimerBuyPackageGuide != null) {
            countDownTimerBuyPackageGuide?.cancel()
            countDownTimerBuyPackageGuide = null
        }
        binding.playerUI.hideBuyPackageGuide()
    }

    private fun verifyNumberRemainingToShowGuide(): Boolean {

        val lastTime = sharedPreferences.timeSavedSuggestBuyPackageInstreamAds()
        val currentTime = System.currentTimeMillis()

        // reset the number of show buy package guide after the new day
        if (areDifferentDaysUsingCalendar(lastTime, currentTime)) {
            sharedPreferences.saveCurrentSuggestBuyPackageInstreamAdsCount(0)
        }

        val limitNumber = sharedPreferences.limitSuggestBuyPackageInstreamAdsPerDay()
        val currentNumber = sharedPreferences.currentSuggestBuyPackageInstreamAdsCount()

        // check if current number is less than limit number if yes then update current number, last time and return true
        if (currentNumber < limitNumber) {
            // save current number
            sharedPreferences.saveCurrentSuggestBuyPackageInstreamAdsCount(currentNumber + 1)
            // save last time
            sharedPreferences.saveTimeSavedSuggestBuyPackageInstreamAds(currentTime)
            return true
        } else {
            return false
        }
    }

    fun showAlertLimitCcu(title: String, desc: String, retry: String, processReloadStream: () -> Unit = {}) {
        stop(force = true)
        isLimitCcuByMqtt = true
        if(limitCcuBinding == null) {
            limitCcuBinding = binding.vtLimitCcu.commonSafeInflate()
        }
        Timber.tag("LimitCCU").d("showAlertLimitCcu isLimitCcuByMqtt: $isLimitCcuByMqtt canPlayInstreamAd: $canPlayInstreamAd")
        limitCcuBinding?.isVisible = canPlayInstreamAd

        limitCcuBinding?.bringToFront()
        (limitCcuBinding as? ConstraintLayout)?.let {
            val textViewTitle: TextView = it.findViewById(R.id.tv_title)
            val textViewDescription: TextView = it.findViewById(R.id.tv_desc)
            val buttonBack: ImageButton = it.findViewById(R.id.ib_back)
            val buttonRetry: ConstraintLayout = it.findViewById(R.id.btn_retry)
            val textViewRetry: TextView = it.findViewById(R.id.tv_retry)
            val loading: LottieAnimationView = it.findViewById(R.id.v_loading)
            textViewTitle.text = title.ifBlank { sharedPreferences.getMqttTitleVn() }.ifBlank { it.context.getString(R.string.mqtt_limit_title_vn) }
            textViewDescription.text = desc.ifBlank { sharedPreferences.getMqttDescVn() }.ifBlank { it.context.getString(R.string.mqtt_limit_desc_vn) }
            buttonRetry.isEnabled = true
            textViewRetry.text = retry
            textViewRetry.isInvisible = false
            loading.isVisible = false
            buttonBack.setOnClickListener {
                fragmentActivity?.onBackPressed()
            }
            buttonRetry.setOnClickListener {
                isLimitCcuByMqtt = false
                buttonRetry.isEnabled = false
                processReloadStream()
                textViewRetry.isInvisible = true
                loading.isVisible = true
            }
        }
    }

    fun hideAlertLimitCcu() {
        stop(force = true)
        isLimitCcuByMqtt = false
        Timber.tag("LimitCCU").d("hideAlertLimitCcu isLimitCcuByMqtt: $isLimitCcuByMqtt")
        limitCcuBinding?.let {
            it.isVisible = false
        }
    }

    fun setCodeLimitCcu(code: String) {
        codeLimitCcu = code
    }
}