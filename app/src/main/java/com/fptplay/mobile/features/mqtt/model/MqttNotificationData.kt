package com.fptplay.mobile.features.mqtt.model

import com.fptplay.mobile.features.mqtt.model.MqttAutomaticRetry.Companion.emptyMqttAutomaticRetry
import com.fptplay.mobile.features.mqtt.model.MqttConnectionConfig.Companion.emptyMqttConnectionConfig
import com.fptplay.mobile.features.mqtt.model.MqttNotificationConfig.Companion.emptyMqttNotificationConfig
import com.google.gson.annotations.SerializedName
import com.xhbadxx.projects.module.domain.entity.fplay.common.MQTTConfig

data class MqttNotification(
    @SerializedName("module")
    val module: String = "",

    @SerializedName("detail")
    var detail: MqttNotificationDetail? = null
)

data class MqttNotificationDetail(
    @SerializedName("notification_type")
    val notificationType: String = "",

    @SerializedName("mqtt")
    val mqtt: MqttNotificationConfig? = null
){
    companion object{
        @JvmStatic
        val emptyMqttNotificationDetail = MqttNotificationDetail(
            notificationType = "",
            mqtt = emptyMqttNotificationConfig
        )
    }
}
data class MqttConnectionConfig(
    @SerializedName("expires")
    var expires: String? = "",

    @SerializedName("jwtFrom")
    var jwtFrom: String? = "",

    @SerializedName("rooms")
    var option: MQTTConfig.Option?,

    @SerializedName("token")
    var token: String? = "",

    @SerializedName("url")
    var url: MQTTConfig.Url?,

){
    companion object{
        @JvmStatic
        val emptyMqttConnectionConfig = MqttConnectionConfig(
            expires = "",
            jwtFrom = "",
            option = MQTTConfig.Option(),
            token = "",
            url = MQTTConfig.Url()
        )
    }
}

data class MqttNotificationConfig(
    @SerializedName("enable")
    var enable: String? = "0",
    @SerializedName("emmenency_rooms")
    var rooms: List<MqttRoom>? = emptyList() ,

    @SerializedName("automatic_retry")
    var automaticRetry: MqttAutomaticRetry?= null,

    @SerializedName("options")
    var options: List<MqttModeOption>? = null,

    @SerializedName("config")
    var config: MqttConnectionConfig? = null,

    @SerializedName("emergency_enable_random_delay")
    var emergencyEnableRandomDelay: String? = ""
){
    companion object{
        @JvmStatic
        val emptyMqttNotificationConfig = MqttNotificationConfig(
            enable = "0",
            rooms = listOf(),
            automaticRetry = emptyMqttAutomaticRetry,
            options = emptyList(),
            config = emptyMqttConnectionConfig
        )
        val emptyMqttRoomList = listOf(
            MqttRoom(
                roomId = "android",
                roomType = "mqtt"
            ),
            MqttRoom(
                roomId = "android-beta",
                roomType = "mqtt"
            )
        )

    }
}

data class MqttAutomaticRetry(
    @SerializedName("enable")
    var enable: String? = "0",

    @SerializedName("max_retry_interval")
    var maxRetryInterval: String? = "",

    @SerializedName("min_retry_interval")
    var minRetryInterval: String? = "",

    @SerializedName("random")
    var random: String? = ""
){
    companion object{
        @JvmStatic
        val emptyMqttAutomaticRetry = MqttAutomaticRetry(
            enable = "0",
            maxRetryInterval = null,
            minRetryInterval = null,
            random = null
        )
    }
}

data class MqttModeOption(
    @SerializedName("mqtt_mode")
    var mqttMode: String? = "",

    @SerializedName("waiting_approval")
    var waitingApproval: String? = "",

    @SerializedName("enable_backup_api")
    var enableBackupApi: String? = "",

    @SerializedName("max_retry_backup_api")
    var maxRetryBackupApi: String? = "",

    @SerializedName("preview_waiting_approval")
    var previewWaitingApproval: String? = ""
){
    companion object{
        @JvmStatic
        val emptyMqttModeOption = MqttModeOption(
            mqttMode = "1",
            waitingApproval = null,
            enableBackupApi = null,
            maxRetryBackupApi = null,
            previewWaitingApproval = null
        )
    }
}

data class MqttRoom(
    @SerializedName("room_id")
    var roomId: String = "",
    @SerializedName("room_type")
    var roomType: String = "",
){
    companion object{
        @JvmStatic
        val emptyMqttRoom = MqttRoom(
            roomId = "",
            roomType = ""
        )
    }
}

