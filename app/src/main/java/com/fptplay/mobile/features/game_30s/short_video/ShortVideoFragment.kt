package com.fptplay.mobile.features.game_30s.short_video

import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.database.ContentObserver
import android.graphics.Paint
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.util.UnstableApi
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.BuildConfig
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.removeParent
import com.fptplay.mobile.common.global.GlobalEvent
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.DateTimeUtils
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.common.utils.viewutils.ShareLinkLocalUtils
import com.fptplay.mobile.databinding.ShortVideoFragmentBinding
import com.fptplay.mobile.databinding.ShortVideoGuidelineLayoutBinding
import com.fptplay.mobile.features.game_30s.Game30sViewModel
import com.fptplay.mobile.features.game_30s.short_video.adapter.ShortVideoAdapter
import com.fptplay.mobile.player.utils.isEnableAutoScreenRotationInSettings
import com.tear.modules.player.exo.ExoPlayerProxy
import com.tear.modules.player.exo.ExoPlayerView
import com.tear.modules.player.util.IPlayer
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.game.game30s.Game30sMemberVideo
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.*
import javax.inject.Inject

@UnstableApi
@AndroidEntryPoint
class ShortVideoFragment : BaseFragment<Game30sViewModel.Game30sState, Game30sViewModel.Game30sIntent>() {
    override val viewModel: Game30sViewModel by activityViewModels()

    //region Variables
    override val hasEdgeToEdge: Boolean = true
    private val safeArgs by navArgs<ShortVideoFragmentArgs>()
    private var _binding: ShortVideoFragmentBinding? = null
    private val binding get() = _binding!!

    private var _stubBinding: ShortVideoGuidelineLayoutBinding? = null
    private val stubBinding get() = _stubBinding!!

    @Inject
    lateinit var sharedPreferences: SharedPreferences
    private lateinit var shortVideoAdapter: ShortVideoAdapter
    private var teamId: String = ""
    private var gameId: String = ""
    private var videoId: String = ""
    @Inject
    lateinit var trackingProxy: TrackingProxy
    @Inject
    lateinit var trackingInfo: Infor
    private var timerRealTimePlaying : Timer?= null
    private var currentDuration = 0L
    private var realTimePlaying = 0
    //player
    private var playerView: ExoPlayerView? = null
    private var player: IPlayer? = null
    private var playerCallback: IPlayer.IPlayerCallback? = null
    private var isLandscapeMode = false
    private val pagerSnapHelper by lazy { PagerSnapHelper() }
    private var currentPlayPos = -1
    private val videoScrollListener by lazy {
        object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    val llm = (recyclerView.layoutManager as LinearLayoutManager)
                    val activeItemPosition = llm.findFirstCompletelyVisibleItemPosition()
                    triggerPlayItem(activeItemPosition)
                }
            }
        }
    }

    //endregion
    //region Overrides
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = ShortVideoFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        viewLifecycleOwner.lifecycle.removeObserver(player as ExoPlayerProxy)
        GlobalEvent.unRegisterEvent(GlobalEvent.VOTE_SHORT_VIDEO_EVENT)
        stopCountRealTimePlaying()
        resetOrientation()
        super.onDestroyView()
        _binding = null
    }

    override fun bindComponent() {
        try {
            teamId = safeArgs.teamId
            gameId = safeArgs.gameId
            videoId = safeArgs.videoId
        } catch (e: Exception) {
            e.printStackTrace()
        }
        binding.rvShortVideo.apply {
            layoutManager = LinearLayoutManager(context)
            pagerSnapHelper.attachToRecyclerView(this)
            addOnScrollListener(videoScrollListener)
            setItemViewCacheSize(6)
        }
        if (sharedPreferences.isShow30sGameGuideline()) {
            showGuideline()
        }

        handleScreenRotation()
        initPlayer()
    }

    override fun bindData() {
        if (sharedPreferences.userLogin()) {
            if (teamId.isNotBlank()) {
                viewModel.dispatchIntent(
                    Game30sViewModel.Game30sIntent.GetCollection(
                        teamId,
                        sharedPreferences.userId(),
                        gameId = gameId
                    )
                )
            }
        } else {
            navigateToLoginWithParams(isDirect = true)
        }

    }

    override fun bindEvent() {
        binding.toolbar.setNavigationOnClickListener {
            findNavController().navigateUp()
        }
    }

    override fun retryLoadPage() {
        bindData()
    }

    private fun initPlayer() {
        player = ExoPlayerProxy(
            context = requireContext(),
            useCronetForNetworking = true,
            requireMinimumResolutionH265 = "",
            requireMinimumResolutionH265HDR = "",
            requireMinimumResolutionAV1 = "",
            requireMinimumResolutionVP9 = "",
            requireMinimumResolutionDolbyVision = "",
        )
        viewLifecycleOwner.lifecycle.apply {
            player?.let { addObserver(it as ExoPlayerProxy) }
        }
        playerView = ExoPlayerView(requireContext()).apply {
            setResizeMode(AspectRatioFrameLayout.RESIZE_MODE_FIT)
            useControl(false)
            updateShowProgressWhenBuffering(false)
            player?.setInternalPlayerView(this)
        }
    }
    private fun bindAndPlay(
        data: Game30sMemberVideo?,
        viewHolder: ShortVideoAdapter.ShortVideoItemViewHolder?
    ) {
        viewHolder?.run {
            if (playerView != null) {
                playerView?.removeParent()
            } else {
                playerView = ExoPlayerView(requireContext()).apply {
                    useControl(false)
                    updateShowProgressWhenBuffering(false)
                }
            }
            player?.stop(true)
            if (data?.videos?.isNotEmpty() == true && data.videos[0].hls.isNotBlank()) {
                playerView?.apply {
                    bindPlayerView(this)
                }
                player?.run {
                    resetPlayerListener(viewHolder, data)
                    prepare(
                        request = IPlayer.Request(
                            url = IPlayer.Request.Url(url = data.videos[0].hls),
                            startPosition = 0,
                            forceUsingStartPosition = true,
                            clearRequestWhenOnStop = false,
                        )
                    )
                }

            }
        }
    }
    private fun resetPlayerListener(viewHolder: ShortVideoAdapter.ShortVideoItemViewHolder,data: Game30sMemberVideo) {
        playerCallback?.let {
            player?.removePlayerCallback(it)
        }
        playerCallback = object : IPlayer.IPlayerCallback {
                override fun onPrepare() {
                    super.onPrepare()
                    showBlurLoading()
                    viewHolder.bindLayout()
                    viewHolder.playEffect()
                    startCountRealTimePlaying()
                }
                override fun onReady() {
                    super.onReady()
                    hideBlurLoading()
                    if (data?.videos?.isNotEmpty() == true && data.videos[0].hls.isNotBlank()) {
                        sendTrackingShortVideo(
                            logId = "51",
                            event = "StartMovie",
                            id = data.videos[0].id,
                            title = data.titleVietnam ?: "",
                            url = player?.request?.url?.url ?: "",
                        )
                    }
                }
                override fun onEnd() {
                    if (data?.videos?.isNotEmpty() == true && data.videos[0].hls.isNotBlank()) {
                        sendTrackingShortVideo(
                            logId = "52",
                            event = "StopMovie",
                            id =data.videos[0].id,
                            title = data.titleVietnam?:"",
                            url = player?.request?.url?.url ?: "",
                            totalDuration = getTotalDurationWithCondition("52",player,player?.totalDuration()?:0L),
                            currentDuration = getElapsedTimePlayingWithCondition("52",player?.currentDuration()?:0L),
                            realTimePlaying = getRealTimePlayingWithCondition("52",realTimePlaying),
                        )
                        TrackingUtil.resetIsRecommend() //reset when stop
                    }
                    player?.seek(0)
                    if (data?.videos?.isNotEmpty() == true && data.videos[0].hls.isNotBlank()) {
                        sendTrackingShortVideo(
                            logId = "51",
                            event = "StartMovie",
                            id = data.videos[0].id,
                            title = data.titleVietnam ?: "",
                            url = player?.request?.url?.url ?: "",
                        )
                    }
                    startCountRealTimePlaying()
                }

            override fun onStop() {
                super.onStop()
                if (data?.videos?.isNotEmpty() == true && data.videos[0].hls.isNotBlank()) {
                    sendTrackingShortVideo(
                        logId = "52",
                        event = "StopMovie",
                        id =data.videos[0].id,
                        title = data.titleVietnam?:"",
                        url = player?.request?.url?.url ?: "",
                        totalDuration = getTotalDurationWithCondition("52",player,player?.totalDuration()?:0L),
                        currentDuration = getElapsedTimePlayingWithCondition("52",player?.currentDuration()?:0L),
                        realTimePlaying = getRealTimePlayingWithCondition("52",realTimePlaying),
                    )
                    TrackingUtil.resetIsRecommend() //reset when stop
                }
                stopCountRealTimePlaying()
            }
                override fun onPause() {
                    super.onPause()
                    viewHolder.pauseEffect()
                }
                override fun onPlay() {
                    super.onPlay()
                    viewHolder.playEffect()
                }
            }
        (player as ExoPlayerProxy).addPlayerCallback(playerCallback!!)
    }
    private fun triggerPlayItem(activeItemPosition: Int) {
        Timber.d("******cur: $currentPlayPos - up $activeItemPosition")
        if(activeItemPosition >=0 && currentPlayPos != activeItemPosition) {
            val currentDataPlay = shortVideoAdapter.item(activeItemPosition)

            currentPlayPos = activeItemPosition
            if (activeItemPosition >= 0 && activeItemPosition < shortVideoAdapter.size()) {
                if(_binding!=null){
                    val viewHolder =
                        (binding.rvShortVideo.findViewHolderForAdapterPosition(activeItemPosition) as? ShortVideoAdapter.ShortVideoItemViewHolder)
                    bindAndPlay(shortVideoAdapter.item(activeItemPosition), viewHolder)
                }
            }
        }
    }
    override fun Game30sViewModel.Game30sState.toUI() {
        when (this) {
            is Game30sViewModel.Game30sState.Loading -> {
                showLoading()
            }
            is Game30sViewModel.Game30sState.Error -> {
                hideLoading()
                showPageLightError(binding.flError, getString(R.string.server_error))
            }
            is Game30sViewModel.Game30sState.ErrorNoInternet -> {
                hideLoading()
                showPageLightError(binding.flError, message)
            }
            is Game30sViewModel.Game30sState.Done -> {
                hideLoading()
            }
            is Game30sViewModel.Game30sState.ResultGetCollection -> {
                hideLoading()
                if (this.data.isNotEmpty()) {
                    val pos = checkSelectedPositionVideo(videoId, safeArgs.selectedPos, this.data)
                    bindShortVideo(this.data, pos)
                } else {
                    showPageLightError(binding.flError, this.statusMessage)
                }
            }
            is Game30sViewModel.Game30sState.ResultVoteShortVideo -> {
                hideLoading()
                if (this.statusCode == 200 || this.statusCode == 205) {
                    shortVideoAdapter.item(this.votePos)?.isVoted = true
                    GlobalEvent.pushEvent(GlobalEvent.VOTE_SHORT_VIDEO_EVENT, this.memberId)
                } else Toast.makeText(context, this.errorMessage, Toast.LENGTH_SHORT).show()
            }
            else -> {
                hideLoading()
            }
        }
    }
    //endregion
    private fun showGuideline() {
        _stubBinding ?: run {
            binding.vsGuideline.apply {
                setOnInflateListener { _, inflatedView ->
                    _stubBinding = ShortVideoGuidelineLayoutBinding.bind(inflatedView)
                }
                inflate()
            }
        }
        stubBinding.apply {
            root.show()
            tvConfirm.apply {
                paintFlags = tvConfirm.paintFlags or Paint.UNDERLINE_TEXT_FLAG
                setOnClickListener {
                    sharedPreferences.saveIsShow30sGameGuideline(false)
                    root.hide()
                }
            }
        }
    }
    private fun checkSelectedPositionVideo(videoId: String, position: Int?, listVideo: List<Game30sMemberVideo>): Int {
        if (videoId.isNotBlank()) {
            try {
                listVideo.forEachIndexed { index, game30sMemberVideo ->
                    if (game30sMemberVideo.id == videoId)
                        return index
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return position ?: 0
    }
    private fun bindShortVideo(items: List<Game30sMemberVideo>, selectedPosition: Int) {
        shortVideoAdapter = ShortVideoAdapter()
        shortVideoAdapter.setHasStableIds(true)
        binding.rvShortVideo.adapter = shortVideoAdapter
        shortVideoAdapter.bind(items) {
            scrollToVideo(false, selectedPosition)
        }
        shortVideoAdapter.eventListener = object : IEventListener<Game30sMemberVideo> {
            override fun onClickView(position: Int, view: View?, data: Game30sMemberVideo) {
                when (view?.id) {
                    R.id.fl_player -> {
                        if (player?.isPlaying() == true) {
                            player?.pause(true)
                            if (data?.videos?.isNotEmpty() == true && data.videos[0].hls.isNotBlank()) {
                                sendTrackingShortVideo(
                                    logId = "53",
                                    event = "PauseMovie",
                                    id = data.videos[0].id,
                                    title = data.titleVietnam ?: "",
                                    url = player?.request?.url?.url ?: "",
                                    totalDuration = getTotalDurationWithCondition(
                                        "53",
                                        player,
                                        player?.totalDuration() ?: 0L
                                    ),
                                    currentDuration = getElapsedTimePlayingWithCondition(
                                        "53",
                                        player?.currentDuration() ?: 0L
                                    ),
                                    realTimePlaying = getRealTimePlayingWithCondition(
                                        "53",
                                        realTimePlaying
                                    ),
                                )
                            }
                        } else {
                            player?.play(true)
                            if (data?.videos?.isNotEmpty() == true && data.videos[0].hls.isNotBlank()) {
                                sendTrackingShortVideo(
                                logId = "54",
                                event = "ResumeMovie",
                                id =data.videos[0].id,
                                title = data.titleVietnam?:"", url = player?.request?.url?.url ?: "",
                                totalDuration = getTotalDurationWithCondition("54",player,player?.totalDuration()?:0L),
                                currentDuration = getElapsedTimePlayingWithCondition("54",player?.currentDuration()?:0L),
                                realTimePlaying = getRealTimePlayingWithCondition("54",realTimePlaying),
                                )
                            }
                        }
                    }
                    R.id.layout_share -> {
                        ShareLinkLocalUtils.onShareLink(binding.root.context, data.deepLink)
                    }
                    R.id.lnl_layout_vote -> {
                        viewModel.dispatchIntent(
                            Game30sViewModel.Game30sIntent.VoteShortVideo(
                                fptPlayId = sharedPreferences.userId(), memberId = data.id,
                                matches = "0",
                                phoneNumber = sharedPreferences.userPhone(),
                                votePos = position
                            )
                        )
                    }
                }
            }
        }
    }
    private fun startCountRealTimePlaying() {
        stopCountRealTimePlaying()
        timerRealTimePlaying = Timer()
        timerRealTimePlaying?.scheduleAtFixedRate(
            object: TimerTask() {
                override fun run() {
                    lifecycleScope.launch(Dispatchers.Main) {
                        if (_binding != null && player?.isPlaying() == true) {
                            currentDuration = player?.currentDuration()?:0L
                            realTimePlaying += 1
                        }
                    }
                }
            }, 1000, 1000)
    }
    private fun stopCountRealTimePlaying() {
        realTimePlaying = 0
        if (timerRealTimePlaying != null) {
            timerRealTimePlaying?.cancel()
            timerRealTimePlaying = null
        }
    }
    @SuppressLint("SimpleDateFormat")
    private fun sendTrackingShortVideo(logId:String,screen:String = "General",event:String,id:String,url: String = "",title:String,
    totalDuration:String="",currentDuration:String="",realTimePlaying:String="",errorCode:String="",errorMessage:String=""){
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = logId,
                appId = TrackingUtil.currentAppId,
                appName = "NgôiSao30s",
                screen = screen,
                event = event,
                chapterId = "",
                itemId = id,
                itemName = title?:"",
                url =url?:"",
                directors = "",
                publishCountry  = "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = "0",
                subMenuId ="",
                duration = totalDuration,
                elapsedTimePlaying = currentDuration,
                realTimePlaying = realTimePlaying,
                keyword = "",
                idRelated = "",
                errorCode = errorCode,
                errorMessage = errorMessage,
                videoQuality = "auto_vip",
                businessPlan = "",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                isRecommend = TrackingUtil.isRecommend
            ))
    }
    private fun getTotalDurationWithCondition(logId: String,player:IPlayer?,totalDuration:Long) : String {
        return if (logId == "52") (totalDuration / 1000).toString() else (player?.totalDuration()?:0L / 1000L).toString()
    }
    private fun getElapsedTimePlayingWithCondition(logId: String,currentDuration:Long) : String {
        return if (logId == "52" || logId == "53") (currentDuration / 1000).toString() else ""
    }
    private fun getRealTimePlayingWithCondition(logId: String,timerRealTimePlaying:Int) : String {
        return if (logId == "52" || logId == "53") timerRealTimePlaying.toString() else ""
    }
    private fun scrollToVideo(smoothScroll: Boolean, position: Int) {
        if (binding.rvShortVideo.scrollState != RecyclerView.SCROLL_STATE_IDLE) return
        if (smoothScroll) {
            binding.rvShortVideo.smoothScrollToPosition(position)
        } else {
            binding.rvShortVideo.scrollToPosition(position)
        }
        binding.rvShortVideo.postDelayed({
            triggerPlayItem(position)
        }, 400L)
    }

    private fun handleScreenRotation() {
        activity?.run {
            if (isEnableAutoScreenRotationInSettings()) {
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR
            }
        }
    }

    private fun resetOrientation() {
        if (context.isTablet()) {
            activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        } else {
            activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        when (newConfig.orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> {
                if (context.isTablet()) {
                    isLandscapeMode = true
                    switchToLandscape()
                }
            }
            Configuration.ORIENTATION_PORTRAIT -> {
                if (context.isTablet()) {
                    isLandscapeMode = false
                    switchToPortrait()
                }
            }
            else -> {}
        }
    }


    private fun switchToLandscape() {
        activity?.run {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
            if (isEnableAutoScreenRotationInSettings()) {
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR
            }
            if(_binding!=null){
                binding.root.postDelayed({
                    val viewHolder = (binding.rvShortVideo.findViewHolderForAdapterPosition(currentPlayPos) as? ShortVideoAdapter.ShortVideoItemViewHolder)
                    viewHolder?.updateLayout(true)
                },200)
            }
        }
    }

    private fun switchToPortrait() {
        activity?.run {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
            if (isEnableAutoScreenRotationInSettings()) {
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR
            }
            if(_binding!=null){
                binding.root.postDelayed({
                    val viewHolder = (binding.rvShortVideo.findViewHolderForAdapterPosition(currentPlayPos) as? ShortVideoAdapter.ShortVideoItemViewHolder)
                    viewHolder?.updateLayout(false)
                },200)
            }
        }
    }


}