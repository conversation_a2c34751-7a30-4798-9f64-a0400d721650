<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/player_playlist_overlay_item_wrapper_width"
    android:layout_height="@dimen/player_playlist_overlay_item_wrapper_height">

    <androidx.cardview.widget.CardView
        android:id="@+id/thumb_container"
        android:layout_width="0dp"
        android:layout_height="@dimen/player_playlist_overlay_item_poster_height"
        android:layout_margin="@dimen/item_poster_margin_small"
        android:background="@color/place_holder_color"
        android:translationZ="0dp"
        app:cardBackgroundColor="@color/transparent"
        app:cardCornerRadius="@dimen/corner_medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_thumb"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:translationZ="@dimen/_40sdp" />

        <ProgressBar
            android:id="@+id/pb_time_watched"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="@dimen/item_horizontal_slider_progress_height"
            android:layout_gravity="bottom"
            android:background="@drawable/progress_bar_bg"
            android:max="100"
            android:progress="50"
            android:progressDrawable="@drawable/progress_bar_progress_style"
            android:translationZ="@dimen/_50sdp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible" />

    </androidx.cardview.widget.CardView>

    <ImageView
        android:id="@+id/iv_ribbon_payment"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/player_playlist_overlay_list_item_ribbon_height"
        android:layout_marginStart="@dimen/player_playlist_overlay_list_item_ribbon_margin"
        android:layout_marginTop="@dimen/player_playlist_overlay_list_item_ribbon_margin"
        android:adjustViewBounds="true"
        android:translationZ="@dimen/_50sdp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iv_playing"
        android:layout_width="@dimen/player_playlist_overlay_item_playing_status_size"
        android:layout_height="@dimen/player_playlist_overlay_item_playing_status_size"
        android:src="@drawable/player_playlist_overlay_playing_status"
        android:translationZ="@dimen/_50sdp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/thumb_container"
        app:layout_constraintEnd_toEndOf="@id/thumb_container"
        app:layout_constraintStart_toStartOf="@id/thumb_container"
        app:layout_constraintTop_toTopOf="@id/thumb_container" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_ribbon"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/thumb_container"
        app:layout_constraintEnd_toEndOf="@id/thumb_container"
        app:layout_constraintStart_toStartOf="@id/thumb_container"
        app:layout_constraintTop_toTopOf="@id/thumb_container">

        <include layout="@layout/block_item_ribbon" />

        <include layout="@layout/block_item_playlist" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.fptplay.mobile.features.poster_overlay.PosterOverlayView
        android:id="@+id/poster_overlay"
        android:layout_width="@dimen/player_playlist_overlay_item_wrapper_width"
        android:layout_height="@dimen/player_playlist_overlay_item_poster_wrapper_height"
        android:translationZ="@dimen/_40sdp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_medium"
        android:layout_marginBottom="@dimen/margin_medium"
        android:background="@drawable/rectangle_dark_bg_round_corner"
        android:fontFamily="@font/sf_pro_display_medium"
        android:paddingHorizontal="@dimen/margin_medium"
        android:textColor="@color/white_87"
        android:textSize="@dimen/player_playlist_overlay_time_text_size"
        android:translationZ="@dimen/_50sdp"
        app:layout_constraintBottom_toBottomOf="@id/thumb_container"
        app:layout_constraintStart_toStartOf="@id/thumb_container"
        tools:text="52 phut" />


    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/item_auto_expand_title_margin_top"
        android:ellipsize="end"
        android:fontFamily="@font/sf_pro_display_regular"
        android:maxLines="1"
        android:textColor="@color/white_87"
        android:textSize="@dimen/player_playlist_overlay_title_text_size"
        app:layout_constraintEnd_toStartOf="@id/btn_book"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/thumb_container" />

    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:ellipsize="end"
        android:fontFamily="@font/sf_pro_display_regular"
        android:maxLines="2"
        android:textColor="@color/white_60"
        android:textSize="@dimen/player_playlist_overlay_sub_title_text_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

</androidx.constraintlayout.widget.ConstraintLayout>