package com.fptplay.mobile.features.pladio.service.notification

import android.annotation.SuppressLint
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.ComponentName
import android.content.Intent
import android.graphics.BitmapFactory
import android.os.Build
import androidx.annotation.OptIn
import androidx.core.app.NotificationCompat
import androidx.media3.common.util.UnstableApi
import androidx.media3.session.MediaSession
import androidx.media3.session.MediaStyleNotificationHelper
import com.fptplay.mobile.HomeActivity
import com.fptplay.mobile.R
import com.fptplay.mobile.common.utils.DeeplinkConstants
import com.fptplay.mobile.features.pladio.data.Song
import com.fptplay.mobile.features.pladio.service.PlaybackService
import com.fptplay.mobile.features.pladio.service.PlaybackService.Companion.ACTION_QUIT
import com.fptplay.mobile.features.pladio.service.PlaybackService.Companion.ACTION_REWIND
import com.fptplay.mobile.features.pladio.service.PlaybackService.Companion.ACTION_SKIP
import com.fptplay.mobile.features.pladio.service.PlaybackService.Companion.ACTION_TOGGLE_PAUSE
import com.fptplay.mobile.features.pladio.util.PladioUtil.loadBitmapPalette

@SuppressLint("RestrictedApi")
@OptIn(UnstableApi::class)
class PlayingNotificationImpl24(
    val context: PlaybackService,
    val mediaSession: MediaSession,
) : PlayingNotification(context) {

    init {
        val action = Intent(context, HomeActivity::class.java)
        action.putExtra(DeeplinkConstants.REQUIRE_RESUME_ACTIVITY, true)
        val clickIntent =
            PendingIntent.getActivity(
                context,
                0,
                action,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

        val serviceName = ComponentName(context, PlaybackService::class.java)
        val intent = Intent(ACTION_QUIT)
        intent.component = serviceName
        val deleteIntent = PendingIntent.getService(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or (PendingIntent.FLAG_IMMUTABLE)
        )
        val playPauseAction = buildPlayAction(true)
        val previousAction = NotificationCompat.Action(
            R.drawable.pladio_noti_ic_skip_previous,
            context.getString(R.string.pladio_player_control_previous_text),
            retrievePlaybackAction(ACTION_REWIND)
        )
        val nextAction = NotificationCompat.Action(
            R.drawable.pladio_noti_ic_skip_next,
            context.getString(R.string.pladio_player_control_next_text),
            retrievePlaybackAction(ACTION_SKIP)
        )
        val dismissAction = NotificationCompat.Action(
            R.drawable.pladio_player_control_quit_enable,
            context.getString(R.string.pladio_player_control_cancel_text),
            retrievePlaybackAction(ACTION_QUIT)
        )
        setSmallIcon( R.drawable.ic_notification_transparent)
        setContentIntent(clickIntent)
        setDeleteIntent(deleteIntent)
        setShowWhen(false)
        setColorized(true)
        addAction(previousAction)
        addAction(playPauseAction)
        addAction(nextAction)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            addAction(dismissAction)
        }

        setStyle(
            MediaStyleNotificationHelper.MediaStyle(mediaSession)
                .setShowActionsInCompactView(0, 1, 2)
        )
        setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
    }

    override fun updateMetadata(song: Song, onUpdate: () -> Unit) {
        if (song == Song.emptySong) return
        setContentTitle(song.title)
        val description = if (song.artist.isNullOrBlank()) {
            song.playlistTitle
        } else song.artist
        setContentText(description)
//        setSubText(song.albumName)
        val bigNotificationImageSize = context.resources
            .getDimensionPixelSize(R.dimen.pladio_notification_expanded_big_image_size)

        context.loadBitmapPalette(
            url = song.posterUrl ?: "",
            isCenterCrop = true,
            onCompleted = { dominantColor, bitmap ->
                setLargeIcon(bitmap)
                onUpdate()
            },
            onLoadFailed = { _ ->
                setLargeIcon(
                    BitmapFactory.decodeResource(
                        context.resources,
                        R.drawable.pladio_bottom_navigation_pladio_icon_focus
                    )
                )
                onUpdate()
            },
            onLoadCleared = { _ ->
                setLargeIcon(
                    BitmapFactory.decodeResource(
                        context.resources,
                        R.drawable.pladio_bottom_navigation_pladio_icon_focus
                    )
                )
                onUpdate()
            }
        )
    }

    private fun buildPlayAction(isPlaying: Boolean): NotificationCompat.Action {
        val playButtonResId =
            if (isPlaying) R.drawable.pladio_noti_ic_pause_48 else R.drawable.pladio_noti_ic_play_48
        return NotificationCompat.Action.Builder(
            playButtonResId,
            context.getString(R.string.pladio_player_control_play_pause_text),
            retrievePlaybackAction(ACTION_TOGGLE_PAUSE)
        ).build()
    }

    override fun setPlaying(isPlaying: Boolean) {
        mActions[1] = buildPlayAction(isPlaying)
    }

    override fun updateFavorite(isFavorite: Boolean) {
//        mActions[0] = buildFavoriteAction(isFavorite)
    }

    private fun retrievePlaybackAction(action: String): PendingIntent {
        val serviceName = ComponentName(context, PlaybackService::class.java)
        val intent = Intent(action)
        intent.component = serviceName
        return PendingIntent.getService(
            context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT or
                    PendingIntent.FLAG_IMMUTABLE
        )
    }

    companion object {

        fun from(
            context: PlaybackService,
            notificationManager: NotificationManager,
            mediaSession: MediaSession,
        ): PlayingNotification {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                createNotificationChannel(context, notificationManager)
            }
            return PlayingNotificationImpl24(context, mediaSession)
        }
    }
}