package com.fptplay.mobile.features.mega

import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.ui.view.WarningDialogFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.TrackingConstants
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.common.utils.ZendeskUtils
import com.fptplay.mobile.databinding.MegaFragmentBinding
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.adjust.SourcePage
import com.fptplay.mobile.features.ads.utils.AdsUtils
import com.fptplay.mobile.features.home.HomeMainFragment
import com.fptplay.mobile.features.mega.adapter.MegaMenuV3Adapter
import com.fptplay.mobile.features.mega.data.BlockAccountLogout
import com.fptplay.mobile.features.mega.util.CheckNavigateMegaUtils
import com.fptplay.mobile.features.mega.util.FoxpayUtils
import com.fptplay.mobile.features.mega.util.MegaMenuItemDecoration
import com.fptplay.mobile.features.mqtt.MqttConnectManager
import com.fptplay.mobile.features.multi_profile.model.ProfileItem
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.player.utils.PlayerPiPHelper
import com.ftel.foxpay.foxsdk.feature.FoxSdkManager
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenu
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenuItem
import com.xhbadxx.projects.module.domain.entity.fplay.user.UserInfo
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class MegaFragmentV3 : BaseFragment<MegaViewModel.MegaState, MegaViewModel.MegaIntent>() {
    override val hasEdgeToEdge: Boolean = true

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    override val viewModel: MegaViewModel by activityViewModels()
    val megaNavigateViewModel: MegaNavigateViewModel by activityViewModels()
    private var _binding: MegaFragmentBinding? = null
    private val binding get() = _binding!!
    private var megaAppId = ""
    private var megaAppName = ""

    private var oldProfileType = ""
    private var oldProfileId = ""

    private val megaMenusV2 by lazy {
        val blockProfile = if (::sharedPreferences.isInitialized && sharedPreferences.userLogin()) {

            generateProfileMenu(
                userInfo = UserInfo(
                    name = sharedPreferences.displayName(),
                    phone = sharedPreferences.userPhone(),
                    avatar = sharedPreferences.userAvatar()
                ),
                profileItem = if (sharedPreferences.profileId().isNotBlank()) {
                    ProfileItem.Profile(
                        name = sharedPreferences.profileName(),
                        avatar = sharedPreferences.profileAvatar(),
                        id = sharedPreferences.profileId()
                    )
                } else null

            )

        } else {
            generateProfileMenu(null)

        }

        arrayListOf(blockProfile).apply {
            generateLogoutMenu(sharedPreferences.userLogin())?.let {
                add(it)
            }
        }

    }

    //    private val menuAdapter by lazy { MegaMenuAdapter(binding.root.context) }
    private val menuAdapter by lazy { MegaMenuV3Adapter(binding.root.context, sharedPreferences) }
    private val menuMarginTop by lazy {
        requireContext().resources.getDimensionPixelSize(R.dimen.mega_block_item_margin_top)
    }
    private var isFirstInit = true
    private var warningDialogFragment: WarningDialogFragment? = null

    private val sdkManager: FoxSdkManager.Companion by lazy {
        FoxpayUtils.initFoxpaySDK(sharedPreferences.linkingToken(), requireContext())
    }

    private val checkNavigateMegaUtils: CheckNavigateMegaUtils by lazy {
        CheckNavigateMegaUtils(
            context = requireContext(),
            sharedPreferences = MainApplication.INSTANCE.sharedPreferences,
            megaViewModel = viewModel,
            megaNavigateViewModel = megaNavigateViewModel,
            navHostFragment = parentFragment?.parentFragment,
            checkNavigateLoyaltyUtils = checkNavigateLoyaltyUtils,
            foxPaySdkManager = sdkManager
        )
    }

    override fun onResume() {
        super.onResume()
        AdjustAllEvent.clearData()
        AdjustAllEvent.dataCur.sourcePage = SourcePage.mega_module
        if (isFirstInit) {
            getAllInfoOnInit()
            menuAdapter.bind(megaMenusV2.copy())
        } else {
            val menuAccount = generateProfileMenu(
                UserInfo(
                    name = sharedPreferences.displayName(),
                    phone = sharedPreferences.userPhone(),
                    avatar = sharedPreferences.userAvatar()
                ),
                profileItem = if (sharedPreferences.profileId().isNotBlank()) {
                    ProfileItem.Profile(
                        name = sharedPreferences.profileName(),
                        avatar = sharedPreferences.profileAvatar(),
                        id = sharedPreferences.profileId()
                    )
                } else null

            )
            megaMenusV2.updateItem(newMenu = menuAccount)
            val menuLogout = generateLogoutMenu(sharedPreferences.userLogin())
            megaMenusV2.updateMenuLogout(menuLogout)
            menuAdapter.bind(megaMenusV2.copy())
            getUserInfo()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        megaAppId = TrackingUtil.currentAppId
        megaAppName = TrackingUtil.currentAppName
        lifecycle.addObserver(checkNavigateLoyaltyUtils)
        lifecycle.addObserver(checkNavigateMegaUtils)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = MegaFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        warningDialogFragment?.dismissAllowingStateLoss()
    }

    override fun onDestroy() {
        super.onDestroy()
        lifecycle.removeObserver(checkNavigateLoyaltyUtils)
        lifecycle.removeObserver(checkNavigateMegaUtils)

    }

    //region Handle exit app
    override val handleBackPressed = true
    override fun backHandler() {
        checkExit()
    }

    private var backCount = 0
    private var lastBackTime = 0L
    private fun checkExit() {
        backCount += 1
        if (backCount == 1) {
            Toast.makeText(
                context,
                getString(R.string.press_back_2_times_to_exit_app),
                Toast.LENGTH_SHORT
            ).show()
            lastBackTime = System.currentTimeMillis()
        } else if (backCount > 1) {
            if (System.currentTimeMillis() - lastBackTime < 1000L) {
                activity?.finish()
            } else {
                Toast.makeText(
                    context,
                    getString(R.string.press_back_2_times_to_exit_app),
                    Toast.LENGTH_SHORT
                ).show()
                lastBackTime = System.currentTimeMillis()
                backCount = 1
            }
        }
    }
    //endregion Handle exit app

    override fun bindComponent() {
        //
        if (context.isTablet()) {
            activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        }
        //

//        sdkManager = FoxpayUtils.initFoxpaySDK(sharedPreferences.linkingToken(), requireContext())
//        checkNavigateMegaUtils =  CheckNavigateMegaUtils(
//            context = requireContext(),
//            sharedPreferences = MainApplication.INSTANCE.sharedPreferences,
//            megaViewModel = viewModel,
//            megaNavigateViewModel = megaNavigateViewModel,
//            navHostFragment = parentFragment?.parentFragment,
//            checkNavigateLoyaltyUtils = checkNavigateLoyaltyUtils,
//            foxPaySdkManager = sdkManager
//        )
        binding.rvMega.apply {
            adapter = menuAdapter
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            addItemDecoration(MegaMenuItemDecoration(menuMarginTop))
        }
//        viewModel.dispatchIntent(MegaViewModel.MegaIntent.GetListApp)
    }

    override fun bindData() {
        (parentFragment as? HomeMainFragment)?.hideNoInternetView()
        (parentFragment as? HomeMainFragment)?.hidePageError()
//        if (viewModel.userPhone().isBlank()) viewModel.dispatchIntent(MegaViewModel.MegaIntent.GetUserInfo)
        oldProfileType = sharedPreferences.profileType()
        oldProfileId = sharedPreferences.profileId()

    }

    override fun bindEvent() {
        menuAdapter.eventListener = object : IEventListener<MegaMenu.Block> {

            override fun onClickView(position: Int, view: View?, data: MegaMenu.Block) {
//                if (data is MegaMenu.MenuMegaApp && view?.id == R.id.tv_view_all) {
//                    navigateToViewAll(data)
//                    Timber.d("---Navigate to view more")
//                }
                Timber.tag("tam-mega").d("onClickView view = ${view?.id}")
                if (view?.id == R.id.iv_hamburger) {
                    findNavController().navigateSafe(
                        NavHomeMainDirections.actionGlobalToMegaHamburgerMenuFragment()
                    )
                    Timber.tag("tam-mega").d("---Navigate to hamburger")
                }
                if (view?.id == R.id.iv_qr_code) {
                    if (sharedPreferences.userLogin()) {
                        findNavController().navigate(NavHomeMainDirections.actionGlobalToQrCodeFragment())
                    } else {
                        navigateToLoginWithParams(
                            isDirect = false,
                            navigationId = R.id.action_global_to_qr_code_fragment
                        )
                    }
                }
            }

            override fun onClickedItem(position: Int, data: MegaMenu.Block) {

                TrackingUtil.currentAppId = megaAppId
                TrackingUtil.currentAppName = megaAppName

                if(data == BlockAccountLogout) {
                    AlertDialog().apply {
                        setShowTitle(true)
                        setMessage(<EMAIL>(R.string.logout_confirm_message))
                        setTextConfirm(<EMAIL>(R.string.confirm))
                        setListener(object : AlertDialogListener {
                            override fun onConfirm() {
                                viewModel.dispatchIntent(MegaViewModel.MegaIntent.Logout)
                            }
                        })
                    }.show(childFragmentManager, "LogoutDialog")

                    return
                }
                checkNavigateMegaUtils.navigateToSelectedContent(data)
//                when (data) {
//                    is MegaMenu.BlockProfileLogin -> {
//                        navigateToLogin()
//                    }
//                    is MegaMenu.BlockProfile -> {
//                        checkUserLoginBeforeNavigate(navigationId = R.id.action_global_to_account_info) {
//                            parentFragment?.parentFragment?.findNavController()?.navigate(
//                                NavHomeMainDirections.actionGlobalToAccountInfo()
//                            )
//                        }
//                    }
//                    else -> {
//                    }
//                }
            }
        }

        menuAdapter.megaMenuItemClickListener = object : IEventListener<MegaMenuItem> {
            override fun onClickedItem(position: Int, data: MegaMenuItem) {
                TrackingUtil.currentAppId = megaAppId
                TrackingUtil.currentAppName = megaAppName

                val eventLog = if (data.blockType == MegaMenu.Type.MegaApp) {
                    "AccessApp"
                } else {
                    "AccessItem"
                }
                trackingProxy.sendEvent(
                    infor = InforMobile(
                        infor = trackingInfo,
                        appId = TrackingUtil.currentAppId,
                        appName = TrackingUtil.currentAppName,
                        logId = TrackingConstants.EVENT_LOG_ID_MEGA_FUNCTION_CLICK,
                        screen = TrackingConstants.SCREEN_NAME_MEGA_FUNCTION_CLICK,
                        event = eventLog,
                        itemId = data.id,
                        itemName = data.title
                    )
                )
                //
                if (data.blockType == MegaMenu.Type.MegaApp) {
                    TrackingUtil.currentAppId = data.id
                    TrackingUtil.currentAppName = data.title

                    megaNavigateViewModel.saveNavigateMenu(data)
                    parentFragment?.parentFragment?.findNavController()?.navigate(
                        NavHomeMainDirections.actionGlobalToNavMegaApp(
                            megaAppId = data.id,
                            fromDeeplink = false

                        )
                    )
                } else {
                    checkNavigateMegaUtils.navigateToSelectedContent(data)
                }

                Logger.d("trangtest logid = 108 $data")

            }
        }
        listenLoginAndRefreshData()
    }

    private fun listenLoginAndRefreshData() {
        // Tablet - tạm thời đối với dialog sẽ sử dụng listener này để refresh
        parentFragment?.setFragmentResultListener(Constants.LOGIN_SUCCESS) { _, bundle ->
            val refresh = bundle.getBoolean(Constants.HAVE_CHANGE_DATA, false)
            Logger.d("trangtest setFragmentResultListener LOGIN_SUCCESS = $refresh")
            if (refresh)
                getUserInfo()
            listenLoginAndRefreshData()
        }
        parentFragment?.setFragmentResultListener(Constants.REFRESH_DATA) { _, bundle ->
            val refresh = bundle.getBoolean(Constants.HAVE_CHANGE_DATA, false)
            Logger.d("trangtest setFragmentResultListener REFRESH_DATA = $refresh")
            if (refresh)
                getUserInfo()
            listenLoginAndRefreshData()
        }
    }

    override fun MegaViewModel.MegaState.toUI() {
        when (this) {
            is MegaViewModel.MegaState.ErrorNoInternet -> {
                if (intent is MegaViewModel.MegaIntent.Logout) {
                    showWarningDialog(message)
                }
            }
            is MegaViewModel.MegaState.ResultGetClusterInfoAtInit -> {
                handleClusterInfoResult(this.data)
            }

            is MegaViewModel.MegaState.ErrorRequiredLogin -> {
                if (this.intent is MegaViewModel.MegaIntent.GetUserInfo) {
                    viewModel.saveUserInfo(UserInfo())
                    val menuAccount = generateProfileMenu(null)
                    megaMenusV2.updateItem(newMenu = menuAccount)
                    megaMenusV2.updateMenuLogout(null)
                    menuAdapter.bind(megaMenusV2.copy())
                }
            }

            is MegaViewModel.MegaState.ResultUserInfo -> {
                viewModel.saveUserInfo(data)
                val menuAccount = generateProfileMenu(
                    userInfo = data,
                    profileItem = if (sharedPreferences.profileId().isNotBlank()) {
                        ProfileItem.Profile(
                            name = sharedPreferences.profileName(),
                            avatar = sharedPreferences.profileAvatar(),
                            id = sharedPreferences.profileId()
                        )
                    } else null

                )
                megaMenusV2.updateItem(newMenu = menuAccount)
                megaMenusV2.updateMenuLogout(generateLogoutMenu(sharedPreferences.userLogin()))
                menuAdapter.bind(megaMenusV2.copy())
            }
            is MegaViewModel.MegaState.ResultLogout -> {
                if (data.isSuccess()) {
                    val isSaleMode = sharedPreferences.isEnableSalesMode()
                    AdsUtils.saveUserType(sharedPreferences, false)
                    trackingProxy.sendEvent(
                        InforMobile(
                            infor = trackingInfo,
                            logId = "180",
                            appId = TrackingUtil.currentAppId,
                            appName = TrackingUtil.currentAppName,
                            screen = "Logout",
                            event = "Logout"
                        )
                    )
                    //trangttm5 - reset session and userphone when logout
                    Utils.clearUserData(sharedPreferences = sharedPreferences, clearSaleMode = true)
                    trackingInfo.updateUserSession(0)
                    trackingInfo.updateUserPhone("")
                    trackingInfo.updateUserContract("")
                    trackingInfo.updateUserId("")
                    //trangttm5 - end
//                    findNavController().navigateUp()
                    MultiProfileUtils.sendEventProfileChangedIfChange(
                        fragment = parentFragment,
                        sharedPreferences = sharedPreferences,
                        sourceChange = "ResultLogout",
                        oldProfileId = oldProfileId,
                        oldProfileType = oldProfileType
                    )

                    // Zendesk
                    ZendeskUtils.updateZendeskIdentity(userLogin = false, userToken = "", userTokenType = "")

                    //utm
                    Utils.clearUtmData()

                    // Pairing control
                    MainApplication.INSTANCE.pairingConnectionHelper.let {
                        if (it.isConnected) {
                            it.disconnect()
                        }
                    }
                    //

                    // Picture in Picture
                    PlayerPiPHelper.saveSupportPictureInPicture(sharedPreferences, isSupport = false)

                    // Restart application if in sale mode
                    if (isSaleMode) {
                        Utils.restartApp(requireContext())
                    }

                    //MQTT
                    MqttConnectManager.INSTANCE.unSubscriberToUserTopic()
                } else {
                    if (data.message.isNotBlank()) Toast.makeText(context, data.message, Toast.LENGTH_SHORT).show()
                }
            }

            else -> {}
        }
    }

    // region Commons


    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_LONG).show()
    }

    private fun navigateToLogin(navigationId: Int? = null) {
        navigationId?.let {
            parentFragment?.parentFragment?.navigateToLoginWithParams(
                isDirect = true,
                navigationId = it
            )
        } ?: run {
            parentFragment?.parentFragment?.navigateToLoginWithParams(isDirect = true)
        }
    }


    // region Handle Apis
    private fun handleClusterInfoResult(result: List<MegaViewModel.MegaState>) {
        isFirstInit = false
        result.forEach {
            when (it) {
                is MegaViewModel.MegaState.ResultGetUserInfo -> {
                    viewModel.saveUserInfo(it.data)
                    val menuAccount = generateProfileMenu(
                        userInfo = it.data,
                        profileItem = if (sharedPreferences.profileId().isNotBlank()) {
                            ProfileItem.Profile(
                                name = sharedPreferences.profileName(),
                                avatar = sharedPreferences.profileAvatar(),
                                id = sharedPreferences.profileId()
                            )
                        } else null

                    )
                    megaMenusV2.updateItem(newMenu = menuAccount)
                    megaMenusV2.updateMenuLogout(generateLogoutMenu(sharedPreferences.userLogin()))
                }

                is MegaViewModel.MegaState.ResultMegaMenuV2 -> {
                    Timber.tag("tam-mega").d("ResultMegaMenuV2 ${it.data.blocks}")
                    for (block in it.data.blocks) {
                        megaMenusV2.updateItem(newMenu = block)

                    }
                }

                is MegaViewModel.MegaState.ErrorRequiredLogin -> {
                    if (it.intent is MegaViewModel.MegaIntent.GetUserInfo) {
                        viewModel.saveUserInfo(UserInfo())
                        val menuAccount = generateProfileMenu(null)
                        megaMenusV2.updateItem(newMenu = menuAccount)
                        megaMenusV2.updateMenuLogout(null)
//                        menuAdapter.bind(megaMenusV2.copy())
                    }
                }

                else -> {
                }
            }
        }
//        Timber.tag("tam-mega").i("megaMenusV2 ${megaMenusV2}")
        menuAdapter.bind(megaMenusV2.copy())
    }

    // endregion Handle Apis

    // region generate Menu
    var index = -1
    private fun generateProfileMenu(
        userInfo: UserInfo?,
        profileItem: ProfileItem.Profile? = null
    ): MegaMenu.Block {

        return if (userInfo == null) {
            MegaMenu.BlockProfileLogin()
        } else {
//            MegaMenu.BlockProfile(
//                title = "",
//                userName = userInfo.displayName(),
//                userAvatar = userInfo.avatar ?: "",
//            )
            val name = if (profileItem?.name?.isNotBlank() == true) {
                profileItem.name
            } else {
                getString(R.string.multi_profile_default_profile_name)
            }
            val avatar = if (profileItem?.avatar?.isNotBlank() == true) {
                profileItem.avatar
            } else {
                ""
            }
            MegaMenu.BlockMultiProfile(
                title = "",
                profileName = name,
                profileAvatar = avatar,
            )
        }
    }

    private fun generateLogoutMenu(isLogin: Boolean): BlockAccountLogout? {

        return if (isLogin) {
            BlockAccountLogout
        } else {
            null
        }
    }

    // endregion generate Menu

    private fun getUserInfo() {
        if (sharedPreferences.userLogin()) {
            viewModel.dispatchIntent(MegaViewModel.MegaIntent.GetUserInfo)
        } else {
            megaMenusV2.updateItem(generateProfileMenu(userInfo = null))
            menuAdapter.bind(megaMenusV2.copy())
        }
    }

    private fun getAllInfoOnInit() {
        val listApis = listOf(
//            MegaViewModel.MegaIntent.GetListApp,
            MegaViewModel.MegaIntent.GetUserInfo,
            MegaViewModel.MegaIntent.GetConfig,
            MegaViewModel.MegaIntent.GetMegaMenuV2(miniAppSdkVersion = Constants.MINI_APP_SDK_SUPPORTED)
        )
        viewModel.dispatchIntent(MegaViewModel.MegaIntent.GetClusterInfoAtInit(data = listApis))
    }

    private fun ArrayList<MegaMenu.Block>.copy(): List<MegaMenu.Block> {
        val cloneOfMegaMenus = ArrayList<MegaMenu.Block>()
        forEach { item -> cloneOfMegaMenus.add(item.clone()) }
        return cloneOfMegaMenus
    }

    private fun MutableList<MegaMenu.Block>.updateItem(
        newMenu: MegaMenu.Block,
        indexInserted: Int = -1
    ): Int {
        if (this.isEmpty()) {
            add(newMenu)
            return size - 1
        }

        if (newMenu.blockType == MegaMenu.Type.Profile || newMenu.blockType == MegaMenu.Type.AccountLogout) {
            // if blockType Profile, replace if exist, not add new
            var indexMenu = -1
            for ((i, megaMenu) in this.withIndex()) {
                if (megaMenu.blockType == newMenu.blockType) {
                    this[i] = newMenu
                    indexMenu = i
                    break
                }
            }
            if (indexMenu == -1) {
                // Not in list menu more yet
                indexMenu = if (indexInserted in 0 until size) {
                    add(indexInserted, newMenu)
                    indexInserted
                } else {
                    add(newMenu)
                    size - 1
                }
            }
            return indexMenu
        }
        // if another block type, add new
        var indexMenu = if (indexInserted in 0 until size) {
            add(indexInserted, newMenu)
            indexInserted
        } else {
            if (get(size - 1) == BlockAccountLogout) {
                // if have block logout, add before log out
                add(size - 1, newMenu)
                size - 2
            } else {
                add(newMenu)
                size - 1
            }

        }
//        add(newMenu)
        return indexMenu
    }

    private fun MutableList<MegaMenu.Block>.updateMenuLogout(
        newMenu: BlockAccountLogout?,
        indexInserted: Int = -1
    ): Int {
        if (newMenu == null) {
            // remove block logout
            for ((i, megaMenu) in this.withIndex()) {
                if (megaMenu is BlockAccountLogout) {
                    removeAt(i)
                    break
                }
            }
            return -1
        }
        if (this.isEmpty()) {
            add(newMenu)
            return size - 1
        }

        var indexMenu = -1
        for ((i, megaMenu) in this.withIndex()) {
            if (megaMenu.blockType == newMenu.blockType) {
                this[i] = newMenu
                indexMenu = i
                break
            }
        }
        if (indexMenu == -1) {
            // Not in list menu more yet
            indexMenu = if (indexInserted in 0 until megaMenusV2.size) {
                add(indexInserted, newMenu)
                indexInserted
            } else {
                add(newMenu)
                size - 1
            }
        }
        return indexMenu
    }

    private fun UserInfo.displayName(): String {
        return if (name.isNullOrEmpty()) {
            phone ?: ""
        } else {
            name!!
        }
    }
    // endregion Commons
}