package com.fptplay.mobile.features.home

import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.PageId
import com.fptplay.mobile.features.hbo.HBOGoFragment
import com.fptplay.mobile.features.livetv_detail.TVFragment
import com.fptplay.mobile.features.mega.MegaFragment
import com.fptplay.mobile.features.short_video.ShortVideosFragment
import com.fptplay.mobile.features.sport.SportFragment
import com.fptplay.mobile.homebase.HomeBaseFragment
import com.fptplay.mobile.homebase.HomeBaseImplFragment
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.TabMenu

class HomeSelectTabUtils(private val tabMenus: List<TabMenu>) {
    fun getTabLabel(position: Int): String {
        return if (position in tabMenus.indices) {
            tabMenus[position].name
        } else ""
    }
    fun getTabId(position: Int):String{
        return if (position in tabMenus.indices) {
            tabMenus[position].pageId
        } else ""
    }

    fun createFragment(position: Int, shortVideoId: String = "", shortVideoChapter:String = ""): Fragment {
        val bundle = bundleOf(Constants.RELOAD_PAGE to tabMenus[position].reload)
        return when (tabMenus[position].pageId) {
            "home" -> HomeBaseFragment.Instance(HomeFragment::class.java).newInstance(bundle = bundle)
            "channel" -> HomeBaseFragment.Instance(TVFragment::class.java).newInstance(bundle = bundle)
            "sport" -> HomeBaseFragment.Instance(SportFragment::class.java).newInstance(bundle = bundle)
            "hbogo" -> HomeBaseFragment.Instance(HBOGoFragment::class.java).newInstance(bundle = bundle)
            PageId.ShortVideoPageId.id -> {
                bundle.putString(Constants.SHORT_VIDEO_ID , shortVideoId)
                bundle.putString(Constants.SHORT_VIDEO_CHAPTER , shortVideoChapter)
                ShortVideosFragment().apply { arguments = bundle }
            }
            "app" -> MegaFragment().apply { arguments = bundle }

            else -> HomeBaseImplFragment.newInstance(screenProvider = tabMenus[position].pageId, reloadTime = tabMenus[position].reload)
        }
    }
}