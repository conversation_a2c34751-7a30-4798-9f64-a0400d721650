{"formatVersion": "1.1", "component": {"group": "androidx.media3", "module": "media3-session", "version": "1.6.1.rc01", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.media3", "module": "media3-common", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-session-1.6.1.rc01.aar", "url": "media3-session-1.6.1.rc01.aar", "size": 982417, "sha512": "d0cd6390db86da616853f89d5ab877d32409510f12706717b10cb4eb8e5c13b30840b2737c0ec464267f91c64a490fd0a9902dc6d79e81166bf8bb58b09b451c", "sha256": "212ae147ea465d3998eeaaf65709456e08e29804f2437ccb1c4c3dca409203ed", "sha1": "abd9b00d7bf7e72ef76963a97656e896a072ea20", "md5": "3e51170160fe5c065dbd5526bef0105d"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "androidx.media3", "module": "media3-datasource", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.collection", "module": "collection", "version": {"requires": "1.2.0"}}, {"group": "androidx.media", "module": "media", "version": {"requires": "1.7.0"}}, {"group": "androidx.core", "module": "core", "version": {"requires": "1.8.0"}}, {"group": "androidx.media3", "module": "media3-common", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-session-1.6.1.rc01.aar", "url": "media3-session-1.6.1.rc01.aar", "size": 982417, "sha512": "d0cd6390db86da616853f89d5ab877d32409510f12706717b10cb4eb8e5c13b30840b2737c0ec464267f91c64a490fd0a9902dc6d79e81166bf8bb58b09b451c", "sha256": "212ae147ea465d3998eeaaf65709456e08e29804f2437ccb1c4c3dca409203ed", "sha1": "abd9b00d7bf7e72ef76963a97656e896a072ea20", "md5": "3e51170160fe5c065dbd5526bef0105d"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "media3-session-1.6.1.rc01-sources.jar", "url": "media3-session-1.6.1.rc01-sources.jar", "size": 426965, "sha512": "b7698b0928f55503206d1e22c665a7ed28040ca9189220817c835f76643ac263cca1af26e9eb352cd4538413d69056c13591b04da66c9e66f77366d642b69778", "sha256": "892d44a25b69efb80a81eee83871f2156a42ef9c2eaea1c370851820271b44c3", "sha1": "7965ba3b329e2764c6e33ceb8a96da9b1c5476ad", "md5": "d2b8441e57de9791132df03d34c3ced1"}]}]}