package com.fptplay.mobile.welcome

import android.os.Build
import android.util.Log
import androidx.lifecycle.SavedStateHandle
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.features.ads.utils.AdsUtils
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.player.utils.PlayerPiPHelper
import com.tear.modules.player.util.PlayerUtils
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.common.AppVersion
import com.xhbadxx.projects.module.domain.entity.fplay.common.Config
import com.xhbadxx.projects.module.domain.entity.fplay.common.ForceUpdateApp
import com.xhbadxx.projects.module.domain.entity.fplay.common.GeneralInfoMessage
import com.xhbadxx.projects.module.domain.entity.fplay.common.LandingPage
import com.xhbadxx.projects.module.domain.entity.fplay.common.SettingGeneral
import com.xhbadxx.projects.module.domain.entity.fplay.common.TriggerEvent
import com.xhbadxx.projects.module.domain.entity.fplay.user.UserInfo
import com.xhbadxx.projects.module.domain.repository.fplay.CommonRepository
import com.xhbadxx.projects.module.domain.repository.fplay.LiveRepository
import com.xhbadxx.projects.module.domain.repository.fplay.PaymentRepository
import com.xhbadxx.projects.module.domain.repository.fplay.UserRepository
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.system.measureTimeMillis
import com.fptplay.mobile.player.PlayerUtils as AppPlayerUtils

@HiltViewModel
class WelcomeViewModel @Inject constructor(
    private val commonRepository: CommonRepository,
    private val liveRepository: LiveRepository,
    private val userRepository: UserRepository,
    private val paymentRepository: PaymentRepository,
    private val sharedPreferences: SharedPreferences,
    private val savedState: SavedStateHandle
) :
    BaseViewModel<WelcomeViewModel.WelcomeIntent, WelcomeViewModel.WelcomeState>() {

    private var triggerEvent : TriggerEvent.Event? = null

    sealed class WelcomeState : ViewState {
        object Init : WelcomeState()
        data class Loading(val intent: WelcomeIntent? = null) : WelcomeState()
        data class ErrorRequiredLogin(val message: String, val intent: WelcomeIntent? = null) : WelcomeState()

        data class Error(val message: String, val intent: WelcomeIntent? = null) : WelcomeState()
        data class NoInternet(val message: String, val intent: WelcomeIntent? = null) : WelcomeState()
        data class Done(val intent: WelcomeIntent? = null) : WelcomeState()
        data class ResultAppVersion(val isCached: Boolean, val data: AppVersion) : WelcomeState()

        data class ResultGetClusterAppInfo(val data: List<WelcomeState>) : WelcomeState()
        data class ResultForceUpdateApp(val isCached: Boolean, val data: ForceUpdateApp) : WelcomeState()
        data class ResultUserInfo(val isCached: Boolean, val data: UserInfo) : WelcomeState()
        data class ResultSettingGeneral(val isCached: Boolean, val data: SettingGeneral) : WelcomeState()
        data class ResultConfig(val isCached: Boolean, val data: Config) : WelcomeState()
        data class ResultGeneralInfoMessage(val isCached: Boolean, val data: GeneralInfoMessage) : WelcomeState()
        data class ResultEventTrigger(val isCached: Boolean, val data: TriggerEvent) : WelcomeState()
    }

    sealed class WelcomeIntent : ViewIntent {
        object GetAppVersion : WelcomeIntent()
        data class GetPackageUser(val userId: String) : WelcomeIntent()
        object GetUserInfo : WelcomeIntent()
        data class GetClusterAppInfo(val data: List<Pair<Int, WelcomeIntent>>) : WelcomeIntent()
        object GetForceUpdateApp : WelcomeIntent()
        object GetSettingGeneral : WelcomeIntent()
        object GetConfig : WelcomeIntent()
        object GetGeneralInfoMessage : WelcomeIntent()
        object RegisterFcmToken: WelcomeIntent()
        object InitAppInfo: WelcomeIntent()
        object GetEventTrigger : WelcomeIntent()
    }

    fun saveTriggerEvent(data: TriggerEvent.Event) {
        triggerEvent = data
    }
    fun getTriggerEvent(): TriggerEvent.Event? = triggerEvent

    override fun dispatchIntent(intent: WelcomeIntent) {
        safeLaunch {
            when (intent) {
                is WelcomeIntent.InitAppInfo -> {
                    checkResetPreventCodec()
                    prepareDataForMediaCodec()
                    prepareSupportMediaCodec()
                    prepareDataForFingerprint()
                }

                is WelcomeIntent.GetForceUpdateApp -> {
                    commonRepository.getForceUpdateApp().collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            WelcomeState.ResultForceUpdateApp(isCached = isCached, data = data)
                        }
                    }
                }
                is WelcomeIntent.GetClusterAppInfo -> {
                    getClusterAppInfo(intentGroup = intent)
                }
                is WelcomeIntent.GetSettingGeneral -> {
                    // save info in repository
                    commonRepository.getSettingGeneral().collect()
//                    commonRepository.getSettingGeneral().collect {
//                        _state.value = it.reduce(intent = intent) { isCached, data ->
//                            WelcomeState.ResultSettingGeneral(isCached = isCached, data = data)
//                        }
//                    }
                }
                is WelcomeIntent.GetConfig -> {
                    commonRepository.getConfig().collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            WelcomeState.ResultConfig(isCached = isCached, data = data)
                        }
                    }
                }

                is WelcomeIntent.GetGeneralInfoMessage -> {
                    commonRepository.getGeneralInfoMessage().collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            WelcomeState.ResultGeneralInfoMessage(isCached = isCached, data = data)

                        }
                    }
                }

                is WelcomeIntent.RegisterFcmToken -> {
                    if (sharedPreferences.userLogin() && sharedPreferences.userId().isNotBlank()) {
                        liveRepository.checkFollow(type = "general", id = "all").collect {
                            Timber.d("***** check register $it")
                            if (it is Result.Success) {
                                if (it.successData.status == 0) {

                                    userRepository.subscribeUser(type = "user", id = sharedPreferences.userId()).collect {
                                        Timber.d("Run subscribe room user $it")
                                    }
                                    liveRepository.addFollow(type = "general", id = "all").collect {
                                        Timber.d("Run subscribe room general $it")
                                    }
                                }
                            }
                        }
                    }
                    commonRepository.addDeviceRegistrationToken(sharedPreferences.fcmToken(), sharedPreferences.userId().ifBlank { null }).collect {
                        Timber.d("Run register fcm token $it")
                    }
                }
                is WelcomeIntent.GetUserInfo -> {
                    userRepository.getUserInfo().collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            Utils.saveUserInfo(sharedPreferences, data)
                            WelcomeState.ResultUserInfo(isCached = isCached, data = data)
                        }
                    }
                }
                is WelcomeIntent.GetEventTrigger -> {
                    commonRepository.getTriggerEvent().collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            WelcomeState.ResultEventTrigger(isCached = isCached, data = data)
                        }
                    }
                }
                else -> {

                }
            }
        }
    }

    override fun <T> Result<T>.reduce(
        intent: WelcomeIntent?,
        successFun: (Boolean, T) -> WelcomeState
    ): WelcomeState {
        return when (this) {
            is Result.Init -> WelcomeState.Loading(intent = intent)
            is Result.Success -> {
                successFun(this.isCached, this.successData)
            }
            is Result.Error -> {
                if (this is Result.Error.Intenet) {
                    WelcomeState.NoInternet(message = this.message, intent = intent)
                } else {
                    WelcomeState.Error(message = this.message, intent = intent)
                }
            }
            Result.Done -> WelcomeState.Done(intent = intent)
        }
    }

    private suspend fun <T> Flow<Result<T>>.process(
        successFun: (Boolean, T) -> Unit,
        errorFun: (Result.Error) -> Unit
    ) {
        this.filter { it is Result.Success || it is Result.Error}
            .collect {
                if( it is Result.Success) {
                    successFun(it.isCached, it.successData)
                } else if(it is Result.Error) {
                    errorFun(it)
                }


            }
    }

    private fun Result.Error.processError(intent: WelcomeIntent): WelcomeState {
        return when (this){
            is Result.UserError.RequiredLogin -> WelcomeState.ErrorRequiredLogin(message = this.message, intent = intent)
            is Result.Error.Intenet -> WelcomeState.NoInternet(message = this.message, intent = intent)
            else -> WelcomeState.Error(message = this.message, intent = intent)

        }
    }

    private suspend fun getClusterAppInfo(intentGroup: WelcomeIntent.GetClusterAppInfo) {
        val measureTime = measureTimeMillis {
//            withTimeoutOrNull(5000) {
            safeLaunch {
                try {
                    val arrDeferred = arrayListOf<Deferred<Unit>>()
                    val pairs = intentGroup.data // Pair[Index, WelcomeIntent]
                    val arrResults = arrayListOf<Pair<Int, WelcomeState>>()

                    for (index in pairs.indices) {
                        val intent = pairs[index].second
                        when (intent) {
                            is WelcomeIntent.GetPackageUser -> {
                                arrDeferred.add(async {
                                    paymentRepository.getPackageUser(userId = intent.userId).process (
                                        successFun = { _, result ->
                                            // No need toUI, only update sharedPreferences
                                            AdsUtils.saveUserType(
                                                sharedPreferences = sharedPreferences,
                                                haveDisAdsPackage = AdsUtils.userHaveDisAdsPackage(result)
                                            )

                                            // Picture in Picture
                                            PlayerPiPHelper.saveSupportPictureInPicture(
                                                sharedPreferences = sharedPreferences,
                                                isSupport = PlayerPiPHelper.userHaveSupportPip(result)
                                            )
                                         },
                                        errorFun = {
//                                            AdsUtils.saveUserType(sharedPreferences, false)
                                        })
                                })
                            }
                            is WelcomeIntent.GetConfig -> {
                                arrDeferred.add(async {
                                    commonRepository.getConfig().process(
                                        successFun = { isCached, result ->
                                            arrResults.add(
                                                Pair(
                                                    pairs[index].first,
                                                    WelcomeState.ResultConfig(
                                                        isCached = isCached,
                                                        data = result
                                                    )
                                                )
                                            )
                                        },
                                        errorFun = {
                                            arrResults.add(Pair(pairs[index].first,  WelcomeState.Error(message = it.message, intent = intent)))
                                        }
                                    )
                                })
                            }
                            is WelcomeIntent.GetUserInfo -> {
                                arrDeferred.add(async {
                                    val oldProfileId = sharedPreferences.profileId()
                                    val oldProfileType = sharedPreferences.profileType()
                                    userRepository.getUserInfo().process (
                                        successFun = { isCached, result ->
                                            // No need toUI, only update sharedPreferences
                                            Utils.saveUserInfo(sharedPreferences, result)
                                            if(MultiProfileUtils.checkProfileChanged(sharedPreferences, oldProfileId, oldProfileType)) {
                                                sharedPreferences.saveProfileChangeWhenOpenApp(true)
                                            } else {
                                                // if not different, not save in case open app multiple time but not go to home
                                            }
                                        },
                                        errorFun = {
                                            // clear user info if 401
                                            if(it is Result.UserError.RequiredLogin) {
                                                Utils.clearUserData(sharedPreferences)
                                                if(MultiProfileUtils.checkProfileChanged(sharedPreferences, oldProfileId, oldProfileType)) {
                                                    sharedPreferences.saveProfileChangeWhenOpenApp(true)
                                                } else {
                                                    // if not different, not save in case open app multiple time but not go to home
                                                }
                                            }
                                        })
                                })
                            }
                            is WelcomeIntent.GetEventTrigger -> {
                                arrDeferred.add(async {
                                    commonRepository.getTriggerEvent().process(
                                        successFun = { isCached, result ->
                                            arrResults.add(
                                                Pair(pairs[index].first, WelcomeState.ResultEventTrigger(
                                                    isCached = isCached,
                                                    data = result
                                                ))
                                            )
                                        },
                                        errorFun = {
                                            arrResults.add(Pair(pairs[index].first, it.processError(intent = intent)))
                                        }
                                    )
                                })
                            }
                            else -> {
                            }
                        }
                    }
                    arrDeferred.forEach { it.await() }
                    arrResults.sortBy { it.first }
                    withContext(Dispatchers.Main) {
                        _state.value = WelcomeState.ResultGetClusterAppInfo(
                            data = arrResults.map { it.second }
                        )
                    }
                } catch (ex: Exception) {
                    Logger.d("Exception: ${ex.message}")

                }
                // Process timeout or return null
                withContext(Dispatchers.Main) { _state.value = WelcomeState.Done() }

            }
        }
        Log.d("tamlog","Measure time of getClusterAppInfo: ${measureTime / 1000.0}s ->  ${Thread.currentThread().name}")

    }


    private fun checkResetPreventCodec() {
        // H265
        if(AppPlayerUtils.isPreventH265DrmNeedToReset(sharedPreferences)) {
            Logger.d("Reset prevent h265 drm")
            AppPlayerUtils.resetPreventH265Drm(sharedPreferences)
        }

        if(AppPlayerUtils.isPreventH265NoDrmNeedToReset(sharedPreferences)) {
            Logger.d("Reset prevent h265 no drm")
            AppPlayerUtils.resetPreventH265NoDrm(sharedPreferences)
        }

        // H265 (HDR, HDR 10 Plus)
        if (AppPlayerUtils.isPreventH265HdrDrmNeedToReset(sharedPreferences)) {
            Logger.d("Reset prevent h265 hdr drm")
            AppPlayerUtils.resetPreventH265HdrDrm(sharedPreferences)
        }

        if (AppPlayerUtils.isPreventH265HdrNoDrmNeedToReset(sharedPreferences)) {
            Logger.d("Reset prevent h265 hdr no drm")
            AppPlayerUtils.resetPreventH265HdrNoDrm(sharedPreferences)
        }

        // AV1
        if(AppPlayerUtils.isPreventAV1DrmNeedToReset(sharedPreferences)) {
            Logger.d("Reset prevent AV1 drm")
            AppPlayerUtils.resetPreventAV1Drm(sharedPreferences)
        }

        if(AppPlayerUtils.isPreventAV1NoDrmNeedToReset(sharedPreferences)) {
            Logger.d("Reset prevent AV1 no drm")
            AppPlayerUtils.resetPreventAV1NoDrm(sharedPreferences)
        }

        // VP9
        if(AppPlayerUtils.isPreventVP9DrmNeedToReset(sharedPreferences)) {
            Logger.d("Reset prevent VP9 drm")
            AppPlayerUtils.resetPreventVP9Drm(sharedPreferences)
        }

        if(AppPlayerUtils.isPreventVP9NoDrmNeedToReset(sharedPreferences)) {
            Logger.d("Reset prevent VP9 no drm")
            AppPlayerUtils.resetPreventVP9NoDrm(sharedPreferences)
        }

        // Dolby Vision
        if(AppPlayerUtils.isPreventDolbyVisionDrmNeedToReset(sharedPreferences)) {
            Logger.d("Reset prevent Dolby Vision drm")
            AppPlayerUtils.resetPreventDolbyVisionDrm(sharedPreferences)
        }

        if(AppPlayerUtils.isPreventDolbyVisionNoDrmNeedToReset(sharedPreferences)) {
            Logger.d("Reset prevent Dolby Vision no drm")
            AppPlayerUtils.resetPreventDolbyVisionNoDrm(sharedPreferences)
        }
    }

    private fun prepareDataForMediaCodec() {
        sharedPreferences.saveVideoCodecInfo(getVideoNonDrmCodec())
        sharedPreferences.saveVideoDrmCodecInfo(getVideoDrmCodec())
        sharedPreferences.saveAudioCodecInfo(getAudioCodecInfo())
    }

    private fun prepareSupportMediaCodec() {
        // H265 HDR
        saveSupportMediaCodecH265HDRNoDrm()
        saveSupportMediaCodecH265HDRDrm()
        // H265 HDR 10 Plus
        saveSupportMediaCodecH265HDR10PlusNoDrm()
        saveSupportMediaCodecH265HDR10PlusDrm()
        // H265 Hlg
        saveSupportMediaCodecH265HlgNoDrm()
        saveSupportMediaCodecH265HlgDrm()
        // AV1
        saveSupportMediaCodecAV1NoDrm()
        saveSupportMediaCodecAV1Drm()
        // Dolby Vision
        saveSupportMediaCodecDolbyVisionNoDrm()
        saveSupportMediaCodecDolbyVisionDrm()
    }

    private fun saveSupportMediaCodecH265HDRNoDrm() {
        val h265DecoderNoDrm = PlayerUtils.getVideoH265DecoderInfo(secure = false)
        val isSupport = if (h265DecoderNoDrm.isNotEmpty()) {
            val isSupportHdr = if (h265DecoderNoDrm.size > 1) h265DecoderNoDrm.drop(1).any { it % 10 == 3 } else false
            h265DecoderNoDrm.first() == 1 && isSupportHdr
        } else false
        sharedPreferences.saveSupportMediaCodecH265HdrNoDrm(isSupport)
    }

    private fun saveSupportMediaCodecH265HDRDrm() {
        val h265DecoderDrm = PlayerUtils.getVideoH265DecoderInfo(secure = true)
        val isSupport = if (h265DecoderDrm.isNotEmpty()) {
            val isSupportHdr = if (h265DecoderDrm.size > 1) h265DecoderDrm.drop(1).any { it % 10 == 3 } else false
            h265DecoderDrm.first() == 1 && isSupportHdr
        } else false
        sharedPreferences.saveSupportMediaCodecH265HdrDrm(isSupport)
    }

    private fun saveSupportMediaCodecH265HDR10PlusNoDrm() {
        val h265DecoderNoDrm = PlayerUtils.getVideoH265DecoderInfo(secure = false)
        val isSupport = if (h265DecoderNoDrm.isNotEmpty()) {
            val isSupportHdr = if (h265DecoderNoDrm.size > 1) h265DecoderNoDrm.drop(1).any { it % 10 == 4 } else false
            h265DecoderNoDrm.first() == 1 && isSupportHdr
        } else false
        sharedPreferences.saveSupportMediaCodecH265Hdr10PlusNoDrm(isSupport)
    }

    private fun saveSupportMediaCodecH265HDR10PlusDrm() {
        val h265DecoderDrm = PlayerUtils.getVideoH265DecoderInfo(secure = true)
        val isSupport = if (h265DecoderDrm.isNotEmpty()) {
            val isSupportHdr = if (h265DecoderDrm.size > 1) h265DecoderDrm.drop(1).any { it % 10 == 4 } else false
            h265DecoderDrm.first() == 1 && isSupportHdr
        } else false
        sharedPreferences.saveSupportMediaCodecH265Hdr10PlusDrm(isSupport)
    }

    private fun saveSupportMediaCodecH265HlgNoDrm() {
        val h265DecoderNoDrm = PlayerUtils.getVideoH265DecoderInfo(secure = false)
        val isSupport = if (h265DecoderNoDrm.isNotEmpty()) {
            h265DecoderNoDrm.first() == 1
        } else false
        sharedPreferences.saveSupportMediaCodecH265HlgNoDrm(isSupport)
    }

    private fun saveSupportMediaCodecH265HlgDrm() {
        val h265DecoderDrm = PlayerUtils.getVideoH265DecoderInfo(secure = true)
        val isSupport = if (h265DecoderDrm.isNotEmpty()) {
            h265DecoderDrm.first() == 1
        } else false
        sharedPreferences.saveSupportMediaCodecH265HlgDrm(isSupport)
    }

    private fun saveSupportMediaCodecAV1NoDrm() {
        val av1DecoderNoDrm = PlayerUtils.getVideoAV1DecoderInfo(secure = false)
        sharedPreferences.saveSupportMediaCodecAV1NoDrm(av1DecoderNoDrm.firstOrNull() == 1)
    }

    private fun saveSupportMediaCodecAV1Drm() {
        val av1DecoderDrm = PlayerUtils.getVideoAV1DecoderInfo(secure = true)
        sharedPreferences.saveSupportMediaCodecAV1Drm(av1DecoderDrm.firstOrNull() == 1)
    }

    private fun saveSupportMediaCodecDolbyVisionNoDrm() {
        val dolbyVisionDecoderNoDrm = PlayerUtils.getVideoDolbyVisionDecoderInfo(secure = false)
        sharedPreferences.saveSupportMediaCodecDolbyVisionNoDrm(dolbyVisionDecoderNoDrm.firstOrNull() == 1)
    }

    private fun saveSupportMediaCodecDolbyVisionDrm() {
        val dolbyVisionDecoderDrm = PlayerUtils.getVideoDolbyVisionDecoderInfo(secure = true)
        sharedPreferences.saveSupportMediaCodecDolbyVisionDrm(dolbyVisionDecoderDrm.firstOrNull() == 1)
    }

    private fun getVideoNonDrmCodec(): String {
        return "avc:${PlayerUtils.getVideoH264DecoderInfo(false)}," +
                "hevc:${PlayerUtils.getVideoH265DecoderInfo(false)}," +
                "vp9:${PlayerUtils.getVideoVP9DecoderInfo(false)}," +
                "av1:${PlayerUtils.getVideoAV1DecoderInfo(false)}," +
                "dolby_vision:${PlayerUtils.getVideoDolbyVisionDecoderInfo(false)}"
    }

    private fun getVideoDrmCodec(): String {
        return "avc:${PlayerUtils.getVideoH264DecoderInfo(true)}," +
                "hevc:${PlayerUtils.getVideoH265DecoderInfo(true)}," +
                "vp9:${PlayerUtils.getVideoVP9DecoderInfo(true)}," +
                "av1:${PlayerUtils.getVideoAV1DecoderInfo(true)}," +
                "dolby_vision:${PlayerUtils.getVideoDolbyVisionDecoderInfo(true)}"
    }

    private fun getAudioCodecInfo(): String {
        return "eac3:${PlayerUtils.getAudioEac3DecoderInfo()}," +
                "ac4:${PlayerUtils.getAudioAc4DecoderInfo()}," +
                "ac3:${PlayerUtils.getAudioAc3DecoderInfo()}," +
                "flac:${PlayerUtils.getAudioFlacDecoderInfo()}," +
                "aac:${PlayerUtils.getAudioAacDecoderInfo()}"
    }

    private fun prepareDataForFingerprint() {
        sharedPreferences.saveDeviceFingerprint(Build.FINGERPRINT)
    }
}