{"formatVersion": "1.1", "component": {"group": "androidx.media3", "module": "media3-common", "version": "1.6.1.rc01", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.google.guava", "module": "guava", "version": {"requires": "33.3.1-android"}, "excludes": [{"group": "com.google.j2objc", "module": "j2objc-annotations"}, {"group": "org.checkerframework", "module": "checker-compat-qual"}, {"group": "com.google.code.findbugs", "module": "jsr305"}, {"group": "org.codehaus.mojo", "module": "animal-sniffer-annotations"}, {"group": "org.checkerframework", "module": "checker-qual"}, {"group": "com.google.errorprone", "module": "error_prone_annotations"}]}, {"group": "androidx.annotation", "module": "annotation-experimental", "version": {"requires": "1.3.1"}}], "files": [{"name": "media3-common-1.6.1.rc01.aar", "url": "media3-common-1.6.1.rc01.aar", "size": 521636, "sha512": "f8d8885f8b73c88a28e5dbc25d9ee49a6782347998bc1da88df102c1118f085436dcdf8d0efd87a24bf289b551d4a9e84ab83b97dfbfbe0e385a3e50aacf3dc2", "sha256": "92170b625c502dfc087a3ea23d4f2af859a33a13a07142aa990516dc9a0018aa", "sha1": "2be4a89d63cbf40a2b22ab1e1af8dc6682a2a522", "md5": "2b2eefabdccad215bea90395ffefe8c6"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.6.0"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-bom", "version": {"requires": "1.8.0"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "com.google.guava", "module": "guava", "version": {"requires": "33.3.1-android"}, "excludes": [{"group": "com.google.j2objc", "module": "j2objc-annotations"}, {"group": "org.checkerframework", "module": "checker-compat-qual"}, {"group": "com.google.code.findbugs", "module": "jsr305"}, {"group": "org.codehaus.mojo", "module": "animal-sniffer-annotations"}, {"group": "org.checkerframework", "module": "checker-qual"}, {"group": "com.google.errorprone", "module": "error_prone_annotations"}]}, {"group": "androidx.annotation", "module": "annotation-experimental", "version": {"requires": "1.3.1"}}], "dependencyConstraints": [{"group": "androidx.media3", "module": "media3-cast", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-common-ktx", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-container", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-database", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-datasource", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-datasource-cronet", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-datasource-okhttp", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-datasource-rtmp", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-decoder", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-exoplayer-midi", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-effect", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-exoplayer", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-exoplayer-dash", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-exoplayer-hls", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-exoplayer-ima", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-exoplayer-rtsp", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-exoplayer-smoothstreaming", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-exoplayer-workmanager", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-extractor", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-muxer", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-session", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-transformer", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-ui", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-ui-compose", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-ui-leanback", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-test-utils", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-test-utils-robolectric", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-common-1.6.1.rc01.aar", "url": "media3-common-1.6.1.rc01.aar", "size": 521636, "sha512": "f8d8885f8b73c88a28e5dbc25d9ee49a6782347998bc1da88df102c1118f085436dcdf8d0efd87a24bf289b551d4a9e84ab83b97dfbfbe0e385a3e50aacf3dc2", "sha256": "92170b625c502dfc087a3ea23d4f2af859a33a13a07142aa990516dc9a0018aa", "sha1": "2be4a89d63cbf40a2b22ab1e1af8dc6682a2a522", "md5": "2b2eefabdccad215bea90395ffefe8c6"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "media3-common-1.6.1.rc01-sources.jar", "url": "media3-common-1.6.1.rc01-sources.jar", "size": 437028, "sha512": "91c130f927f684b3f63396a7d23c875bf90efb63a355b83f98030ebdf923c77d20eaeb111d8caf59254465240b79543fb914f2ae4c5d536cc435456e8d0ea466", "sha256": "d591ddb9871490543958b0ac8bececad7e6da23f8702d2558664f93b44824492", "sha1": "fbe456df69582950e44651d2b4325abbc5e63ac4", "md5": "9194d75c90cfb2cca906d24eb5c6611d"}]}]}