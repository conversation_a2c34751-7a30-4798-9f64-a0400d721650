package com.fptplay.mobile.common.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import android.widget.SeekBar
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.fptplay.mobile.R
import com.fptplay.mobile.databinding.PlayerVodSeekBarBinding
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.logger.Logger
import kotlin.math.abs

class PlayerVodSeekbarView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    style: Int = 0
) : FrameLayout(context, attrs, style) {
    private val TAG = "PlayerVodSeekbarView"
    private var binding: PlayerVodSeekBarBinding? = null
    private var isSeekbarHidden = false
    private var isDragging = false
    private var isActualMove = false
    private var touchPosition = 0f
    private var onSeekBarChangeListener: SeekBar.OnSeekBarChangeListener? = null

    init {
        binding = PlayerVodSeekBarBinding.inflate(LayoutInflater.from(context), this, true)
    }

    /**
     * Override onTouchEvent to handle dragging when seekbar is hidden
     */
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        event ?: return super.onTouchEvent(event)
        
        if (isSeekbarHidden) {
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    Logger.d("$TAG > onTouchEvent: ACTION_DOWN")
                    isDragging = true
                    touchPosition = event.x

//                    // Calculate and set progress based on touch position
//                    val progress = calculateProgressFromTouch(event.x)
//                    // Notify listener of seek start
//                    onSeekBarChangeListener?.onStartTrackingTouch(binding?.playerSeekProgress)
//                    onSeekBarChangeListener?.onProgressChanged(binding?.playerSeekProgress, progress, true)
                    return true
                }
                MotionEvent.ACTION_MOVE -> {
                    Logger.d("$TAG > onTouchEvent: ACTION_MOVE > isDragging: $isDragging - haveMoveProgress: $isActualMove")
                    if (isDragging) {
                        // Update progress based on drag position
                        if (!isActualMove) {
                            if (abs(event.x - touchPosition) > 50) {
                                onSeekBarChangeListener?.onStartTrackingTouch(binding?.playerSeekProgress)
                                isActualMove = true
                                binding?.playerSeekProgress?.show()
                                // Change progress bar drawable to focused state during drag
                                binding?.playerProgressBar?.progressDrawable =
                                    ContextCompat.getDrawable(
                                        context,
                                        R.drawable.player_vod_seek_bar_progress_drawable
                                    )
                            }
                            // Show seekbar temporarily during drag
                        }

                        if (isActualMove) {
                            val progress = calculateProgressFromTouch(event.x)
                            Logger.d("$TAG > onTouchEvent: ACTION_MOVE > progress: $progress")
                            setProgressPreview(progress)
                            // Notify listener of progress change
                            onSeekBarChangeListener?.onProgressChanged(
                                binding?.playerSeekProgress,
                                progress,
                                true
                            )
                        }
                    }
                    return true
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    Logger.d("$TAG > onTouchEvent: ACTION_UP > isDragging: $isDragging - haveMoveProgress: $isActualMove")
                    if (isDragging && isActualMove) {
                        isDragging = false
                        isActualMove = false
                        onSeekBarChangeListener?.onStopTrackingTouch(binding?.playerSeekProgress)
                        // Calculate final progress
                        val progress = calculateProgressFromTouch(event.x)
                        setProgress(progress)
                        // Notify listener of seek end
                        onSeekBarChangeListener?.onProgressChanged(binding?.playerSeekProgress, progress, true)
                        onSeekBarChangeListener?.onStopTrackingTouch(binding?.playerSeekProgress)
                        // Change progress bar drawable back to timeline state
                        binding?.playerProgressBar?.progressDrawable = ContextCompat.getDrawable(context, R.drawable.player_vod_timeline_progress_drawable)
                        // Hide seekbar again
                        binding?.playerSeekProgress?.hide()
                        return true
                    } else {
                        isDragging = false
                        isActualMove = false
                    }
                }
            }
        }
        
        return super.onTouchEvent(event)
    }

    /**
     * Calculate progress based on touch X position
     */
    private fun calculateProgressFromTouch(touchX: Float): Int {
        val seekBar = binding?.playerSeekProgress ?: return 0
        val width = width.toFloat()
        val paddingStart = paddingStart.toFloat()
        val paddingEnd = paddingEnd.toFloat()
        val usableWidth = width - paddingStart - paddingEnd
        
        if (usableWidth <= 0) return 0
        
        val clampedX = touchX.coerceIn(paddingStart, width - paddingEnd)
        val relativeX = clampedX - paddingStart
        val percentage = relativeX / usableWidth
        
        return (percentage * seekBar.max).toInt().coerceIn(0, seekBar.max)
    }

    /**
     * Set OnTouchListener with debugging
     */
    override fun setOnTouchListener(listener: OnTouchListener?) {
        super.setOnTouchListener { view, event ->
            listener?.onTouch(view, event) ?: false
        }
        binding?.playerSeekProgress?.setOnTouchListener { view, event ->
            listener?.onTouch(view, event) ?: false
        }
    }

    /**
     * Get the internal SeekBar for setting listeners and interactions
     */
    fun getSeekBar(): SeekBar? = binding?.playerSeekProgress

    /**
     * Get the internal ProgressBar for visual feedback
     */
    fun getProgressBar() = binding?.playerProgressBar

    /**
     * Set progress on both seekbar and progress bar
     */
    fun setProgress(progress: Int) {
        Logger.d("$TAG > setProgress: $progress")
        binding?.playerSeekProgress?.progress = progress
        binding?.playerProgressBar?.progress = progress
    }

    fun setProgressPreview(progress: Int) {
        Logger.d("$TAG > setProgressPreview: $progress")
        binding?.playerSeekProgress?.progress = progress
    }

    /**
     * Set secondary progress (buffer progress)
     */
    fun setSecondaryProgress(progress: Int) {
        binding?.playerSeekProgress?.secondaryProgress = progress
        binding?.playerProgressBar?.secondaryProgress = progress
    }

    /**
     * get secondary progress (buffer progress)
     */
    fun getSecondaryProgress(): Int = binding?.playerSeekProgress?.secondaryProgress ?: 0

    /**
     * Set maximum value
     */
    fun setMax(max: Int) {
        binding?.playerSeekProgress?.max = max
        binding?.playerProgressBar?.max = max
    }

    /**
     * Get current progress
     */
    fun getProgress(): Int = binding?.playerSeekProgress?.progress ?: 0

    /**
     * Get maximum value
     */
    fun getMax(): Int = binding?.playerSeekProgress?.max ?: 0

    /**
     * Set OnSeekBarChangeListener
     */
    fun setOnSeekBarChangeListener(listener: SeekBar.OnSeekBarChangeListener?) {
        this.onSeekBarChangeListener = listener
        binding?.playerSeekProgress?.setOnSeekBarChangeListener(listener)
    }

    fun hideSeekbarControl() {
        isSeekbarHidden = true
        binding?.playerSeekProgress?.post {
            binding?.playerSeekProgress.hide()
        }
        binding?.playerProgressBar?.progressDrawable = ContextCompat.getDrawable(context, R.drawable.player_vod_timeline_progress_drawable)
    }

    fun showSeekbarControl() {
        isSeekbarHidden = false
        isDragging = false
        binding?.playerSeekProgress?.post {
            binding?.playerSeekProgress.show()
        }
        binding?.playerProgressBar?.progressDrawable = ContextCompat.getDrawable(context, R.drawable.player_vod_seek_bar_progress_drawable)
    }

    fun isSeekBarControlVisible(): Boolean {
        return (binding?.playerSeekProgress?.isVisible == true)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        binding = null
    }
}