dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        maven { url 'https://zendesk.jfrog.io/zendesk/repo' }
        google()
        mavenLocal()
        mavenCentral()
        jcenter() // Warning: this repository is going to shut down soon
        maven {
            url 'https://jitpack.io'
            credentials { username "jp_55pdrborhg7p9299tpjj2cel37" }
        }
        maven {
            url "https://artifactory.foxpay.vn/artifactory/example-repo-local"
            credentials {
                username = "${artifactory_username}"
                password = "${artifactory_password}"
            }
        }
        maven { url "https://maven.sigmadrm.com" }
    }
}
rootProject.name = "FPT Play"
include ':app'
//include ':downloader'