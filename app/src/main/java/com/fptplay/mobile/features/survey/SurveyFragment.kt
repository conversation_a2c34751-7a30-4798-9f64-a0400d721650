package com.fptplay.mobile.features.survey

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.HomeActivity
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.common.extensions.ActivityExtensions.startHome
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.DeeplinkUtils.addAdjustTrueLinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFacebookDeeplinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFirebaseDeeplinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFirebaseNotificationData
import com.fptplay.mobile.databinding.SurveyFragmentBinding
import com.google.gson.Gson
import com.xhbadxx.projects.module.domain.entity.fplay.common.TriggerEvent
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.image.ImageProxy
import javax.inject.Inject

@dagger.hilt.android.AndroidEntryPoint
class SurveyFragment : BaseFragment<SurveyViewModel.SurveyState, SurveyViewModel.SurveyIntent>() {
    override val viewModel: SurveyViewModel by activityViewModels()
    private var _binding: SurveyFragmentBinding? = null
    private val binding get() = _binding!!
    private val safeArgs: SurveyFragmentArgs by navArgs()
    override val hasEdgeToEdge: Boolean = true
    override val handleBackPressed: Boolean = true
    @Inject
    lateinit var sharedPreferences: SharedPreferences

    private var event: TriggerEvent.Event? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = SurveyFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun initData() {
        event = try {
            Gson().fromJson(safeArgs.event, TriggerEvent.Event::class.java)
        } catch (e: Exception) {
            null
        }
        event?.let {
            viewModel.dataStart = SurveyViewModel.DataStart(
                title = it.title,
                description = it.description,
                icon = it.media.thumbnail.portrait.getOrNull(0)?: "",
                surveyId = it.surveysId,
                eventId = it.eventId,
                typeEvent = it.type
            )
            loadImageBackground(MainApplication.INSTANCE.applicationContext.resources.configuration.orientation)
        }?: run {
            startHome()
        }

    }

    override fun bindEvent() {
        binding.ivClose.setOnClickListener {
            startHome()
        }
    }

    private fun loadImageBackground(orientation:Int) {
        event?.let {
            val isPortrait = orientation == Configuration.ORIENTATION_PORTRAIT
            val url = if (isPortrait) it.media.banner.portrait.getOrNull(0)?:"" else it.media.banner.landscape.getOrNull(0)?:""
            ImageProxy.load(
                context = binding.root.context,
                url = url,
                width = binding.ivBackground.width,
                height = binding.ivBackground.height,
                target = binding.ivBackground
            )
        }?: run{
            binding.ivBackground.setImageResource(0)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        loadImageBackground(newConfig.orientation)
    }

    fun startHome() {
        requireActivity().startHome(sharedPreferences, safeArgs.originalLink)
    }

    override fun SurveyViewModel.SurveyState.toUI() {

    }
}
