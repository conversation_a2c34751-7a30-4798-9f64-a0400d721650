<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/player_control_bottom_button_height">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/player_ic_subtitle" />

    <ImageView
        android:id="@+id/iv_badge"
        android:layout_width="@dimen/player_control_bottom_button_badge_icon_size"
        android:layout_height="@dimen/player_control_bottom_button_badge_icon_size"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/tv_title"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/margin_common_small"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        tools:text="Subtitle" />

</androidx.constraintlayout.widget.ConstraintLayout>