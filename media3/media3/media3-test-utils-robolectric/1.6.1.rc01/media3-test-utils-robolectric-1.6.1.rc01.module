{"formatVersion": "1.1", "component": {"group": "androidx.media3", "module": "media3-test-utils-robolectric", "version": "1.6.1.rc01", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.media3", "module": "media3-exoplayer", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-test-utils", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-test-utils-robolectric-1.6.1.rc01.aar", "url": "media3-test-utils-robolectric-1.6.1.rc01.aar", "size": 46843, "sha512": "7df8405d30ae585748bdba1fb6c0dd22bd4e42c3517620e0daa32e183e8a91c583f931484ddb32f9d381fd977d704ed04d81992baa11bb24f3674175f4b09dc0", "sha256": "a625ce21299dcda5eb38c3b7dccf52892b2ea1f803914349b406b587803cc7a5", "sha1": "72d9b7f5349bed47b099da4b6e990cf850a36f37", "md5": "ea6b0ba2c52d57bcabf2707b79024505"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.6.0"}}, {"group": "org.robolectric", "module": "robolectric", "version": {"requires": "4.14.1"}}, {"group": "androidx.media3", "module": "media3-exoplayer", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-test-utils", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-test-utils-robolectric-1.6.1.rc01.aar", "url": "media3-test-utils-robolectric-1.6.1.rc01.aar", "size": 46843, "sha512": "7df8405d30ae585748bdba1fb6c0dd22bd4e42c3517620e0daa32e183e8a91c583f931484ddb32f9d381fd977d704ed04d81992baa11bb24f3674175f4b09dc0", "sha256": "a625ce21299dcda5eb38c3b7dccf52892b2ea1f803914349b406b587803cc7a5", "sha1": "72d9b7f5349bed47b099da4b6e990cf850a36f37", "md5": "ea6b0ba2c52d57bcabf2707b79024505"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "media3-test-utils-robolectric-1.6.1.rc01-sources.jar", "url": "media3-test-utils-robolectric-1.6.1.rc01-sources.jar", "size": 21429, "sha512": "ace7bbacdf21c9410f07185d44c18cdfe278fa4e136e51061a365f180286635ae430674dd15cc627e5ab749db582e3f6b69e8a90d23e44f91bf3d209542349ac", "sha256": "b6d83d77bcee15ef1a2d07b600e0c7c17fb50edcac0ceb8b16fca5a472476dfb", "sha1": "322bbdce955a28149c930c8adb7cb1cfecd30298", "md5": "eb84a4b31caa18fd65cf110aca3163af"}]}]}