{"formatVersion": "1.1", "component": {"group": "androidx.media3", "module": "media3-transformer", "version": "1.6.1.rc01", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.media3", "module": "media3-exoplayer", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-effect", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-muxer", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-transformer-1.6.1.rc01.aar", "url": "media3-transformer-1.6.1.rc01.aar", "size": 389760, "sha512": "e34ad40e60b4a841a8afa72a992e6f1a9b6d73ad9a5f7533494885582191f75ca8ca734fdef60ceb37b060b224d02fbb22b3ced163daf035ac856f55810b297e", "sha256": "ab073705099cc36626384fe508fe0b6a1af8f5ecb44983fa4974b212bec99373", "sha1": "564bf7412aca461f95c6474689a267d734533549", "md5": "381f7bc027144885e41b8e1e9a848be4"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.6.0"}}, {"group": "androidx.concurrent", "module": "concurrent-futures", "version": {"requires": "1.2.0"}}, {"group": "androidx.media3", "module": "media3-datasource", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-container", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-exoplayer", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-effect", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-muxer", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-transformer-1.6.1.rc01.aar", "url": "media3-transformer-1.6.1.rc01.aar", "size": 389760, "sha512": "e34ad40e60b4a841a8afa72a992e6f1a9b6d73ad9a5f7533494885582191f75ca8ca734fdef60ceb37b060b224d02fbb22b3ced163daf035ac856f55810b297e", "sha256": "ab073705099cc36626384fe508fe0b6a1af8f5ecb44983fa4974b212bec99373", "sha1": "564bf7412aca461f95c6474689a267d734533549", "md5": "381f7bc027144885e41b8e1e9a848be4"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "media3-transformer-1.6.1.rc01-sources.jar", "url": "media3-transformer-1.6.1.rc01-sources.jar", "size": 256917, "sha512": "86ed4d91bc2fc7719b2b215997307dff7dbb417689b60451e4ba8f318a6308cb9a8c8d4af2f4dd33781e1a81ae3c5477e68a7d727c86c666a54b569fd537498b", "sha256": "bde3002a2cb78f331c41bd7d133288436c7d8c011e1dc911f3e629de9fc2822f", "sha1": "4775de20848144f6954ad18fa04fc8388f402bc0", "md5": "e43a09e1573a8b07851c1d49c8acb80d"}]}]}