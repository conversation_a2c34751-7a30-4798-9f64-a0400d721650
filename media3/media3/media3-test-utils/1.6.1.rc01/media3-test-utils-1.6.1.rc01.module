{"formatVersion": "1.1", "component": {"group": "androidx.media3", "module": "media3-test-utils", "version": "1.6.1.rc01", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.mockito", "module": "mockito-core", "version": {"requires": "3.12.4"}}, {"group": "androidx.test", "module": "core", "version": {"requires": "1.5.0"}}, {"group": "androidx.test.ext", "module": "junit", "version": {"requires": "1.1.5"}}, {"group": "androidx.test.ext", "module": "truth", "version": {"requires": "1.5.0"}}, {"group": "junit", "module": "junit", "version": {"requires": "4.13.2"}}, {"group": "com.google.truth", "module": "truth", "version": {"requires": "1.4.0"}}, {"group": "com.google.truth.extensions", "module": "truth-java8-extension", "version": {"requires": "1.4.0"}}, {"group": "androidx.media3", "module": "media3-exoplayer", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-test-utils-1.6.1.rc01.aar", "url": "media3-test-utils-1.6.1.rc01.aar", "size": 377772, "sha512": "8c0b151f5f1b26fa04ffbffaa207d35980d629ab1214d962ed813bd7cf0ed3c23d06e6df02a6d49c993b61e306c309bd4dbd87fff8933775a4d0ca970f026f30", "sha256": "8c2e188531cf86c0ff58db8c2ee4245690085954dbea9d0d04392337e05a5828", "sha1": "c3d172fd8b0ce5825dd3c84f41805e766ea4f913", "md5": "ea84a0cdc7250f6df396f9941db17632"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.6.0"}}, {"group": "com.squareup.okhttp3", "module": "mockwebserver", "version": {"requires": "4.12.0"}}, {"group": "org.mockito", "module": "mockito-core", "version": {"requires": "3.12.4"}}, {"group": "androidx.test", "module": "core", "version": {"requires": "1.5.0"}}, {"group": "androidx.test.ext", "module": "junit", "version": {"requires": "1.1.5"}}, {"group": "androidx.test.ext", "module": "truth", "version": {"requires": "1.5.0"}}, {"group": "junit", "module": "junit", "version": {"requires": "4.13.2"}}, {"group": "com.google.truth", "module": "truth", "version": {"requires": "1.4.0"}}, {"group": "com.google.truth.extensions", "module": "truth-java8-extension", "version": {"requires": "1.4.0"}}, {"group": "androidx.media3", "module": "media3-exoplayer", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-test-utils-1.6.1.rc01.aar", "url": "media3-test-utils-1.6.1.rc01.aar", "size": 377772, "sha512": "8c0b151f5f1b26fa04ffbffaa207d35980d629ab1214d962ed813bd7cf0ed3c23d06e6df02a6d49c993b61e306c309bd4dbd87fff8933775a4d0ca970f026f30", "sha256": "8c2e188531cf86c0ff58db8c2ee4245690085954dbea9d0d04392337e05a5828", "sha1": "c3d172fd8b0ce5825dd3c84f41805e766ea4f913", "md5": "ea84a0cdc7250f6df396f9941db17632"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "media3-test-utils-1.6.1.rc01-sources.jar", "url": "media3-test-utils-1.6.1.rc01-sources.jar", "size": 205876, "sha512": "3316127d6d72ec32914c6b1a37277e5433c6a6dfd133cfaecf2096963fb588a014ba267fb2411f7983e079094337e21fb24471f7aed35eac090454a059610406", "sha256": "20ceaee6d82f224eb5078dd58d0d0d33e60ca28ab5bf8a8940d17e9cad4bfce4", "sha1": "9f15562b774def7f538f959423d2f5915abdb5d4", "md5": "838114836481fe2e390f917b5853b49b"}]}]}