package com.fptplay.mobile.player.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.adapter.BaseViewHolder
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.databinding.PlayerMenuMoreItemBinding
import com.fptplay.mobile.player.model.PlayerBottomControlData
import com.fptplay.mobile.player.utils.PlayerConstants
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.visible
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger

class PlayerMenuMoreAdapter :
    BaseAdapter<PlayerBottomControlData, PlayerMenuMoreAdapter.PlayerMenuMoreItemViewHolder>() {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): PlayerMenuMoreItemViewHolder {
        return PlayerMenuMoreItemViewHolder(
            PlayerMenuMoreItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun onBindViewHolder(
        holder: PlayerMenuMoreItemViewHolder,
        position: Int
    ) {
        holder.bind(differ.currentList[position])
    }


    inner class PlayerMenuMoreItemViewHolder(val viewBinding: PlayerMenuMoreItemBinding) :
        BaseViewHolder(viewBinding) {
        init {
            viewBinding.root.onClickDelay {
                if (absoluteAdapterPosition in differ.currentList.indices) {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        differ.currentList[absoluteAdapterPosition]
                    )
                }
            }
        }

        fun bind(data: PlayerBottomControlData) {
            Logger.d("PlayerMenuMoreAdapter > Bind Item > ${data._id} > ${data.isEnable}")
            if (data.isEnable) {

                var valueToDisplay = data.value

                if (data._id == PlayerConstants.PlayerBottomControlType.ID_AUDIO_SETTING) {
                    MainApplication.INSTANCE.appConfig.audioReplacementName.forEach { audioPatternData ->
                        if (data.value.contains(audioPatternData.pattern)) {
                            valueToDisplay = data.value.replace(audioPatternData.pattern, "")
                            viewBinding.ivBadge.apply {
                                visible()
                                ImageProxy.load(
                                    context = viewBinding.root.context,
                                    url = audioPatternData.thumb,
                                    width = resources.getDimensionPixelSize(R.dimen.player_control_bottom_button_badge_icon_size),
                                    height = resources.getDimensionPixelSize(R.dimen.player_control_bottom_button_badge_icon_size),
                                    target = viewBinding.ivBadge,
                                )
                            }
                        } else {
                            viewBinding.ivBadge.gone()
                        }
                    }
                }

                viewBinding.tvTitle.text = data.title
                viewBinding.ivIcon.setImageResource(data.iconRes)
                viewBinding.tvValue.text = valueToDisplay
            } else {
                viewBinding.llContainer.gone()
            }
        }
    }
}