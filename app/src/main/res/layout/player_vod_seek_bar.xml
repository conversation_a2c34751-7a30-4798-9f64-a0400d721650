<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/player_timeline_progress_bar_wrapper">

    <ProgressBar
        android:id="@+id/player_progress_bar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:max="100"
        android:paddingVertical="@dimen/player_timeline_progress_bar_padding"
        android:progress="20"
        android:progressDrawable="@drawable/player_vod_timeline_progress_drawable"
        android:secondaryProgress="30"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="invisible" />

    <androidx.appcompat.widget.AppCompatSeekBar
        android:id="@+id/playerSeekProgress"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingVertical="@dimen/player_timeline_progress_bar_padding"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:progress="20"
        android:progressDrawable="@drawable/player_vod_seek_bar_progress_unfocus_drawable"
        android:splitTrack="false"
        android:thumb="@drawable/player_vod_seek_bar_thumb"
        app:layout_constraintBottom_toBottomOf="@id/player_progress_bar"
        app:layout_constraintTop_toTopOf="@id/player_progress_bar" />

</androidx.constraintlayout.widget.ConstraintLayout>