package com.fptplay.mobile.features.introduce

import android.annotation.SuppressLint
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.fptplay.mobile.HomeActivity
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.ActivityExtensions.startHome
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.DeeplinkUtils.addAdjustTrueLinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFacebookDeeplinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFirebaseDeeplinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFirebaseNotificationData
import com.fptplay.mobile.databinding.InteractionFragmentBinding
import com.fptplay.mobile.databinding.IntroduceFragmentBinding
import com.fptplay.mobile.databinding.SurveyFragmentBinding
import com.google.android.material.tabs.TabLayoutMediator
import com.google.gson.Gson
import com.xhbadxx.projects.module.domain.entity.fplay.common.TriggerEvent
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger
import javax.inject.Inject

@dagger.hilt.android.AndroidEntryPoint
class InteractionFragment : BaseFragment<IntroduceViewModel.IntroduceState, IntroduceViewModel.IntroduceIntent>() {
    override val viewModel: IntroduceViewModel by activityViewModels()
    private var _binding: InteractionFragmentBinding? = null
    private val binding get() = _binding!!
    override val hasEdgeToEdge: Boolean = true
    override val handleBackPressed: Boolean = true
    @Inject
    lateinit var sharedPreferences: SharedPreferences
    private val args: InteractionFragmentArgs by navArgs()
    private var event: TriggerEvent.Event? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = InteractionFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun initData() {
        event = try {
            Gson().fromJson(args.event, TriggerEvent.Event::class.java)
        } catch (e: Exception) {
            null
        }
        event?.let {
            loadImageBackground(MainApplication.INSTANCE.applicationContext.resources.configuration.orientation)
            binding.apply {
                tvHeadTitle.text = it.headTitle
                tvTitle.text = it.title
                tvDes.text = it.description
                it.buttons.getOrNull(0)?.let { button->
                    btnGo.text = button.text
                    binding.btnGo.setOnClickListener {
                        startHome(button.linkOtt)
                    }
                } ?: run {
                    startHome()
                }

            }
            loadImage()
        }?: run {
            startHome()
        }

    }

    private fun loadImage() {
        event?.let {
            val url = it.media.thumbnail.portrait.firstOrNull()?:""
            if (url.isNotEmpty()) {
                ImageProxy.load(
                    context = binding.root.context,
                    url = url,
                    width = binding.ivInteraction.width,
                    height = binding.ivInteraction.height,
                    target = binding.ivInteraction
                )
            }
        }
    }

    private fun loadImageBackground(orientation:Int) {
        event?.let {
            val isPortrait = orientation == Configuration.ORIENTATION_PORTRAIT
            val url = if (isPortrait) it.media.banner.portrait.getOrNull(0)?:"" else it.media.banner.landscape.getOrNull(0)?:""
            ImageProxy.load(
                context = binding.root.context,
                url = url,
                width = binding.ivBackground.width,
                height = binding.ivBackground.height,
                target = binding.ivBackground
            )
        }?: run{
            binding.ivBackground.setImageResource(0)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        loadImageBackground(newConfig.orientation)
    }
    override fun bindEvent() {
        binding.ivClose.setOnClickListener {
            startHome()
        }
    }

    private fun startHome(deepLink:String? = null) {
        requireActivity().startHome(sharedPreferences, deepLink?: args.originalLink)
    }

    override fun IntroduceViewModel.IntroduceState.toUI() {

    }
}
