package com.fptplay.mobile.features.pladio.service

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Build
import android.view.KeyEvent
import androidx.media3.common.Rating
import androidx.media3.common.util.UnstableApi
import androidx.media3.session.MediaLibraryService
import androidx.media3.session.MediaSession
import androidx.media3.session.SessionResult
import com.fptplay.mobile.features.pladio.data.Song
import com.google.common.util.concurrent.Futures
import com.google.common.util.concurrent.ListenableFuture
import com.tear.modules.player.exo.ExoPlayerProxy
import com.xhbadxx.projects.module.util.logger.Logger


@UnstableApi
@SuppressLint("RestrictedApi")
class MediaLibrarySessionCallback(
    private val playbackService: PlaybackService,
) : MediaLibraryService.MediaLibrarySession.Callback {

    private val TAG = "PladioMediaLibrarySessionCallback"

    override fun onConnect(
        session: MediaSession,
        controller: MediaSession.ControllerInfo
    ): MediaSession.ConnectionResult {
        val availableSessionCommands = MediaSession.ConnectionResult.DEFAULT_SESSION_COMMANDS.buildUpon()
        val availablePlayerCommands = playbackService.getCommand()
        return MediaSession.ConnectionResult.accept(
            availableSessionCommands.build(),
            availablePlayerCommands
        )
    }

    override fun onPostConnect(session: MediaSession, controller: MediaSession.ControllerInfo) {
        super.onPostConnect(session, controller)
//        playbackService.updateMediaMetadata(playbackService.exoPlayer as? ExoPlayerProxy)
    }

    override fun onSetRating(
        session: MediaSession,
        controller: MediaSession.ControllerInfo,
        rating: Rating
    ): ListenableFuture<SessionResult> {
        return super.onSetRating(session, controller, rating)
    }
    override fun onMediaButtonEvent(
        session: MediaSession,
        controller: MediaSession.ControllerInfo,
        mediaButtonEvent: Intent
    ): Boolean {
        val keyEvent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            mediaButtonEvent.getParcelableExtra(Intent.EXTRA_KEY_EVENT, KeyEvent::class.java)
        } else {
            @Suppress("DEPRECATION")
            mediaButtonEvent.getParcelableExtra(Intent.EXTRA_KEY_EVENT)
        }
        // TODO: Handle seek to

        Logger.d("$TAG -> MediaButtonEvent -> KeyEvent: $keyEvent -> keycode: ${keyEvent?.keyCode}")
        if (keyEvent != null && keyEvent.action == KeyEvent.ACTION_DOWN) {
            when (keyEvent.keyCode) {
                KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE -> {
                    if (playbackService.checkHandlePlayPause()) {
                        return true
                    }
                    return !checkHandlePlayPause()
                }
                KeyEvent.KEYCODE_MEDIA_PLAY -> {
                    if (playbackService.checkHandlePlayPause()) {
                        return true
                    }
                    return !handlePlay()
                }
                KeyEvent.KEYCODE_MEDIA_PAUSE -> {
                    if (playbackService.checkHandlePlayPause()) {
                        return true
                    }
                    return !handlePause()
                }
                KeyEvent.KEYCODE_MEDIA_NEXT -> {
                    return !handleSkipToNext()
                }
                KeyEvent.KEYCODE_MEDIA_PREVIOUS -> {
                    return !handleSkipToPrevious()
                }
                KeyEvent.KEYCODE_MEDIA_STOP -> {
                    return !handleStop()
                }
            }
        }
        return false
    }

    override fun onSetMediaItems(
        mediaSession: MediaSession,
        controller: MediaSession.ControllerInfo,
        mediaItems: List<androidx.media3.common.MediaItem>,
        startIndex: Int,
        startPositionMs: Long
    ): ListenableFuture<MediaSession.MediaItemsWithStartPosition> {
        return Futures.immediateFuture(
            MediaSession.MediaItemsWithStartPosition(
                mediaItems,
                startIndex,
                startPositionMs
            )
        )
    }

    private fun checkHandlePlayPause(): Boolean {
        Logger.d("$TAG -> Custom checkHandlePlayPause")
        try {
            playbackService.handlePlayPause(true)
            return true
        } catch (e: Exception) {
            Logger.d("$TAG -> Error in handlePlay: ${e.message}")
            return false
        }
    }

    private fun handlePlay(): Boolean {
        Logger.d("$TAG -> Custom handlePlay")
        try {
            if (playbackService.currentSong != Song.emptySong) playbackService.play()
            return true
        } catch (e: Exception) {
            Logger.d("$TAG -> Error in handlePlay: ${e.message}")
            return false
        }
    }

    private fun handlePause(): Boolean {
        Logger.d("$TAG -> Custom handlePause")
        try {
            playbackService.pause()
            return true
        } catch (e: Exception) {
            Logger.d("$TAG -> Error in handlePause: ${e.message}")
            return false
        }
    }

    private fun handleSkipToNext(): Boolean {
        Logger.d("$TAG -> Custom handleSkipToNext")
        try {
            playbackService.playNextSong(true)
            return true
        } catch (e: Exception) {
            Logger.d("$TAG -> Error in handleSkipToNext: ${e.message}")
            return false
        }
    }

    private fun handleSkipToPrevious(): Boolean {
        Logger.d("$TAG -> Custom handleSkipToPrevious")
        try {
            playbackService.playPreviousSong(true)
            return true
        } catch (e: Exception) {
            Logger.d("$TAG -> Error in handleSkipToPrevious: ${e.message}")
            return false
        }
    }

    private fun handleStop(): Boolean {
        Logger.d("$TAG -> Custom handleStop")
        try {
            playbackService.quit()
            return true
        } catch (e: Exception) {
            Logger.d("$TAG -> Error in handleStop: ${e.message}")
            return false
        }
    }

    private fun handleSeek(pos: Long) {
        Logger.d("$TAG -> Custom handleSeek")
        try {
            playbackService.seek(pos)
        } catch (e: Exception) {
            Logger.d("$TAG -> Error in handleSeek: ${e.message}")
        }
    }
}