package com.fptplay.mobile.common.ui.bases

import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.annotation.LayoutRes
import androidx.annotation.RequiresApi
import androidx.core.app.PictureInPictureModeChangedInfo
import androidx.core.util.Consumer
import androidx.fragment.app.FragmentActivity
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import com.clevertap.android.sdk.CTInboxListener
import com.clevertap.android.sdk.CleverTapAPI
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.ActivityExtensions.hideLoading
import com.fptplay.mobile.common.extensions.ActivityExtensions.isInPiPMode
import com.fptplay.mobile.common.ui.view.GlobalSnackbarManager
import com.fptplay.mobile.common.utils.CheckValidUtil
import com.fptplay.mobile.common.utils.DateTimeUtils
import com.fptplay.mobile.common.utils.DeeplinkLocalUtils
import com.fptplay.mobile.common.utils.DeeplinkUtils
import com.fptplay.mobile.common.utils.NetworkUtils
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.common.utils.ZendeskUtils
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.mega.apps.airline.util.LocaleLanguageManager
import com.fptplay.mobile.features.tracking_ga4.TrackingGA4Proxy
import com.fptplay.mobile.services.FireBaseMessagingLifecycleObserver
import com.fptplay.mobile.services.callback.OnFireBaseNotificationInForegroundListener
import com.google.android.material.snackbar.BaseTransientBottomBar.Duration
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.util.concurrent.CopyOnWriteArrayList

@AndroidEntryPoint
open class BaseActivity(@LayoutRes contentLayoutId: Int) : FragmentActivity(contentLayoutId), CTInboxListener {
    open val enableLocalLanguage = false
    open val enableNetworkListener = false

    open val navHostFragment: NavHostFragment? = null
    protected var cleverTapDefaultInstance: CleverTapAPI? = null

    // region Picture in Picture
    private val onUserLeaveHintListeners = CopyOnWriteArrayList<Runnable>()
    private val onPictureInPictureModeChangedListeners = CopyOnWriteArrayList<Consumer<PictureInPictureModeChangedInfo>>()
    // endregion
    protected val firebaseMessagingLifecycleObserver by lazy {
        FireBaseMessagingLifecycleObserver(this, object : OnFireBaseNotificationInForegroundListener{
            override fun onFireBaseNotificationInForegroundListener(
                type: String?,
                typeId: String?,
                title: String?,
                url: String?
            ) {

                navHostFragment?.let {
                    //log kibana 19
                    Logger.d("trangtest === noti base activity")
                    val trackingProxy = MainApplication.INSTANCE.trackingProxy
                    val trackingInfo = MainApplication.INSTANCE.trackingInfo
                    //log kibana 19
                    var appName = ""
                    if (CheckValidUtil.checkValidString(type) && CheckValidUtil.checkValidString(typeId)){
                        appName = type?: ""
                    }else{
                        appName = "Deeplink"
                        Utils.checkAndSaveUTM(
                            deeplink = url ?: "",
                            isDeeplinkCalledInApp = false
                        )
                    }
                    AdjustAllEvent.sendPushNotifyOpenEvent(notificationId = typeId?: "", notificationTitle = title?: "")
                    trackingProxy.sendEvent(
                        InforMobile(infor = trackingInfo,
                            logId = "19",
                            appId = appName,
                            appName = appName,
                            event = "Confirmed",
                            screen = "Ok",
                            boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                            itemId = typeId?:"",
                            itemName = title?:"",

                            ))
                    TrackingUtil.screen = TrackingUtil.screenNotification //set notification for view after

                    val isProcessDeeplinkFromTypeNotification = DeeplinkLocalUtils.processAndPlayDeepLinksFromTypeNotification(context = this@BaseActivity, navHostFragment = it, type = type, contentId = typeId)
                    if(!isProcessDeeplinkFromTypeNotification && !url.isNullOrBlank()) {
                        DeeplinkUtils.parseDeepLinkAndExecute(
                            deeplink = url,
                            useWebViewInApp = false,
                            trackingInfo = MainApplication.INSTANCE.trackingInfo,
                            isDeeplinkCalledInApp = false
                        )
                    }
                }
            }
        })
    }

    override fun attachBaseContext(newBase: Context) {
        if (enableLocalLanguage) {
            super.attachBaseContext(LocaleLanguageManager.setLocale(newBase))
        } else {
            super.attachBaseContext(LocaleLanguageManager.setLocaleVN(newBase))
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initCleverTap()
        addFireBaseNotificationListener()
        initTrackingCrashReportScreenView()
        if (enableNetworkListener) {
            registerNetworkListener()
        }
    }
    //endregion
    open fun initCleverTap() {
        try {
            cleverTapDefaultInstance = CleverTapAPI.getDefaultInstance(applicationContext)

                cleverTapDefaultInstance?.apply {
                    enableDeviceNetworkInfoReporting(true)
                    ctNotificationInboxListener = this@BaseActivity
                    initializeInbox()
                }

        } catch (ex: Exception) {
            Timber.e(ex)
        }
    }

    private fun addFireBaseNotificationListener() {
        ZendeskUtils.initBaseZendeskIfNeed(this)
        lifecycle.addObserver(firebaseMessagingLifecycleObserver)
    }

    private fun initTrackingCrashReportScreenView(){
        val screen = javaClass.simpleName
        TrackingGA4Proxy.sendTrackingCrashReportScreenView(screen = screen)
    }
    override fun inboxDidInitialize() {

    }

    override fun inboxMessagesDidUpdate() {

    }

    override fun onUserLeaveHint() {
        super.onUserLeaveHint()
        for (listener in onUserLeaveHintListeners) {
            listener.run()
        }
    }


    @RequiresApi(Build.VERSION_CODES.O)
    override fun onPictureInPictureModeChanged(
        isInPictureInPictureMode: Boolean,
        newConfig: Configuration
    ) {
        super.onPictureInPictureModeChanged(isInPictureInPictureMode, newConfig)

        for (listener in onPictureInPictureModeChangedListeners) {
            listener.accept(
                PictureInPictureModeChangedInfo(
                    isInPictureInPictureMode, newConfig
                )
            )
        }

        if(isInPictureInPictureMode){
            TrackingGA4Proxy.sendTrackingCrashReportModeApp(modeApp = "appInModePiP")
        }
        else{
            TrackingGA4Proxy.sendTrackingCrashReportModeApp(modeApp = "appExistInModePiP")
        }
    }

    // region Picture in Picture
    open fun addOnUserLeaveHintListener(listener: Runnable) {
        onUserLeaveHintListeners.add(listener)
    }

    open fun removeOnUserLeaveHintListener(listener: Runnable) {
        onUserLeaveHintListeners.remove(listener)
    }

    open fun addOnPictureInPictureChangedListener(
        listener: Consumer<PictureInPictureModeChangedInfo>
    ) {
        onPictureInPictureModeChangedListeners.add(listener)
    }

    open fun removeOnPictureInPictureChangedListener(
        listener: Consumer<PictureInPictureModeChangedInfo>
    ) {
        onPictureInPictureModeChangedListeners.remove(listener)
    }
    // endregion
    // network
    protected open fun bindNetworkStateChange(hasNetWork: Boolean) {}

    protected open fun onBeforeRegisterNetworkListener() {}

    protected open fun registerNetworkListener() {
        GlobalSnackbarManager.isFirstOpenApp = NetworkUtils.isNetworkAvailable()
        onBeforeRegisterNetworkListener()
        MainApplication.INSTANCE.networkDetector.observe(this) {
            it?.let { hasNetwork ->
                bindNetworkStateChange(hasNetwork)
                val isHasNetwork = if (hasNetwork) NetworkUtils.isNetworkAvailable() else false
                if (!isInPiPMode()) GlobalSnackbarManager.showMessageNetworkDetector(isHasNetwork = isHasNetwork)
            }
        }
    }
}
