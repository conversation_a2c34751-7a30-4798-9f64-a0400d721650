package com.fptplay.mobile.features.event_trigger

import android.content.Intent
import android.content.res.Configuration
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.drowsyatmidnight.haint.android_banner_sdk.model.BannerRequestParams
import com.drowsyatmidnight.haint.android_banner_sdk.welcome_banner.WelcomeBanner
import com.drowsyatmidnight.haint.android_banner_sdk.welcome_banner.WelcomeBannerView
import com.fptplay.mobile.HomeActivity
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.ActivityExtensions.startHome
import com.fptplay.mobile.common.extensions.getDisplayHeight
import com.fptplay.mobile.common.extensions.getDisplayWidth
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.DeeplinkUtils
import com.fptplay.mobile.common.utils.DeeplinkUtils.addAdjustTrueLinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addClevertapDeeplinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFacebookDeeplinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFirebaseDeeplinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFirebaseNotificationData
import com.fptplay.mobile.common.utils.DelayHandler
import com.fptplay.mobile.databinding.LandingPageFragmentBinding
import com.fptplay.mobile.features.ads.banner.AdsBannerListener
import com.fptplay.mobile.features.ads.utils.AdsUtils
import com.fptplay.mobile.features.event_trigger.adapter.LandingPageAdapter
import com.fptplay.mobile.features.event_trigger.data.EventButton
import com.fptplay.mobile.welcome.WelcomeViewModel
import com.google.gson.Gson
import com.xhbadxx.projects.module.domain.entity.fplay.common.LandingPage
import com.xhbadxx.projects.module.domain.entity.fplay.common.TriggerEvent
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.image.ImageProxy
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@AndroidEntryPoint
class LandingPageFragment : BaseFragment<WelcomeViewModel.WelcomeState, WelcomeViewModel.WelcomeIntent>() {

    override val viewModel: WelcomeViewModel by activityViewModels()

    @Inject
    lateinit var sharedPreferences: SharedPreferences
    override val handleBackPressed = true
    private var _binding: LandingPageFragmentBinding? = null
    private val binding get() = _binding!!
    private val landingPageAdapter: LandingPageAdapter = LandingPageAdapter()

    private var welcomeBannerView: WelcomeBannerView? = null
    private var welcomeBanner: WelcomeBanner? = null

    private val safeArgs by navArgs<LandingPageFragmentArgs>()

    //  region Expire Time
    private val expireTime = MainApplication.INSTANCE.appConfig.expireWelcome

    // endregion Expire Time

    override val handleConfigurationChange = true
    private var isLandscapeMode = false

    private var event: TriggerEvent.Event? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        _binding = LandingPageFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onStop() {
        super.onStop()
        destroyBannerAds()
    }

    override fun onResume() {
        super.onResume()
        if (binding.rv.tag == 1) { //first time get data from model
            bindData()
        } else binding.rv.tag = 1
    }

    override fun bindComponent() {
        updateOrientationFlag(newConfig = MainApplication.INSTANCE.applicationContext.resources.configuration)
    }

    override fun bindData() {
        event = try {
            Gson().fromJson(safeArgs.event, TriggerEvent.Event::class.java)
        } catch (e: Exception) {
            null
        }
        event?.let {
            var displayWidth = requireContext().getDisplayWidth()
            var displayHeight = requireContext().getDisplayHeight()
            if(isLandscapeMode) {
                displayWidth = requireContext().getDisplayHeight()
                displayHeight = requireContext().getDisplayWidth()
            }
            Timber.tag("tam-landingpage").i("bindData $displayWidth - $displayHeight")
            val height = requireContext().getDisplayHeight()
            val width = (height/16)*9
            val image = it.media.banner.portrait.firstOrNull()
            Timber.d("LogSize || Display [ w:${requireContext().getDisplayWidth()} h:${requireContext().getDisplayHeight()} ] || width : $width || height : $height || url : $image")
            ImageProxy.load(
                context = requireContext(),
                url = image ?: "",
                width = width,
                height = height,
                target = binding.iv
            )
            binding.rv.apply {
                adapter = landingPageAdapter
                layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            }
            val listButton = it.buttons.map { button ->
                EventButton(
                    text = button.text,
                    deeplink = button.linkOtt,
                    countdown = it.skipTime.toIntOrNull()
                )
            }
            landingPageAdapter.bind(listButton)
            landingPageAdapter.eventListener = object : IEventListener<EventButton> {
                override fun onClickView(position: Int, view: View?, data: EventButton) {
                    if (data.deeplink.isNotBlank()) {
                        startHome(data)
                    } else {
                        startHome()
                    }
                }
            }
            val video = it.media.video.landscape.firstOrNull()
            video?.let { v ->
                if(v.isNotBlank()) {
                    startBannerAds(v)
                }
            }
            // Expire
            startDelayExpire()
        } ?: kotlin.run {
            startHome()
        }
    }


    override fun bindOrientationStateChange(newConfig: Configuration) {
        super.bindOrientationStateChange(newConfig)
        updateOrientationFlag(newConfig)
    }

    override fun WelcomeViewModel.WelcomeState.toUI() {
        when (this) {
            is WelcomeViewModel.WelcomeState.Error,
            is WelcomeViewModel.WelcomeState.NoInternet -> {
                startHome()
            }
            else -> {
                Timber.d("Else loi tra ve $this")
            }
        }
    }

    private fun startHome(data: EventButton? = null) {
        requireActivity().startHome(sharedPreferences, data?.deeplink ?: safeArgs.originalLink)
    }

    // region Method Expire Time
    private fun startDelayExpire() {
        if(expireTime > 0) {
            // expire time in seconds
            DelayHandler(TimeUnit.SECONDS.toMillis(expireTime.toLong())).apply {
                runnable {
                    startHome()
                }
                viewLifecycleOwner.lifecycle.addObserver(this)
                start()

            }
        }
    }
    // endregion Method Expire Time

    // region Ads
    private fun initWelcomeBanner() {
        initWelcomeBannerView()
        if (welcomeBanner == null) {
            welcomeBanner = WelcomeBanner(welcomeBannerView, requireContext()).apply {
                bannerListener = AdsBannerListener(viewLifecycleOwner)
            }
        }
    }

    private fun initWelcomeBannerView() {
        if (welcomeBannerView == null) {
            welcomeBannerView = WelcomeBannerView(requireContext()).apply {
                AdsUtils.addBannerView(
                    bannerContainer = binding.ctlWelcomeAdContainer,
                    bannerView = this,
                    width = ConstraintLayout.LayoutParams.MATCH_PARENT,
                    height = ConstraintLayout.LayoutParams.MATCH_PARENT
                )
                hide()
            }
        }

    }

    private fun startBannerAds(prefixUrl: String) {

        if (AdsUtils.canRequestAds(
                activity,
                sharedPreferences,
                disableOn3g = false,
                disableOnDisAdsPurchased = false
            )
        ) {
            initWelcomeBanner()
            val bannerRequestParams = BannerRequestParams(
                deliveryPrefix = prefixUrl.ifBlank { null },
                isUseData = AdsUtils.useMobileData(activity),
                userType = AdsUtils.userType(sharedPreferences),
                appVersion = AdsUtils.versionApp(),
                userId = AdsUtils.userId(sharedPreferences),
                uuid = AdsUtils.uniqueUid(sharedPreferences),
                profileId = AdsUtils.profileId(sharedPreferences),
                profileType = AdsUtils.profileType(sharedPreferences)

            )

            if (prefixUrl.isNotBlank()) {
                welcomeBanner?.showBanner(
                    lifecycleOwner = viewLifecycleOwner,
                    bannerRequestParams = bannerRequestParams
                )
            } else {
                welcomeBanner?.showBanner(
                    lifecycleOwner = viewLifecycleOwner,
                    bannerRequestParams = bannerRequestParams
                )
            }
        }
    }

    private fun destroyBannerAds() {
        welcomeBanner?.destroyBanner() // already destroy view in here
        welcomeBanner = null
        welcomeBannerView = null
        binding.ctlWelcomeAdContainer.removeAllViews()
    }

    // endregion Ads


    private fun updateOrientationFlag(newConfig: Configuration) {
        when (newConfig.orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> {
                isLandscapeMode = true
            }
            else -> {
                isLandscapeMode = false
            }
        }

    }

    override fun backHandler() {
        activity?.finish()
    }

}