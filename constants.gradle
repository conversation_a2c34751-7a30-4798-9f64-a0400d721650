project.ext {
    cardview_version = "1.0.0"
    sp_dp_version = "1.0.6"
    recyclerview_version = "1.2.1"
    constraint_layout_version = "2.1.3"
    app_compat_version = "1.4.1"
    material_version = "1.5.0"
    junit_version = "4.13.2"
    // Test
    ext_junit = "1.1.3"
    core_espresso = "3.4.0"
    // Common
    multidex_version = "2.0.1"
    kotlin_version = "1.8.20"
    kotlin_coroutine_version = "1.5.2"
    // Hilt
    hilt_work_manager_version = "1.2.0"
    hilt_fragment_version = "1.2.0"
    hilt_compiler_version = "1.2.0"
    hilt_dagger_version = "2.56"
    hilt_dagger_gradle_version = "2.51.1"
    // Lifecycle
    lifecycle_version = "2.4.0"
    // Ktx
    ktx_version = "1.10.0"
    ktx_fragment_version = "1.4.1"
    ktx_activity_version = "1.4.0"
    ktx_preference_version = "1.2.0"
    // Room
    room_version = "2.5.1"
    // Work Manager
    work_version = "2.7.1"
    // Server
    ktor_version = "1.5.2"
    retrofit_version = "2.11.0"
    // Logger
    okhttp_logging_version = "5.0.0-alpha.2"
    timber_version = "5.0.1"
    // Okio
    okio_version = "2.10.0"
    // Google Ima
    google_ima = "3.24.0"
    // Gson
    gson_version = "2.10"
    moshi_version = "1.15.0"
    // Navigation
    nav_version = "2.5.0-alpha01"
    // Coil
    coil_version = "2.0.0-alpha04"
    // Glide
    glide_version = "4.12.0"
    // ExoPlayer
    exo_player_version = "2.17.1"
    //hbad player
    sigma_packer_version = "media3-1.6.1:1.0.3"
    media3_version = "1.6.1.rc01"
    exoplayer_version = "2.17.1"
    //Swipe to refresh
    swipe_refresh_version = "1.1.0"
    // Facebook Sharing
    facebook_share_version = "[5,6)"
    // Facebook Login
    facebook_login_version = "16.2.0"
    // Facebook Login
    facebook_applink_version = "[4,5)"
    //App link
    applink_version = "1.4.0"
    // Leanback
    leanback_version = "1.2.0-alpha02"
    // Cast
    media_router = "1.3.1"
    cast_framework = "21.3.0"
    // BarcodeScanner ZXing
    barcodescanner_zxing = "1.9.8"
    // Lottie
    lottie_version = "4.2.2"
    // Flexbox
    flexbox_version = "3.0.0"
    // Google billing
    //Firebase
    firebase_core_version = "17.0.0"
    firebase_bom = "29.3.1"

    google_billing_version = "6.0.1"
    firestore = "20.1.0"
    //Zendesk
    zendesk_version = "5.1.0"
    //Clevertap
    clevertap_version = "7.0.0"
    clevertap_templates_version = "1.2.4"
    //AppFlyer
    app_flyer_version = "6.3.2"
    //InstallReferrer
    install_referrer_version = "2.2"
    //Lottie
    lottieVersion = "5.2.0"
    //NumberPicker
    numberPickerVersion = "2.4.13"
    //Tip-Guis
    tipGuiVersion = "1.4.7"
    //Foxpay
    foxpayVersion = "1.1.8"
    //Camerax
    camerax_version = "1.0.1"
    play_integrity_version = "1.2.0"
}