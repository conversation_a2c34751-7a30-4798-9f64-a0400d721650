package com.fptplay.mobile.features.multi_profile.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import com.fptplay.mobile.HomeActivity
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.DeeplinkConstants
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.features.short_video.DataCacheObject
import com.xhbadxx.projects.module.domain.entity.fplay.user.Profile
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import timber.log.Timber
import java.util.concurrent.TimeUnit

object MultiProfileUtils {


    fun switchProfile(activity: Activity?, sharedPreferences: SharedPreferences, profile: Profile, restartAppAfterSwitch: Boolean = true,restartKeepIntent:Boolean = false) {
        saveCurrentProfile(sharedPreferences, profile)
        TrackingUtil.saveProfileForLog(profile)
        TrackingLogProfile.sendLogSwitchProfile()
        pairingControlStopSession(activity)
        if(restartAppAfterSwitch) {
//            activity?.recreate()
            restartHome(activity,restartKeepIntent = restartKeepIntent)
        }
    }

    fun restartHome(activity: Activity?,restartKeepIntent:Boolean = false) {
        DataCacheObject.dataCache.setNeedToReload() //reset data cache short
        if (restartKeepIntent){
            MainApplication.INSTANCE.isReloadHome = true
            val oldIntent = activity?.intent
            val intent = Intent(activity, HomeActivity::class.java).apply {
                putExtras(oldIntent?.extras ?: Bundle()) // Copy all extras
                putExtra(DeeplinkConstants.REQUIRE_RESUME_ACTIVITY, false)
                data = oldIntent?.data // Preserve deep link URI
            }
            activity?.finish()
            activity?.startActivity(intent)
        }
        else{
            MainApplication.INSTANCE.isReloadHome = true
            val intent = Intent(activity, HomeActivity::class.java)
            activity?.finish()
            activity?.startActivity(intent)
        }

    }

    fun saveCurrentProfile(sharedPreferences: SharedPreferences, profile: Profile) {
        profile.selected = true
        sharedPreferences.apply {
            saveProfileId(profile.id)
            saveProfileName(profile.name)
            saveProfileAvatar(profile.avatarUrl)
            saveProfileType(profile.profileType.id)
            saveProfileEnableD2g(profile.enableD2g)
            saveProfileRoot(profile.isProfileRoot)
            saveProfileEnableSelection(profile.enableSelection)
        }

    }

    fun clearCurrentProfile(sharedPreferences: SharedPreferences) {
        sharedPreferences.apply{
            saveProfileId("")
            saveProfileName("")
            saveProfileAvatar("")
            saveProfileType("")
            saveProfileEnableD2g(true)
            saveProfileRoot(false)
            saveLastTimeSelectProfile(0L)
            saveProfileEnableSelection("")
        }

    }

    fun checkProfileChanged(sharedPreferences: SharedPreferences, oldProfileId: String, oldProfileType: String): Boolean =
        oldProfileId != sharedPreferences.profileId() || oldProfileType != sharedPreferences.profileType()

    fun sendEventProfileChangedIfChange(fragment: Fragment?, sharedPreferences: SharedPreferences, sourceChange: String, oldProfileId: String, oldProfileType: String) {
        val sendEvent = checkProfileChanged(sharedPreferences, oldProfileId, oldProfileType)
        // send event if profile id or type changed
        if(!sendEvent) {
            return
        }
        fragment?.setFragmentResult(
            Utils.PROFILE_CHANGED_EVENT,
            bundleOf(
                Utils.PROFILE_CHANGE_SOURCE to sourceChange,
                Utils.PROFILE_ID_OLD to oldProfileId,
                Utils.PROFILE_ID_NEW to sharedPreferences.profileId(),
                Utils.PROFILE_TYPE_OLD to oldProfileType,

                Utils.PROFILE_TYPE_NEW to sharedPreferences.profileType()
            )
        )

    }
    fun sendEventProfileOnboardingIfSwitchProfileWithPin(
        activity: Activity?,
        profile: Profile,
        fragment: Fragment?,
        sharedPreferences: SharedPreferences,
        sourceChange: String,
        oldProfileId: String,
        oldProfileType: String)
    {
        saveCurrentProfile(sharedPreferences, profile)
        TrackingUtil.saveProfileForLog(profile)
        TrackingLogProfile.sendLogSwitchProfile()
        pairingControlStopSession(activity)
        fragment?.setFragmentResult(
            Utils.PROFILE_ONBOARD_SWITCH_PROFILE_PIN_CHANGED_EVENT,
            bundleOf(
                Utils.PROFILE_CHANGE_SOURCE to sourceChange,
                Utils.PROFILE_ID_OLD to oldProfileId,
                Utils.PROFILE_ID_NEW to sharedPreferences.profileId(),
                Utils.PROFILE_TYPE_OLD to oldProfileType,

                Utils.PROFILE_TYPE_NEW to sharedPreferences.profileType()
            )
        )
    }
    fun sendEventProfileOnboardingIfSwitchProfile(
        activity: Activity?,
        profile: Profile,
        fragment: Fragment?,
        sharedPreferences: SharedPreferences,
        sourceChange: String,
        oldProfileId: String,
        oldProfileType: String)
    {
        saveCurrentProfile(sharedPreferences, profile)
        TrackingUtil.saveProfileForLog(profile)
        TrackingLogProfile.sendLogSwitchProfile()
        pairingControlStopSession(activity)
        fragment?.setFragmentResult(
            Utils.PROFILE_ONBOARD_SWITCH_PROFILE_CHANGED_EVENT,
            bundleOf(
                Utils.PROFILE_CHANGE_SOURCE to sourceChange,
                Utils.PROFILE_ID_OLD to oldProfileId,
                Utils.PROFILE_ID_NEW to sharedPreferences.profileId(),
                Utils.PROFILE_TYPE_OLD to oldProfileType,

                Utils.PROFILE_TYPE_NEW to sharedPreferences.profileType()
            )
        )
    }

    fun Profile.isProfileKid() = this.profileType == Profile.ProfileType.Kid
    fun isProfileKid(profileType: String) = profileType == Profile.ProfileType.Kid.id
    fun isCurrentProfileKid(sharedPreferences: SharedPreferences) = sharedPreferences.userLogin() && sharedPreferences.profileType() == Profile.ProfileType.Kid.id

    fun Profile.isProfilePrivate() = this.pinType == Profile.PinType.RequestPrivatePin
    fun isProfilePrivate(pinType: String) = pinType == Profile.PinType.RequestPrivatePin.id
    fun profileCanEdit(currentProfileId: String, isCurrentProfileKid: Boolean, targetProfile: Profile): Boolean {
        return if(isCurrentProfileKid) {
            // profile kid can only edit their profile
            (targetProfile.id == currentProfileId)
        } else {
            // normal profile can edit every profile
            true
        }
    }
    fun profileEnabledDownload(sharedPreferences: SharedPreferences) = !sharedPreferences.userLogin() || sharedPreferences.isProfileEnableD2g()

    fun profileIsHadOnBoarding(sharedPreferences: SharedPreferences) = MainApplication.INSTANCE.isOpenOnBoardingAndWithProfile && sharedPreferences.getEnableProfileOnLogin() == "1" && sharedPreferences.userLogin()

    fun shouldShowProfileSelection(
        sharedPreferences: SharedPreferences,
    ): Boolean {
        if (sharedPreferences.userLogin()){
            when(sharedPreferences.getEnableProfileOnLogin()) {
                SelectProfileType.ENABLE_SELECT_PROFILE.value -> {
                    // note : logic < sprint 17
                    Timber.tag("tam-multiProfile").d("shouldShowProfileSelection ENABLE_SELECT_PROFILE")
                    return true
                }
                SelectProfileType.ENABLE_SESSION_SELECT_PROFILE.value -> {
                    // note : logic sprint 17
                    if (sharedPreferences.profileEnableSelection() == "1") {
                        // Note : if > 2 profile -> false || if < 1 profile => true
                        Timber.tag("tam-multiProfile").d("shouldShowProfileSelection ENABLE_SESSION_SELECT_PROFILE")
                        return  hasPassedDaysSinceLastProfileSelection(sharedPreferences)
                    }
                }
                else -> {
                    return false
                }
            }
        }
        return false
    }
    
    private fun hasPassedDaysSinceLastProfileSelection(sharedPreferences: SharedPreferences): Boolean {
        val lastTimeShown = sharedPreferences.getLastTimeSelectProfile()
        val selectionResetInterval  = sharedPreferences.getSelectionResetIntervalInSecond()
        val currentTime = System.currentTimeMillis()
        val daysInMillis = TimeUnit.SECONDS.toMillis(selectionResetInterval)
        Timber.tag("tam-multiProfile").d("hasPassedDaysSinceLastProfileSelection selectionResetInterval : $selectionResetInterval lastTimeShown: $lastTimeShown currentTime: $currentTime sevenDaysInMillis: $daysInMillis")
        return lastTimeShown <= 0 || (currentTime - lastTimeShown) >= daysInMillis
    }

    fun saveProfileSelectionShownTime(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveLastTimeSelectProfile(System.currentTimeMillis())
    }

    fun errorMessage(context: Context, message: String?): String {
        return if(message.isNullOrBlank()) {
            context.getString(R.string.multi_profile_login_profile_error)
        } else{
            message
        }
    }

    //region Pairing Control
    private fun pairingControlStopSession(activity: Activity?) {
        MainApplication.INSTANCE.pairingConnectionHelper.let {
            if (it.isConnected) {
                it.switchProfileStopSession()
                activity?.let { activity ->
                    it.showToast(activity.getString(R.string.pairing_cast_title_player_stop_toast, it.getReceiverName()))
                }
            }
        }
    }
    //endregion
}