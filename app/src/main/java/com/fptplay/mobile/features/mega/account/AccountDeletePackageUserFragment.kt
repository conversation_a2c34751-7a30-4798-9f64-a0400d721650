package com.fptplay.mobile.features.mega.account

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.fptplay.mobile.NavAccountInfoDirections
import com.fptplay.mobile.NavAccountOtpActionDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.databinding.AccountDeletePackageUserFragmentBinding
import com.fptplay.mobile.features.mega.MegaViewModel
import com.fptplay.mobile.features.mega.account.adapter.GroupPackageUserAdapter
import com.fptplay.mobile.features.mega.account.model.GroupPackageUser
import com.xhbadxx.projects.module.domain.entity.fplay.otp.ValidateDisableUserData
import com.xhbadxx.projects.module.domain.entity.fplay.otp.ValidateDisableUserPackage
import com.xhbadxx.projects.module.domain.entity.fplay.payment.PackageUserV3Item
import com.xhbadxx.projects.module.domain.entity.fplay.user.CheckUserDeletePackage
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject


@AndroidEntryPoint
class AccountDeletePackageUserFragment : BaseFragment<MegaViewModel.MegaState, MegaViewModel.MegaIntent>() {
    override val hasEdgeToEdge = true
    //region Variables
    override val viewModel by activityViewModels<MegaViewModel>()
    private var _binding: AccountDeletePackageUserFragmentBinding? = null
    private val binding get() = _binding!!
    private val groupAdapter by lazy { GroupPackageUserAdapter() }
    override val handleBackPressed = true
    @Inject
    lateinit var sharedPreferences: SharedPreferences
    //endregion
    private val userDeleteData: ValidateDisableUserData? get() = viewModel.getUserDeleteDataV2()
    //region Overrides
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = AccountDeletePackageUserFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun backHandler() {
        navigateToAccountInfo()
    }

    override fun bindEvent() {
        binding.toolbar.setNavigationOnClickListener {
            backHandler()
        }
        binding.btnContinue.onClickDelay {
            if(userDeleteData?.userHasPackage==true){
                findNavController().navigateSafe(NavAccountOtpActionDirections.actionGlobalToDeleteAccountPolicyFragment())
            }
        }
    }
    override fun bindComponent() {
        binding.rvGroupPackageUser.adapter = groupAdapter

    }
    private fun navigateToAccountInfo(){
        activity?.findNavHostFragment()?.findNavController()?.navigateSafe(
            NavAccountInfoDirections.actionGlobalToAccountInfo())
    }
    override fun bindData() {
        mapStateToDataPackageV2(packages = userDeleteData?.packages , extras = userDeleteData?.extras).let { groupAdapter.bind(it) }
    }
    override fun MegaViewModel.MegaState.toUI() {
        when (this) {
            is MegaViewModel.MegaState.Loading -> {
                if (intent is MegaViewModel.MegaIntent.GetGroupPackageUser) {
                    binding.pbLoading.root.show()
                }
            }
            is MegaViewModel.MegaState.ResultGroupPackageUser -> {
                groupAdapter.bind(data)
            }
            is MegaViewModel.MegaState.Error -> {}
            is MegaViewModel.MegaState.ErrorRequiredLogin -> {
                navigateToLoginWithParams(isDirect = true)
            }
            is MegaViewModel.MegaState.Done -> binding.pbLoading.root.hide()
            else -> {}
        }
    }
    private fun mapStateToDataPackage(packages: List<CheckUserDeletePackage>?, extras :List<CheckUserDeletePackage>?):List<GroupPackageUser>{
        val list  = mutableListOf<GroupPackageUser>()
        if(!packages.isNullOrEmpty()){
            val listItem  = mutableListOf<PackageUserV3Item>()
            packages.map {
                val item = PackageUserV3Item(planName = it.planName, nextDate = it.expiredDate)
                listItem.add(item)
            }
            list.add(GroupPackageUser(title = getString(R.string.account_delete_package_user_used), isExpandable = false, packages = listItem))
        }
        if(!extras.isNullOrEmpty()){
            val listItem  = mutableListOf<PackageUserV3Item>()
            extras.map{
                val item = PackageUserV3Item(planName = it.planName, nextDate = it.expiredDate)
                listItem.add(item)
            }
            list.add(GroupPackageUser(title = getString(R.string.account_delete_extras_user_used), isExpandable = true, packages = listItem))
        }
        return list
    }
    private fun mapStateToDataPackageV2(packages: List<ValidateDisableUserPackage>?, extras :List<ValidateDisableUserPackage>?):List<GroupPackageUser>{
        val list  = mutableListOf<GroupPackageUser>()
        if(!packages.isNullOrEmpty()){
            val listItem  = mutableListOf<PackageUserV3Item>()
            packages.map {
                val item = PackageUserV3Item(planName = it.planName, nextDate = it.expiredDate)
                listItem.add(item)
            }
            list.add(GroupPackageUser(title = getString(R.string.account_delete_package_user_used), isExpandable = false, packages = listItem))
        }
        if(!extras.isNullOrEmpty()){
            val listItem  = mutableListOf<PackageUserV3Item>()
            extras.map{
                val item = PackageUserV3Item(planName = it.planName, nextDate = it.expiredDate)
                listItem.add(item)
            }
            list.add(GroupPackageUser(title = getString(R.string.account_delete_extras_user_used), isExpandable = true, packages = listItem))
        }
        return list
    }
}