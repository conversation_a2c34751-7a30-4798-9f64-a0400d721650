package com.fptplay.mobile.features.livetv_detail

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.fptplay.mobile.common.global.SourcePlayObject
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.features.game_30s.vote.entites.VoteEntities
import com.fptplay.mobile.features.livetv_detail.data.DataTriggerPlayChannel
import com.fptplay.mobile.features.livetv_detail.data.LiveTvPreviewPlayerInfo
import com.fptplay.mobile.features.mqtt.model.MqttOptionData
import com.fptplay.mobile.player.PlayerUtils
import com.fptplay.mobile.player.PlayerView
import com.fptplay.mobile.player.handler.drm.DrmApi
import com.fptplay.mobile.vod.VodDetailViewModel.VodDetailIntent
import com.fptplay.mobile.vod.VodDetailViewModel.VodDetailState
import com.tear.modules.player.util.PlayerControlView
import com.xhbadxx.projects.module.domain.RequiredLogin
import com.xhbadxx.projects.module.domain.RequiredVip
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.common.Constant
import com.xhbadxx.projects.module.domain.entity.fplay.common.ChatMessagePage
import com.xhbadxx.projects.module.domain.entity.fplay.common.MQTTPing
import com.xhbadxx.projects.module.domain.entity.fplay.common.Status
import com.xhbadxx.projects.module.domain.entity.fplay.common.Stream
import com.xhbadxx.projects.module.domain.entity.fplay.drm.DrmKey
import com.xhbadxx.projects.module.domain.entity.fplay.drm.Ping
import com.xhbadxx.projects.module.domain.entity.fplay.drm.PingStreamV2
import com.xhbadxx.projects.module.domain.entity.fplay.home.StructureItem
import com.xhbadxx.projects.module.domain.entity.fplay.live.TvChannel
import com.xhbadxx.projects.module.domain.entity.fplay.live.TvChannelDetail
import com.xhbadxx.projects.module.domain.entity.fplay.live.TvSchedule
import com.xhbadxx.projects.module.domain.entity.fplay.user.UserInfo
import com.xhbadxx.projects.module.domain.repository.fplay.CommonRepository
import com.xhbadxx.projects.module.domain.repository.fplay.DrmRepository
import com.xhbadxx.projects.module.domain.repository.fplay.LiveRepository
import com.xhbadxx.projects.module.domain.repository.fplay.UserRepository
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.zip
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class LiveTVDetailViewModel @Inject constructor(
    private val savedState: SavedStateHandle,
    private val liveRepository: LiveRepository,
    private val commonRepository: CommonRepository,
    private val userRepository: UserRepository,
    private val drmRepository: DrmRepository,
    private val sharedPreferences: SharedPreferences
) : BaseViewModel<LiveTVDetailViewModel.LiveTVDetailIntent, LiveTVDetailViewModel.LiveTVDetailState>(), DrmApi {

    private val getStreamScope = CoroutineScope(Dispatchers.IO)
    private var getStreamJob: Job? = null

    private var _isFullScreen = MutableLiveData<Pair<Boolean, Boolean>?>() // Pair<isFullscreen, isLandscape>
    val isFullScreen get() = _isFullScreen

    private var _initPlayer = MutableLiveData<Boolean?>()
    val initPlayer get() = _initPlayer

    private var _playSchedule = MutableLiveData<Pair<TvSchedule, Boolean>?>()
    val playSchedule get() = _playSchedule

    private var _playChannel = MutableLiveData<DataTriggerPlayChannel?>()
    val playChannel get() = _playChannel

    private var isVipRequired : Pair<Boolean, RequiredVip?>? = null

    fun saveScrollPos(pos: Int) = savedState.set("saveScrollPos", pos)
    fun saveGroupId(groupId: String) = savedState.set("saveGroupId", groupId)

    fun getScrollPos(): Int = savedState.get("saveScrollPos") ?: 0
    fun getGroupId(): String = savedState.get("saveGroupId") ?: ""

    private var curChannel: TvChannel? = null
    private var curTvSchedule: Pair<Long, List<TvSchedule>>? = null // <currentMilliseconds, listTvSchedule>
    private var curPlayingTvSchedule: Pair<Long, List<TvSchedule>>? = null // <currentMilliseconds, listTvSchedule>

    private var _playerBitrates: List<PlayerControlView.Data.Bitrate>? = null
    private var _playerTracks: List<PlayerControlView.Data.Track>? = null
    private var userFollowed = false

    private var publicIP = ""

    private var _preparePlayPreview = MutableLiveData<LiveTvPreviewPlayerInfo?>()
    val preparePlayPreview get() = _preparePlayPreview

    private var _preparePlayTrailer = MutableLiveData<String?>()
    val preparePlayTrailer get() = _preparePlayTrailer

    override fun dispatchIntent(intent: LiveTVDetailIntent) {
        viewModelScope.launch {
            when (intent) {
                is LiveTVDetailIntent.GetPublicIp -> {
                    commonRepository.getPublicIp().collect {
                        if (it is Result.Success) {
                            publicIP = it.successData
                        }
                    }
                }
                is LiveTVDetailIntent.GetTvChannel -> {
                    liveRepository.getTvChannel().collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> LiveTVDetailState.ResultTvChannel(isCached = isCached, data = data) }
                    }
                }
                is LiveTVDetailIntent.GetTvChannelDetail -> {
                    val dataType: String = if(intent.id == SourcePlayObject.SourcePlayLive.getBlockDataType()?.first) {
                        SourcePlayObject.SourcePlayLive.getBlockDataType()?.second ?: ""
                    } else {
                        SourcePlayObject.SourcePlayLive.clearBlockDataType()
                        ""
                    }

                    liveRepository.getTvChannelDetail(id = intent.id, blockDataType = dataType).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> LiveTVDetailState.ResultTvChannelDetail(isCached = isCached, data = data) }
                    }
                }
                is LiveTVDetailIntent.GetTvChannelStream -> {
                    getStreamJob?.cancel()
                    if (intent.delay != 0L) {
                        getStreamJob = getStreamScope.launch {
                            delay(intent.delay)
                            getTvChannelStream(intent = intent)
                        }
                    } else {
                        getTvChannelStream(intent = intent)
                    }
                }
                is LiveTVDetailIntent.GetTVSchedule -> {
                    liveRepository.getTvSchedule(channelId = intent.channelId, page = intent.page, perPage = intent.perPage, day = intent.day).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LiveTVDetailState.ResultGetTVSchedule(
                                isCached = isCached,
                                data = data,
                                isFirstCall = intent.isFirstCall,
                                currentMilliseconds = intent.currentMilliseconds,
                                isPlayerCalled = intent.isPlayerCalled,
                                hasChangeDate = intent.hasChangeDate
                            )
                        }
                    }
                }
                is LiveTVDetailIntent.GetTvScheduleStream -> {
                    getStreamJob?.cancel()
                    if (intent.delay != 0L) {
                        getStreamJob = getStreamScope.launch {
                            delay(intent.delay)
                            getTvScheduleStream(intent = intent)
                        }
                    } else {
                        getTvScheduleStream(intent = intent)
                    }
                }

                is LiveTVDetailIntent.GetTvFollow -> {
                    liveRepository.getFollow(type = intent.type, page = intent.page, perPage = intent.perPage, userId = intent.userId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> LiveTVDetailState.ResultTvFollow(isCached = isCached, data = data) }
                    }
                }

                is LiveTVDetailIntent.CheckFollow -> {
                    liveRepository.checkFollow(type = intent.type, id = intent.id).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> LiveTVDetailState.ResultCheckFollow(isCached = isCached, data = data) }
                    }
                }

                is LiveTVDetailIntent.AddFollow -> {
                    liveRepository.addFollow(type = intent.type, id = intent.id).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> LiveTVDetailState.ResultAddFollow(isCached = isCached, data = data) }
                    }
                }
                is LiveTVDetailIntent.DeleteFollow -> {
                    liveRepository.deleteFollow(type = intent.type, id = intent.id).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> LiveTVDetailState.ResultDeleteFollow(isCached = isCached, data = data) }
                    }
                }
                is LiveTVDetailIntent.GetHighlight -> {
                    commonRepository.getHighlightsLive(mode = intent.mode, page = intent.page, perPage = intent.perPage, sortStartTime = intent.sort).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> LiveTVDetailState.ResultGetHighLight(isCached = isCached, data = data) }
                    }
                }
                is LiveTVDetailIntent.GetHighlightDetail -> {
                    val dataType: String = if(intent.id == SourcePlayObject.SourcePlayLive.getBlockDataType()?.first) {
                        SourcePlayObject.SourcePlayLive.getBlockDataType()?.second ?: ""
                    } else {
                        SourcePlayObject.SourcePlayLive.clearBlockDataType()
                        ""
                    }
                    commonRepository.getHighlight(id = intent.id, dataType = dataType).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> LiveTVDetailState.ResultGetHighLightDetail(isCached = isCached, data = data, intent = intent) }
                    }
                }
                is LiveTVDetailIntent.TriggerPreparePlayer -> {
                    _state.value = LiveTVDetailState.PreparePlayer(
                        details = intent.details,
                        bitrateId = intent.bitrateId,
                        stream = intent.stream,
                        drmKey = intent.drmKey
                    )
                }
                is LiveTVDetailIntent.TriggerPrepareSchedulePlayer -> {
                    _state.value = LiveTVDetailState.PrepareSchedulePlayer(
                        tvSchedule = intent.tvSchedule,
                        data = intent.stream,
                    )
                }
                is LiveTVDetailIntent.GetChat -> {
                    commonRepository.getChats(roomId = intent.roomId, page = intent.page).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> LiveTVDetailState.ResultGetChat(isCached = isCached, data = data) }
                    }
                }
                is LiveTVDetailIntent.GetMoreChat -> {
                    commonRepository.getChats(roomId = intent.roomId, page = intent.page).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> LiveTVDetailState.ResultGetMoreChat(isCached = isCached, data = data) }
                    }
                }
                is LiveTVDetailIntent.GetInfoUser -> {
                    userRepository.getUserInfo().collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LiveTVDetailState.ResultUserInfo(isCached = isCached, data = data)
                        }
                    }

                }
                is LiveTVDetailIntent.TriggerPlayerLayout -> {
                    _state.value = LiveTVDetailState.ResultTriggerPlayerLayout(isScale = intent.isScale)
                }
                is LiveTVDetailIntent.TriggerOpenLiveChatFullScreen -> {
                    _state.value = LiveTVDetailState.ResultTriggerOpenLiveChatFullScreen(isOpen = intent.isOpen)
                }
                is LiveTVDetailIntent.SwitchPlayerMode -> {
                    _state.value = LiveTVDetailState.ResultSwitchPlayerMode(modeFullscreen = intent.modeFullscreen)
                }
                is LiveTVDetailIntent.TriggerShowSnackBar -> {
                    _state.value = LiveTVDetailState.ResultTriggerShowSnackBar(text = intent.text)
                }
                is LiveTVDetailIntent.TriggerShowMsgUserReport->{
                    _state.value =LiveTVDetailState.ResultTriggerMsgUserReport(
                        isReported = intent.isReported, message = intent.message
                    )
                }
                is LiveTVDetailIntent.SaveDRMKey -> {
                    Log.d("DRMCallback", "SaveDRMKey ${intent.drmKey.uid}")
                    drmRepository.insert(drmKey = DrmKey(
                        uid = intent.drmKey.uid,
                        drmKeyBase64 = intent.drmKey.drmKeyBase64,
                        licenseUrl = "",
                        merchant = ""
                    )).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LiveTVDetailState.Done(intent = intent)
                        }
                    }
                }

                is LiveTVDetailIntent.GetDRMKeyAndReturnState -> {
                    drmRepository.getDrmOfflineKey(uid = intent.uid).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data -> LiveTVDetailState.Done(intent = intent) }
                    }
                }

                is LiveTVDetailIntent.TriggerBuyPackageForPreview -> {
                    _state.value = LiveTVDetailState.ResultTriggerBuyPackageForPreview
                }

                is LiveTVDetailIntent.GetMqttBackup -> {
                    commonRepository.pingMQTT(intent.itemId, intent.chapterId, intent.episodeId, intent.playlistId).collect {
                        _state.value = it.reduce(intent) { isCached, data ->
                            LiveTVDetailState.ResultMqttBackup(isCached, data)
                        }
                    }
                }
            }
        }
    }

    private suspend fun getTvChannelStream(intent: LiveTVDetailIntent.GetTvChannelStream) {
        liveRepository.getTvChannelStream(
            id = intent.id,
            bitrateId = intent.bitrateId,
            blockDataType = SourcePlayObject.SourcePlayLive.getBlockDataType()?.second ?: "",
            enablePreview = intent.enablePreview
        )
            .zip(drmRepository.getDrmOfflineKey(uid = PlayerUtils.getLiveDrmUid(intent.id))) { stream, drmKey ->

                when (stream) {
                    is Result.Success -> {
                        if (drmKey is Result.Success) {
                            LiveTVDetailState.ResultTvChannelStream(
                                isCached = false,
                                data = stream.successData,
                                key = drmKey.successData,
                                intent = intent,
                            )
                        } else {
                            LiveTVDetailState.ResultTvChannelStream(
                                isCached = false,
                                data = stream.successData,
                                key = null,
                                intent = intent
                            )
                        }
                    }

                    else -> {
                        stream.reduce(intent = intent) { isCached, data ->
                            LiveTVDetailState.ResultTvChannelStream(
                                isCached = isCached,
                                data = data,
                                key = null,
                                intent = intent
                            )
                        }
                    }
                }
            }.collect {
                viewModelScope.launch(Dispatchers.Main) {
                    _state.value = it
                }
            }
    }

    private suspend fun getTvScheduleStream(intent: LiveTVDetailIntent.GetTvScheduleStream) {
        liveRepository.getTvScheduleStream(scheduleId = intent.scheduleId, bitrateId = intent.bitrateId).collect {
            withContext(Dispatchers.Main) {
                _state.value = it.reduce(intent = intent) { isCached, data ->

                    LiveTVDetailState.ResultTvScheduleStream(
                        isCached = isCached,
                        data = data,
                        intent = intent
                    )
                }
            }
        }
    }


    override fun <T> Result<T>.reduce(
        intent: LiveTVDetailIntent?,
        successFun: (Boolean, T) -> LiveTVDetailState
    ): LiveTVDetailState {
        return when (this) {
            is Result.Init -> LiveTVDetailState.Loading(intent = intent)
            is Result.Success -> {
                successFun(this.isCached, this.successData)
            }
            is Result.UserError.RequiredLogin -> {
                if (intent is LiveTVDetailIntent.GetTvFollow || intent is LiveTVDetailIntent.CheckFollow) {
                    LiveTVDetailState.Error(message = this.message, intent = intent)
                } else {
                    LiveTVDetailState.ErrorRequiredLogin(message = this.message, intent = intent, requiredLogin = this.requiredLogin)
                }
            }
            is Result.UserError.RequiredVip -> LiveTVDetailState.ErrorRequiredVip(
                message = this.message,
                intent = intent,
                requiredVip = this.requiredVip
            )
            is Result.Error.Intenet -> LiveTVDetailState.ErrorByInternet(message = this.message, intent = intent)
            is Result.Error -> LiveTVDetailState.Error(message = this.message, intent = intent)
            Result.Done -> LiveTVDetailState.Done(intent = intent)
        }
    }

    override fun resetState() {
        _state.value = LiveTVDetailState.Init
        _initPlayer.value = null
    }

    fun resetSchedule() {
        _playChannel.value = null
        _playSchedule.value = null
        saveIsPlayingTimeshift(isPlayingTimeshift = false)
    }

    fun cancelGetStreamJob() {
        getStreamJob?.cancel()
    }

    //region Save state
    fun saveChannelId(channelId: String) {
        savedState.set("channelId", channelId)
    }

    fun getChannelId() = savedState.get<String>("channelId") ?: ""

    fun saveVipRequired(isRequired: Boolean, requiredVip: RequiredVip? = null) { isVipRequired = Pair(isRequired, requiredVip)}
    fun getVipRequired() = isVipRequired

    val hasPreview get() = isVipRequired?.second?.enablePreview ?: false

    val availablePreview get() = isVipRequired?.second?.livePreviewInfo?.available ?: false

    val canPreview get() = hasPreview && availablePreview && !isVipRequired?.second?.livePreviewInfo?.url.isNullOrBlank()

    fun saveCurChannel(channel: TvChannel?) {
        curChannel = channel
    }
    fun getCurChannel() = curChannel

    fun saveChannelBitrateId(bitrateId: String) {
        savedState.set("channelBitrateId", bitrateId)
    }
    fun getChannelBitrateId(): String = savedState.get<String>("channelBitrateId") ?: ""

    fun saveScheduleBitrateId(bitrateId: String) {
        savedState.set("scheduleBitrateId", bitrateId)
    }
    fun getScheduleBitrateId(): String = savedState.get<String>("scheduleBitrateId") ?: ""

    fun saveCurrentPlayerBitrates(bitrates: List<PlayerControlView.Data.Bitrate>?) {
        _playerBitrates = bitrates
    }
    fun getCurrentPlayerBitrates() = _playerBitrates

    fun saveTracks(tracks: List<PlayerControlView.Data.Track>?) {
        _playerTracks = tracks
    }

    fun getTracks() = _playerTracks

    fun userFollow() = userFollowed
    fun saveUserFollow(userFollowed: Boolean) {
        this.userFollowed = userFollowed
    }

    fun isPlayingTimeshift() = savedState.get<Boolean>("isPlayingTimeshift") ?: false
    fun saveIsPlayingTimeshift(isPlayingTimeshift: Boolean) {
        savedState.set("isPlayingTimeshift", isPlayingTimeshift)
    }

    fun saveCurTvSchedule(data: Pair<Long, List<TvSchedule>>?) {
        this.curTvSchedule = data
    }

    fun getCurTvSchedule() = this.curTvSchedule

    fun saveCurrentPlayingTvSchedule(data: Pair<Long, List<TvSchedule>>?) {
        data?.let {
            val tvScheduleList = mutableListOf<TvSchedule>().apply { addAll(it.second) }
            this.curPlayingTvSchedule = Pair(it.first, tvScheduleList)
        }
    }

    fun getCurrentPlayingTvSchedule() = this.curPlayingTvSchedule

    fun saveIsListChannelShowing(isShow: Boolean) {
        savedState.set("isListChannelShowing", isShow)
    }

    fun getIsListChannelShowing() = savedState.get<Boolean>("isListChannelShowing") ?: false

    fun saveHaveSwitchChannel(haveSwitch: Boolean) {
        savedState.set("isHaveSwitchChannel", haveSwitch)
    }

    fun getHaveSwitchChannel() = savedState.get<Boolean>("isHaveSwitchChannel") ?: false
    //endregion

    //region Player Options Dialog
    fun savePlayerOptionsType(type: String) {
        savedState.set("playerOptionsType", type)
    }

    fun getPlayerOptionsType() = savedState.get<String>("playerOptionsType") ?: ""

    fun savePlayerOptionsCurItem(idCurItem: String) {
        savedState.set("playerOptionsCurItem", idCurItem)
    }

    fun getPlayerOptionsCurItem() = savedState.get<String>("playerOptionsCurItem") ?: ""

    fun saveClickTimeToPlay(clickTime: Long) = savedState.set("clickTimeToPlay", clickTime)
    fun getClickTimeToPlay(): Long = savedState.get("clickTimeToPlay") ?: 0

    fun savePrepareSourceTimeInMs(time: Long) = savedState.set("prepareSourceTimeInMs", time)
    fun getPrepareSourceTimeInMs(): Long = savedState.get("prepareSourceTimeInMs") ?: 0

    fun saveIsStreamRetry(isStreamRetry: Boolean) = savedState.set("isStreamRetry", isStreamRetry)
    fun getIsStreamRetry(): Boolean = savedState.get("isStreamRetry") ?: false

    fun saveIsPreAds(isPreAds: Boolean) = savedState.set("isPreAds", isPreAds)
    fun getIsPreAds(): Boolean = savedState.get("isPreAds") ?: false

    fun savePreAdsStartTimeInMs(time: Long) = savedState.set("preAdsStartTimeInMs", time)
    fun getPreAdsStartTimeInMs(): Long = savedState.get("preAdsStartTimeInMs") ?: 0
    fun savePreAdsEndTimeInMs(time: Long) = savedState.set("preAdsEndTimeInMs", time)
    fun getPreAdsEndTimeInMs(): Long = savedState.get("preAdsEndTimeInMs") ?: 0

    //endregion


    fun triggerFullScreen(isFull: Boolean, isLandscapeMode: Boolean) {
        _isFullScreen.postValue(Pair(isFull, isLandscapeMode))
    }

    fun triggerInitPlayer() {
        _initPlayer.postValue(true)
    }

    fun triggerPlaySchedule(data: TvSchedule, isUserInteract: Boolean = false) {
        _playSchedule.postValue(Pair(data, isUserInteract))
    }

    fun triggerPlayChannel(data: TvChannel, isForce: Boolean = false) {
        _playChannel.postValue(DataTriggerPlayChannel(data, isForce))
    }

    fun triggerPreparePlayPreview(liveTvPreviewPlayerInfo: LiveTvPreviewPlayerInfo?) {
        _preparePlayPreview.postValue(liveTvPreviewPlayerInfo)
    }

    fun triggerPreparePlayTrailer(url: String?) {
        _preparePlayTrailer.postValue(url)
    }

    //endregion

    //region Intent, State, (Action)
    sealed class LiveTVDetailState : ViewState {
        data class Loading(val intent: LiveTVDetailIntent? = null) : LiveTVDetailState()
        data class ResultTvChannel(val isCached: Boolean, val data: List<TvChannel>) : LiveTVDetailState()
        data class ResultTvChannelDetail(val isCached: Boolean, val data: TvChannelDetail) : LiveTVDetailState()
        data class ResultTvChannelStream(val isCached: Boolean, val data: Stream, val key: DrmKey?, val intent: LiveTVDetailIntent.GetTvChannelStream) : LiveTVDetailState()
        data class ResultGetTVSchedule(
            val isCached: Boolean,
            val data: List<TvSchedule>,
            val isFirstCall: Boolean,
            val currentMilliseconds: Long,
            val isPlayerCalled: Boolean,
            val hasChangeDate: Boolean
        ) : LiveTVDetailState()

        data class ResultTvScheduleStream(val isCached: Boolean, val data: Stream, val intent: LiveTVDetailIntent.GetTvScheduleStream) : LiveTVDetailState()
        data class ResultTvFollow(val isCached: Boolean, val data: List<TvChannel>) : LiveTVDetailState()
        data class ResultAddFollow(val isCached: Boolean, val data: Status) : LiveTVDetailState()
        data class ResultCheckFollow(val isCached: Boolean, val data: Status) : LiveTVDetailState()
        data class ResultDeleteFollow(val isCached: Boolean, val data: Status) : LiveTVDetailState()
        data class Error(val message: String, val intent: LiveTVDetailIntent? = null) : LiveTVDetailState()
        data class ErrorRequiredLogin(val message: String, val intent: LiveTVDetailIntent? = null, val requiredLogin: RequiredLogin?) : LiveTVDetailState()
        data class ErrorRequiredVip(val message: String, val intent: LiveTVDetailIntent? = null, val requiredVip: RequiredVip?) : LiveTVDetailState()
        data class ErrorByInternet(val message: String, val intent: LiveTVDetailIntent? = null) : LiveTVDetailState()
        data class Done(val intent: LiveTVDetailIntent? = null) : LiveTVDetailState()
        data class ResultGetHighLight(val isCached: Boolean, val data: List<StructureItem>) : LiveTVDetailState()
        data class ResultGetHighLightDetail(val isCached: Boolean, val data: StructureItem, val intent: LiveTVDetailIntent.GetHighlightDetail) : LiveTVDetailState()
        object Init : LiveTVDetailState()
        data class PreparePlayer(val details: TvChannelDetail?, val bitrateId: String, val stream: Stream, val drmKey: DrmKey?) : LiveTVDetailState()
        data class PrepareSchedulePlayer(val tvSchedule: TvSchedule?, val data: Stream) : LiveTVDetailState()
        data class ResultGetChat(val isCached: Boolean, val data: ChatMessagePage?) : LiveTVDetailState()
        data class ResultGetMoreChat(val isCached: Boolean, val data: ChatMessagePage?) : LiveTVDetailState()
        data class ResultUserInfo(val isCached: Boolean, val data: UserInfo) : LiveTVDetailState()
        data class ResultTriggerPlayerLayout(val isScale: Boolean) : LiveTVDetailState()
        data class ResultTriggerOpenLiveChatFullScreen(val isOpen: Boolean) : LiveTVDetailState()
        data class ResultSwitchPlayerMode(val modeFullscreen: Boolean) : LiveTVDetailState()
        data class ResultTriggerShowSnackBar(val text: String) : LiveTVDetailState()
        data class ResultTriggerMsgUserReport(val isReported: Boolean, val message: String) :LiveTVDetailState()
        object ResultTriggerBuyPackageForPreview: LiveTVDetailState()
        data class ResultMqttBackup(val isCached: Boolean, val data: MQTTPing) : LiveTVDetailState()

    }


    sealed class LiveTVDetailIntent : ViewIntent {
        object GetPublicIp : LiveTVDetailIntent()
        object GetTvChannel : LiveTVDetailIntent()
        data class GetTvChannelDetail(val id: String) : LiveTVDetailIntent()
        data class GetTvChannelStream(
            val id: String,
            val bitrateId: String,
            val delay: Long = 0L,
            val enablePreview: String,
            val isRetry: Boolean = false,
        ) : LiveTVDetailIntent()
        data class GetTVSchedule(
            val channelId: String,
            val page: Int,
            val perPage: Int,
            val day: String,
            val isFirstCall: Boolean,
            val currentMilliseconds: Long,
            val isPlayerCalled: Boolean = false,
            val hasChangeDate: Boolean = false,
            val isRetry: Boolean = false
        ) : LiveTVDetailIntent()

        data class GetTvScheduleStream(val scheduleId: String, val bitrateId: String, val delay: Long = 0L, val isRetry: Boolean) : LiveTVDetailIntent()
        data class GetTvFollow(val type: String, val page: Int, val perPage: Int, val userId: String) : LiveTVDetailIntent()
        data class AddFollow(val type: String = Constant.LIVE_TV_TYPE, val id: String) : LiveTVDetailIntent()
        data class CheckFollow(val type: String = Constant.LIVE_TV_TYPE, val id: String) : LiveTVDetailIntent()
        data class DeleteFollow(val type: String = Constant.LIVE_TV_TYPE, val id: String) : LiveTVDetailIntent()
        data class TriggerPreparePlayer(val details: TvChannelDetail?, val bitrateId: String, val stream: Stream, val drmKey: DrmKey?) : LiveTVDetailIntent()
        data class TriggerPrepareSchedulePlayer(val tvSchedule: TvSchedule?, val stream: Stream) : LiveTVDetailIntent()
        data class GetChat(val roomId: String, val page: String) : LiveTVDetailIntent()
        data class GetMoreChat(val roomId: String, val page: String) : LiveTVDetailIntent()
        data class GetHighlight(val mode: String = Constants.HIGHLIGHT_LIVE_TYPE, val page: Int, val perPage: Int, val sort: Int = -1) : LiveTVDetailIntent()
        data class GetHighlightDetail(val id: String) : LiveTVDetailIntent()
        object GetInfoUser : LiveTVDetailIntent()
        data class TriggerPlayerLayout(val isScale: Boolean) : LiveTVDetailIntent()
        data class TriggerOpenLiveChatFullScreen(val isOpen: Boolean) : LiveTVDetailIntent()
        data class TriggerShowSnackBar(val text: String) : LiveTVDetailIntent()

        /**
         * @param modeFullscreen: If you want to enter fullscreen, pass "true" to param, otherwise pass "false" -> exit fullscreen
         */
        data class SwitchPlayerMode(val modeFullscreen: Boolean) : LiveTVDetailIntent()


        //region ReportPlayer
        data class TriggerShowMsgUserReport(val isReported: Boolean = false , val message: String): LiveTVDetailIntent()
        //endregion

        data class SaveDRMKey(val drmKey: DrmKey) : LiveTVDetailIntent()

        data class GetDRMKeyAndReturnState(val uid: String) : LiveTVDetailIntent()

        object TriggerBuyPackageForPreview: LiveTVDetailIntent()

        data class GetMqttBackup(val itemId: String, val chapterId: String, val episodeId: String, val playlistId: String, val option: MqttOptionData, val retryCount: Int = 0) : LiveTVDetailIntent()


    }

    fun getPublicIp() = publicIP

    override suspend fun pingPlay(id: String, session: String, lastSession: String, encryptData: Boolean, type: String, eventId: String): Flow<Result<PingStreamV2>> {
        return drmRepository.pingPlay(id = id, session = session, lastSession = lastSession, encryptData = encryptData, type = type, eventId = eventId)
    }

    override suspend fun pingPlay(id: String, type: String, eventId: String): Flow<Result<Ping>> {
        return drmRepository.pingPlay(id = id, type = type, eventId = eventId)
    }

    override suspend fun pingPause(id: String, type: String, eventId: String): Flow<Result<Ping>> {
        return drmRepository.pingPause(id = id, type = type, eventId = eventId)
    }

    override suspend fun pingPlayHbo(operatorId: String, sessionId: String): Flow<Result<com.xhbadxx.projects.module.domain.entity.fplay.hbo.Ping>> {
        return drmRepository.pingPlayHbo(operatorId = operatorId, sessionId = sessionId)
    }

    override suspend fun pingPlayHboByToken(token: String): Flow<Result<com.xhbadxx.projects.module.domain.entity.fplay.hbo.Ping>> {
        return drmRepository.pingPlayHbo(token = token)
    }

    override suspend fun pingEndHbo(token: String): Flow<Result<com.xhbadxx.projects.module.domain.entity.fplay.hbo.Ping>> {
        return drmRepository.pingEndHbo(token = token)
    }

    override suspend fun refreshToken(operatorId: String, sessionId: String): Flow<Result<com.xhbadxx.projects.module.domain.entity.fplay.hbo.Ping>> {
        return drmRepository.refreshTokenHbo(operatorId = operatorId, sessionId = sessionId)
    }

    override suspend fun getTvChannelStream(id: String, bitrateId: String): Flow<Result<Stream>> {
        return drmRepository.getTvStream(id = id, bitrateId = bitrateId, blockDataType = "")
    }
    //endregion
}