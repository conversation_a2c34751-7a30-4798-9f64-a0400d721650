package com.fptplay.mobile.features.notification

import android.graphics.Rect
import android.os.Bundle
import android.util.Range
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.getDisplayHeight
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.*
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.databinding.NotificationFragmentBinding
import com.fptplay.mobile.features.notification.adapters.NotificationAdapter
import com.fptplay.mobile.features.notification.adapters.WrapContentLinearLayoutManager
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.common.Notification
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.LoadMoreHandler
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class NotificationFragment :
    BaseFragment<NotificationViewModel.NotificationState, NotificationViewModel.NotificationIntent>() {

    @Inject
    lateinit var sharedPreferences: SharedPreferences
    override val viewModel: NotificationViewModel by activityViewModels()

    private val notificationAdapter: NotificationAdapter by lazy { NotificationAdapter() }
    private val totalItemInRow by lazy { 1 }
//    private val totalItemInPage by lazy { if(context.isTablet()) 20 else 10 }
    private val totalItemInPage by lazy { 20 }

    private var _binding: NotificationFragmentBinding? = null
    private val binding get() = _binding!!

    private val listUnread by lazy { arrayListOf<Notification>() }
    private var listAll = arrayListOf<Notification>()
    private val realListSize get() = run { listAll.filter { it._type != 1 }.size }
    // new logic hard code skip step 20 for notification, not related to real list data received
    private var currentSkip = 0
    private var filterFlag = "0"

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    private val loadMoreHandler: LoadMoreHandler by lazy {
        LoadMoreHandler(totalItem = notificationAdapter.size(), totalItemInPage = totalItemInPage,
            totalItemInRow = totalItemInRow, onScroll = {
                Timber.d("----------Call noti from scroll")
                viewModel.dispatchIntent(
                    NotificationViewModel.NotificationIntent.GetNotification(
                        sharedPreferences.userId(),
//                        skip = realListSize - 1,
                        skip = currentSkip,
                        limit = totalItemInPage
                    )
                )
            })
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycle.addObserver(checkBeforePlayUtil)
        checkBeforePlayUtil.setScreenProvider("Notification")
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = NotificationFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onDestroy() {
        super.onDestroy()
        lifecycle.removeObserver(checkBeforePlayUtil)
    }

    override fun bindComponent() {
        if (!sharedPreferences.userLogin()) navigateToLoginWithParams()
        binding.rcvNoti.apply {
            adapter = notificationAdapter
            layoutManager = WrapContentLinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        }
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "16",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = TrackingUtil.screenInbox,
                event = "EnterNotification",
            )
        )
    }

    override fun bindEvent() {
        parentFragment?.parentFragment?.setFragmentResultListener(Constants.CHECK_REQUIRE_VIP) { key, bundle ->
            checkBeforePlayUtil.onFragmentResult(key, bundle)
        }

        notificationAdapter.eventListener = object : IEventListener<Notification> {
            override fun onClickedItem(position: Int, data: Notification) {
                viewModel.dispatchIntent(
                    NotificationViewModel.NotificationIntent.UpdateNotification(
                        data.inboxId,
                        "read"
                    )
                )
                TrackingUtil.setDataTracking(screenValue = TrackingUtil.screenInbox, nameBlockVal = "", indexBlockVal = -1)
                TrackingUtil.setTrackingKey(TrackingUtil.TrackingKey.NONE)
                checkBeforePlayUtil.navigateToSelectedContent(data)
                data.status = "read"
                filterFlag = "0"
            }

            override fun onClickView(position: Int, view: View?, data: Notification) {
                viewModel.setData(data, getIndexItem(position))
                findNavController().navigate(NotificationFragmentDirections.actionNotificationToDetail())
            }
        }

        // Load More Item
        binding.rcvNoti.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (!recyclerView.canScrollVertically(1)) {
//                    loadMoreHandler.canScroll(position = realListSize - 1)
                    loadMoreHandler.canScroll(position = currentSkip - 1)
                }
            }
        })

        binding.tvAllNoti.setOnClickListener {
            findNavController().navigate(NotificationFragmentDirections.actionNotificationToFilterNotification())
        }

        binding.icBack.setOnClickListener {
            var statusIcon = "0"
            for (i in listAll.indices) {
                if (listAll[i].status == "unread")
                    statusIcon = "1"
            }
            activity?.supportFragmentManager?.setFragmentResult(
                "NotificationStatus",
                bundleOf("status" to statusIcon)
            )
            findNavController().popBackStack()
        }

        // observe when back from filter noti fragment
        setFragmentResultListener("bundle") { _, bundle ->
            filterFlag = bundle.getString("value", "")
            if (filterFlag == "1") {
                binding.tvAllNoti.text = getString(R.string.noti_unread)
                notificationAdapter.bind(getListUnread(listAll))
            } else {
                binding.tvAllNoti.text = getString(R.string.all_notification)
                notificationAdapter.bind(listAll)
            }
        }
    }

    override fun bindData() {
        if (listAll.isEmpty()) {
            Timber.d("----------Call noti from start")
            currentSkip = 0
            MainApplication.INSTANCE.appConfig.hasNewNotification = false
            viewModel.dispatchIntent(
                NotificationViewModel.NotificationIntent.GetNotification(
                    sharedPreferences.userId(),
                    0,
                    totalItemInPage
                )
            )
        }
    }

    override fun NotificationViewModel.NotificationState.toUI() {
        when (this) {
            is NotificationViewModel.NotificationState.Loading -> {
                showLoading()
            }
            is NotificationViewModel.NotificationState.Done -> {
                hideLoading()
            }
            is NotificationViewModel.NotificationState.Error -> {
                hideLoading()
                if (intent is NotificationViewModel.NotificationIntent.GetNotification) {
                    trackingProxy.sendEvent(
                        InforMobile(
                            infor = trackingInfo,
                            logId = TrackingConstants.EVENT_APP_ERROR,
                            event = AppErrorType.ERROR,
                            appId = TrackingUtil.screenNotification,
                            appName = TrackingUtil.screenNotification,
                            errorCode = AppErrorConstants.GET_NOTIFICATION_CODE,
                            errorMessage = this.message,
                            itemName = AppErrorConstants.GET_NOTIFICATION_MESSAGE,
                            issueId = TrackingUtil.createIssueId(),
                        )
                    )
                    loadMoreHandler.refresh(totalItem = currentSkip, endPage = false)
                }
            }
            is NotificationViewModel.NotificationState.ResultStructure -> {
                hideLoading()
                var result: List<Notification>? = null
                // remove item date if duplicate
                if (!isBind) {
                    if (!data.isNullOrEmpty()) {
                        listAll.lastOrNull()?.let { lastNotification ->
                            if (lastNotification.date() == data.first().date()) {
                                result = data.toMutableList().also { it.removeFirstOrNull() }
                            }
                        }
                    }
                }
                // filter list noti
                listAll.addAll(result ?: data)
                Timber.d("thien test call pai $filterFlag")
                if (filterFlag == "1")
                    notificationAdapter.add(data = getListUnread(result ?: data), isBind = isBind)
                else
                    notificationAdapter.add(data = result ?: data, isBind = isBind)

                val realNewDataSize = (result ?: data).filter { it._type != 1 }.size
//                loadMoreHandler.refresh(
//                    totalItem = realListSize,
//                    endPage = (realNewDataSize < totalItemInPage)
//                )

                // new logic hard code skip step 20 for notification, not related to real list data received
                currentSkip = intent.skip + totalItemInPage

                loadMoreHandler.refresh(
                    totalItem = currentSkip,
                    endPage = realNewDataSize <= 0
                )
            }
            is NotificationViewModel.NotificationState.ResultUpdateNotification -> {
                Logger.d("test update status ${data.message}")
            }
        }
    }

    fun getIndexItem(position: Int): Int {
        val layoutManager: LinearLayoutManager =
            binding.rcvNoti.layoutManager as LinearLayoutManager

        val location = IntArray(2)
        layoutManager.findViewByPosition(position)?.getLocationOnScreen(location)
        val eachRangeSize = binding.root.context.getDisplayHeight() / 3
        val topRange = Range(1, eachRangeSize) // start from 1 (In case view not found it will return 0)
        val middleRange = Range(eachRangeSize, eachRangeSize * 2)
        val bottomRange = Range(eachRangeSize * 2, eachRangeSize * 3)
        Timber.d("index noti ${location[0]} - ${location[1]}")
        Timber.d("index noti $topRange - $middleRange - $bottomRange")
        return when {
            topRange.contains(location[1]) -> 0
            middleRange.contains(location[1]) -> 1
            bottomRange.contains(location[1]) -> 2
            else -> 2
        }
    }

    private fun getListUnread(data: List<Notification>): List<Notification> {
        val listReturn = arrayListOf<Notification>()
        listUnread.clear()
        if (!listAll.isNullOrEmpty()) {
            listUnread.addAll(
                data.filterNot { item -> item.status == "read" }
            )

            for (i in 1 until listUnread.size) {
                if (listUnread[i]._type == 1 && listUnread[i - 1]._type == 1) {
                } else {
                    listReturn.add(listUnread[i - 1])
                }
            }
            if (listReturn.isNotEmpty()) {
                if (listUnread.isNotEmpty() && listReturn.last()._type == 1) listReturn.removeLastOrNull()
            }
        }
        return listReturn
    }
}