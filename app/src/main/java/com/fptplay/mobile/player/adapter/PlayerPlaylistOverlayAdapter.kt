package com.fptplay.mobile.player.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.adapter.BaseViewHolder
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.PlayerPlaylistOverlayItemBinding
import com.fptplay.mobile.player.utils.visible
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger
import kotlin.math.max

class PlayerPlaylistOverlayAdapter :
    BaseAdapter<Details.Episode, PlayerPlaylistOverlayAdapter.PlayerPlaylistOverlayViewHolder>() {

    private var currentPlayingEpisode: Details.Episode? = null

    fun setCurrentEpisode(episode: Details.Episode?) {
        currentPlayingEpisode = episode
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): PlayerPlaylistOverlayViewHolder {
        return PlayerPlaylistOverlayViewHolder(
            PlayerPlaylistOverlayItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun onBindViewHolder(
        holder: PlayerPlaylistOverlayViewHolder,
        position: Int
    ) {
        holder.bind(differ.currentList[position])
    }


    inner class PlayerPlaylistOverlayViewHolder(val binding: PlayerPlaylistOverlayItemBinding) :
        BaseViewHolder(binding) {
        private val thumbWidth by lazy {
            Utils.getSizeInPixel(
                context = binding.root.context,
                resId = R.dimen.poster_track_width
            )
        }
        private val thumbHeight by lazy {
            Utils.getSizeInPixel(
                context = binding.root.context,
                resId = R.dimen.poster_track_height
            )
        }

        init {
            binding.root.onClickDelay {
                if (absoluteAdapterPosition in differ.currentList.indices) {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        differ.currentList[absoluteAdapterPosition]
                    )
                }
            }
        }

        fun bind(data: Details.Episode) {
            Logger.d("PlayerPlaylistOverlayAdapter > data > ${data.realEpisodeId} vs ${currentPlayingEpisode?.realEpisodeId}")
            data.apply {
                ImageProxy.loadLocal(
                    context = binding.root.context,
                    data = horizontalImage,
                    width = thumbWidth,
                    height = thumbHeight,
                    target = binding.ivThumb,
                    placeHolderId = R.drawable.image_placeholder,
                    errorDrawableId = R.drawable.image_placeholder
                )
                binding.tvTitle.text = titleVietnam
                binding.tvDesc.text = description
                binding.tvTime.apply {
                    text = durationS
                    isVisible = durationS.isNotBlank()
                }
                if (ribbonPayment.isNotEmpty()) {
                    binding.ivRibbonPayment.show()
                    ImageProxy.loadLocal(
                        context = binding.root.context,
                        data = ribbonPayment,
                        width = 0,
                        height = binding.root.context.resources.getDimensionPixelSize(R.dimen.block_ribbon_height),
                        target = binding.ivRibbonPayment,
                        placeHolderId = R.drawable.image_placeholder,
                        errorDrawableId = R.drawable.image_placeholder
                    )
                } else {
                    binding.ivRibbonPayment.hide()
                }


                if (!isItemOfPlaylist && duration.isNotBlank() && duration != "0") {
                    // minimum for progress to guarantee UX design is 12%
                    val minProgressValue = 12
                    if (timeWatchedLocal > 0) {
                        binding.pbTimeWatched.show()
                        try {
                            binding.pbTimeWatched.progress =
                                max(minProgressValue, (timeWatchedLocal / Utils.convertStringToDouble(
                                    duration,
                                    0.0
                                ) * 100).toInt())
                        } catch (ex: Exception) {
                            ex.printStackTrace()
                            binding.pbTimeWatched.progress = minProgressValue
                        }
                    } else if (timeWatched.isNotBlank() && timeWatched != "0") {
                        binding.pbTimeWatched.show()
                        try {
                            binding.pbTimeWatched.progress = max(minProgressValue, ((Utils.convertStringToDouble(
                                timeWatched,
                                0.0
                            ) / Utils.convertStringToDouble(duration, 0.0)) * 100).toInt())
                        } catch (ex: Exception) {
                            ex.printStackTrace()
                            binding.pbTimeWatched.progress = minProgressValue
                        }
                    } else {
                        binding.pbTimeWatched.hide()
                    }
                } else {
                    binding.pbTimeWatched.hide()
                }

                if (data.realEpisodeId == currentPlayingEpisode?.realEpisodeId) {
                    binding.ivPlaying.visible()
                } else {
                    binding.ivPlaying.hide()
                }
            }
        }
    }
}