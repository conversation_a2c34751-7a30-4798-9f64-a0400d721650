package com.fptplay.mobile.common.adapter

import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.util.common.IEventListener
import kotlinx.coroutines.runBlocking
import timber.log.Timber

abstract class BaseAdapter<DataType : BaseObject, ViewHolderType : RecyclerView.ViewHolder> :
    RecyclerView.Adapter<ViewHolderType>() {
    open var eventListener: IEventListener<DataType>? = null

    protected val differ: AsyncListDiffer<DataType> by lazy {
        AsyncListDiffer(this, object : DiffUtil.ItemCallback<DataType>() {
            override fun areItemsTheSame(oldItem: DataType, newItem: DataType): Boolean {
                return <EMAIL>(oldItem, newItem)
            }

            override fun areContentsTheSame(oldItem: DataType, newItem: DataType): Boolean {
                return <EMAIL>(oldItem, newItem)
            }

            override fun getChangePayload(oldItem: DataType, newItem: DataType): Any? {
                return <EMAIL>(oldItem, newItem) ?: super.getChangePayload(oldItem, newItem)
            }

        })
    }


    protected open fun areItemTheSame(oldItem: BaseObject, newItem: BaseObject) =
        oldItem.id == newItem.id

    protected open fun areContentTheSame(oldItem: BaseObject, newItem: BaseObject) =
        oldItem == newItem

    protected open fun getChangePayload(oldItem: BaseObject, newItem: BaseObject): Any? = null


    fun  bind(data: List<DataType>?, callback: Runnable? = null) {
        differ.submitList(data, callback)
    }

    fun add(data: DataType, callback: Runnable? = null) {
        val modifyData = differ.currentList.toMutableList()
        modifyData.add(data)
        differ.submitList(modifyData, callback)
    }
    fun removeLast(callback: Runnable? = null) {
        val modifyData = differ.currentList.toMutableList()
        if (modifyData.isNotEmpty()) {
            modifyData.removeLastOrNull()
            differ.submitList(modifyData, callback)
        } else {
            callback?.run()
        }
    }

    @Synchronized
    fun removeIndex(data: DataType,callback: Runnable? = null) {
//        Timber.d("*** Execute remove item $data")
        val modifyData = differ.currentList.toMutableList()
        if (modifyData.isNotEmpty()) {
            val removed = modifyData.remove(data)
//            Timber.d("*** Execute remove item success: $removed for data: $data")
            differ.submitList(modifyData, callback)
        } else {
            callback?.run()
        }
    }

    fun removeIndex(index: Int, callback: Runnable? = null) {
        val modifyData = differ.currentList.toMutableList()
        if (modifyData.isNotEmpty() && modifyData.size > index) {
            modifyData.removeAt(index)
            differ.submitList(modifyData, callback)
        } else {
            callback?.run()
        }
    }

    fun add(index: Int, data: DataType, callback: Runnable? = null) {
        val modifyData = differ.currentList.toMutableList()
        if (modifyData.isNotEmpty() && modifyData.size > index) {
            modifyData.add(index, data)
            differ.submitList(modifyData, callback)
        } else {
            callback?.run()
        }
    }
    fun add(data: List<DataType>, isBind: Boolean = false, callback: Runnable? = null) {
        if (isBind) {
            bind(data, callback)
        } else {
            val mutableData = differ.currentList.toMutableList()
            mutableData.addAll(data)
            differ.submitList(mutableData, callback)
        }
    }

    fun changeDataAt(index: Int, data: DataType, callback: Runnable? = null) {
        runBlocking {
//            Timber.d("*** Execute change data for index: $index")
            val modifyData = differ.currentList.toMutableList()
            if (modifyData.isNotEmpty() && modifyData.size > index) {
                modifyData[index] = data
                differ.submitList(modifyData, callback)
            } else {
                callback?.run()
            }
        }
    }

    override fun getItemCount(): Int = differ.currentList.size

    fun size() = differ.currentList.size
    fun data(): List<DataType> = differ.currentList
    open fun item(pos: Int): DataType? =
        if (pos >= 0 && pos < size()) differ.currentList[pos] else null
}