package com.fptplay.mobile.features.mega

import android.os.Bundle
import androidx.lifecycle.SavedStateHandle
import com.fplay.module.downloader.VideoDownloadManager
import com.fplay.module.downloader.database.entity.CollectionAndChapters
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.fptplay.mobile.features.mega.account.model.GroupPackageUser
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.common.*
import com.xhbadxx.projects.module.domain.entity.fplay.home.Structure
import com.xhbadxx.projects.module.domain.entity.fplay.login.Login
import com.xhbadxx.projects.module.domain.entity.fplay.login.RequestOtp
import com.xhbadxx.projects.module.domain.entity.fplay.loginv2.AccountSetPinEntity
import com.xhbadxx.projects.module.domain.entity.fplay.loginv2.PluginSaleMode
import com.xhbadxx.projects.module.domain.entity.fplay.otp.DisableAccount
import com.xhbadxx.projects.module.domain.entity.fplay.otp.ResetPinEntity
import com.xhbadxx.projects.module.domain.entity.fplay.otp.SendOtpV2Entity
import com.xhbadxx.projects.module.domain.entity.fplay.otp.UserOtpType
import com.xhbadxx.projects.module.domain.entity.fplay.otp.ValidateDisableUserData
import com.xhbadxx.projects.module.domain.entity.fplay.otp.ValidateUserPinEntity
import com.xhbadxx.projects.module.domain.entity.fplay.otp.VerifyOTPV2Entity
import com.xhbadxx.projects.module.domain.entity.fplay.payment.PackageHistory
import com.xhbadxx.projects.module.domain.entity.fplay.payment.PromotionStatus
import com.xhbadxx.projects.module.domain.entity.fplay.user.*
import com.xhbadxx.projects.module.domain.repository.fplay.CommonRepository
import com.xhbadxx.projects.module.domain.repository.fplay.LoginRepositoryV2
import com.xhbadxx.projects.module.domain.repository.fplay.OtpRepository
import com.xhbadxx.projects.module.domain.repository.fplay.PaymentRepository
import com.xhbadxx.projects.module.domain.repository.fplay.UserRepository
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.system.measureTimeMillis

@HiltViewModel
class MegaViewModel @Inject constructor(
    private val commonRepository: CommonRepository,
    private val userRepository: UserRepository,
    private val loginRepository: LoginRepositoryV2,
    private val savedState: SavedStateHandle,
    private val paymentRepository: PaymentRepository,
    private val otpRepository: OtpRepository,
    private val sharedPreferences: SharedPreferences,
) : BaseViewModel<MegaViewModel.MegaIntent, MegaViewModel.MegaState>() {


    private var listSubMenus: List<MegaMenuItem>? = null
    private var userDeleteData: CheckUserDeleteData? = null
    private var userDeleteDataV2: ValidateDisableUserData? = null

    //region Overrides
    override fun resetState() {
        _state.value = MegaState.Idle
    }

    override fun dispatchIntent(intent: MegaIntent) {
        safeLaunch {
            when (intent) {
                MegaIntent.GetUserInfoWithLocation -> {
                    getUserInfoWithLocation(intent)
                }
                is MegaIntent.GetClusterInfoAtInit -> {
                    getClusterInfoAtInit(intent)
                }

                MegaIntent.GetUserInfo -> {
                    userRepository.getUserInfo().collect { result ->
                        _state.value = result.reduce(intent = intent) { isCached, data ->
                            sharedPreferences.saveUserAvatar(result.data?.avatar ?: "")
                            MegaState.ResultUserInfo(isCached = isCached, data = data)
                        }
                    }
                }
                MegaIntent.GetListWidgets -> {
                    commonRepository.getListWidget().collect { result ->
                        _state.value = result.reduce { isCached, widget ->
                            MegaState.ResultGetListWidget(
                                isCached = isCached,
                                data = widget
                            )
                        }
                    }
                }
                MegaIntent.GetListCity -> {
                    commonRepository.getListCity().collect { result ->
                        _state.value = result.reduce(intent = intent) { isCached, data ->
                            MegaState.ResultListCity(isCached = isCached, data = data)
                        }
                    }
                }
                is MegaIntent.UpdateUserInfo -> {
                    userRepository.updateUserInformation(
                        fullName = intent.fullName,
                        email = intent.email,
                        sexCode = intent.sexCode,
                        birthDay = intent.birthDay,
                        avatar = intent.avatar,
                        location = intent.location
                    ).collect { result ->
                        _state.value = result.reduce(intent = intent) { _, data ->
                            MegaState.ResultUpdateUserInfo(
                                data = data,
                                oldUserInfo = intent.oldUserInfo
                            )
                        }
                    }
                }

                is MegaIntent.ChangePassword -> {
                    userRepository.changePassword(
                        phone = intent.phone,
                        currentPassword = intent.currentPassword,
                        newPassword = intent.newPassword,
                        newPasswordAgain = intent.newPasswordAgain
                    ).collect { result ->
                        _state.value = result.reduce(intent = intent) { _, data ->
                            MegaState.ResultChangePassword(data = data)
                        }
                    }
                }
                is MegaIntent.GetConfig -> {
                    commonRepository.getConfig().collect { result ->
                        _state.value = result.reduce { isCached, data ->
                            MegaState.ResultConfig(isCached = isCached, data = data)
                        }
                    }
                }
                is MegaIntent.CheckPromotion -> {
                    // For testing purpose
                    if (intent.code.lowercase() == "revision") {
                        commonRepository.getRevision().collect { result ->
                            _state.value = result.reduce(intent = intent) { _, data ->
                                MegaState.ResultGetRevisionSuccess(revision = data.revision)
                            }
                        }
                    } else if (intent.code.lowercase() == "deleterevision") {
                        _state.value = MegaState.ResultDeleteRevision
                    } else if (intent.code.lowercase() == "drmstaging") {
                        _state.value = MegaState.ResultSetDrmKeyEnv(intent.code)
                    } else if (intent.code.lowercase() == "drmproduct") {
                        _state.value = MegaState.ResultSetDrmKeyEnv(intent.code)
                    }

                    paymentRepository.checkPromotion(code = intent.code).collect { result ->
                        _state.value = result.reduce(intent = intent) { _, data ->
                            MegaState.ResultCheckPromotion(data = data)
                        }
                    }

                }
                is MegaIntent.GetGroupPackageUser -> {
                    paymentRepository.getPackageUserV3(userId = intent.userId).collect { result ->
                        _state.value = result.reduce(intent = intent) { isCached, data ->
                            val groupPackageUsers = mutableListOf<GroupPackageUser>().apply {
                                add(GroupPackageUser("Gói đã đăng ký", false, data.packages))

                                if (data.extras.isNotEmpty()) {
                                    add(
                                        GroupPackageUser(
                                            "Chi tiết dịch vụ đang sử dụng",
                                            true,
                                            data.extras
                                        )
                                    )
                                }
                            }

                            MegaState.ResultGroupPackageUser(
                                isCached = isCached,
                                data = groupPackageUsers
                            )
                        }
                    }
                }
                is MegaIntent.GetPaymentHistory -> {
                    paymentRepository.getPackageHistory(
                        page = intent.page,
                        perPage = intent.perPage
                    )
                        .collect { result ->
                            _state.value = result.reduce(intent = intent) { isCached, data ->
                                MegaState.ResultPaymentHistory(
                                    isCached = isCached,
                                    data = data,
                                    isBind = (intent.page == 1)
                                )
                            }
                        }
                }
                MegaIntent.GetDeviceToken -> {
                    userRepository.getDeviceTokenV2().collect { result ->
                        _state.value = result.reduce(intent = intent) { isCached, data ->
                            MegaState.ResultDeviceToken(isCached = isCached, data = data)
                        }
                    }
                }
                is MegaIntent.DeleteDeviceToken -> {
                    userRepository.deleteDeviceToken(intent.tokens, intent.verifyToken)
                        .collect { result ->
                            _state.value = result.reduce(intent = intent) { isCached, data ->
                                MegaState.ResultDeleteDeviceToken(isCached = isCached, data = data)
                            }
                        }
                }
                MegaIntent.Logout -> {
                    userRepository.logout().collect { result ->
                        _state.value = result.reduce(intent = intent) { _, data ->
                            MegaState.ResultLogout(data = data)
                        }
                    }
                }
                MegaIntent.TriggerDismissOmniBottomSheet -> {
                    _state.value = MegaState.DismissOmniBottomSheet
                }

                is MegaIntent.RequestChangePasswordOtp -> {
                    userRepository.requestOtp(
                        RequestOtp.CHANGE_PASS,
                        "",
                        sharedPreferences.userPhone()
                    )
                        .collect { result ->
                            _state.value = result.reduce { _, requestOtp ->
                                MegaState.ResultRequestOtp(data = requestOtp)
                            }
                        }
                }

                is MegaIntent.ResendRequestChangePasswordOtp -> {
                    userRepository.resendRequestOtp(
                        RequestOtp.CHANGE_PASS,
                        sharedPreferences.userPhone()
                    )
                        .collect { result ->
                            _state.value = result.reduce { _, requestOtp ->
                                MegaState.ResultRequestOtp(data = requestOtp)
                            }
                        }
                }

                is MegaIntent.VerifyChangePassOtp -> {
                    userRepository.verifyOtpV1(
                        RequestOtp.CHANGE_PASS,
                        intent.otpCode,
                        sharedPreferences.userPhone(),
                        sharedPreferences.fcmToken()
                    ).collect { result ->
                        _state.value = result.reduce { _, verifyOtpV1 ->
                            MegaState.ResultVerifyOtp(data = verifyOtpV1)
                        }
                    }
                }
                is MegaIntent.GetMegaMenuV3 -> {
                    commonRepository.getMegaMenusV3(miniAppSdkVersion = intent.miniAppSdkVersion)
                        .collect { result ->
                            _state.value = result.reduce { isCached, megaMenu ->
                                MegaState.ResultMegaMenuV3(isCached = isCached, data = megaMenu)
                            }
                        }
                }
                is MegaIntent.GetMegaHamburgerMenu -> {
                    commonRepository.getMegaMenusHamburgerV2(miniAppSdkVersion = intent.miniAppSdkVersion)
                        .collect { result ->
                            _state.value = result.reduce(intent = intent) { isCached, megaMenu ->
                                MegaState.ResultMegaMenusHamburger(isCached = isCached, data = megaMenu)
                            }
                        }

                }
                is MegaIntent.CheckAccount -> {
                    userRepository.checkUserCanDelete().collect { result ->
                        _state.value = result.reduce { isCached, data ->
                            MegaState.ResultCheckAccount(
                                status = data.status,
                                message = data.message,
                                data = data.data
                            )
                        }
                    }
                }
                is MegaIntent.GetDeletePolicyAccount -> {
                    commonRepository.getInformationPage(pageName = intent.pageName)
                        .collect { result ->
                            _state.value = result.reduce { isCached, data ->
                                MegaState.ResultGetDeletePolicyAccount(
                                    isCached = isCached,
                                    data = data
                                )
                            }
                        }
                }
                is MegaIntent.DeleteAccount -> {
                    userRepository.deleteUser(verifyToken = intent.verityToken).collect { result ->
                        _state.value = result.reduce { isCached, data ->
                            MegaState.ResultDeleteAccount(
                                isCached = isCached,
                                status = data.status,
                                message = data.message
                            )
                        }
                    }
                }
                is MegaIntent.RequestDeleteAccountOtp -> {
                    userRepository.requestOtp(
                        RequestOtp.DISABLE_ACCOUNT,
                        "",
                        sharedPreferences.userPhone()
                    )
                        .collect { result ->
                            _state.value = result.reduce { _, requestOtp ->
                                MegaState.ResultRequestOtp(data = requestOtp)
                            }
                        }
                }

                is MegaIntent.ResendDeleteAccountOtp -> {
                    userRepository.resendRequestOtp(
                        RequestOtp.DISABLE_ACCOUNT,
                        sharedPreferences.userPhone()
                    )
                        .collect { result ->
                            _state.value = result.reduce { _, requestOtp ->
                                MegaState.ResultRequestOtp(data = requestOtp)
                            }
                        }
                }
                is MegaIntent.VerifyDeleteAccountOtp -> {
                    userRepository.verifyOtpV1(
                        RequestOtp.DISABLE_ACCOUNT,
                        intent.otpCode,
                        sharedPreferences.userPhone(),
                        sharedPreferences.fcmToken()
                    ).collect { result ->
                        _state.value = result.reduce { _, verifyOtpV1 ->
                            MegaState.ResultVerifyOtp(data = verifyOtpV1)
                        }
                    }
                }
                is MegaIntent.ConfirmPassword -> {
                    userRepository.checkUserPassword(
                        passcode = intent.currentPassword,
                        type = CheckPasswordUserType.DeleteAccount
                    ).collect { result ->
                        _state.value = result.reduce { _, data ->
                            MegaState.ResultConfirmPassword(status =  data.data.statusCode, message = data.message, checkPasswordData = data.data)
                        }
                    }
                }

                is MegaIntent.ChangePinSettingOtpV1 -> {
                    userRepository.changePasswordOtpV1(verifyToken = intent.verifyToken, otpType = intent.otpType, newPassword = intent.pass, newPasswordAgain = intent.passConfirm).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            MegaState.ResultChangePinSettingOtpV1(isCached = isCached, data = data)
                        }
                    }
                }

                is MegaIntent.CreatePinSetting -> {
                    loginRepository.accountSetPinV2(pin = intent.pass, confirmPin = intent.passConfirm).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            MegaState.ResultCreatePinSetting(isCached = isCached, data = data)
                        }
                    }
                }
                is MegaIntent.RequestOtpV2 -> {
                    otpRepository.sendOtpV2(
                        phone = sharedPreferences.userPhone(),
                        verifyToken = intent.verifyToken,
                        typeOtp = intent.otpType,
                        email = intent.email,
                    ).collect { result ->
                        _state.value = result.reduce(intent = intent) { _, sendOtpV2Entity ->
                            MegaState.ResultRequestOtpV2(data = sendOtpV2Entity)
                        }
                    }
                }
                is MegaIntent.RequestResendOtpV2 -> {
                    otpRepository.resendOtpV2(
                        phone = sharedPreferences.userPhone(),
                        typeOtp = intent.otpType,
                        email = intent.email,
                    ).collect { result ->
                        _state.value = result.reduce(intent = intent) { _, sendOtpV2Entity ->
                            MegaState.ResultRequestOtpV2(data = sendOtpV2Entity)
                        }
                    }
                }
                is MegaIntent.VerifyOtpV2 -> {
                    otpRepository.verifyOtpV2(
                        phone = sharedPreferences.userPhone(),
                        typeOtp = intent.otpType,
                        otp =  intent.otpCode,
                        email = intent.email,
                    ).collect { result ->
                        _state.value = result.reduce(intent = intent) { _, verifyOtpV2 ->
                            MegaState.ResultVerifyOtpV2(data = verifyOtpV2)
                        }
                    }
                }
                is MegaIntent.DeleteAccountV2 -> {
                    otpRepository.disableAccout(verifyToken = intent.verityToken).collect { result ->
                        _state.value = result.reduce(intent = intent) { isCached, data ->
                            MegaState.ResultDeleteAccountV2(
                                isCached = isCached,
                                status = data.status,
                                message = data.message,
                                data = data
                            )
                        }
                    }
                }
                is MegaIntent.CheckAccountV2 -> {
                    otpRepository.validateDisableUser().collect { result ->
                        _state.value = result.reduce(intent = intent) { isCached, data ->
                            MegaState.ResultCheckAccountV2(
                                status = data.status,
                                message = data.message,
                                data = data.data,
                                errorCode = data.errorCode
                            )
                        }
                    }
                }

                is MegaIntent.UpdateUserPinSettingNewFlowOtp -> {
                    otpRepository.accountResetPin(
                        pinCode = intent.pass,
                        confirmPinCode = intent.passConfirm,
                        typeValidate = intent.otpType,
                        verifyToken = intent.verifyToken
                    ).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            MegaState.ResultCreateUserPinSettingNewFlowOtp(isCached = isCached, data = data)
                        }
                    }
                }
                is MegaIntent.RequestValidateUserPin -> {
                    otpRepository.validateUserPin(
                        phone = sharedPreferences.userPhone(),
                        typeValidate = intent.type
                    )
                        .collect { result ->
                            _state.value = result.reduce(intent = intent) { _, validateUserPin ->
                                MegaState.ResultValidateUserPin(data = validateUserPin, type = intent.type)
                            }
                        }
                }

                is MegaIntent.UpdateAccountToSaleMode -> {
                    loginRepository.pluginSaleMode(
                        verifyToken = intent.verifyToken,
                        pushRegId = sharedPreferences.fcmToken(),
                        phone = sharedPreferences.userPhone()
                    ).collect { result ->

                        _state.value = result.reduce(intent = intent) { isCached, data ->
                            if(data.status == "1") {
                                sharedPreferences.apply {
                                    saveUserLogin(true)
                                    saveAccessToken(data.data.accessToken)
                                    saveAccessTokenType(data.data.accessTokenType)
                                    setLinkingToken("")

                                    saveEnableSalesMode(enableSalesMode = true)
                                }
                                MegaState.ResultUpdateAccountToSaleMode(isCached = isCached, data = data)
                            } else {
                                MegaState.Error(message = data.message, intent = intent)
                            }
                        }
                    }
                }

                is MegaIntent.GetAllDownloadVideos -> {
                    if(intent.isAirline) {
                        VideoDownloadManager.instance.getAllForAirlineDownloadVideos(intent.isAirline).collect {
                            _state.value = it.reduce(intent = intent) { isCached, data ->
                                MegaState.GetAllDownloadVideosResult(
                                    isCached = isCached,
                                    data = data
                                )
                            }
                        }
                    } else {
                        VideoDownloadManager.instance.getAllDownloadVideos().collect {
                            _state.value = it.reduce(intent = intent) { isCached, data ->
                                MegaState.GetAllDownloadVideosResult(
                                    isCached = isCached,
                                    data = data
                                )
                            }
                        }
                    }
                }

                else -> {}
            }
        }
    }

    override fun <T> Result<T>.reduce(
        intent: MegaIntent?,
        successFun: (Boolean, T) -> MegaState
    ): MegaState {
        return when (this) {
            is Result.Init -> MegaState.Loading(intent = intent)
            is Result.Success -> {
                successFun(this.isCached, this.successData)
            }
            is Result.ServerError.ManyRequest -> MegaState.ManyRequest(message = this.message, seconds = this.seconds, data = intent)

            is Result.UserError.RequiredLogin -> MegaState.ErrorRequiredLogin(
                this.message,
                intent = intent
            )
            is Result.Error.Intenet -> MegaState.ErrorNoInternet(
                message = this.message,
                intent = intent
            )
            is Result.Error -> MegaState.Error(message = this.message, intent = intent)
            Result.Done -> MegaState.Done(intent = intent)
        }
    }

    private suspend fun <T> Flow<Result<T>>.process(
        successFun: (Boolean, T) -> Unit
    ) {

        this
//            .filter { it is Result.Success }
            .collect {
                if (it is Result.Success) {
                    it.let { res ->
                        successFun(res.isCached, res.successData)
                    }
                }
            }
    }

    private suspend fun <T> Flow<Result<T>>.processUserInfo(
        successFun: (Boolean, T) -> Unit,
        errorFun: (Result.Error) -> Unit
    ) {

        this.filter { it is Result.Success || it is Result.Error }
            .collect {
                if (it is Result.Success) {
                    it.let { res ->
                        successFun(res.isCached, res.successData)
                    }
                } else if (it is Result.Error) {
                    errorFun(it)
                }
            }
    }

    private fun Result.Error.processError(intent: MegaIntent): MegaState {
        return if (this is Result.UserError.RequiredLogin) {
            MegaState.ErrorRequiredLogin(message = this.message, intent = intent)
        } else {
            MegaState.Error(message = this.message, intent = intent)
        }
    }

    private suspend fun getUserInfoWithLocation(intent: MegaIntent) {
        safeLaunch {
            try {
                val deferreds = mutableListOf<Deferred<Unit>>()
                var userInfo: UserInfo? = null
                var errorState: MegaState? = null
                var locations: Map<String, String> = mutableMapOf()
                deferreds.add(async {
                    userRepository.getUserInfo().processUserInfo(

                        successFun = { isCached, data ->
                            userInfo = data
                        },
                        errorFun = { error ->
                            errorState = error.processError(intent = intent)
                        }
                    )
                })
                deferreds.add(async {
                    commonRepository.getListCity().process { isCached, data ->
                        locations = data
                    }
                })
                deferreds.forEach { it.await() }
                withContext(Dispatchers.Main) {
                    userInfo?.let { userInfoData ->
                        val userLocationId =
                            getUserLocationId(userInfoData.location, locations) ?: ""
                        savedState.set("locationId", userLocationId)
                        val userInfoCopy = userInfoData.copy(
                            location = locations[userLocationId]
                        )
                        _state.value =
                            MegaState.ResultGetUserInfoWithLocation(
                                isCached = false,
                                userInfo = userInfoCopy,
                                locations = locations
                            )
                    } ?: errorState?.let {
                        _state.value = it
                    } ?: kotlin.run {
                        _state.value =
                            MegaState.Error(
                                intent = intent,
                                message = "Get userinfo error"
                            )
                    }
                }
            } catch (ex: Exception) {
                Logger.d("Exception: ${ex.message}")
            }
            withContext(Dispatchers.Main) { _state.value = MegaState.Done(intent = intent) }
        }
    }

    private fun getUserLocationId(location: String?, locations: Map<String, String>): String? {
        if (locations.keys.contains(location)) return location else locations.entries.forEach {
            if (it.value == location) return it.key
        }
        return location
    }

    private suspend fun getClusterInfoAtInit(intentGroup: MegaIntent.GetClusterInfoAtInit) {
        Timber.tag("tam-mega").e("getClusterInfoAtInit")
        val measureTime = measureTimeMillis {
//            withTimeoutOrNull(5000) {
            safeLaunch {
                try {
                    val deferreds: ArrayList<Deferred<Unit>> = arrayListOf()
                    val intents = intentGroup.data
                    val listResult = ArrayList<MegaState>()
                    for (intent in intents) {
                        when (intent) {
                            is MegaIntent.GetUserInfo -> {
                                deferreds.add(async {
//                                    userRepository.getUserInfo().process { isCached, userInfo ->
//                                        sharedPreferences.saveUserAvatar(userInfo.avatar ?: "")
//                                        listResult.add(
//                                            MegaState.ResultGetUserInfo(
//                                                isCached = isCached,
//                                                data = userInfo
//                                            )
//                                        )
//                                    }
                                    userRepository.getUserInfo().processUserInfo(
                                        successFun = { isCached, data ->
                                            listResult.add(
                                                MegaState.ResultGetUserInfo(
                                                    isCached = isCached,
                                                    data = data
                                                )
                                            )
                                        },
                                        errorFun = { error ->
                                            listResult.add(error.processError(intent = intent))
                                        }
                                    )

                                })
                            }
                            is MegaIntent.GetConfig -> {
                                deferreds.add(async {
                                    commonRepository.getConfig().process { isCached, config ->
                                        listResult.add(
                                            MegaState.ResultConfig(
                                                isCached = isCached,
                                                data = config
                                            )
                                        )
                                    }

                                })
                            }
                            is MegaIntent.GetMegaMenuV3 -> {
                                Timber.tag("tam-mega").e("GetMegaMenuV2")
                                deferreds.add(async {

                                    commonRepository.getMegaMenusV3(miniAppSdkVersion = intent.miniAppSdkVersion)
                                        .collect { res ->
                                            if (res is Result.Success) {
                                                Timber.tag("tam-mega")
                                                    .e("GetMegaMenuV2 success ${res.successData}")
                                                listResult.add(
                                                    MegaState.ResultMegaMenuV3(
                                                        isCached = res.isCached,
                                                        data = res.successData
                                                    )
                                                )

                                            } else if (res is Result.Error) {
                                                // return default data for case error
                                                Timber.tag("tam-mega").e("GetMegaMenuV2 Error")
                                                listResult.add(
                                                    MegaState.ResultMegaMenuV3(
                                                        isCached = false,
                                                        data = MegaMenu.defaultDataV3()
                                                    )
                                                )
                                            }
                                        }
                                })
                            }

                            else -> {}
                        }
                    }

                    deferreds.forEach { it.await() }
                    withContext(Dispatchers.Main) {
                        _state.value =
                            MegaState.ResultGetClusterInfoAtInit(
                                data = listResult
                            )
                    }
                } catch (ex: Exception) {
                    Logger.d("Exception: ${ex.message}")
                }
                // Process timeout or return null
                withContext(Dispatchers.Main) { _state.value = MegaState.Done() }
            }
        }
        Timber.d("tam-mega: Measure time of getClusterInfoAtInit: ${measureTime / 1000.0}s ->  ${Thread.currentThread().name}")
    }

    //endregion

    //region Commons

    fun saveUserInfo(data: UserInfo) {
        savedState.set("avatar", data.avatar)
        savedState.set("id", data.id)
        savedState.set("name", data.name)
        savedState.set("phone", data.phone)
        savedState.set("email", data.email)
        savedState.set("sexCode", data.sexCode)
        savedState.set("birthDate", data.birthDate)
        savedState.set("location", data.location)
        savedState.set("subContract", data.subContract)
        savedState.set("subStatus", data.subStatus)
    }

    fun userAvatar() = savedState.get<String>("avatar") ?: ""
    fun userId() = savedState.get<String>("id") ?: ""
    fun userName() = savedState.get<String>("name") ?: ""
    fun userPhone() = savedState.get<String>("phone") ?: ""
    fun userEmail() = savedState.get<String>("email") ?: ""
    fun userSexCode() = savedState.get<Int>("sexCode") ?: -1
    fun userBirthDate() = savedState.get<String>("birthDate") ?: ""
    fun userLocation() = savedState.get<String>("location") ?: ""
    fun userLocationId() = savedState.get<String>("locationId") ?: ""

    fun saveListSubmenus(data: List<MegaMenuItem>) {
        this.listSubMenus = data
    }

    fun getListSubmenus() = this.listSubMenus

    fun getBundleToSaveState(): Bundle {
        if (getWebViewBundle() == null) {
            val bundle = Bundle()
            savedState["webviewState"] = bundle
            return bundle
        } else {
            return getWebViewBundle()!!
        }
    }

    fun saveUserDeleteData(data: CheckUserDeleteData) {
        this.userDeleteData = data
    }

    fun getUserDeleteData() = this.userDeleteData

    // v2
    fun saveUserDeleteDataV2(data: ValidateDisableUserData) {
        this.userDeleteDataV2 = data
    }

    fun getUserDeleteDataV2() = this.userDeleteDataV2
    fun getWebViewBundle(): Bundle? = savedState["webviewState"]
    //endregion

    //region Intent, State
    sealed class MegaState : ViewState {
        object Idle : MegaState()
        data class Loading(val intent: MegaIntent? = null) : MegaState()
        data class StructureResult(val isCached: Boolean, val data: List<Structure>) : MegaState()
        data class ResultClusterStructureItem(
            val isCached: Boolean,
            val data: List<Pair<Int, Structure>>
        ) : MegaState()

        data class ResultGetClusterInfoAtInit(val data: List<MegaState>) : MegaState()
        data class ResultGetUserInfo(val isCached: Boolean, val data: UserInfo) : MegaState()
        data class ResultGetUserInfoWithLocation(
            val isCached: Boolean,
            val userInfo: UserInfo,
            val locations: Map<String, String>
        ) : MegaState()

        data class ResultUserInfo(val isCached: Boolean, val data: UserInfo) : MegaState()
        data class ResultGetListWidget(val isCached: Boolean, val data: List<Widget>) : MegaState()
        data class ResultSettingGeneral(val isCached: Boolean, val data: SettingGeneral) :
            MegaState()

        data class ResultListCity(val isCached: Boolean, val data: Map<String, String>) :
            MegaState()

        data class ResultUpdateUserInfo(val data: Status, val oldUserInfo: UserInfo? = null) :
            MegaState()

        data class ResultUploadUserAvatar(val data: String) : MegaState()
        data class ResultChangePassword(val data: Status) : MegaState()
        data class ResultConfig(val isCached: Boolean, val data: Config) : MegaState()
        data class ResultMegaMenuV3(val isCached: Boolean, val data: MegaMenu) : MegaState()
        data class ResultMegaMenusHamburger(val isCached: Boolean, val data: MegaMenu) : MegaState()
        data class ResultCheckPromotion(val data: PromotionStatus) : MegaState()
        data class ResultGetRevisionSuccess(val revision: String) : MegaState()
        object ResultDeleteRevision : MegaState()
        data class ResultSetDrmKeyEnv(val env: String) : MegaState()
        data class ResultDeviceToken(val isCached: Boolean, val data: List<DeviceToken>) :
            MegaState()

        data class ResultDeleteDeviceToken(val isCached: Boolean, val data: DeleteDeviceToken) :
            MegaState()

        data class ResultGroupPackageUser(val isCached: Boolean, val data: List<GroupPackageUser>) :
            MegaState()

        data class ResultPaymentHistory(
            val isCached: Boolean,
            val data: List<PackageHistory>,
            val isBind: Boolean
        ) :
            MegaState()

        data class ResultLogout(val data: Status) : MegaState()
        data class ResultRequestOtp(val data: RequestOtp) : MegaState()
        data class ResultVerifyOtp(val data: Login) : MegaState()
        data class Error(val message: String, val intent: MegaIntent? = null) : MegaState()
        data class ErrorRequiredLogin(val message: String, val intent: MegaIntent? = null) :
            MegaState()

        data class Done(val intent: MegaIntent? = null) : MegaState()
        object DismissOmniBottomSheet : MegaState()
        data class ResultCheckAccount(
            val status: Int,
            val message: String,
            val data: CheckUserDeleteData
        ) : MegaState()

        data class ResultGetDeletePolicyAccount(
            val isCached: Boolean,
            val data: List<IntroductionPage>
        ) : MegaState()

        data class ResultDeleteAccount(
            val isCached: Boolean,
            val status: Int,
            val message: String
        ) : MegaState()

        data class ErrorNoInternet(val intent: MegaIntent? = null, val message: String) :
            MegaState()

        data class ResultConfirmPassword(val status: Int, val message: String, val checkPasswordData: CheckPassword.CheckPasswordData) : MegaState()

        data class ResultChangePinSettingOtpV1(val isCached: Boolean, val data: Status) : MegaState()
        data class ResultCreatePinSetting(val isCached: Boolean, val data: AccountSetPinEntity) : MegaState()
        // OTP v2
        data class ResultCheckAccountV2(
            val status: Int,
            val message: String,
            val data: ValidateDisableUserData,
            val errorCode :String
        ) : MegaState()

        //sale mode
        data class ResultUpdateAccountToSaleMode(val isCached: Boolean, val data: PluginSaleMode) : MegaState()

        data class ManyRequest(val message: String, val seconds:Long, val data: MegaIntent? = null) : MegaState()



        data class ResultDeleteAccountV2(
            val isCached: Boolean,
            val status: String,
            val message: String,
            val data: DisableAccount
        ) : MegaState()

        // OTP new flow
        data class ResultRequestOtpV2(val data: SendOtpV2Entity) : MegaState()
        data class ResultVerifyOtpV2(val data: VerifyOTPV2Entity) : MegaState()
        data class ResultValidateUserPin(val data: ValidateUserPinEntity, val type: UserOtpType) : MegaState()
        data class ResultCreateUserPinSettingNewFlowOtp(val isCached: Boolean, val data: ResetPinEntity) : MegaState()

        // Download
        data class GetAllDownloadVideosResult(val isCached: Boolean, val data: List<CollectionAndChapters>) : MegaState()

    }

    sealed class MegaIntent : ViewIntent {
        object GetUserInfoWithLocation : MegaIntent()
        object GetUserInfo : MegaIntent()
        object GetListCity : MegaIntent()
        object GetDeviceToken : MegaIntent()
        object GetListApp : MegaIntent()
        object GetListWidgets : MegaIntent()
        object Logout : MegaIntent()
        object RequestChangePasswordOtp : MegaIntent()
        object ResendRequestChangePasswordOtp : MegaIntent()
        object GetConfig : MegaIntent()
        data class GetMegaMenuV3(val miniAppSdkVersion: String) : MegaIntent()
        data class GetMegaHamburgerMenu(val miniAppSdkVersion: String) : MegaIntent()
        data class VerifyChangePassOtp(val otpCode: String) : MegaIntent()
        data class UpdateUserInfo(
            val fullName: String?,
            val email: String?,
            val sexCode: Int?,
            val birthDay: String?,
            val avatar: String?,
            val location: String?,
            val oldUserInfo: UserInfo? = null
        ) : MegaIntent()

        data class ChangePassword(
            val phone: String,
            val currentPassword: String,
            val newPassword: String,
            val newPasswordAgain: String
        ) : MegaIntent()

        data class CheckPromotion(val code: String) : MegaIntent()
        data class GetGroupPackageUser(val userId: String) : MegaIntent()
        data class GetPaymentHistory(val page: Int, val perPage: Int) : MegaIntent()
        data class GetClusterInfoAtInit(val data: List<MegaIntent>) : MegaIntent()
        data class DeleteDeviceToken(val tokens: List<String>, val verifyToken: String) :
            MegaIntent()

        object TriggerDismissOmniBottomSheet : MegaIntent()
        object CheckAccount : MegaIntent()
        data class GetDeletePolicyAccount(val pageName: String) : MegaIntent()
        data class DeleteAccount(val verityToken: String) : MegaIntent()
        object RequestDeleteAccountOtp : MegaIntent()
        object ResendDeleteAccountOtp : MegaIntent()
        data class VerifyDeleteAccountOtp(val otpCode: String) : MegaIntent()
        data class ConfirmPassword(
            val phone: String,
            val currentPassword: String,
        ) : MegaIntent()
        data class ChangePinSettingOtpV1(val verifyToken: String, val otpType: String, val pass: String, val passConfirm: String) : MegaIntent()
        data class CreatePinSetting(val pass: String, val passConfirm: String) : MegaIntent()

        // OTP v2
        object CheckAccountV2 : MegaIntent()
        data class DeleteAccountV2(val verityToken: String) : MegaIntent()

        //sale mode
        data class UpdateAccountToSaleMode(val verifyToken: String) : MegaIntent()

        // OTP new flow
        data class RequestOtpV2(val verifyToken: String, val otpType: UserOtpType, val email: String? = null) : MegaIntent()
        data class RequestResendOtpV2(val verifyToken: String, val otpType: UserOtpType, val email: String? = null) : MegaIntent()
        data class VerifyOtpV2(val otpCode: String, val otpType: UserOtpType, val email: String? = null) : MegaIntent()
        data class UpdateUserPinSettingNewFlowOtp(val pass: String, val passConfirm: String, val otpType: UserOtpType, val verifyToken: String) : MegaIntent()
        data class RequestValidateUserPin(val type: UserOtpType) : MegaIntent()

        // Download
        data class GetAllDownloadVideos(val isAirline : Boolean) : MegaIntent()

    }
    //endregion
}
