package com.fptplay.mobile

import android.app.ActivityManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.ActivityInfo
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.activity.viewModels
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.fplay.module.downloader.VideoDownloadManager
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.ActivityExtensions.isInPiPMode
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseActivity
import com.fptplay.mobile.common.ui.view.GlobalSnackbarManager
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.DeeplinkConstants
import com.fptplay.mobile.common.utils.NetworkUtils
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.features.mega.apps.airline.AirlineActivity
import com.fptplay.mobile.features.payment.PaymentViewModel
import com.fptplay.mobile.features.payment.PaymentViewModel.PaymentViewIntent.RetryVerifyGoogleBillingTransaction
import com.fptplay.mobile.features.payment.google_billing.BillingClientLifecycleV6
import com.fptplay.mobile.features.payment.google_billing.BillingUtils.isEnableGoogleBilling
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject


@AndroidEntryPoint
class WelcomeActivity : BaseActivity(R.layout.welcome_activity) {
    private val paymentViewModel by viewModels<PaymentViewModel>()
    private var billingClientLifecycle: BillingClientLifecycleV6? = null
    @Inject
    lateinit var trackingProxy: TrackingProxy
    @Inject
    lateinit var trackingInfo: Infor
    @Inject
    lateinit var sharedPreferences: SharedPreferences

    override val enableNetworkListener = true

    override fun onCreate(savedInstanceState: Bundle?) {

        MainApplication.INSTANCE.currentActivity?.let { currentActivity ->
            if (currentActivity.isInPiPMode()) {
                if (intent.data != null) {
                    currentActivity.moveTaskToBack(true)

                    // Create a new intent with application context

                    try {
                        //Logic fix xiaomi navigate from facebook app into fptplay when pip
                        Handler(Looper.getMainLooper()).postDelayed({
                            finishAndRemoveTask()

                            //Logic fix s24 ultra navigate from facebook app into fptplay when pip
                            Handler(Looper.getMainLooper()).postDelayed({
                                try {
                                    applicationContext.startActivity(intent)
                                } catch (ex: Exception) {
                                    ex.printStackTrace()
                                }
                            }, 150)
                        }, 150)
                    } catch (ex: Exception) {
                        ex.printStackTrace()
                    }
                } else {
                    val intent = currentActivity.intent
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
                    finish()
                    startActivity(intent)
                    super.onCreate(savedInstanceState)
                    return
                }
            }
        }
        super.onCreate(savedInstanceState)

        checkUtm(intent)
        TrackingUtil.startAppAndSession()
        Utils.getLocalDataUtm(sharedPreferences, trackingInfo)
        Utils.getLocalDataItm(sharedPreferences, trackingInfo)
        initGoogleBilling()
        Utils.pushUserProfileCleverTap(this, sharedPreferences)
        TrackingUtil.arrayLogOffline = getLogD2GList() //get danh sach log offline khi khoi dong app
        Logger.d("trangtest log D2G ${TrackingUtil.arrayLogOffline.size}")
        pauseTheDownloadingTask()
        // Enable rotation for tablet
        enableRotateForTablet()
    }

    override fun onStart() {
        super.onStart()
        registerAdjustBroadcastReceiver()
    }

    override fun onStop() {
        unregisterAdjustBroadcastReceiver()
        super.onStop()
    }

    /**
     * pause downloading task when not internet
     */
    private fun pauseTheDownloadingTask() {
        if (NetworkUtils.isNetworkAvailable()) {
            return
        }
        if (VideoDownloadManager.instance.isDownloading()) {
            VideoDownloadManager.instance.pauseAllTasksFromDB()
        }
    }

    //Get log D2G offline from sharedPreferences and parse to ArrayList<InforMobile>
    private fun getLogD2GList():ArrayList<InforMobile>{
        val log = sharedPreferences.getLogD2G()
        Logger.d("trangtest log D2G sharedPreferences $log")
        return if(log.isNullOrBlank()){
            arrayListOf()
        }else{
            val gson = Gson()
            val itemType = object : TypeToken<ArrayList<InforMobile>>() {}.type
            val data = gson.fromJson<ArrayList<InforMobile>>(log, itemType)
            if(data.isEmpty()) arrayListOf() else data
        }
    }
    //region Google Billing
    private fun initGoogleBilling() {
        if (isEnableGoogleBilling()) {
            billingClientLifecycle = BillingClientLifecycleV6.getInstance(applicationContext)
            billingClientLifecycle?.let { clientLifecycle ->
                lifecycle.addObserver(clientLifecycle)
                clientLifecycle.purchasesInApp.observe(this) { data ->
                    clientLifecycle.handlerPurchase(data, BillingClientLifecycleV6.PurchaseType.ResponseInAppPurchase,
                        onPurchaseSuccess = object : BillingClientLifecycleV6.OnPurchaseSuccess {
                            override fun onPurchaseSuccess(orderId: String, planId: String, googlePurchaseToken: String) {
                                val utmData = Utils.getLocalDataUtm(sharedPreferences, trackingInfo)
                                paymentViewModel.dispatchIntent(RetryVerifyGoogleBillingTransaction(
                                    planId = planId,
                                    googlePurchaseToken = googlePurchaseToken,
                                    affiliateSource = utmData.first,
                                    trafficId = utmData.second))
                            }
                        },
                        onPurchasePending = null,
                        onPurchaseFail = null
                    )
                }
                clientLifecycle.purchasesSubs.observe(this) { data ->
                    clientLifecycle.handlerPurchase(data, BillingClientLifecycleV6.PurchaseType.ResponseSubsPurchase,
                        onPurchaseSuccess = object : BillingClientLifecycleV6.OnPurchaseSuccess {
                            override fun onPurchaseSuccess(orderId: String, planId: String, googlePurchaseToken: String) {
                                val utmData = Utils.getLocalDataUtm(sharedPreferences, trackingInfo)
                                paymentViewModel.dispatchIntent(RetryVerifyGoogleBillingTransaction(
                                    planId = planId,
                                    googlePurchaseToken =googlePurchaseToken,
                                    isRetry = 1,
                                    affiliateSource = utmData.first,
                                    trafficId = utmData.second))
                            }
                        },
                        onPurchasePending = null,
                        onPurchaseFail = null
                    )
                }
            }
        }
    }
    //endregion

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        checkUtm(intent)
        Timber.d("***Newintent: ")

    }
    private fun logCloseApp(){
        //tạm thời ko log close app khi destroy welcome
//        trackingProxy.sendEvent(
//            InforMobile(
//                infor = trackingInfo, logId = "12", appId = "Welcome", appName = "Welcome",
//                event = "CloseApplication",
//                boxTime = DateTimeUtils.getLogSessionAtCurrentTime()
//            )
//        )
    }

    private fun checkUtm(intent: Intent?) {
        try {
            //Case click on link share
            if (intent?.data is Uri) {
                Utils.checkAndSaveUTM(
                    deeplink = intent.data.toString(),
                    isDeeplinkCalledInApp = false
                )
            } else if (!intent?.getStringExtra(Constants.FIREBASE_NOTIFICATION_FILED_URL).isNullOrBlank()) {
                //Case enter app using notification
                val url = intent?.getStringExtra(Constants.FIREBASE_NOTIFICATION_FILED_URL) ?: ""
                Utils.checkAndSaveUTM(
                    deeplink = url,
                    isDeeplinkCalledInApp = true
                )
            }

        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    override fun onDestroy() {
        logCloseApp()
        MainApplication.INSTANCE.networkDetector.removeObservers(this)
        super.onDestroy()

    }

    //region Tablet Rotation
    private fun enableRotateForTablet() {
        if (isTablet()) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        }
    }
    //endregion Tablet Rotation

    private val adjustBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            Logger.d("HomeActivity >> AdjustBroadcastReceiver onReceive intent: ${intent?.data}")
            intent?.let {
                <EMAIL> { welcomeIntent ->
                    if (intent.getBooleanExtra(
                            DeeplinkConstants.ADJUST_TRUE_LINK_NEW_KEY,
                            false
                        )
                    ) {
                        try {
                            val urlDeepLink =
                                intent.getStringExtra(DeeplinkConstants.ADJUST_TRUE_LINK_URL_KEY)
                                    ?: ""
                            if (urlDeepLink.isNotBlank()) {
                                welcomeIntent.putExtra(
                                    DeeplinkConstants.ADJUST_TRUE_LINK_NEW_KEY,
                                    true
                                )
                                welcomeIntent.putExtra(
                                    DeeplinkConstants.ADJUST_TRUE_LINK_URL_KEY,
                                    urlDeepLink
                                )
                            }
                        } catch (ex: Exception) {
                            ex.printStackTrace()
                        }
                    }
                }
            }
        }
    }

    private fun registerAdjustBroadcastReceiver() {
        val filter = IntentFilter(DeeplinkConstants.ADJUST_TRUE_LINK_BROADCAST_INTENT)
        LocalBroadcastManager.getInstance(MainApplication.INSTANCE.applicationContext).registerReceiver(adjustBroadcastReceiver, filter)
    }

    private fun unregisterAdjustBroadcastReceiver() {
        LocalBroadcastManager.getInstance(MainApplication.INSTANCE.applicationContext).unregisterReceiver(adjustBroadcastReceiver)
    }
}