<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_survey_answer"
    android:layout_marginVertical="@dimen/_2sdp"
    android:paddingVertical="@dimen/_8sdp"
    android:paddingHorizontal="@dimen/_6sdp">

    <TextView
        android:id="@+id/tv_answer_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white_87"
        android:textSize="@dimen/_6ssp"
        android:fontFamily="@font/sf_pro_display_semibold"
        android:maxLines="2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Khác"
        android:layout_marginBottom="@dimen/_10sdp"
        app:layout_constraintBottom_toTopOf="@+id/et_answer_textarea" />

    <EditText
        android:id="@+id/et_answer_textarea"
        android:layout_width="0dp"
        android:layout_height="@dimen/_50sdp"
        android:background="@drawable/bg_textarea"
        android:textColor="@color/white_87"
        android:textSize="@dimen/_6ssp"
        android:gravity="top|start"
        android:textColorHint="@color/white_38"
        android:hint="@string/survey_hint_answer_textarea"
        android:isScrollContainer="true"
        android:scrollbars="vertical"
        android:overScrollMode="always"
        android:maxLines="5"
        android:inputType="textMultiLine|textCapSentences"
        android:textCursorDrawable="@drawable/account_edittext_cursor"
        android:imeOptions="actionDone"
        android:letterSpacing="0.02"
        android:paddingHorizontal="@dimen/_5sdp"
        android:paddingTop="@dimen/_4sdp"
        android:paddingBottom="@dimen/_12sdp"
        android:fontFamily="@font/sf_pro_display_regular"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_answer_text"
        app:layout_constraintBottom_toBottomOf="parent"
        android:scrollHorizontally="false"
        android:singleLine="false"/>

    <TextView
        android:letterSpacing="0.02"
        android:fontFamily="@font/sf_pro_display_medium"
        android:id="@+id/tv_char_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white_38"
        android:textSize="@dimen/_6ssp"
        android:paddingHorizontal="@dimen/_5sdp"
        android:paddingBottom="@dimen/_2sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/et_answer_textarea"
        />

</androidx.constraintlayout.widget.ConstraintLayout>
