package com.fptplay.mobile.player.handler

import android.content.Context
import android.util.Base64
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.modifyPlayerListener
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.features.mqtt.MqttConnectManager
import com.fptplay.mobile.player.handler.drm.*
import com.xhbadxx.projects.module.domain.Result.*
import com.xhbadxx.projects.module.domain.entity.fplay.common.Stream
import com.tear.modules.player.util.IPlayer
import com.tear.modules.player.util.PlayerControlCallback
import com.tear.modules.tracking.model.CommonInfor
import com.xhbadxx.projects.module.domain.entity.fplay.drm.PingStreamV2
import com.xhbadxx.projects.module.util.logger.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import kotlin.coroutines.EmptyCoroutineContext

class PlayerHandler(
    private val context: Context,
    private val drmApi: DrmApi,
    var onEvents: OnEvents ?= null,
    var onCastSessionEvents: OnCastSessionEvents ?= null,
    var coroutineScope: CoroutineScope ?= null,
) : DefaultLifecycleObserver {

    //region Variables
    private lateinit var iRequest: Request
    private lateinit var iResponse: Response
    private lateinit var iPlayer: IPlayer
    private lateinit var iStream: Stream
    private lateinit var iPlayerRequest: IPlayer.Request
    //
    private val partnerDrm: PartnerDrm by lazy { PartnerDrm() }
    //    private val pingStreamEvent: PingStreamEvent by lazy { PingStreamEvent() }
    //
    private val kplusHandler: KPlusHandler by lazy { KPlusHandler() }
    private val hboHandler: HboHandler by lazy { HboHandler() }
    private val pingHandler: PingHandler by lazy { PingHandler() }
    //
    private val pingV2Handler: PingV2Handler by lazy { PingV2Handler() }
    private val pingV2Event: PingV2Event by lazy { PingV2Event() }
    //endregion

    //region LifecycleObserver -> to start all services
    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        Logger.d("$Tag -> onStart")
        val isCastSession = onCastSessionEvents?.checkCastSession() ?: false
        if (!isCastSession) {
            startAll()
        }
    }
    //endregion

    //region LifecycleObserver -> to stop all currently services
    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        Logger.d("$Tag -> OnStop")
        stopAll(includePlayer = false)
    }
    //endregion

    //region Feature -> Start all services
    private fun startAll(){
        Logger.d("$Tag -> StartAllService")
        if (this::iPlayer.isInitialized){
            stopAll(includePlayer = false)
            when(val response = check(iRequest)){
                is Response.Drm -> {
                    bindPingStreamV2()
                    when (response) {
                        is Response.Drm.KPlus -> {
                            kplusHandler.check(stream = iStream, processPlay = {
                                // When app background -> foreground, don't need prepare player again -> player auto prepare
                            })
                        }
                        is Response.Drm.Hbo -> {
                            coroutineScope?.launch {
                                if (decodeSession()){
                                    if (iStream.pingQnet == 1){
                                        partnerDrm.pingPlay(isRestartService = true, operatorId = iStream.operatorId, sessionId = iStream.sessionId, token = hboHandler.token())
                                    }else{
                                        // When app background -> foreground, don't need prepare player again -> player auto prepare
                                    }
                                }
                            }
                        }
                        is Response.Drm.Other -> {
                            // When app background -> foreground, don't need prepare player again -> player auto prepare
                        }
                    }
                }
                is Response.NoDrm -> {
                    bindPingStreamV2()
                    // When app background -> foreground, don't need prepare player again -> player auto prepare
                }
            }
        }
    }
    //endregion

    //region Feature -> Stop all services
    fun stopAll(includePlayer: Boolean = true, changeChannel: Boolean = true){
        Logger.d("$Tag -> StopAllServices changeChannel: $changeChannel")
        if (includePlayer) iPlayer.stop(force = true)
        if (this::iResponse.isInitialized){
            unBindPingStreamV2()
            when (iResponse){
                is Response.Drm -> {
//                    unBindPingStream()
                    when (iResponse){
                        is Response.Drm.KPlus -> { unBindKplus() }
                        is Response.Drm.Hbo -> { unBindHbo(changeChannel = changeChannel) }
                        else -> {}
                    }
                }
                else -> {}
            }
        }
    }
    //endregion

    //region Feature -> Show error in player
    private fun error(reason: String){
        onEvents?.showError(reason = reason)
    }
    //endregion


    //region Feature -> Check content: drm (kplus, hbo), no-drm
    private fun check(request: Request?): Response {
        iResponse = if (request == null) Response.NoDrm
        else {
            if (request.isDrm) {
                when {
                    request.provider == "k-plus" -> Response.Drm.KPlus
                    request.merchant == "qnet" -> Response.Drm.Hbo
                    else -> { Response.Drm.Other }
                }
            } else {
                Response.NoDrm
            }
        }
        return iResponse
    }
    //endregion

    //region Feature -> Check Running Service Ping Ccu
    fun isRunningServicePingCcu() = this::iRequest.isInitialized && iRequest.pingEnable
    //endregion Feature -> Check Running Service Ping Ccu

    //region Feature -> Bind data and check drm/no-drm from request after starting all services.
    fun bindAndPlay(
        request: Request,
        player: IPlayer,
        playerRequest: IPlayer.Request,
        stream: Stream
    ){
        stopAll(includePlayer = false, changeChannel = !this::iRequest.isInitialized || request.streamId != this.iRequest.streamId)
        Logger.d("$Tag -> BindAndPlay -> Request: [$request], current request ${if (this::iRequest.isInitialized) iRequest else null}")
        //
        this.iPlayer = player
        this.iRequest = request
        this.iStream = stream
        this.iPlayerRequest = playerRequest
        //
        MqttConnectManager.INSTANCE.checkLimitCcu(pingMqtt = stream.pingMqtt, mqttMode = stream.mqttMode, codeLimitCcu = request.codeLimitCcu) {
            when(val response = check(iRequest)){
                is Response.Drm -> {
//                bindPingStream() // Ping stream for all Partner's Drm.
                    bindPingStreamV2()
                    when (response) {
                        is Response.Drm.KPlus -> {
                            kplusHandler.check(stream = iStream, processPlay = {
                                iPlayer.apply {
                                    prepare(request = iPlayerRequest)
                                }
                            })
                        }
                        is Response.Drm.Hbo -> {
                            coroutineScope?.launch {
                                if (decodeSession()){
                                    if (iStream.pingQnet == 1){
                                        partnerDrm.pingPlay(operatorId = iStream.operatorId, sessionId = iStream.sessionId, token = hboHandler.token())
                                    }else{
                                        iPlayer.prepare(request = iPlayerRequest)
                                    }
                                }
                            }
                        }
                        is Response.Drm.Other -> {
                            iPlayer.prepare(request = iPlayerRequest)
                        }
                    }
                }
                is Response.NoDrm -> {
                    bindPingStreamV2()
                    iPlayer.prepare(request = iPlayerRequest.copy(drm = null))
                }
            }
        }
    }
    //endregion

    //region Drm -> Kplus
    private fun KPlusHandler.check(stream: Stream, processPlay: () -> Unit = {}){
        Logger.d("$KplusTag -> Check -> Stream: [$stream]")
        this.update(
            statusCode = stream.codeState,
            errorCode = stream.codeError,
            revalidateSpanTime = stream.timeRevalidateSpan,
            revalidateTime = stream.timeRevalidate,
            drm = partnerDrm,
            urlTrailer = stream.urlTrailer,
            paymentBackground = stream.vipImage,
            paymentPlanId = stream.vipPlan,
            title = stream.requireVipTitle,
            description = stream.requireVipDescription,
            btnActive = stream.btnActive,
            btnSkip = stream.btnSkip
        )
        when (this.checkStream())
        {
            is KPlusHandler.Response.Error.Policy,
            is KPlusHandler.Response.Error.Expired -> {
                stopAll()
            }
            is KPlusHandler.Response.Play -> {
                Logger.d("$KplusTag -> Play")
                processPlay()
            }
        }
    }

    private fun unBindKplus(){
        Logger.d("$KplusTag -> UnBindKplus")
        kplusHandler.stop()
    }
    //endregion

    //region Drm -> Hbo
    private fun HboHandler.check(isRestartService: Boolean, result: com.xhbadxx.projects.module.domain.entity.fplay.hbo.Ping){
        Logger.d("$HboTag -> Check -> Result: [$result]")
        this.update(operatorId = iStream.operatorId, sessionId = iStream.sessionId, drm = partnerDrm)
        when (val response = this.checkResult(result = result))
        {
            is HboHandler.Response.Play -> {
                if (!isRestartService) iPlayer.prepare(request = iPlayerRequest)
            }
            is HboHandler.Response.Stop -> {
                stopAll()
                error(reason = when (response.code){
                    401 -> context.getString(R.string.hbo_error_401)
                    405 -> context.getString(R.string.hbo_error_405)
                    else -> ""
                })
            }
            else -> {}
        }
    }

    private fun unBindHbo(changeChannel: Boolean){
        Logger.d("$HboTag -> UnBindHbo, changeChannel $changeChannel")
        hboHandler.stop()
        if (changeChannel) partnerDrm.pingEnd(token = hboHandler.token(), isObserver = false)
        hboHandler.reset(resetToken = changeChannel)
    }

    private suspend fun decodeSession() : Boolean{
        Logger.d("$HboTag -> DecodeSession")
        return withContext(Dispatchers.Default) {
            try {
                val detachData = iStream.sessionId.split(".")
                if (detachData.size >= 2) {
                    val decodeData = Base64.decode(detachData[1], Base64.URL_SAFE)
                    val data = String(decodeData, Charsets.UTF_8)
                    val jsonData = JSONObject(data)
                    val userId = jsonData.optString("userId", "")
                    if (!userId.isNullOrEmpty()){
                        return@withContext withContext(Dispatchers.Main){
                            iPlayerRequest = iPlayerRequest.copy(
                                drm = iPlayerRequest.drm?.copy(
                                    userId = userId
                                )
                            )
                            true
                        }
                    }
                }
            }catch (ex: Exception){
                ex.printStackTrace()
            }
            return@withContext withContext(Dispatchers.Main){
                stopAll()
                error(reason = context.getString(R.string.hbo_error_decode_session))
                false
            }
        }
    }
    //endregion

    //region Drm -> Ping stream (currently v1 -> kplus)
//    private fun PingHandler.bindAndPing(delayBeforePing: Boolean = true, pingType: PingHandler.Type = PingHandler.Type.Play, result: PingStream ?= null){
//        Logger.debug("$PingTag -> BindAndPing -> DelayBeforePing: [$delayBeforePing]")
//        if (check(request = iRequest) == Response.Drm.KPlus){
//            this.apply {
//                update(
//                    id = iRequest.streamId,
//                    timeToPing = result?.timeToPing ?: 60,
//                    pingType = pingType,
//                    drm = partnerDrm
//                )
//                start(delayBeforePing = delayBeforePing)
//            }
//        }
//    }
//
//    private fun unBindPingStream(){
//        Logger.debug("$PingTag -> UnBindPingStream")
//        if (check(request = iRequest) == Response.Drm.KPlus){
//            if (this::iPlayer.isInitialized){
//                iPlayer.apply {
//                    modifyPlayerListener(listener = pingV2Event, isRegister = false)
//                    removePlayerControlCallback(playerControlCallback = pingStreamEvent)
//                }
//            }
//            pingHandler.stop()
//            partnerDrm.pingPause(id = iRequest.streamId, isObserver = false)
//        }
//    }
//
//    private fun bindPingStream(){
//        Logger.debug("$PingTag -> BindPingStream")
//        if (check(request = iRequest) == Response.Drm.KPlus){
//            iPlayer.apply {
//                if (!playerCallback().contains(pingStreamEvent)) modifyPlayerListener(listener = pingStreamEvent, isRegister = true)
//                if (!playerControlCallback().contains(pingStreamEvent)) addPlayerControlCallback(playerControlCallback = pingStreamEvent)
//            }
//        }
//    }
//
//    private inner class PingStreamEvent : PlayerControlCallback, IPlayer.IPlayerCallback {
//        override fun onPauseButton() {
//            Logger.debug("$PingTag -> OnPauseButton")
//            pingHandler.bindAndPing(delayBeforePing = false, pingType = PingHandler.Type.Pause)
//        }
//
//        override fun onPlayButton() {
//            Logger.debug("$PingTag -> OnPlayButton")
//            pingHandler.bindAndPing(delayBeforePing = false, pingType = PingHandler.Type.Play)
//        }
//
//        override fun onError(code: Int, name: String, detail: String) {
//            Logger.debug("$PingTag -> OnError")
//            pingHandler.bindAndPing(delayBeforePing = false, pingType = PingHandler.Type.Pause)
//        }
//
//        override fun onReady() {
//            Logger.debug("$PingTag -> onReady")
//            pingHandler.bindAndPing(delayBeforePing = false, pingType = PingHandler.Type.Play)
//        }
//    }
    //endregion

    //region Drm -> Ping Stream (Version 2 -> Enable By Server)
    private fun PingV2Handler.bindAndPing(delayBeforePing: Boolean = true, pingType: PingV2Handler.Type, response: PingStreamV2?= null) {
        this.apply {
            update(
                id = iRequest.streamId,
                type = iRequest.type,
                eventId = iRequest.eventId,
                drm = partnerDrm,
                pingType = when {
                    pingType == PingV2Handler.Type.Pause -> pingType
                    iRequest.pingEncrypt -> PingV2Handler.Type.PlayWithEncrypt
                    else -> PingV2Handler.Type.Play
                },
                timeToPing = response?.data?.interval?.toLong() ?: 0L, // TODO check: delayTimeToPing <=> interval
                session = response?.data?.session ?: ""
            )
            start(delayBeforePing = delayBeforePing)
        }
    }

    private fun bindPingStreamV2() {
        if (iRequest.pingEnable) {
            pingV2Handler.initSession(session = iRequest.pingSession)
            iPlayer.apply {
                if (!playerCallback().contains(pingV2Event)) modifyPlayerListener(listener = pingV2Event, isRegister = true)
                if (!playerControlCallback().contains(pingV2Event)) addPlayerControlCallback(playerControlCallback = pingV2Event)
            }
        }
    }

    private fun unBindPingStreamV2(){
        Logger.d("$PingV2Tag -> UnBindPingStream")
        if (this::iRequest.isInitialized && iRequest.pingEnable){
            if (this::iPlayer.isInitialized){
                iPlayer.apply {
                    modifyPlayerListener(listener = pingV2Event, isRegister = false)
                    removePlayerControlCallback(playerControlCallback = pingV2Event)
                }
            }
            pingV2Handler.stop()
            pingV2Handler.reset()
            partnerDrm.pingPause(id = iRequest.streamId, isObserver = false, type = iRequest.type, eventId = iRequest.eventId)
            onEvents?.hideShowIp()
        }
    }

    private inner class PingV2Event : PlayerControlCallback, IPlayer.IPlayerCallback {
        override fun onPauseButton() {
            Logger.d("$PingV2Tag -> OnPauseButton")
        }

        override fun onPlayButton() {
            Logger.d("$PingV2Tag -> OnPlayButton")
        }

        override fun onError(code: Int, name: String, detail: String, error403: Boolean, responseCode: Int) {
            Logger.d("$PingV2Tag -> OnError")
            pingV2Handler.bindAndPing(delayBeforePing = false, pingType = PingV2Handler.Type.Pause)
        }

        override fun onReady() {
            Logger.d("$PingV2Tag -> onReady")
            pingV2Handler.bindAndPing(delayBeforePing = false, pingType = PingV2Handler.Type.Play)
        }
    }
    //endregion

    //region Model -> Request, Response, ScreenType, Static
    data class Request(
        val isDrm: Boolean = false,
        val provider: String = "",
        val merchant: String = "",
        //
        val screenType: ScreenType ,
        val streamId: String = "",
        val type: String = "",
        val eventId: String = "",
        // Live
        val bitrateId: String = "",
        val isFullHd: Int = 0,
        // Vod
        val episodeId: String = "",
        // Ping
        val pingEnable: Boolean = false,
        val pingSession: String = "",
        val pingEncrypt: Boolean = false,
        val codeLimitCcu: String = "",
        val processShowIp: (Long, Int, Int) -> Unit = { _, _, _ -> },
        val processReloadStream: () -> Unit = {},
        val processShowMatrixIp: () -> Unit = {}
    )

    sealed interface ScreenType{
        object Live : ScreenType
        object Vod : ScreenType
        object Premiere : ScreenType
    }

    private sealed interface Response {
        sealed interface Drm : Response{
            object Hbo : Drm
            object KPlus : Drm
            object Other : Drm
        }
        object NoDrm : Response
    }

    companion object{
        const val PingTag = "PingHandler"
        const val PingV2Tag = "PingV2Handler"
        const val HboTag = "HboHandler"
        const val KplusTag = "KplusHandler"
        const val Tag = "PlayerHandler"
    }
    //endregion

    //region Business rules for Partner's Drm.
    private inner class PartnerDrm : KPlusDrm, HboDrm, PingDrm {
        //region KPlus
        override fun errorExpired(urlTrailer: String, paymentBackground: String, paymentPlanId: String, title: String, description: String, btnActive: String, btnSkip: String) {
            Logger.d("$KplusTag -> ErrorExpired")
            coroutineScope?.launch {
                delay(200)
                withContext(Dispatchers.Main) {
                    onEvents?.navigateToRequiredVip(
                        planId = paymentPlanId,
                        fromSource = "play",
                        idToPlay = iRequest.streamId,
                        vipBackground = paymentBackground,
                        title = title,
                        description = description,
                        btnActive = btnActive,
                        btnSkip = btnSkip,
                        trailerUrl = urlTrailer
                    )
                }
            }
        }

        override fun validateStream() {
            coroutineScope?.launch {
                when (iRequest.screenType){
                    ScreenType.Live -> {
                        drmApi.getTvChannelStream(id = iRequest.streamId, bitrateId = iRequest.bitrateId).collect { result ->
                            Logger.d("$PingV2Tag -> getTvChannelStream -> Result: [$result]")
                            when (result) {
                                is Success -> {
                                    result.data?.run { kplusHandler.check(stream = this) }
                                }
                                is Error -> {
                                    kplusHandler.start(useRevalidateSpanTime = true)
                                }
                                else -> {}
                            }
                        }
                    }
                    ScreenType.Vod -> {
                        drmApi.getVodStream(id = iRequest.streamId, bitrateId = iRequest.bitrateId, episodeId = iRequest.episodeId).collect { result ->
                            Logger.d("$PingV2Tag -> getTvChannelStream -> Result: [$result]")
                            when (result) {
                                is Success -> {
                                    result.data?.run { kplusHandler.check(stream = this) }
                                }
                                is Error -> {
                                    kplusHandler.start(useRevalidateSpanTime = true)
                                }
                                else -> {}
                            }
                        }
                    }
                    else -> {}
                }
            }
        }

        override fun errorPolicy() {
            Logger.d("$KplusTag -> ErrorPolicy")
            error(reason = context.getString(R.string.kplus_error_policy))
        }
        //endregion

        //region Ping Stream
        override fun pingPlayV2(id: String, session: String, lastSession: String, encryptData: Boolean, type: String, eventId: String) {
            coroutineScope?.launch {
                val result = drmApi.pingPlay(id = id, session = session, lastSession = lastSession, encryptData = encryptData, type = type, eventId = eventId).collect { result ->
                    Logger.d("$PingV2Tag -> PingPlay -> Result: [$result]")
                    when (result) {
                        is Success -> {
                            result.data?.let { dataStream ->
                                when (dataStream.code) {
                                    400, 406, 451 -> {
                                        stopAll()
                                        error(reason = dataStream.msg)
                                        onEvents?.sendTrackingPingPlayCcu(actionType = CommonInfor.PingStreamActionType.PING_STREAM_RECEIVER_ACTION,
                                            message = dataStream.msg,
                                            type = dataStream.code.toString())
                                    }
                                    else -> {
                                        if (dataStream.code == 200) {
                                            pingV2Handler.updateLastedPingStreamResponse(data = dataStream) { sameResponse ->
                                                if (sameResponse){
                                                    pingV2Handler.increaseTimeToShowMatrixIp {
                                                        iRequest.processShowMatrixIp()
                                                    }
                                                }else {
                                                    pingV2Handler.resetTimeToShowMatrixIp()
                                                }
                                            }
                                        }
                                        pingV2Handler.bindAndPing(pingType = PingV2Handler.Type.Play, response = result.data)
                                        val actions = dataStream.data.pingActions
                                        actions.forEachIndexed { _, action ->
                                            if (action.type == "SHOW_IP"){
                                                iRequest.processShowIp(action.duration.toLong(), action.x, action.y)
                                            }else if (action.type == "RELOAD_STREAM"){
                                                iRequest.processReloadStream()
                                            }
                                            onEvents?.sendTrackingPingPlayCcu(actionType = CommonInfor.PingStreamActionType.PING_STREAM_RECEIVER_ACTION,
                                                showFingerprint = action.type == "SHOW_IP", type = action.type)
                                        }
                                    }
                                }
                            }
                        }
                        is UserError.RequiredLogin -> {
                            stopAll()
                            onEvents?.navigateToRequireLogin(
                                message = result.message,
                                idToPlay = iRequest.streamId
                            )
                            onEvents?.sendTrackingPingPlayCcu(actionType = CommonInfor.PingStreamActionType.PING_STREAM_RECEIVER_ACTION,
                                type = "401", message = result.message)
                        }
                        else -> {
                            pingV2Handler.bindAndPing(pingType = PingV2Handler.Type.Play)
                            pingV2Handler.increaseTimeToShowMatrixIp{
                                iRequest.processShowMatrixIp()
                            }
                        }
                    }
                }
            }
        }

        override fun pingPlay(id: String, type: String, eventId: String) {
//            coroutineScope?.launch {
//                val result = drmApi.pingPlay(id = id)
//                Logger.debug("$PingTag -> PingPlay -> Result: [$result]")
//                if (result is Success) {
//                    if (result.data.status == 1){
//                        pingHandler.bindAndPing(result = result.data)
//                    }else{
//                        stopAll()
//                        error(reason = result.data.message)
//                    }
//                }
//                else if (result is RequiredLogin) {
//                    stopAll()
//                    onEvents?.navigateToRequireLogin(
//                        message = context.getString(R.string.text_error_kplus_policy),
//                        idToPlay = iRequest.streamId
//                    )
//                }
//                else if (result is Error) {
//                    pingHandler.bindAndPing()
//                }
//            }
        }

        /**
         * Can't observer and bindAndPing (call when success or error) -> error when switch other channel.
         */
        override fun pingPause(id: String, isObserver: Boolean, type: String, eventId: String) {
            if (isObserver){
                coroutineScope?.launch {
                    drmApi.pingPause(id = id, type = type, eventId = eventId).collect {
                        Logger.d("$PingTag -> PingPause -> Result: [$it, $isObserver]")
                    }
                }
            }else {
                CoroutineScope(EmptyCoroutineContext).launch {
                    drmApi.pingPause(id = id, type = type, eventId = eventId).collect {
                        Logger.d("$PingTag -> PingPause -> Result: [$it, $isObserver]")
                    }
                }
            }
        }
        //endregion

        //region HBO
        override fun pingPlay(isRestartService: Boolean, operatorId: String, sessionId: String, token: String) {
            coroutineScope?.launch {
                if (token.isEmpty()){
                    drmApi.pingPlayHbo(operatorId = operatorId, sessionId = sessionId).collect { result ->
                        Logger.d("$HboTag -> PingPlay -> Result: [$result]")
                        if (result is Success) {
                            result.data?.let { hboHandler.check(isRestartService = isRestartService, result = it) }
                        }
                    }
                }else{
                    drmApi.pingPlayHboByToken(token = token).collect { result ->
                        Logger.d("$HboTag -> PingPlay -> Result: [$result]")
                        if (result is Success) {
                            result.data?.let { hboHandler.check(isRestartService = isRestartService, result = it) }
                        }
                    }
                }
            }
        }

        /**
         * Can't observer and updateToken (call when success) -> error when switch other channel.
         */
        override fun pingEnd(token: String, isObserver: Boolean) {
            if (isObserver){
                coroutineScope?.launch {
                    drmApi.pingEndHbo(token = token).collect {
                        Logger.d("$HboTag -> PingEnd -> Result: [$it, $isObserver]")
                    }
                }
            }else {
                CoroutineScope(EmptyCoroutineContext).launch(Dispatchers.Default) {
                    drmApi.pingEndHbo(token = token).collect {
                        Logger.d("$HboTag -> PingEnd -> Result: [$it, $isObserver]")
                    }
                }
            }
        }

        override fun refreshToken(operatorId: String, sessionId: String) {
            coroutineScope?.launch {
                drmApi.refreshToken(operatorId = operatorId, sessionId = sessionId).collect { result ->
                    Logger.d("$HboTag -> Refresh Token -> Result: [$result]")
                    if (result is Success) {
                        result.data?.let { data ->
                            if (data.code == 200 && data.token.isNotEmpty()){
                                hboHandler.apply {
                                    update(token = data.token)
                                    start()
                                }
                            }else{
                                stopAll()
                                error(reason = context.getString(R.string.hbo_error_refresh_token))
                            }
                        }
                    }
                }
            }
        }
        //endregion
    }
    //endregion

    //region Callbacks
    interface OnEvents{
        fun showError(reason: String) {}
        fun navigateToRequiredVip(
            planId: String,
            fromSource: String,
            idToPlay: String,
            vipBackground: String,
            title: String,
            description: String,
            btnActive: String,
            btnSkip: String,
            trailerUrl: String,
        ){}
        fun navigateToRequireLogin(
            message: String,
            idToPlay: String
        ){}
        fun hideShowIp() {}
        fun sendTrackingPingPlayCcu(actionType: CommonInfor.PingStreamActionType, message: String = "",
                                    showFingerprint: Boolean = false, type: String = "teletext"){}
    }

    interface OnCastSessionEvents {
        fun checkCastSession() : Boolean
    }
    //endregion
}