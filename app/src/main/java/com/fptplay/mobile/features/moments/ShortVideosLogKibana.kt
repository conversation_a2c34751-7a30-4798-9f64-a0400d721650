package com.fptplay.mobile.features.moments

import com.fptplay.mobile.common.utils.CheckBeforePlayUtil
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.features.comment_v2.data.CommentTrackingData
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.moment.MomentDetail

class ShortVideosLogKibana(var trackingProxy: TrackingProxy, var trackingInfo: Infor) {
    enum class ShortVideosReaction { Like, Share, Comment }
    data class DataFirstItemSend(
        var screen:String = TrackingUtil.screenGeneral,
        var isRecommend:String = "",
        var position:String = "",
        var blockPos:String = "",
        var subMenuId:String = "",
        var keyword:String  = ""
    )

    private var infoMobile: InforMobile = InforMobile()
    private var playerName = "ExoPlayer_2.17.1"
    private var url = ""
    private var initPlayerTime: Long = 0L
    private var isInit: Boolean = true
    private var isRepeat: Boolean = false
    private var startSeek: Boolean = false
    private var timeStartSeek: Long = 0L
    private var playingSession: Long = 0L
    private var totalDuration: String = ""
    private var autoScroll:Boolean = false
    private var isStartMovie:Boolean = false
    private var dataFirstItemSend = DataFirstItemSend()
    fun setDataLogSendFirstItem(sourceScreen: String){
        dataFirstItemSend.screen = if(sourceScreen == CheckBeforePlayUtil.SourceScreen.VodDetail.name) "MomentDetail" else TrackingUtil.screen
        dataFirstItemSend.position = TrackingUtil.position
        dataFirstItemSend.blockPos = TrackingUtil.blockIndex
        dataFirstItemSend.subMenuId = TrackingUtil.blockId
        dataFirstItemSend.keyword = if (TrackingUtil.screen == TrackingUtil.screenSearch) TrackingUtil.keyword else ""
    }
    fun setIsRecommendValue(sourceScreen: String){
        dataFirstItemSend.isRecommend = if(sourceScreen == CheckBeforePlayUtil.SourceScreen.VodDetail.name) "0" else "1"
    }

    private fun resetDataFirstItem(){
        dataFirstItemSend.screen = TrackingUtil.screenGeneral
        dataFirstItemSend.position = ""

    }
    fun setDataMomentCur(data: MomentDetail, playlistId: String) {
        infoMobile = InforMobile(
            appId = TrackingUtil.currentAppId,
            appName = TrackingUtil.currentAppName,
            appSource = data.content.appId, //app_id moment
            screen = dataFirstItemSend.screen,
            isRecommend = dataFirstItemSend.isRecommend,
            position = dataFirstItemSend.position,
            isLive = "11", //shorts-video
            itemId = data.content.momentId,
            itemName = data.related?.title ?: "",
            chapterId = data.content.index.toString(),
            EpisodeID = data.content.chapterId,
            isLastEpisode = data.content.isLastest, //is_lasted moment
            isLinkDRM = "0",
            blocKPosition = dataFirstItemSend.blockPos,
            subMenuId = dataFirstItemSend.subMenuId,
            keyword = dataFirstItemSend.keyword,
            idRelated = data.related?.id ?: "",
            playerName = playerName,
            refItemId = TrackingUtil.contentPlayingInfo.refId,
            refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
            refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
            playlistID = playlistId
        )
    }
    fun setAutoScroll(auto:Boolean = true){
        autoScroll = auto
    }

    fun sendLogStart(isInit: Boolean = true, url: String, isRepeat: Boolean = false) { //log 51 - onRepair
        this.isInit = isInit
        this.initPlayerTime = System.currentTimeMillis()
        this.url = url
        this.isRepeat = isRepeat
        this.isStartMovie = true
        this.playingSession = System.currentTimeMillis()
        trackingInfo.updatePlayingSession(playingSession)
        trackingProxy.sendEvent(
            infoMobile.copy(
                infor = trackingInfo,
                logId = "51",
                event = "StartMovie",
                url = url,
                repeatType = getRepeatVal()
            )
        )
    }

    fun sendLogStartFirstFrame(totalDuration: String, currentDuration: String, realTimePlaying: String) { //log 520 - onReady
        this.totalDuration = totalDuration
        trackingInfo.updatePlayingSession(playingSession)
        trackingProxy.sendEvent(
            infoMobile.copy(
                infor = trackingInfo,
                logId = "520",
                event = if (isInit) "Initial" else "Retry",
                url = url,
                duration = totalDuration,
                realTimePlaying = realTimePlaying,
                elapsedTimePlaying = currentDuration,
                initPlayerTime = (System.currentTimeMillis() - initPlayerTime).toString(),
                repeatType = getRepeatVal()
            )
        )
    }

    /*fun sendLogPlayAttempt(isInit:Boolean = true, url:String, isRepeat:Boolean = false){ //log 521 - onRepair
        this.isInit = isInit
        this.initPlayerTime = System.currentTimeMillis()
        this.url = url
        this.isRepeat = isRepeat
        trackingProxy.sendEvent(infoMobile.copy(
            infor = trackingInfo,
            logId = "521",
            event = if(isInit) "FirstLoad" else "PlayAttemp",
            url = url,
            isRepeat = if(this.isRepeat) "1" else "0"
        ))
    }*/
    fun sendLogStop(currentDuration: String, realTimePlaying: String) { //log 52 - onStop
        if(!isStartMovie) return
        trackingInfo.updatePlayingSession(playingSession)
        trackingProxy.sendEvent(
            infoMobile.copy(
                infor = trackingInfo,
                logId = "52",
                event = "StopMovie",
                url = url,
                duration = this.totalDuration,
                realTimePlaying = realTimePlaying,
                elapsedTimePlaying = currentDuration,
                repeatType = getRepeatVal()
            )
        )
        playingSession = 0L
        trackingInfo.updatePlayingSession(playingSession)
        isStartMovie = false
        resetDataFirstItem()
    }

    fun sendLogPause(currentDuration: String, realTimePlaying: String) { //log 53 - pause
        trackingInfo.updatePlayingSession(playingSession)
        trackingProxy.sendEvent(
            infoMobile.copy(
                infor = trackingInfo,
                logId = "53",
                event = "PauseMovie",
                url = url,
                duration = this.totalDuration,
                realTimePlaying = realTimePlaying,
                elapsedTimePlaying = currentDuration,
                repeatType = getRepeatVal()
            )
        )
    }

    fun sendLogResume(currentDuration: String, realTimePlaying: String) { //log 54 - resume
        trackingInfo.updatePlayingSession(playingSession)
        trackingProxy.sendEvent(
            infoMobile.copy(
                infor = trackingInfo,
                logId = "54",
                event = "ResumeMovie",
                url = url,
                duration = this.totalDuration,
                realTimePlaying = realTimePlaying,
                elapsedTimePlaying = currentDuration,
                repeatType = getRepeatVal()
            )
        )
    }

    fun sendLogNext(currentDuration: String, realTimePlaying: String) {//log 55 - next video
        if(!isStartMovie) return //neu co start truoc do thi goi next
        trackingInfo.updatePlayingSession(playingSession)
        trackingProxy.sendEvent(
            infoMobile.copy(
                infor = trackingInfo,
                logId = "55",
                event = "NextMovie",
                url = url,
                duration = this.totalDuration,
                realTimePlaying = realTimePlaying,
                elapsedTimePlaying = currentDuration,
                repeatType = getRepeatVal(),
                status = if (autoScroll) "1" else "0"
            )
        )
        autoScroll = false
    }

    fun startSeekLog() { //set start seek log when start seek in seekbar
        startSeek = true
        timeStartSeek = System.currentTimeMillis()
    }

    fun sendLogSeek(currentDuration: String) { //log 514 - seek - onStart
        if (startSeek) {
            trackingInfo.updatePlayingSession(playingSession)
            trackingProxy.sendEvent(
                infoMobile.copy(
                    infor = trackingInfo,
                    logId = "514",
                    event = "Seek",
                    url = url,
                    duration = this.totalDuration,
                    realTimePlaying = (System.currentTimeMillis() - timeStartSeek).toString(),
                    elapsedTimePlaying = currentDuration,
                    repeatType = getRepeatVal()
                )
            )
        }
        startSeek = false
    }

    fun sendLogError(desError: String, errorCode: String, errorMess: String) {// log 515
        trackingInfo.updatePlayingSession(playingSession)
        trackingProxy.sendEvent(
            infoMobile.copy(
                infor = trackingInfo,
                logId = "515",
                event = "PlaybackError",
                screen = desError,
                url = url,
                errorCode = errorCode,
                errorMessage = errorMess
            )
        )
    }

    fun sendLogReaction(reaction: ShortVideosReaction) { //log 516
        trackingInfo.updatePlayingSession(playingSession)
        trackingProxy.sendEvent(
            infoMobile.copy(
                infor = trackingInfo,
                logId = "516",
                event = reaction.name,
                url = url
            )
        )
    }

    fun sendLogPing(
        currentDuration: String,
        realTimePlaying: String,
        bandwidth: String,
        streamBandwidth: String,
        streamBandwidthAudio: String,
        totalByteLoaded: String,
        resolution: String,
        bitrate: String
    ) {
        if(!isStartMovie) return
        trackingProxy.sendEvent(
            infoMobile.copy(
                infor = trackingInfo,
                logId = "111",
                screen = "PingVOD",
                event = "Ping",
                url = url,
                duration = this.totalDuration,
                realTimePlaying = realTimePlaying,
                elapsedTimePlaying = currentDuration,
                repeatType = getRepeatVal(),
                bandwidth = bandwidth,
                streamBandwidth = streamBandwidth,
                streamBandwidthAudio = streamBandwidthAudio,
                totalByteLoaded = totalByteLoaded,
                pingInterval = "30",  // 30 for Short Video, 60 for normal content. Currently only apply for Short video in sprint 14
                Resolution = resolution,
                Bitrate = bitrate,
                videoQuality = "auto_vip"
            )
        )
    }

    fun updateCommentTrackingData(data: CommentTrackingData): CommentTrackingData {
        return data.apply {
            appSource = infoMobile.appSource
            itemName = infoMobile.itemName
            isLinkDrm = infoMobile.isLinkDRM
            businessPlan = infoMobile.businessPlan
            chapterId = infoMobile.chapterId
            episodeId = infoMobile.EpisodeID
            playerName = <EMAIL>
        }
    }
    private fun getRepeatVal():String{
        return if(this.isRepeat) "NoRepeat"
        else "RepeatOne"
    }
}