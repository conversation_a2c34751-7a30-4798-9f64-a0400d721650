<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/transparent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:orientation="vertical"
        android:layout_marginHorizontal="@dimen/_29sdp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/tv_survey_title"
            tools:text="FPT Play mời bạn tham gia kh<PERSON>o sát"
            android:layout_gravity="center"
            android:gravity="center"
            android:textSize="@dimen/_17ssp"
            android:textColor="@color/white_87"
            android:fontFamily="@font/sf_pro_display_semibold"
            android:lineSpacingMultiplier="1.1"
            android:letterSpacing="0.02"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <androidx.appcompat.widget.AppCompatImageView
            android:layout_gravity="center"
            android:id="@+id/iv_survey_image"
            android:layout_width="@dimen/_171sdp"
            android:layout_height="@dimen/_171sdp"
            android:layout_marginTop="@dimen/_20sdp"
            android:layout_marginVertical="@dimen/_17sdp"
            android:scaleType="fitXY"
            tools:srcCompat="@drawable/thumb_survey" />
        <TextView
            android:id="@+id/tv_survey_description"
            tools:text="Trả lời câu hỏi để giúp FPT Play phục vụ bạn tốt hơn"
            android:layout_gravity="center"
            android:textSize="@dimen/_11ssp"
            android:textColor="@color/white_87"
            android:fontFamily="@font/sf_pro_display_semibold"
            android:gravity="center"
            android:layout_width="@dimen/_171sdp"
            android:layout_height="wrap_content"
            android:lineSpacingMultiplier="1.3"
            android:letterSpacing="0.02"/>
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_start_survey"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_34sdp"
        android:layout_marginBottom="@dimen/_23sdp"
        tools:text="@string/survey_start_survey"
        android:textAllCaps="false"
        android:textSize="@dimen/_13ssp"
        android:textColor="@color/white_87"
        android:fontFamily="@font/sf_pro_display_semibold"
        android:layout_marginHorizontal="@dimen/_11sdp"
        android:background="@drawable/login_button_rectangle_enable"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
