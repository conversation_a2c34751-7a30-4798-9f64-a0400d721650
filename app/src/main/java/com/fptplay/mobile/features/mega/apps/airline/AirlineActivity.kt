package com.fptplay.mobile.features.mega.apps.airline

import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.view.KeyEvent
import androidx.annotation.RequiresApi
import androidx.fragment.app.FragmentContainerView
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.fragment.NavHostFragment
import com.fplay.module.downloader.VideoDownloadManager
import com.fplay.module.downloader.model.TaskInitialStatus
import com.fptplay.dial.connection.FConnectionManager
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.ActivityExtensions.isInPiPMode
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseActivity
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.KeyEventHelper
import com.fptplay.mobile.common.utils.NetworkUtils
import com.fptplay.mobile.common.utils.PlayerPipEventType
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class AirlineActivity : BaseActivity(R.layout.airline_activity) {
//    override val enableLocalLanguage = true

    override val navHostFragment by lazy { this.findViewById<FragmentContainerView>(R.id.nav_host_fragment)?.getFragment() as? NavHostFragment }

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    // Pairing control
    private val pairingConnection by lazy { MainApplication.INSTANCE.pairingConnectionHelper }
    //
    var isFirstDownload = true
    companion object {
        const val AIRLINE_BRAND_KEY = "airline_brand"
    }

    override val enableNetworkListener = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // Enable rotation for tablet
        enableRotateForTablet()

        // Pairing control
        pairingConnection.addConnectionListener(connectionListener)
    }

    //region Tablet Rotation
    private fun enableRotateForTablet() {
        if (isTablet()) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        }
    }
    //endregion Tablet Rotation

    override fun onDestroy() {
        // Pairing control
        pairingConnection.removeConnectionListener(connectionListener)
        //
        MainApplication.INSTANCE.networkDetector.removeObservers(this)
        super.onDestroy()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (KeyEventHelper.getInstance().isHandleKeyManual()) {
            if (KeyEventHelper.getInstance().handleOnKeyDown(keyCode = keyCode, event = event)) {
                true
            } else {
                super.onKeyDown(keyCode, event)
            }
        } else {
            super.onKeyDown(keyCode, event)
        }
    }


    //region Pairing control
    private val connectionListener = object : FConnectionManager.FConnectionListener {
        override fun onConnectError(errorCode: Int, message: String) {
        }
        override fun onConnectSuccess(message: String) {
            runOnUiThread {

            }
        }
        override fun onDisconnectError(message: String) {}
        override fun onDisconnectSuccess(message: String) {
        }
    }

    override fun bindNetworkStateChange(hasNetWork: Boolean) {
        isFirstDownload = MainApplication.INSTANCE.isFirstDownload
        if (hasNetWork) {
            checkNetworkAndDownloadTasks(MainApplication.INSTANCE.appConfig.d2gTime)
        } else {
            pauseDownloadtasks()
        }
    }

    private fun pauseDownloadtasks() {
        if (VideoDownloadManager.instance.isDownloading()) {
            Timber.tag("LogReDownload").d("pause")
            if (isFirstDownload) {
                VideoDownloadManager.instance.pauseAllDownloadTasks()
                MainApplication.INSTANCE.isFirstDownload = false
            } else {
                VideoDownloadManager.instance.pauseDownloadTasksAndAddToGroup(Constants.GROUP_BY_INTERNET)
            }
        }
    }

    private fun checkNetworkAndDownloadTasks(d2gTime : Int) {
        if (VideoDownloadManager.instance.isDownloading()) {
            val networkType = com.fptplay.mobile.common.utils.Utils.checkNetWorkType(this)
            if (NetworkUtils.isNetworkAvailable()) {
                if (sharedPreferences.alwaysDownloadByWifi() && networkType == com.fptplay.mobile.common.utils.Utils.NetworkType.MOBILE) {
                    return
                }
                if (isFirstDownload) {
                    Timber.tag("LogReDownload").d("reDownload")
                    VideoDownloadManager.instance.reDownloadAllDownloadingTask(d2gTime, TaskInitialStatus.REOPENAPP)
                    MainApplication.INSTANCE.isFirstDownload = false
                } else {
                    Timber.tag("LogReDownload").d("resume")
                    VideoDownloadManager.instance.resumeDownloadTasksInGroup(Constants.GROUP_BY_INTERNET)
                }
            }
        }
    }
    //endregion

    //region Picture in Picture
    override fun onUserLeaveHint() {
        sendPiPBroadcast(data = PlayerPipEventType.USER_LEAVE_HINT, fromEvent = true)
        super.onUserLeaveHint()
    }

    override fun onRestart() {
        super.onRestart()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            sendPiPBroadcast(
                data = if (isInPictureInPictureMode) PlayerPipEventType.PIP_OPEN else PlayerPipEventType.PIP_CLOSE,
                fromEvent = false
            )
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onPictureInPictureModeChanged(
        isInPictureInPictureMode: Boolean, newConfig: Configuration
    ) {
        super.onPictureInPictureModeChanged(isInPictureInPictureMode, newConfig)
        sendPiPBroadcast(
            data = if (isInPictureInPictureMode) PlayerPipEventType.PIP_OPEN else PlayerPipEventType.PIP_CLOSE,
            fromEvent = true
        )
    }

    private fun sendPiPBroadcast(data: PlayerPipEventType, fromEvent: Boolean) {
        LocalBroadcastManager.getInstance(this).sendBroadcast(Intent(Constants.PLAYER_PIP_BROADCAST_EVENT).apply {
            putExtra(Constants.PLAYER_PIP_BROADCAST_EVENT_TYPE, data.value)
            putExtra(Constants.PLAYER_PIP_BROADCAST_EVENT_SOURCE, fromEvent)
        })
    }
    //endregion
}
