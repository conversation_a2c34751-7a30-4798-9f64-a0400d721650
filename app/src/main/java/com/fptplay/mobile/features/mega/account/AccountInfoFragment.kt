package com.fptplay.mobile.features.mega.account

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import coil.load
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavAccountInfoDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.extensions.showSnackBar
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.Navigation.navigateToLogin
import com.fptplay.mobile.common.utils.TrackingConstants
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.common.utils.ZendeskUtils
import com.fptplay.mobile.databinding.AccountInfoFragmentBinding
import com.fptplay.mobile.features.ads.utils.AdsUtils
import com.fptplay.mobile.features.login.LoginViewModel
import com.fptplay.mobile.features.mega.MegaViewModel
import com.fptplay.mobile.features.mega.MegaViewModel.MegaIntent.*
import com.fptplay.mobile.features.mega.MegaViewModel.MegaState.*
import com.fptplay.mobile.features.mega.account.model.AccountOtpTargetScreen
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.player.utils.PlayerPiPHelper
import com.fptplay.mobile.player.utils.gone
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenuItem
import com.xhbadxx.projects.module.domain.entity.fplay.otp.UserOtpType
import com.xhbadxx.projects.module.domain.entity.fplay.user.UserInfo
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

@AndroidEntryPoint
class AccountInfoFragment : BaseFragment<MegaViewModel.MegaState, MegaViewModel.MegaIntent>() {

    //region Variables
    override val viewModel by activityViewModels<MegaViewModel>()
    private val loginViewModel by activityViewModels<LoginViewModel>()
    private var _binding: AccountInfoFragmentBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var trackingProxy: TrackingProxy
    @Inject
    lateinit var trackingInfo: Infor
    @Inject
    lateinit var sharedPreferences: SharedPreferences
    lateinit var tokenPreference : android.content.SharedPreferences
    private val localDateFormat by lazy { SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()) }
    private val serveDateFormat by lazy { SimpleDateFormat("MM-dd-yyyy", Locale.getDefault()) }

    private var localUserInfo = UserInfo()
    private var oldProfileType = ""
    private var oldProfileId = ""

    //endregion

    //region Overrides
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = AccountInfoFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()

        MultiProfileUtils.sendEventProfileChangedIfChange(
            fragment = this,
            sharedPreferences = sharedPreferences,
            sourceChange = "onDestroy AccountInfoFragment",
            oldProfileId = oldProfileId,
            oldProfileType = oldProfileType
        )
        Timber.tag("tam-multiProfile").w("newProfileType - newProfileId in ${this.javaClass.simpleName} : ${sharedPreferences.profileType()} - ${sharedPreferences.profileId()}")
        _binding = null
    }

    override fun bindComponent() {}

    override fun bindEvent() {
        binding.toolbar.setNavigationOnClickListener {
            findNavController().navigateUp()
        }

        binding.apply {


            layoutUserPassword.onClickDelay {

                if(sharedPreferences.allowPin()) {
//                if(true) {
                    findNavController().navigateSafe(
                        directions = NavAccountInfoDirections.accountInfoFragmentToAccountOtpDeleteV2Fragment(
                            targetScreen = AccountOtpTargetScreen.CreatePinSetting
                        )
                    )
                } else {
                    viewModel.dispatchIntent(RequestValidateUserPin(type = UserOtpType.LoginChangePass))

                }
            }

            btnDelete.setOnClickListener{
                val megaMenuItem = MegaMenuItem.getUserAccountInfoMetadata()
                trackingProxy.sendEvent(
                    infor = InforMobile(
                        infor = trackingInfo,
                        appId = TrackingUtil.currentAppId,
                        appName = TrackingUtil.currentAppName,
                        logId = TrackingConstants.EVENT_LOG_ID_MEGA_FUNCTION_CLICK,
                        screen = TrackingConstants.SCREEN_NAME_MEGA_FUNCTION_CLICK,
                        event = "DeactivateAccount",
                        itemId = megaMenuItem.id,
                        itemName = megaMenuItem.title
                    )
                )
                viewModel.dispatchIntent(CheckAccountV2)
            }
        }

        setFragmentResultListener(Constants.LOGIN_SUCCESS) { _, bundle ->
            val isSuccess = bundle.getBoolean(Constants.LOGIN_SUCCESS_KEY, false)
            when {
                isSuccess -> bindData()
                else -> findNavController().navigateUp()
            }
            setFragmentResult(Constants.REFRESH_DATA, bundle)
        }

        setFragmentResultListener(REQUEST_UPDATE_PASSWORD) { _, bundle ->
            viewModel.dispatchIntent(GetUserInfoWithLocation)
        }

    }
    override fun bindData() {
        oldProfileType = sharedPreferences.profileType()
        oldProfileId = sharedPreferences.profileId()
        Timber.tag("tam-multiProfile").w("oldProfileType - oldProfileId in ${this.javaClass.simpleName} : $oldProfileType - $oldProfileId")

        if (localUserInfo.id.isNullOrBlank() || localUserInfo.id != sharedPreferences.userId()) viewModel.dispatchIntent(GetUserInfoWithLocation) else bindDataToView()
    }

    override fun observeState() {
        super.observeState()
        loginViewModel.state.observe(viewLifecycleOwner) { it.toUI() }
    }

    override fun MegaViewModel.MegaState.toUI() {
        Timber.d("MegaState: $this")
        Timber.tag("tam-account").i("${<EMAIL>} MegaState: $this")
        when (this) {
            is Loading -> {
                if (intent is GetUserInfo && localUserInfo.id.isNullOrBlank()) {
                    binding.pbLoading.root.show()
                    binding.layoutUserInfo.hide()
                } else {
                    binding.pbLoading.root.show()
                }
            }
//            is ResultUserInfo -> {
//                viewModel.saveUserInfo(data)
//                localUserInfo = data
//                sharedPreferences.saveDisplayName(localUserInfo.name ?: "") //trangttm5 save displayname when change display name success
//                bindDataToView()
//            }
            is ResultGetUserInfoWithLocation -> {
                viewModel.saveUserInfo(userInfo)
                localUserInfo = userInfo
                sharedPreferences.saveDisplayName(
                    localUserInfo.name ?: ""
                ) //trangttm5 save displayname when change display name success
                bindDataToView()
            }
            is ResultUploadUserAvatar -> {
                if (data.isNotBlank()) {
                    val newUserAvatar = data
                        .trim()
                        .replace("\"", "")
                        .replace("\n", "") // Avatar url returned from core libs contains special character
                    val oldUserInfo = localUserInfo.copy()
                    localUserInfo = localUserInfo.copy(avatar = newUserAvatar)
                    updateUserInfo(avatar = localUserInfo.avatar, oldUserInfo = oldUserInfo)
                }
            }
            is ResultUpdateUserInfo -> {
                if (data.isSuccess()) {
                    viewModel.dispatchIntent(GetUserInfoWithLocation)
                    Toast.makeText(context, R.string.update_user_info_success, Toast.LENGTH_SHORT).show()
                } else {
                    if(oldUserInfo != null) {
                        localUserInfo = oldUserInfo
                        bindDataToView()
                    }
                    if (data.message.isNotBlank()) Toast.makeText(context, data.message, Toast.LENGTH_SHORT).show()
                }

                trackingProxy.sendEvent(
                    InforMobile(
                        infor = trackingInfo,
                        logId = "198",
                        appId = TrackingUtil.currentAppId,
                        appName = TrackingUtil.currentAppName,
                        screen = "ModifiedInformation",
                        event = "ChangeInformation",
                        status = if (data.isSuccess()) "Success" else "Failed"
                    )
                )
            }
            is ResultLogout -> {
                if (data.isSuccess()) {
                    AdsUtils.saveUserType(sharedPreferences, false)
                    trackingProxy.sendEvent(
                        InforMobile(
                            infor = trackingInfo,
                            logId = "180",
                            appId = TrackingUtil.currentAppId,
                            appName = TrackingUtil.currentAppName,
                            screen = "Logout",
                            event = "Logout"
                        )
                    )
                    //trangttm5 - reset session and userphone when logout
                    Utils.clearUserData(sharedPreferences)
                    trackingInfo.updateUserSession(0)
                    trackingInfo.updateUserPhone("")
                    trackingInfo.updateUserContract("")
                    trackingInfo.updateUserId("")
                    //trangttm5 - end
                    findNavController().navigateUp()
                    // Zendesk
                    ZendeskUtils.updateZendeskIdentity(userLogin = false, userToken = "", userTokenType = "")

                    // Pairing control
                    MainApplication.INSTANCE.pairingConnectionHelper.let {
                        if (it.isConnected) {
                            it.disconnect()
                        }
                    }
                    //

                    // Picture in Picture
                    PlayerPiPHelper.saveSupportPictureInPicture(sharedPreferences, isSupport = false)

                } else {
                    if (data.message.isNotBlank()) Toast.makeText(context, data.message, Toast.LENGTH_SHORT).show()
                }
            }
            is Error -> {
                if(intent is CheckAccount){
                    showWarningMessageDialog(message = message, textConfirm = getString(R.string.btn_accept))
                }
                if(intent is CheckAccountV2 ||
                    intent is RequestValidateUserPin){
//                    showSnackbar(message)
                    binding.root.showSnackBar(message)
                }
            }
            is ErrorRequiredLogin -> {
                if (findNavController().currentDestination?.id == R.id.account_info_fragment)
                    navigateToLogin()
            }
            is Done -> {
                binding.pbLoading.root.hide()
            }
            is ErrorNoInternet->{
                showWarningDialog(message)
            }
            is ResultCheckAccount ->{
                viewModel.saveUserDeleteData(data)
                when (status) {
                    200 -> {
                        findNavController().navigateSafe(directions = AccountInfoFragmentDirections.accountInfoFragmentToAccountOtpDeleteV2Fragment(targetScreen = AccountOtpTargetScreen.DeleteAccount))

                    }
                    302 -> {
                        findNavController().navigateSafe(directions = AccountInfoFragmentDirections.accountInfoFragmentToAccountOtpDeleteV2Fragment(
                            isPackage = true,
                            targetScreen = AccountOtpTargetScreen.DeleteAccount
                        ))
                    }
                    else -> {
                        showWarningMessageDialog(message = message, textConfirm = getString(R.string.close))
                    }
                }
            }
            is ResultCheckAccountV2 ->{
                viewModel.saveUserDeleteDataV2(data)
                if(status == 1 && errorCode =="0") {
                    findNavController().navigateSafe(directions = AccountInfoFragmentDirections.accountInfoFragmentToAccountOtpDeleteV2Fragment(
                        isPackage = data.userHasPackage,
                        targetScreen = AccountOtpTargetScreen.DeleteAccount,
                        verifyToken = data.verifyToken
                    ))
                } else {
                    if (errorCode == "1") {
                        binding.root.showSnackBar(message)
//                        showSnackbar(message)
                    } else {
                        showWarningMessageDialog(
                            message = message,
                            textConfirm = getString(R.string.close)
                        )
                    }
                }
            }

            is ResultValidateUserPin -> {
                if(data.status == "1" && data.errorCode == "0" ) {
                    findNavController().navigateSafe(
                        directions = NavAccountInfoDirections.accountInfoFragmentToAccountOtpDeleteV2Fragment(
                            targetScreen = AccountOtpTargetScreen.VerifyOtpChangePassword,
                            verifyToken = data.verifyToken
                        )
                    )
                }
                else {
//                    showSnackbar(data.msg)
                    binding.root.showSnackBar(data.msg)
                }
            }
            else -> {}
        }
    }
    //endregion

    //region Commons
    private fun LoginViewModel.LoginState.toUI() {
        when (this) {
            is LoginViewModel.LoginState.Loading -> binding.pbLoading.root.show()
            is LoginViewModel.LoginState.ResultResendOTP -> {

            }
            is LoginViewModel.LoginState.Error -> {}
            is LoginViewModel.LoginState.Done -> binding.pbLoading.root.hide()
            else -> {}
        }
    }
    private fun bindDataToView() {
        binding.apply {
            layoutUserInfo.show()

            ivAvatar.load(localUserInfo.avatar)

            if (!localUserInfo.subStatus.isNullOrBlank() && !localUserInfo.subContract.isNullOrBlank()) {
                tvContractValue.text = localUserInfo.subContract
                tvContract.show()
                tvContractValue.show()
//                viewDividerContract.show()
            } else {
                tvContract.hide()
                tvContractValue.hide()
//                viewDividerContract.hide()
            }

            binding.layoutUserEmail.apply {
                if (localUserInfo.saleModeEmail.isBlank()) {
                    this.gone()
                } else {
                    this.show()
                    tvEmailEdit.text = localUserInfo.saleModeEmail
                    onClickDelay {
                        findNavController().navigate(
                            AccountInfoFragmentDirections.accountInfoFragmentToUpdateEmailFragment(userEmail = localUserInfo.saleModeEmail)
                        )
                    }
                }
            }

            Timber.tag("tam-multiProfile").w("bindDataToView : $localUserInfo")

            tvUserName.setInfo(localUserInfo.name)
            tvUidValue.setInfo(localUserInfo.id)
            tvPhoneNumberValue.setInfo(localUserInfo.phone)
            tvContractValue.setInfo(localUserInfo.subContract)
            tvPasswordEdit.setText(if(sharedPreferences.allowPin()) {
                R.string.account_pin_setting_not_set_label
            } else {
                R.string.account_password_edit_label
            })
        }
    }
    private fun updateUserInfo(
        avatar: String? = null,
        fullName: String? = null,
        email: String? = null,
        sexCode: Int? = null,
        birthDay: String? = null,
        location: String? = null,
        oldUserInfo: UserInfo
    ) {
        viewModel.dispatchIntent(
            UpdateUserInfo(
                avatar = avatar,
                fullName = fullName,
                email = email,
                sexCode = sexCode,
                birthDay = birthDay,
                location = location,
                oldUserInfo = oldUserInfo
            )
        )
    }


    private fun TextView.setInfo(info: String?) {
        if (!info.isNullOrBlank()) {
            text = info
            setTextColor(Color.parseColor("#DEFFFFFF"))
        } else {
            text = getString(R.string.not_updated_yet)
            setTextColor(Color.parseColor("#61FFFFFF"))
        }
    }



    companion object {
        const val REQUEST_UPDATE_PASSWORD = "UPDATE_PASSWORD"
    }
    //endregion
}
