package com.fptplay.mobile.welcome

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.ComponentName
import android.content.Intent
import android.content.pm.ComponentInfo
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import bolts.AppLinks
import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustDeeplink
import com.fptplay.mobile.*
import com.fptplay.mobile.common.extensions.isHostAdjustTrueLink
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.utils.DateTimeUtils
import com.fptplay.mobile.common.utils.DeeplinkConstants
import com.fptplay.mobile.common.utils.DeeplinkUtils.addAdjustTrueLinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addClevertapDeeplinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFacebookDeeplinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFirebaseDeeplinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFirebaseNotificationData
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.databinding.WelcomeFragmentBinding
import com.fptplay.mobile.features.ads.utils.AdsUtils
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.features.tracking_ga4.TrackingGA4Proxy
import com.fptplay.mobile.player.utils.PlayerPiPHelper
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.visible
import com.google.firebase.dynamiclinks.FirebaseDynamicLinks
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import com.tear.modules.player.util.PlayerUtils
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.common.Config
import com.xhbadxx.projects.module.util.common.RootUtil
import com.xhbadxx.projects.module.util.common.Util
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import timber.log.Timber
import java.util.*
import javax.inject.Inject
import kotlin.collections.ArrayList


@AndroidEntryPoint
class WelcomeFragment : BaseFragment<WelcomeViewModel.WelcomeState, WelcomeViewModel.WelcomeIntent>() {
//    private val safeArgs: WelcomeFragmentArgs by navArgs()

    override val viewModel: WelcomeViewModel by activityViewModels()

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    private var _binding: WelcomeFragmentBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        sharedPreferences.saveIsShowAdsHomeScreenKey(true) // reset flag for show ads welcome screen
        sharedPreferences.saveIsFirstTimeShowPopup3GKey(true) // reset flag for show popup 3G
        processDeeplink()
        if (!sharedPreferences.isFirstOpenApp()) {
            TrackingGA4Proxy.sendTrackingFirstOpenEvent()
            sharedPreferences.saveFristOpenApp(true)
        }
        updatePlayerWidthHeight()
        bindAppInfo()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = WelcomeFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        //check force update
        viewModel.dispatchIntent(WelcomeViewModel.WelcomeIntent.GetForceUpdateApp)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun bindData() {
        bindDrmInfo()
        bindFullHD()
        sharedPreferences.saveAndroidId(Util.deviceId(requireContext()))
        sharedPreferences.saveVersionCode(BuildConfig.VERSION_CODE.toString())
        sharedPreferences.saveVersionName(BuildConfig.VERSION_NAME)
        viewModel.dispatchIntent(WelcomeViewModel.WelcomeIntent.GetSettingGeneral)  // save info in repository
        viewModel.dispatchIntent(WelcomeViewModel.WelcomeIntent.RegisterFcmToken)

        binding.btToDownloadMega.isActivated = true
//        Timber.d("Screen: ${safeArgs.screen} || Menu: ${safeArgs.menu} || SubMenu: ${safeArgs.subMenu} || ID: ${safeArgs.id} || Arg: ${safeArgs.arg} || OriginalLink: ${getOriginalDeeplink()}")
    }

    private fun bindDrmInfo() {
        CoroutineScope(Dispatchers.IO).launch {
            var hasDrm = false
            var widevineLevel = ""
            var isRooted = false
            var buildNumber = ""
            try {
                hasDrm = Util.hasDrm()
                widevineLevel = Util.wvLevel()
                buildNumber = Util.getDeviveBuildNumber()
                context?.let {
                    isRooted = RootUtil.isDeviceRooted(it)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            Timber.d("*****Save hasDRM: $hasDrm")
            sharedPreferences.apply {
                saveSupportDRM(supportDRM = if (hasDrm) "1" else "0")
                saveBuildNumber(buildNumber = buildNumber)
                saveIsDeviceRooted(isRooted = if(isRooted) "1" else "0")
                saveWideVineLevel(wvLevel = widevineLevel)
            }
            trackingProxy.sendEvent(
                InforMobile(
                    infor = trackingInfo, logId = "27",
                    appId = TrackingUtil.currentAppId,
                    appName = TrackingUtil.currentAppName,
                    screen = "CheckDeviceDRM", event = "CheckDeviceDRM",
                    boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                    itemId = if (hasDrm) "1" else "0",
                    isRoot = if(isRooted) "1" else "0",
                    widevineLevel = widevineLevel,
                    buildNumber = buildNumber
                )
            )
            sendLogDeviceCodec(hasDrm, isRooted, widevineLevel, buildNumber)
        }
    }

    private fun bindFullHD() {
        sharedPreferences.saveSupportFullHD(supportFullHD = if (Util.supportFullHD(MainApplication.INSTANCE.applicationContext)) "1" else "0")
    }

    private fun bindAppInfo() {
        viewModel.dispatchIntent(WelcomeViewModel.WelcomeIntent.InitAppInfo)
    }

    private fun sendLogDeviceCodec(hasDrm: Boolean, isRooted: Boolean, widevineLevel: String, buildNumber: String) {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "31",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "CodecDeviceInformation",
                event = "CodecDeviceInformation",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                itemId = if (hasDrm) "1" else "0",
                isRoot = if(isRooted) "1" else "0",
                widevineLevel = widevineLevel,
                buildNumber = buildNumber,
                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),
            )
        )
    }


    override fun bindEvent() {
        binding.apply {
            btToDownloadMega.setOnClickListener {
                findNavController().navigate(NavWelcomeDirections.actionGlobalToDownloadV2())
            }
            btRetry.setOnClickListener {
                viewModel.dispatchIntent(WelcomeViewModel.WelcomeIntent.GetForceUpdateApp)
                binding.clNoInternet.visibility = View.GONE
            }
        }
    }

    override fun WelcomeViewModel.WelcomeState.toUI() {
        Timber.d("toUI $this")
        when (this) {
            is WelcomeViewModel.WelcomeState.Loading -> {
            }
            is WelcomeViewModel.WelcomeState.Error -> {
                when (intent) {
                    is WelcomeViewModel.WelcomeIntent.GetLandingPage -> {
                        startHome()
                    }
                    is WelcomeViewModel.WelcomeIntent.GetForceUpdateApp -> {
                        passForceUpdate()
                    }

                    is WelcomeViewModel.WelcomeIntent.GetConfig -> {
                        saveConfig()
                        passServiceRegion()
                    }
                    else -> {
                        Logger.d("Error else $this")
                    }
                }
            }

            is WelcomeViewModel.WelcomeState.ResultConfig -> {
                saveConfig(this.data)
                activity?.let {
                    val newIconApp = this.data.iconApp
                    sharedPreferences.saveAppIconName(newIconApp)
                    //for test
//                    val oldIconApp = sharedPreferences.getAppIconName()
//                    var newIconApp = this.data.iconApp
//                    if(oldIconApp == "app_default"){
//                        newIconApp = "lunar_new_year_2025"
//                    }else if(oldIconApp == "lunar_new_year_2025"){
//                        newIconApp = "app_default"
//                    }
//                    sharedPreferences.saveAppIconName(newIconApp)
                    //endregion test
                    if(!checkIsCurrentIcon(newIconApp)){
                        showWarningMessageDialog(
                            message = resources.getString(R.string.change_icon_app_message),
                            textConfirm = resources.getString(R.string.alert_confirm),
                            onConfirm = {
                                changeIconApp(newIconApp, it)
                            })
                        return
                    }
                }
                if (this.data.isVN == "1") {
                    passServiceRegion()
                } else {
                    findNavController().navigate(WelcomeFragmentDirections.actionWelcomeFragmentToOutOfServiceRegionFragment())
                }
            }
            is WelcomeViewModel.WelcomeState.NoInternet -> {
                showNoInternetView()
            }
            is WelcomeViewModel.WelcomeState.Done -> {
            }
            is WelcomeViewModel.WelcomeState.ResultForceUpdateApp -> {
                val message =
                    getString(R.string.welcome_message_force_update_app)
                val textConfirm = getString(R.string.welcome_update)
                try {
                    if (BuildConfig.VERSION_CODE < data.requireVersion.toInt()) {
                        AlertDialog().apply {
                            setMessage(message)
                            setTextConfirm(textConfirm)
                            setHandleBackPress(true)
                            setListener(object : AlertDialogListener {
                                override fun onExit() {
                                    requireActivity().finish()
                                }

                                override fun onConfirm() {
                                    launchAppLinkToMarket(data.link)
                                }

                                override fun onBackPress() {
                                    requireActivity().finish()
                                }
                            })
                        }.show(childFragmentManager, "DialogUpdateApp")
                    } else {
                        passForceUpdate()
                    }
                }catch (e:Exception){
                    Logger.d("check require version exeption = $e")
                    passForceUpdate()
                }

            }
            is WelcomeViewModel.WelcomeState.ResultLandingPage -> {
                // Skip landing page if open from deeplink
//                startHome()
                Timber.tag("tam-landingpage").d("toUI ResultLandingPage it.data ${data}")
                if (data.isNotEmpty()) {
                    viewModel.saveListLandingPage(data)
                    findNavController().navigate(
                        WelcomeFragmentDirections.actionWelcomeFragmentToLandingPageFragment(
                            originalLink = getNavigationDeeplink()
                        ))
                } else {
                    startHome()
                }
            }
            is WelcomeViewModel.WelcomeState.ResultAppVersion -> {
                Timber.d("Version app: ${this.data.version}")
                startHome()

            }
            is WelcomeViewModel.WelcomeState.ResultGetClusterAppInfo -> {
                handleResultAppInfo(results = data)
            }
            is WelcomeViewModel.WelcomeState.ResultSettingGeneral -> {
                //save data for login ==> use new config from api config
//                sharedPreferences.saveGoogleLogin(data.gmailRegister == 1)
//                sharedPreferences.saveFacebookLogin(data.facebookRegister == 1)
            }
            else -> {
                Timber.d("Else with $this")
            }
        }
    }

    private fun checkIsCurrentIcon(newIconApp: String):Boolean{
        activity?.apply {
            when (newIconApp) {
                "lunar_new_year_2025" -> {
                    val componentLunarNewYear2025Alias = ComponentName(applicationContext, EventLunarNewYear2025Alias::class.java)
                    return checkComponentEnable(componentLunarNewYear2025Alias)
                }
                "app_default" -> {
                    val componentDefault = ComponentName(applicationContext, DefaultLauncherAlias::class.java)
                    return checkComponentEnable(componentDefault)
                }
                else -> {
                    val componentDefault = ComponentName(applicationContext, DefaultLauncherAlias::class.java)
                    return checkComponentEnable(componentDefault)
                }
            }
        }
        return false
    }
    private fun checkComponentEnable(componentName: ComponentName):Boolean{
        activity?.packageManager?.apply {
            val compState = getComponentEnabledSetting(componentName)
            when (compState) {
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED -> return false
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED -> return true
                PackageManager.COMPONENT_ENABLED_STATE_DEFAULT ->       // We need to get the application info to get the component's default state
                    try {
                        val packageInfo: PackageInfo = getPackageInfo(
                            componentName.packageName, PackageManager.GET_ACTIVITIES
                                    or PackageManager.GET_RECEIVERS
                                    or PackageManager.GET_SERVICES
                                    or PackageManager.GET_PROVIDERS
                                    or PackageManager.GET_DISABLED_COMPONENTS
                        )

                        val components: ArrayList<ComponentInfo> = ArrayList()
                        if (packageInfo.activities != null) Collections.addAll(components, *packageInfo.activities)
                        if (packageInfo.services != null) Collections.addAll(components, *packageInfo.services)
                        if (packageInfo.providers != null) Collections.addAll(components, *packageInfo.providers)

                        for (componentInfo in components) {
                            if (componentInfo.name == componentName.className) {
                                return componentInfo.isEnabled
                            }
                        }

                        // the component is not declared in the AndroidManifest
                        return false
                    } catch (e: PackageManager.NameNotFoundException) {
                        // the package isn't installed on the device
                        return false
                    }

                else ->
                    try {
                        val packageInfo: PackageInfo = getPackageInfo(
                            componentName.packageName, PackageManager.GET_ACTIVITIES
                                    or PackageManager.GET_RECEIVERS
                                    or PackageManager.GET_SERVICES
                                    or PackageManager.GET_PROVIDERS
                                    or PackageManager.GET_DISABLED_COMPONENTS
                        )

                        val components: ArrayList<ComponentInfo> = ArrayList()
                        if (packageInfo.activities != null) Collections.addAll(components, *packageInfo.activities)
                        if (packageInfo.services != null) Collections.addAll(components, *packageInfo.services)
                        if (packageInfo.providers != null) Collections.addAll(components, *packageInfo.providers)

                        for (componentInfo in components) {
                            if (componentInfo.name == componentName.className) {
                                return componentInfo.isEnabled
                            }
                        }

                        return false
                    } catch (e: PackageManager.NameNotFoundException) {
                        return false
                    }
            }
        }
        return false
    }

    private fun changeIconApp(newIconApp:String, activity: Activity){
        Logger.d("trangtest iconApp == $newIconApp")
        val componentDefault = ComponentName(activity.applicationContext, DefaultLauncherAlias::class.java)
        val componentLunarNewYear2025Alias = ComponentName(activity.applicationContext, EventLunarNewYear2025Alias::class.java)

        activity.packageManager.let { pk ->
            when (newIconApp) {
                "lunar_new_year_2025" -> {
                    pk.setComponentEnabledSetting(
                        componentDefault,
                        PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                        PackageManager.DONT_KILL_APP
                    )
                    pk.setComponentEnabledSetting(
                        componentLunarNewYear2025Alias,
                        PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                        PackageManager.DONT_KILL_APP
                    )
                }


                "app_default" -> {
                    pk.setComponentEnabledSetting(
                        componentDefault,
                        PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                        PackageManager.DONT_KILL_APP
                    )
                    pk.setComponentEnabledSetting(
                        componentLunarNewYear2025Alias,
                        PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                        PackageManager.DONT_KILL_APP
                    )
                }

                else -> {
                    pk.setComponentEnabledSetting(
                        componentDefault,
                        PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                        PackageManager.DONT_KILL_APP
                    )
                    pk.setComponentEnabledSetting(
                        componentLunarNewYear2025Alias,
                        PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                        PackageManager.DONT_KILL_APP
                    )
                }
            }
        }
        activity.finish()
    }

    // region Commons
    private fun handleResultAppInfo(results: List<WelcomeViewModel.WelcomeState>) {
        results.forEach {
            when (it) {
                is WelcomeViewModel.WelcomeState.Error -> {
                    when (it.intent) {
                        WelcomeViewModel.WelcomeIntent.GetLandingPage -> {
                            startHome()
                        }
                        WelcomeViewModel.WelcomeIntent.GetConfig -> {
                            saveConfig()
                        }
                        else -> {
                            Logger.d("Error else $this")
                        }
                    }
                }
                is WelcomeViewModel.WelcomeState.NoInternet -> {
                    showNoInternetView()
                }
                is WelcomeViewModel.WelcomeState.ResultConfig -> {
                    saveConfig(apiResult = it.data)
                }
                is WelcomeViewModel.WelcomeState.ResultLandingPage -> {
                    // Skip landing page if open from deeplink
//                    startHome()
                    Timber.tag("tam-landingpage").d("handleResultAppInfo ResultLandingPage it.data ${it.data}")
                    if (it.data.isNotEmpty()) {
                        viewModel.saveListLandingPage(it.data)
                        findNavController().navigate(
                            WelcomeFragmentDirections.actionWelcomeFragmentToLandingPageFragment(
                                originalLink = getNavigationDeeplink()
                            ))
                    } else {
                        startHome()
                    }
                }
                else -> {
                }
            }
        }
    }

    fun launchAppLinkToMarket(linkToAppStore: String) {
        if (linkToAppStore.isNotBlank()) {
            val uri = Uri.parse(linkToAppStore)
            val myAppLinkToMarket = Intent(Intent.ACTION_VIEW, uri)
            try {
                startActivity(myAppLinkToMarket)
            } catch (e: ActivityNotFoundException) {
                try {
                    startActivity(
                        Intent(
                            Intent.ACTION_VIEW,
                            Uri.parse(
                                java.lang.String.format(
                                    Locale.getDefault(),
                                    "%s%s",
                                    resources.getString(R.string.menu_more_fragment_prefix_url_ch_play),
                                    requireContext().packageName
                                )
                            )
                        )
                    )
                } catch (exception: Exception) {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.text_all_app_store_unavailable),
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        } else {
            passForceUpdate()
        }
    }

    private fun passForceUpdate() {
        viewModel.dispatchIntent(WelcomeViewModel.WelcomeIntent.GetConfig)
        viewModel.dispatchIntent(WelcomeViewModel.WelcomeIntent.GetGeneralInfoMessage)
    }

    private fun passServiceRegion() {
//        viewModel.dispatchIntent(WelcomeViewModel.WelcomeIntent.GetLandingPage)
        val arrIntents = arrayListOf<Pair<Int, WelcomeViewModel.WelcomeIntent>>(
            Pair(1, WelcomeViewModel.WelcomeIntent.GetLandingPage)
        )
        if (sharedPreferences.userLogin()) {
            arrIntents.add(
                Pair(2, WelcomeViewModel.WelcomeIntent.GetPackageUser(sharedPreferences.userId()))
            )
            arrIntents.add(
                Pair(3, WelcomeViewModel.WelcomeIntent.GetUserInfo)
            )
        } else {
            AdsUtils.saveUserType(sharedPreferences, false)
            // Picture in Picture
            PlayerPiPHelper.saveSupportPictureInPicture(sharedPreferences, isSupport = false)
        }
        viewModel.dispatchIntent(WelcomeViewModel.WelcomeIntent.GetClusterAppInfo(arrIntents))

    }

    private fun startHome() {
        Timber.d("startHome")
        startActivity(
            Intent(
                requireActivity(),
                HomeActivity::class.java
            ).apply {
                Logger.d("startHome >> addDeeplinkFor ${activity?.intent}")
                if (sharedPreferences.isProfileChangeWhenOpenApp()) {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                }
                addFirebaseNotificationData(activity?.intent)
                addClevertapDeeplinkData(activity?.intent)
                addFirebaseDeeplinkData(activity?.intent)
                addFacebookDeeplinkData(activity?.intent)
                addAdjustTrueLinkData(activity?.intent)
                putExtra("originalLink", getNavigationDeeplink())
            }
        )
        requireActivity().finish()

//        val intent = Intent(requireContext(), AirlineActivity::class.java)
//        intent.putExtra(AirlineActivity.AIRLINE_BRAND_KEY, AirlineBrand.BAMBOO_AIRWAYS)
//        startActivity(intent)
//        requireActivity().finish()

    }

    private fun saveConfig(apiResult: Config? = null) {
        val result: Config = apiResult ?: getConfigFromSharedPreferences()
        MainApplication.INSTANCE.appConfig = result
    }

    private fun getConfigFromSharedPreferences(): Config {
        return if (sharedPreferences.getAppConfig().isNotBlank()) {
            val moshi = Moshi.Builder().add(KotlinJsonAdapterFactory()).build()
            val jsonAdapter: JsonAdapter<Config> = moshi.adapter(Config::class.java)
            jsonAdapter.fromJson(sharedPreferences.getAppConfig()) ?: Config()
        } else {
            Config()
        }
    }

    private fun showNoInternetView() {
        binding.apply {
//            if(sharedPreferences.isProfileEnableD2g()) {
            if(MultiProfileUtils.profileEnabledDownload(sharedPreferences)) {
                btRetry.setBackgroundResource(R.drawable.no_internet_button_disabled_bg)
                btRetry.setTextColor(ContextCompat.getColor(requireContext(), R.color.white_87))
                tvDes.visible()
                btToDownloadMega.visible()
            } else {
                tvDes.gone()
                btToDownloadMega.gone()
                btRetry.setBackgroundResource(R.drawable.no_internet_button_enabled_bg)
                btRetry.setTextColor(ContextCompat.getColor(requireContext(), R.color.color_white))

            }
            clNoInternet.visible()
        }

    }
    // region deeplink
    private fun getNavigationDeeplink(): String? {
        Timber.d("getNavigationDeeplink ${arguments}")

        return try {
            val intent = arguments?.get(NavController.KEY_DEEP_LINK_INTENT) as? Intent ?: return null
            Timber.i("getNavigationDeeplink : ${intent.data?.buildUpon()?.toString()}")
            intent.data?.buildUpon()?.toString()
        } catch (e: Exception) {
            Timber.e(e, "KEY_DEEP_LINK_INTENT")
            null
        }
    }

    private fun processDeeplink() {
        activity?.let {
            initFirebaseDynamicLink(welcomeIntent = it.intent)
            initFacebookAppLinkViaFacebookSDK(welcomeIntent = it.intent)
            initFacebookAppLinkViaActionView(welcomeIntent = it.intent)
            initAdjustTrueLink(welcomeIntent = it.intent)
        }
    }

    //Facebook AppLink
    private fun initFacebookAppLinkViaActionView(welcomeIntent: Intent?) {
        if (welcomeIntent != null
            && Intent.ACTION_VIEW == welcomeIntent.action
            && DeeplinkConstants.SCHEME_FPT_PLAY == welcomeIntent.scheme
            && welcomeIntent.dataString != null
        ) {
            welcomeIntent.putExtra(DeeplinkConstants.APP_LINK_FACE_BOOK_NEW_KEY, true)
            welcomeIntent.putExtra(DeeplinkConstants.APP_LINK_FACE_BOOK_DATA_KEY, welcomeIntent.dataString)
            welcomeIntent.putExtra(DeeplinkConstants.APP_LINK_FACE_BOOK_VIA_SDK_KEY, false)
        }
    }

    private fun initFacebookAppLinkViaFacebookSDK(welcomeIntent: Intent?) {
        if (welcomeIntent == null) return
        try {
            val currentIntent = requireActivity().intent
            val targetUrl = AppLinks.getTargetUrlFromInboundIntent(requireActivity(), currentIntent)
            Timber.w("initFacebookAppLink : $targetUrl")
            if (targetUrl != null) {
                // add the url as data of the intent then process later if not have firebase deep link
//                welcomeIntent.data = targetUrl
                welcomeIntent.putExtra(DeeplinkConstants.APP_LINK_FACE_BOOK_NEW_KEY, true)
                welcomeIntent.putExtra(DeeplinkConstants.APP_LINK_FACE_BOOK_DATA_KEY, targetUrl.toString())
                welcomeIntent.putExtra(DeeplinkConstants.APP_LINK_FACE_BOOK_VIA_SDK_KEY, true)

            }
        } catch (exception: java.lang.Exception) {
            Timber.e(exception)
        }
    }

    private fun initFirebaseDynamicLink(welcomeIntent: Intent?) {
        Timber.i("initFirebaseDynamicLink")
        if (welcomeIntent == null) return
        FirebaseDynamicLinks.getInstance().getDynamicLink(welcomeIntent)
            .addOnSuccessListener(requireActivity()) { pendingDynamicLinkData ->
                if (pendingDynamicLinkData?.link != null) {
                    welcomeIntent.putExtra(DeeplinkConstants.FIREBASE_DYNAMIC_LINK_NEW_KEY, true)
                    welcomeIntent.putExtra(
                        DeeplinkConstants.FIREBASE_DYNAMIC_LINK_URL_KEY,
                        pendingDynamicLinkData.link.toString()
                    )
                }
            }
    }

    private fun initAdjustTrueLink(welcomeIntent: Intent?) {
        Timber.i("initAdjustTrueLink with intent: ${welcomeIntent?.data?.toString()}")
        if (welcomeIntent == null) return

        try {
            val intentLink = welcomeIntent.data?.toString()
            val uri = Uri.parse(intentLink)
            val host = uri.host
            if (host.isHostAdjustTrueLink()) {
                val adjustDeeplink = AdjustDeeplink(welcomeIntent.data)
                CoroutineScope(Dispatchers.IO).launch {
                    withTimeoutOrNull(5_000L) {
                        Adjust.processAndResolveDeeplink(
                            adjustDeeplink,
                            MainApplication.INSTANCE.applicationContext
                        ) { resolvedLink ->
                            Timber.i("initAdjustTrueLink : $resolvedLink")
                            if (resolvedLink != null) {
                                val broadcastintent =
                                    Intent(DeeplinkConstants.ADJUST_TRUE_LINK_BROADCAST_INTENT)
                                broadcastintent.putExtra(
                                    DeeplinkConstants.ADJUST_TRUE_LINK_NEW_KEY,
                                    true
                                )
                                broadcastintent.putExtra(
                                    DeeplinkConstants.ADJUST_TRUE_LINK_URL_KEY,
                                    resolvedLink.toString()
                                )
                                LocalBroadcastManager.getInstance(MainApplication.INSTANCE.applicationContext)
                                    .sendBroadcast(broadcastintent)

                            }
                        }
                    } ?: run {
                        Logger.d("initAdjustTrueLink >> process timeout")
                    }
                }


            }
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    // endregion deeplink

    // region device width height

    private fun updatePlayerWidthHeight() {
        context?.let {
            val displayModeSize = PlayerUtils.getCurrentDisplayModeSize(it)
            sharedPreferences.saveDisplayWidth(displayModeSize.x)
            sharedPreferences.saveDisplayHeight(displayModeSize.y)


        }
    }
    // endregion

    // endregion Commons
}