package com.fptplay.mobile.features.survey

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.R
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.databinding.SurveyStartEndFragmentBinding
import com.fptplay.mobile.features.moments.MomentsViewModel
import com.fptplay.mobile.features.short_video.DataCacheObject
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger

@dagger.hilt.android.AndroidEntryPoint
class SurveyStartFragment : BaseFragment<SurveyViewModel.SurveyState, SurveyViewModel.SurveyIntent>() {
    override val viewModel: SurveyViewModel by activityViewModels()
    private var _binding: SurveyStartEndFragmentBinding? = null
    private val binding get() = _binding!!
    override val hasEdgeToEdge: Boolean = true
    override val handleBackPressed: Boolean = true

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = SurveyStartEndFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun initData() {
        binding.apply {
            tvSurveyTitle.text = viewModel.dataStart.title
            tvSurveyDescription.text = viewModel.dataStart.description
            ImageProxy.load(
                context = binding.root.context,
                url = viewModel.dataStart.icon,
                width = binding.ivSurveyImage.width,
                height = binding.ivSurveyImage.height,
                target = binding.ivSurveyImage
            )
            btnStartSurvey.text = resources.getString(R.string.survey_start_survey)
        }
    }

    override fun bindEvent() {
        binding.btnStartSurvey.setOnClickListener {
            viewModel.dispatchIntent(SurveyViewModel.SurveyIntent.GetSurveyQuestion(
                type = viewModel.dataStart.typeEvent,
                surveyId = viewModel.dataStart.surveyId,
                eventId = viewModel.dataStart.eventId
            ))
        }
    }

    override fun SurveyViewModel.SurveyState.toUI() {
        when (this) {
            is SurveyViewModel.SurveyState.Error -> {
                (parentFragment?.parentFragment as? SurveyFragment)?.startHome()
            }

            is SurveyViewModel.SurveyState.ErrorNoInternet -> {
                showWarningDialog(
                    getString(R.string.error_no_internet),
                    textConfirm = getString(R.string.all_retry),
                    onConfirm = {
                        viewModel.dispatchIntent(SurveyViewModel.SurveyIntent.GetSurveyQuestion(
                            type = viewModel.dataStart.typeEvent,
                            surveyId = viewModel.dataStart.surveyId,
                            eventId = viewModel.dataStart.eventId
                        ))
                    })
            }

            is SurveyViewModel.SurveyState.ErrorRequiredLogin -> {
                (parentFragment?.parentFragment as? SurveyFragment)?.startHome()
            }
            is SurveyViewModel.SurveyState.GetSurveyQuestionResult -> {
                if (this.data.status == "1" && this.data.questions.isNotEmpty()) {
                    findNavController().navigate(directions = SurveyStartFragmentDirections.actionSurveyStartFragmentToSurveyQuestionFragment())
                } else {
                    (parentFragment?.parentFragment as? SurveyFragment)?.startHome()
                }
            }
            else -> {}
        }
    }
}
