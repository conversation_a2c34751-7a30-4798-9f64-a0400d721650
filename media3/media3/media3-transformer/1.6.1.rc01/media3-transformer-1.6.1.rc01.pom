<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>androidx.media3</groupId>
  <artifactId>media3-transformer</artifactId>
  <version>1.6.1.rc01</version>
  <packaging>aar</packaging>
  <name>Media3 Transformer module</name>
  <description>Media3 Transformer module</description>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>The Android Open Source Project</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/androidx/media.git</connection>
    <url>https://github.com/androidx/media</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>androidx.media3</groupId>
      <artifactId>media3-exoplayer</artifactId>
      <version>1.6.1.rc01</version>
      <scope>compile</scope>
      <type>aar</type>
    </dependency>
    <dependency>
      <groupId>androidx.media3</groupId>
      <artifactId>media3-effect</artifactId>
      <version>1.6.1.rc01</version>
      <scope>compile</scope>
      <type>aar</type>
    </dependency>
    <dependency>
      <groupId>androidx.media3</groupId>
      <artifactId>media3-muxer</artifactId>
      <version>1.6.1.rc01</version>
      <scope>compile</scope>
      <type>aar</type>
    </dependency>
    <dependency>
      <groupId>androidx.annotation</groupId>
      <artifactId>annotation</artifactId>
      <version>1.6.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.concurrent</groupId>
      <artifactId>concurrent-futures</artifactId>
      <version>1.2.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.media3</groupId>
      <artifactId>media3-datasource</artifactId>
      <version>1.6.1.rc01</version>
      <scope>runtime</scope>
      <type>aar</type>
    </dependency>
    <dependency>
      <groupId>androidx.media3</groupId>
      <artifactId>media3-container</artifactId>
      <version>1.6.1.rc01</version>
      <scope>runtime</scope>
      <type>aar</type>
    </dependency>
  </dependencies>
</project>
