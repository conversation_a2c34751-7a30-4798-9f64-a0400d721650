<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_home_main"
    app:startDestination="@id/home_main_fragment">

    <include app:graph="@navigation/nav_vod" />
    <include app:graph="@navigation/nav_tv" />
    <include app:graph="@navigation/nav_tv_detail" />
    <include app:graph="@navigation/nav_choihaychia" />
    <include app:graph="@navigation/nav_notification" />
    <include app:graph="@navigation/nav_category" />
    <include app:graph="@navigation/nav_payment"/>
    <include app:graph="@navigation/nav_payment_detail" />
    <include app:graph="@navigation/nav_payment_in_app_purchase" />
    <include app:graph="@navigation/nav_account_info" />
    <include app:graph="@navigation/nav_about" />
    <include app:graph="@navigation/nav_play_star_30s" />
    <include app:graph="@navigation/nav_dowload_v2" />
    <include app:graph="@navigation/nav_premiere" />
    <include app:graph="@navigation/nav_hip_fest" />
    <include app:graph="@navigation/nav_pairing_control_host" />
    <include app:graph="@navigation/nav_loyalty" />
    <include app:graph="@navigation/nav_loyalty_delivery"/>
    <include app:graph="@navigation/nav_ekyc" />
    <include app:graph="@navigation/nav_mini_app" />
    <include app:graph="@navigation/nav_mega_app" />
    <include app:graph="@navigation/nav_moment" />
    <include app:graph="@navigation/nav_multi_profile" />
    <include app:graph="@navigation/nav_game" />
    <include app:graph="@navigation/nav_pladio_outer"/>

    <fragment
        android:id="@+id/home_main_fragment"
        android:name="com.fptplay.mobile.features.home.HomeMainFragment"
        android:label="Home Main Fragment"
        tools:layout="@layout/home_main_fragment">

        <argument
            android:name="screen"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="menu"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="subMenu"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="id"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="arg"
            app:argType="boolean"
            android:defaultValue="true"/>
        <argument
            android:name="originalLink"
            app:argType="string"
            app:nullable="true"
            android:defaultValue=""/>
        <argument
            android:name="defaultTabId"
            app:argType="string"
            app:nullable="true"
            android:defaultValue=""/>
        <argument
            android:name="navigateToVodPage"
            app:argType="boolean"
            android:defaultValue="false"/>
        <argument
            android:name="chapterId"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="isFromDeeplink"
            app:argType="boolean"
            android:defaultValue="false"/>
        <argument android:name="showMetadata"
            app:argType="boolean"
            android:defaultValue="true"/>
        <argument android:name="sourceScreen"
            app:argType="string"
            android:defaultValue=""/>
        <argument android:name="relatedId"
            app:argType="string"
            android:defaultValue=""
            app:nullable="true"/>

    </fragment>

    <fragment
        android:id="@+id/qrcode_fragment"
        android:name="com.fptplay.mobile.features.qrcode.QRCodeFragment"
        android:label="QR Code Fragment"
        tools:layout="@layout/qr_code_fragment" >
        <argument
            android:name="isFromDeeplink"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="loginKey"
            android:defaultValue=""
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/category_detail_fragment"
        android:name="com.fptplay.mobile.homebase.HomeBaseDetailFragment"
        android:label="Home Detail Fragment"
        tools:layout="@layout/home_base_detail_fragment">
        <argument
            android:name="id"
            android:defaultValue="560e368317dc1310a164d2c7"
            app:argType="string" />
        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="description"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="screenProvider"
            android:defaultValue=""
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/search_fragment"
        android:name="com.fptplay.mobile.features.search.SearchFragment"
        android:label="Search Fragment"
        tools:layout="@layout/search_fragment" />

    <dialog
        android:id="@+id/view_more_fragment"
        android:name="com.fptplay.mobile.viewmore.ViewMoreBaseFragment"
        android:label="ViewMore Fragment Dialog"
        tools:layout="@layout/viewmore_base_fragment">

        <!--To show data in horizontal or vertical ratio-->
        <argument
            android:name="blockStyle"
            android:defaultValue=""
            app:argType="string" />

        <!--To call api if type = default-->
        <argument
            android:name="blockType"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="id"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="header"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="subHeader"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="deeplink"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="screenProvider"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="customData"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="source_show"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true"/>

        <argument
            android:name="page_id"
            android:defaultValue=""
            app:argType="string" />

    </dialog>

    <action
        android:id="@+id/action_global_to_home"
        app:popUpTo="@+id/home_main_fragment"
        app:popUpToInclusive="false" />

    <action
        android:id="@+id/action_global_to_playlist"
        app:destination="@+id/vod_playlist_fragment">
        <argument android:name="playlist_id"
            app:argType="string"/>
    </action>

    <action
        android:id="@+id/action_global_to_vod"
        app:destination="@id/nav_vod"
        app:popUpTo="@id/nav_vod">
        <argument
            android:name="id"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="screenProvider"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="isPlaylist"
            android:defaultValue="false"
            app:argType="boolean" />
    </action>
    <action
        android:id="@+id/action_global_to_tv"
        app:destination="@id/nav_tv">
        <argument
            android:name="shouldNavigate"
            android:defaultValue="true"
            app:argType="boolean" />
        <argument
            android:name="request_focus_group"
            android:defaultValue=""
            app:argType="string" />
    </action>

    <action
        android:id="@+id/action_global_to_tv_detail"
        app:destination="@id/nav_tv_detail" >
        <argument
            android:name="idToPlay"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="time_shift_limit"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="time_shift"
            android:defaultValue="0"
            app:argType="integer" />

        <argument
            android:name="use_args"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="groupId"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="startTime"
            android:defaultValue="0"
            app:argType="string" />
        <argument
            android:name="timeShow"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="name"
            android:defaultValue=""
            app:argType="string" />
    </action>

    <action
        android:id="@+id/action_global_to_download_v2"
        app:destination="@id/nav_dowload_v2"
        app:popUpTo="@id/nav_dowload_v2"
        app:enterAnim="@anim/slide_right_to_left_in"
        app:exitAnim="@anim/slide_right_to_left_out"
        app:popEnterAnim="@anim/slide_left_to_right_in"
        app:popExitAnim="@anim/slide_left_to_right_out">
<!--        <argument-->
<!--            android:name="targetScreen"-->
<!--            app:argType="com.fptplay.mobile.features.download.model.StartingTargetScreenV2Type"/>-->
        <argument
            android:name="targetScreen"
            app:argType="integer"
            android:defaultValue="0"/>
        <argument
            android:name="title"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="movie_id"
            app:argType="string"
            android:defaultValue="" />
        <argument android:name="isAirline"
            app:argType="boolean"
            android:defaultValue="false"/>

        <argument
            android:name="chapter_id"
            app:argType="string"
            android:defaultValue="" />
        <argument
            android:name="file_hash"
            app:argType="string"
            android:defaultValue="" />

    </action>

    <action
        android:id="@+id/action_global_to_search_fragment_dialog"
        app:destination="@id/search_fragment"
        app:enterAnim="@anim/slide_right_to_left_in"
        app:exitAnim="@anim/slide_right_to_left_out"
        app:popEnterAnim="@anim/slide_left_to_right_in"
        app:popExitAnim="@anim/slide_left_to_right_out" />

    <action
        android:id="@+id/action_global_to_notification"
        app:destination="@id/nav_notification" />

    <action
        android:id="@+id/action_global_to_loyalty_delivery"
        app:destination="@id/nav_loyalty_delivery" />

    <action android:id="@+id/action_global_to_about"
        app:destination="@id/nav_about"/>

    <action
        android:id="@+id/action_global_to_category"
        app:destination="@id/nav_category" />

    <action
        android:id="@+id/action_global_to_qr_code_fragment"
        app:destination="@id/qrcode_fragment">
        <argument
            android:name="isFromDeeplink"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="loginKey"
            android:defaultValue=""
            app:argType="string" />
    </action>

    <action
        android:id="@+id/action_global_to_category_detail"
        app:destination="@id/category_detail_fragment" >

        <argument
            android:name="id"
            android:defaultValue="560e368317dc1310a164d2c7"
            app:argType="string" />
        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="description"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="screenProvider"
            android:defaultValue=""
            app:argType="string" />
    </action>

    <action
        android:id="@+id/action_global_to_premiere"
        app:destination="@id/nav_premiere">
        <argument
            android:name="idToPlay"
            android:defaultValue=""
            app:argType="string" />
    </action>

    <action
        android:id="@+id/action_global_to_view_more_fragment"
        app:destination="@id/view_more_fragment">
        <argument
            android:name="blockStyle"
            android:defaultValue=""
            app:argType="string" />
        <!--To call api if type = default-->
        <argument
            android:name="blockType"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="id"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="header"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="subHeader"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="screenProvider"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="customData"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="source_show"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true"/>

        <argument
            android:name="page_id"
            android:defaultValue=""
            app:argType="string" />


    </action>

    <action
        android:id="@+id/action_global_to_home_main_fragment"
        app:destination="@id/home_main_fragment"
        app:popUpTo="@id/home_main_fragment"
        app:popUpToInclusive="true">
        <argument
            android:name="defaultTabId"
            app:argType="string"
            app:nullable="true"
            android:defaultValue=""/>
        <argument
            android:name="navigateToVodPage"
            app:argType="boolean"
            android:defaultValue="false"/>
    </action>
    <action
        android:id="@+id/action_global_to_short_videos"
        app:destination="@id/home_main_fragment"
        app:popUpTo="@id/home_main_fragment"
        app:popUpToInclusive="true">
        <argument
            android:name="defaultTabId"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="short-videos"/>
        <argument
            android:name="id"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="chapterId"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="isFromDeeplink"
            app:argType="boolean"
            android:defaultValue="false"/>
        <argument android:name="showMetadata"
            app:argType="boolean"
            android:defaultValue="true"/>
        <argument android:name="sourceScreen"
            app:argType="string"
            android:defaultValue=""/>
        <argument android:name="relatedId"
            app:argType="string"
            android:defaultValue=""
            app:nullable="true"/>
    </action>

    <!--region Sport-->
    <fragment
        android:id="@+id/sport_schedule_fragment"
        android:name="com.fptplay.mobile.features.sport.schedule.SportScheduleFragment"
        android:label="SportScheduleFragment"
        tools:layout="@layout/sport_schedule_fragment">

        <argument
            android:name="id"
            app:argType="string" />
        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/sport_tournament_schedule_and_result_fragment"
        android:name="com.fptplay.mobile.features.sport.tournament.schedule_and_result.SportTournamentScheduleAndResultFragment"
        android:label="SportTournamentScheduleAndResultFragment"
        tools:layout="@layout/sport_tournament_schedule_and_result_fragment">

        <argument
            android:name="seasonId"
            app:argType="string" />
        <argument
            android:name="screenType"
            app:argType="integer" />
        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="description"
            android:defaultValue=""
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/sport_tournament_team_rank_fragment"
        android:name="com.fptplay.mobile.features.sport.tournament.team_rank.SportTournamentTeamRankFragment"
        android:label="SportTournamentRankFragment"
        tools:layout="@layout/sport_tournament_team_rank_fragment">
        <argument
            android:name="seasonId"
            app:argType="string" />
        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="description"
            android:defaultValue=""
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/vod_playlist_fragment"
        android:name="com.fptplay.mobile.vod.VodPlaylistFragment"
        android:label="Vod Playlist Fragment"
        tools:layout="@layout/playlist_fragment">
        <argument android:name="playlist_id"
            app:argType="string"/>

    </fragment>

    <action
        android:id="@+id/action_global_to_sport_schedule"
        app:destination="@id/sport_schedule_fragment"/>

    <action
        android:id="@+id/action_global_to_sport_tournament_schedule_and_result"
        app:destination="@id/sport_tournament_schedule_and_result_fragment"/>

    <action
        android:id="@+id/action_global_to_sport_tournament_team_rank"
        app:destination="@id/sport_tournament_team_rank_fragment"/>
    <!--endregion-->

    <action
        android:id="@+id/action_global_to_choihaychia"
        app:destination="@id/nav_choihaychia" />

    <action
        android:id="@+id/action_global_to_nav_mini_app"
        app:destination="@id/nav_mini_app" >
        <argument
            android:name="megaAppId"
            android:defaultValue=""
            app:argType="string"
          />
        <argument
            android:name="closeAppInfo"
            android:defaultValue="@null"
            app:nullable="true"
            app:argType="com.fptplay.mobile.features.mini_app.CloseInfoAppMiniAppData"
            />
        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true"/>
        <argument
            android:name="url"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="listFunctionMiniApp"
            android:defaultValue="@null"
            app:argType="string[]"
            app:nullable="true" />
        <argument
            android:name="listEventsMiniApp"
            android:defaultValue="@null"
            app:argType="string[]"
            app:nullable="true" />
        <argument
            android:name="listPermissionsMiniApp"
            android:defaultValue="@null"
            app:argType="string[]"
            app:nullable="true" />
        <argument
            android:name="deeplinkUrl"
            app:argType="string"
            android:defaultValue="@null"
            app:nullable="true"/>
        <argument
            android:name="contentType"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="partnerId"
            app:argType="string"
            android:defaultValue=""/>
        <argument android:name="allowWildCard"
            app:argType="string[]"
            app:nullable="true" />
        <argument android:name="theme"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="@null"/>
    </action>


    <action
        android:id="@+id/action_global_to_nav_mega_app"
        app:destination="@id/nav_mega_app" >
        <argument
            android:name="megaAppId"
            app:argType="string"
            android:defaultValue="@null"
            app:nullable="true"/>
        <argument
            android:name="deeplinkUrl"
            app:argType="string"
            android:defaultValue="@null"
            app:nullable="true"/>

        <argument
            android:name="fromDeeplink"
            app:argType="boolean"
            android:defaultValue="false"/>
    </action>
    <!--region payment-->
    <action android:id="@+id/action_global_to_payment"
        app:destination="@id/nav_payment">

        <argument
            android:name="idToPlay"
            android:defaultValue=""
            app:argType="string"/>

        <argument
            android:name="extraId"
            android:defaultValue=""
            app:argType="string"/>

        <argument
            android:name="bitrateId"
            android:defaultValue=""
            app:argType="string"/>

        <argument
            android:name="isPremiere"
            android:defaultValue="false"
            app:argType="boolean" />

        <argument
            android:name="type"
            android:defaultValue=""
            app:argType="string"/>

        <argument
            android:name="view_type"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="package_type"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="popupToId"
            android:defaultValue="-1"
            app:argType="integer" />

        <argument
            android:name="from_source"
            android:defaultValue="play"
            app:argType="string" />

    </action>
    <!--endregion-->

    <!--region payment detail-->
    <action android:id="@+id/action_global_to_payment_detail"
        app:destination="@id/nav_payment_detail">

        <argument
            android:name="package_type"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="continue_watch"
            android:defaultValue="false"
            app:argType="boolean" />

        <argument
            android:name="navigationId"
            android:defaultValue="-1"
            app:argType="integer" />

        <argument
            android:name="idToPlay"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />

        <argument
            android:name="time_shift_limit"
            android:defaultValue="0"
            app:argType="integer" />

        <argument
            android:name="time_shift"
            android:defaultValue="0"
            app:argType="integer" />

        <argument
            android:name="popupToId"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="popUpToInclusive"
            android:defaultValue="true"
            app:argType="boolean" />

        <argument
            android:name="from_source"
            android:defaultValue="play"
            app:argType="string" />
        <argument
            android:name="requestFromCast"
            app:argType="boolean"
            android:defaultValue="false" />
    </action>
    <!--endregion-->

    <!--region payment in app purchase-->
    <action android:id="@+id/action_global_to_payment_in_app_purchase"
        app:destination="@id/nav_payment_in_app_purchase">
        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="message"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="negativeText"
            android:defaultValue=""
            app:argType="string"/>
        <argument
            android:name="positiveText"
            android:defaultValue=""
            app:argType="string"/>
        <argument
            android:name="requestKey"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="packageId"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="continueWatch"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="navigationId"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="idToPlay"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="time_shift_limit"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="time_shift"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="popupToId"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="popUpToInclusive"
            android:defaultValue="true"
            app:argType="boolean" />
        <argument
            android:name="isDirect"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="requestFromCast"
            android:defaultValue="false"
            app:argType="boolean" />
    </action>
    <!--endregion-->

    <!--region Mega Apps-->
    <fragment
        android:id="@+id/fpt_play_reward_fragment"
        android:name="com.fptplay.mobile.features.mega.apps.fpt_play_reward.FptPlayRewardFragment"
        android:label="FptPlayRewardFragment"
        tools:layout="@layout/fpt_play_reward_fragment">
        <argument
            android:name="section"
            android:defaultValue="exchange-gifts"
            app:argType="string" />
        <argument
            android:name="title"
            app:argType="string"
            android:defaultValue=""/>
    </fragment>

    <action
        android:id="@+id/action_global_to_fpt_play_reward"
        app:destination="@id/fpt_play_reward_fragment">
        <argument
            android:name="section"
            android:defaultValue="exchange-gifts"
            app:argType="string" />
        <argument
            android:name="title"
            app:argType="string"
            android:defaultValue=""/>
    </action>

    <fragment
        android:id="@+id/fpt_play_invite_friend_fragment"
        android:name="com.fptplay.mobile.features.mega.apps.fpt_invite_friends.FptPlayInviteFriendsFragment"
        android:label="FptPlay Invite Friends Fragment"
        tools:layout="@layout/base_web_view_fragment">
        <argument
            android:name="isLoadHistory"
            android:defaultValue="false"
            app:argType="boolean" />
    </fragment>

    <action
        android:id="@+id/action_global_to_fpt_invite_friends"
        app:destination="@id/fpt_play_invite_friend_fragment">
        <argument
            android:name="isLoadHistory"
            android:defaultValue="false"
            app:argType="boolean" />
    </action>

    <fragment
        android:id="@+id/fpt_play_shop_fragment"
        android:name="com.fptplay.mobile.features.mega.apps.fptpplayshop.FPTPlayShopFragment"
        android:label="FPT Play Shop"
        tools:layout="@layout/base_web_view_fragment">
        <argument
            android:name="url"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="screenType"
            android:defaultValue="screenListOrder"
            app:argType="string" />
        <argument
            android:name="detailId"
            android:defaultValue=""
            app:argType="string" />
    </fragment>

    <action
        android:id="@+id/action_global_to_fpt_play_shop"
        app:destination="@id/fpt_play_shop_fragment">
        <argument
            android:name="url"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="screenType"
            app:argType="string"
            android:defaultValue="screenListOrder"/>
        <argument
            android:name="detailId"
            android:defaultValue=""
            app:argType="string" />
    </action>

    <fragment
        android:id="@+id/mega_submenu_fragment"
        android:name="com.fptplay.mobile.features.mega.MegaSubMenuFragment"
        android:label="MegaSubMenuFragment"
        tools:layout="@layout/mega_submenu_fragment" >
        <argument
            android:name="title"
            app:argType="string"
            android:defaultValue=""
            />
        <argument
            android:name="id"
            app:argType="string"
            android:defaultValue=""
            />
    </fragment>

    <action
        android:id="@+id/action_global_to_mega_submenu"
        app:destination="@id/mega_submenu_fragment" >
        <argument
            android:name="title"
            app:argType="string"
            android:defaultValue=""
            />
    </action>

    <fragment
        android:id="@+id/about_app_info_fragment"
        android:name="com.fptplay.mobile.features.about.AboutAppInfoFragment"
        android:label="AboutAppInfoFragment"
        tools:layout="@layout/about_child_fragment" >
        <argument
            android:name="type"
            app:argType="string"
            android:defaultValue=""
            />
        <argument
            android:name="title"
            app:argType="string"
            android:defaultValue=""
            />
    </fragment>
    <action
        android:id="@+id/action_global_to_about_app_info"
        app:destination="@id/about_app_info_fragment" >
        <argument
            android:name="type"
            app:argType="string"
            android:defaultValue=""
            />
        <argument
            android:name="title"
            app:argType="string"
            android:defaultValue=""
            />
    </action>

    <fragment
        android:id="@+id/about_app_introduce_fragment"
        android:name="com.fptplay.mobile.features.about.AboutAppIntroduceFragment"
        android:label="AboutAppIntroduceFragmentBinding"
        tools:layout="@layout/about_app_introduce_fragment" >
        <argument
            android:name="type"
            app:argType="string"
            android:defaultValue=""
            />
        <argument
            android:name="title"
            app:argType="string"
            android:defaultValue=""
            />
    </fragment>

    <action
        android:id="@+id/action_global_to_about_app_introduce"
        app:destination="@id/about_app_introduce_fragment" >
        <argument
            android:name="type"
            app:argType="string"
            android:defaultValue=""
            />
        <argument
            android:name="title"
            app:argType="string"
            android:defaultValue=""
            />
    </action>

    <!--endregion-->

    <!--region Account-->

    <fragment
        android:id="@+id/web_view_fragment"
        android:name="com.fptplay.mobile.webview.WebViewFragment"
        android:label="Web view Fragment"
        tools:layout="@layout/web_view_fragment">
        <argument
            android:name="url"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="hasMenu"
            android:defaultValue="false"
            app:argType="boolean" />

    </fragment>

     <fragment
        android:id="@+id/account_action_fragment"
        android:name="com.fptplay.mobile.features.mega.account.AccountActionFragment"
        android:label="AccountActionFragment"
        tools:layout="@layout/account_action_fragment" >
        <argument
            android:name="targetScreen"
            android:defaultValue="DeviceManager"
            app:argType="com.fptplay.mobile.features.mega.account.util.AccountMegaScreen" />
        <argument
            android:name="promotionCode"
            android:defaultValue=""
            app:argType="string" />
         <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string" />
    </fragment>

    <action
        android:id="@+id/action_global_to_account_action"
        app:destination="@id/account_action_fragment" >
        <argument
            android:name="targetScreen"
            android:defaultValue="DeviceManager"
            app:argType="com.fptplay.mobile.features.mega.account.util.AccountMegaScreen" />
        <argument
            android:name="promotionCode"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string" />
    </action>
    <fragment
        android:id="@+id/login_list_device_fragment"
        android:name="com.fptplay.mobile.features.login.fragmentV2.LoginListDeviceFragment"
        android:label="Login List Device Fragment"
        tools:layout="@layout/login_list_device_fragment" >
        <argument
            android:name="callFromQrCode"
            app:argType="boolean"
            android:defaultValue="false"/>
        <argument
            android:name="loginKey"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="verifyToken"
            app:argType="string" />
        <argument
            android:name="typeShow"
            app:argType="string" />
        <argument
            android:name="description"
            app:argType="string" />
        <argument
            android:name="title"
            app:argType="string" />
    </fragment>
    <action
        android:id="@+id/action_global_to_list_device"
        app:destination="@id/login_list_device_fragment" >
        <argument
            android:name="callFromQrCode"
            app:argType="boolean"
            android:defaultValue="false"/>
        <argument
            android:name="loginKey"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="verifyToken"
            app:argType="string" />
        <argument
            android:name="typeShow"
            app:argType="string" />
        <argument
            android:name="description"
            app:argType="string" />
        <argument
            android:name="title"
            app:argType="string" />
    </action>

    <dialog
        android:id="@+id/account_action_dialog_fragment"
        android:name="com.fptplay.mobile.features.mega.account.dialog_tablet.AccountActionDialogFragment"
        android:label="AccountDialogTabletFragment"
        tools:layout="@layout/account_action_dialog_fragment" >
        <argument
            android:name="targetScreen"
            android:defaultValue="DeviceManager"
            app:argType="com.fptplay.mobile.features.mega.account.util.AccountMegaScreen" />
        <argument
            android:name="promotionCode"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string" />

    </dialog>

    <action
        android:id="@+id/action_global_to_account_action_dialog"
        app:destination="@id/account_action_dialog_fragment" >
        <argument
            android:name="targetScreen"
            android:defaultValue="DeviceManager"
            app:argType="com.fptplay.mobile.features.mega.account.util.AccountMegaScreen" />
        <argument
            android:name="promotionCode"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string" />
    </action>

    <action
        android:id="@+id/action_global_to_account_info"
        app:destination="@id/nav_account_info" />

    <action android:id="@+id/action_global_to_web_view_fragment"
        app:destination="@id/web_view_fragment">
        <argument
            android:name="url"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string" />

        <argument
            android:name="hasMenu"
            android:defaultValue="false"
            app:argType="boolean" />
    </action>
    <!--endregion-->

    <!--  region Ads  -->
    <dialog
        android:id="@+id/welcomeScreenBottomSheetDialog"
        android:name="com.fptplay.mobile.features.ads.banner.welcome_screen.WelcomeScreenBottomSheetDialog"
        android:label="WelcomeScreenBottomSheetDialog" >
        <argument
            android:name="contentHtml"
            android:defaultValue=""
            app:argType="string" />

    </dialog>
    <action
        android:id="@+id/action_global_to_welcomeScreenBottomSheetDialog"
        app:destination="@id/welcomeScreenBottomSheetDialog">
        <argument
            android:name="contentHtml"
            android:defaultValue=""
            app:argType="string" />
    </action>

    <!--  endregion Ads  -->
    <!--  region Login  -->
    <include app:graph="@navigation/nav_login"/>

    <action
        android:id="@+id/action_global_to_login"
        app:destination="@id/nav_login">
        <argument
            android:name="idToPlay"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="time_shift_limit"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="time_shift"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="navigationId"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="popupToId"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="popUpToInclusive"
            android:defaultValue="true"
            app:argType="boolean" />
        <argument
            android:name="enterRegister"
            app:argType="boolean"
            android:defaultValue="false" />
        <argument
            android:name="checkRequireVip"
            app:argType="boolean"
            android:defaultValue="false" />
        <argument
            android:name="isPlaylist"
            app:argType="boolean"
            android:defaultValue="false" />
        <argument
            android:name="requestFromCast"
            app:argType="boolean"
            android:defaultValue="false" />
        <argument
            android:name="requestRestartApp"
            app:argType="boolean"
            android:defaultValue="false" />
        <argument
            android:name="requestFromOnBoarding"
            app:argType="boolean"
            android:defaultValue="false" />
    </action>

    <!--  endregion Login  -->

    <!--  region require vip  -->


    <!--  endregion require vip  -->


    <!--  region Navigation Mega  -->

    <dialog
        android:id="@+id/omniBottomSheetDialogFragment"
        android:name="com.fptplay.mobile.features.mega.apps.omni_shop.OmniBottomSheetDialogFragment"
        android:label="OmniBottomSheetDialogFragment" >
        <argument
            android:name="loadListProduct"
            android:defaultValue="true"
            app:argType="boolean" />
        <argument
            android:name="url"
            android:defaultValue=""
            app:argType="string"
            />

        <argument
            android:name="jsonData"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true"/>
    </dialog>

    <action
        android:id="@+id/action_global_to_omni_bottom_sheet_dialog"
        app:destination="@id/omniBottomSheetDialogFragment" >
        <argument
            android:name="loadListProduct"
            android:defaultValue="true"
            app:argType="boolean" />
        <argument
            android:name="url"
            android:defaultValue=""
            app:argType="string"
            />
        <argument
            android:name="jsonData"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true"/>
    </action>
    <fragment
        android:id="@+id/megaAppMDBDFragment"
        android:name="com.fptplay.mobile.features.game_mdbd.MuaDayBanDinhFragment"
        android:label="MegaAppMDBDFragment"
        tools:layout="@layout/fragment_mua_day_ban_dinh"/>
    <action
        android:id="@+id/action_global_to_game_mdbd"
        app:destination="@id/megaAppMDBDFragment"
        />
    <!--  endregion Navigation Mega   -->

    <!--  region Navigation Game  -->
    <action
        android:id="@+id/action_global_to_game"
        app:destination="@id/nav_game">
        <argument
            android:name="gameId"
            android:defaultValue=""
            app:argType="string"/>
        <argument
            android:name="deeplinkUrl"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="fromDeeplink"
            android:defaultValue="false"
            app:argType="boolean" />
    </action>
    <!--  endregion Navigation Game   -->


    <!--region Login-->
    <dialog
        android:id="@+id/login_required_dialog"
        tools:layout="@layout/login_required_dialog"
        android:label="Required Login Dialog"
        android:name="com.fptplay.mobile.common.ui.view.RequiredLoginDialog">
        <argument
            android:name="titleNotify"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="titlePosition"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="titleNegation"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="requestKey"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="oneButton"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="canBack"
            android:defaultValue="true"
            app:argType="boolean" />
        <argument
            android:name="idToPlay"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="time_shift_limit"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="time_shift"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="checkRequireVip"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="isPlaylist"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="navigationId"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="popupToId"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="popUpToInclusive"
            android:defaultValue="true"
            app:argType="boolean" />

        <argument
            android:name="displayInDialogForMobile"
            android:defaultValue="false"
            app:argType="boolean" />

        <argument
            android:name="requestFromCast"
            app:argType="boolean"
            android:defaultValue="false" />
        <argument
            android:name="requestRestartApp"
            app:argType="boolean"
            android:defaultValue="false" />
        <argument
            android:name="requestFromOnBoarding"
            app:argType="boolean"
            android:defaultValue="false" />
        <argument
            android:name="isPopupToBeforeLogin"
            app:argType="boolean"
            android:defaultValue="false" />

    </dialog>

    <action
        android:id="@+id/action_global_to_required_login_dialog"
        app:destination="@id/login_required_dialog"
        app:enterAnim="@anim/fade_out"
        app:exitAnim="@anim/fade_in"
        app:popEnterAnim="@anim/fade_out"
        app:popExitAnim="@anim/fade_in" />

    <!--endregion Login-->

    <!--region Login-->
    <dialog
        android:id="@+id/require_buy_package_dialog"
        tools:layout="@layout/required_buy_package_dialog"
        android:label="Required Buy Package Dialog"
        android:name="com.fptplay.mobile.common.ui.view.RequireBuyPackageDialog">

        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="message"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="negativeText"
            android:defaultValue=""
            app:argType="string"/>
        <argument
            android:name="positiveText"
            android:defaultValue=""
            app:argType="string"/>
        <argument
            android:name="requestKey"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="packageId"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="continueWatch"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="navigationId"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="idToPlay"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="time_shift_limit"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="time_shift"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="popupToId"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="popUpToInclusive"
            android:defaultValue="true"
            app:argType="boolean" />

        <argument
            android:name="from_source"
            android:defaultValue="play"
            app:argType="string" />

        <argument
            android:name="requestFromCast"
            app:argType="boolean"
            android:defaultValue="false" />

    </dialog>

    <action
        android:id="@+id/action_global_to_required_buy_package"
        app:destination="@id/require_buy_package_dialog"
        app:enterAnim="@anim/fade_out"
        app:exitAnim="@anim/fade_in"
        app:popEnterAnim="@anim/fade_out"
        app:popExitAnim="@anim/fade_in" />

    <!--endregion Login-->

    <!--  region Download  -->
    <fragment
        android:id="@+id/downloadMainFragment"
        android:name="com.fptplay.mobile.features.download.fragment.DownloadMainFragment"
        android:label="DownloadMainFragment"
        tools:layout="@layout/download_main_fragment"/>
    <action
        android:id="@+id/action_global_to_downloadMainFragment"
        app:destination="@id/downloadMainFragment" />
    <!--  endregion Download  -->
    <action
        android:id="@+id/action_global_to_game_play_start_30s"
        app:destination="@id/nav_play_star_30s"
        />
    <action
        android:id="@+id/action_global_to_qr_code_ticket"
        app:destination="@id/nav_hit_fest"
        />
    <action
        android:id="@+id/action_global_to_category_of_categories_fragment"
        app:destination="@id/categoryOfCategoriesFragment"/>

    <action
        android:id="@+id/action_global_to_loyalty"
        app:destination="@id/nav_loyalty"
        app:enterAnim="@anim/slide_right_to_left_in"
        app:exitAnim="@anim/slide_right_to_left_out"
        app:popEnterAnim="@anim/slide_left_to_right_in"
        app:popExitAnim="@anim/slide_left_to_right_out" />
    <action
        android:id="@+id/action_global_to_loyalty_ekyc"
        app:destination="@id/nav_ekyc"
        app:enterAnim="@anim/slide_right_to_left_in"
        app:exitAnim="@anim/slide_right_to_left_out"
        app:popEnterAnim="@anim/slide_left_to_right_in"
        app:popExitAnim="@anim/slide_left_to_right_out" />
    <dialog
        android:id="@+id/categoryOfCategoriesFragment"
        android:name="com.fptplay.mobile.features.categories.CategoryOfCategoriesFragment"
        android:label="CategoryOfCategoriesFragment"
        tools:layout="@layout/category_of_categories_fragment">
        <argument android:name="type"
            app:argType="string"
            android:defaultValue=""/>
        <argument android:name="blockId"
            app:argType="string"
            android:defaultValue=""/>
        <argument android:name="blockType"
            app:argType="string"
            android:defaultValue=""/>
        <argument android:name="customData"
            app:argType="string"
            android:defaultValue=""/>
    </dialog>

    <dialog android:id="@+id/start_zendesk_dialog"
        tools:layout="@layout/ui_loading_base"
        android:name="com.fptplay.mobile.features.zendesk.StartZendeskDialog"
        android:label="Start Zendesk Dialog"/>

    <action android:id="@+id/action_global_to_zendesk"
        app:destination="@id/start_zendesk_dialog"/>

    <action android:id="@+id/action_global_to_momentsFragment"
        app:destination="@id/nav_moment">
        <argument android:name="momentId"
            app:argType="string"
            android:defaultValue=""/>
        <argument android:name="clickItem"
            app:argType="boolean"
            android:defaultValue="false"/>
        <argument
            android:name="chapterId"
            android:defaultValue=""
            app:argType="string"/>
        <argument
            android:name="isPlaylistLayout"
            android:defaultValue="false"
            app:argType="boolean"/>
        <argument
            android:name="indexForPlaylist"
            android:defaultValue="1"
            app:argType="integer"/>
        <argument
            android:name="startPosition"
            android:defaultValue="0L"
            app:argType="long"/>

        <argument
            android:name="isPlaying"
            android:defaultValue="true"
            app:argType="boolean"/>
        <argument
            android:name="startNextEpisode"
            android:defaultValue="false"
            app:argType="boolean"/>

        <argument android:name="showMetadata"
            app:argType="boolean"
            android:defaultValue="true"/>
        <argument android:name="sourceScreen"
            app:argType="string"
            android:defaultValue=""/>
        <argument android:name="relatedId"
            app:argType="string"
            android:defaultValue=""
            app:nullable="true"/>
        <argument android:name="isFromDeeplink"
            app:argType="boolean"
            android:defaultValue="false"/>
        <argument android:name="openListEpisodeDialog"
            app:argType="boolean"
            android:defaultValue="false"/>
    </action>
    <action
        android:id="@+id/action_global_to_pairing_control"
        app:destination="@id/nav_pairing_control_host">
        <argument
            android:name="type"
            android:defaultValue="0"
            app:argType="integer"/>
        <argument
            android:name="keyCode"
            android:defaultValue="0"
            app:argType="integer"/>
    </action>

    <action
        android:id="@+id/action_global_to_multi_profile"
        app:destination="@id/nav_multi_profile" >
        <argument
            android:name="targetScreen"
            app:argType="com.fptplay.mobile.features.multi_profile.utils.StartingTargetScreenType"/>

        <argument
            android:name="isSelectionOnBoarding"
            app:argType="boolean"
            android:defaultValue="false"/>
        <argument
            android:name="isFromLoginScreen"
            app:argType="boolean"
            android:defaultValue="false"/>
    </action>

    <dialog
        android:id="@+id/deeplinkNotSupportedDialog"
        android:name="com.fptplay.mobile.common.ui.view.DeeplinkNotSupportedDialog"
        android:label="DeeplinkNotSupportedDialog" >
        <argument
            android:name="title"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="message"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="titlePositive"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />

    </dialog>

    <action
        android:id="@+id/action_global_to_deeplink_not_supported_dialog"
        app:destination="@id/deeplinkNotSupportedDialog"
        app:enterAnim="@anim/fade_out"
        app:exitAnim="@anim/fade_in"
        app:popEnterAnim="@anim/fade_out"
        app:popExitAnim="@anim/fade_in" />
    <fragment
        android:id="@+id/megaHamburgerMenuFragment"
        android:name="com.fptplay.mobile.features.mega.MegaHamburgerMenuFragment"
        android:label="MegaHamburgerMenuFragment" />
    <action
        android:id="@+id/action_global_to_megaHamburgerMenuFragment"
        app:destination="@id/megaHamburgerMenuFragment" />

    <action
        android:id="@+id/action_global_to_nav_pladio"
        app:destination="@id/nav_pladio_outer"
        app:popUpTo="@id/nav_pladio_outer"
        app:popUpToInclusive="true">
        

        <argument
            android:name="contentId"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="@null"/>
        <argument
            android:name="contentType"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="@null"/>

        <argument
            android:name="navigateType"
            android:defaultValue="-1"
            app:argType="integer" />

        <argument
            android:name="pladioContentType"
            android:defaultValue="-1"
            app:argType="integer"/>
        <argument
            android:name="pladioType"
            android:defaultValue="-1"
            app:argType="integer"/>

        <argument
            android:name="playlistId"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="@null"/>

        <argument
            android:name="startTime"
            app:argType="integer"
            android:defaultValue="0"/>
        <argument
            android:name="autoplay"
            app:argType="boolean"
            android:defaultValue="true"/>

        <argument
            android:name="episodeId"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="@null"/>
    </action>
    <fragment
        android:id="@+id/megaAudioVideoConfigureFragment"
        android:name="com.fptplay.mobile.features.mega.MegaAudioVideoConfigureFragment"
        android:label="MegaAudioVideoConfigureFragment" >
        <argument
            android:name="title"
            app:argType="string"
            android:defaultValue=""
            />
        <argument
            android:name="id"
            app:argType="string"
            android:defaultValue=""
            />

    </fragment>
    <action
        android:id="@+id/action_global_to_mega_audio_video_configure_fragment"
        app:destination="@id/megaAudioVideoConfigureFragment" >
        <argument
            android:name="title"
            app:argType="string"
            android:defaultValue=""
            />
        <argument
            android:name="id"
            app:argType="string"
            android:defaultValue=""
            />
    </action>
    <include app:graph="@navigation/nav_library" />
    <action
        android:id="@+id/action_global_to_library"
        app:destination="@id/nav_library" >
        <argument
            android:name="title"
            app:argType="string"
            />

        <argument
            android:name="profileId"
            app:argType="string"
            />

        <!--        <argument-->
<!--            android:name="targetScreen"-->
<!--            app:argType="com.fptplay.mobile.features.multi_profile.utils.StartingTargetScreenType"/>-->

<!--        <argument-->
<!--            android:name="isSelectionOnBoarding"-->
<!--            app:argType="boolean"-->
<!--            android:defaultValue="false"/>-->
<!--        <argument-->
<!--            android:name="isFromLoginScreen"-->
<!--            app:argType="boolean"-->
<!--            android:defaultValue="false"/>-->
    </action>

</navigation>