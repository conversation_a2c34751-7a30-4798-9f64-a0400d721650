package com.fptplay.mobile.player.views

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import androidx.core.view.doOnAttach
import androidx.recyclerview.widget.LinearLayoutManager
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.utils.viewutils.HorizontalItemDecoration
import com.fptplay.mobile.databinding.PlayerPlaylistOverlayViewBinding
import com.fptplay.mobile.player.adapter.PlayerPlaylistOverlayAdapter
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.logger.Logger

class PlayerPlaylistOverlayView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    style: Int = 0
) : FrameLayout(context, attrs, style) {
    private var binding: PlayerPlaylistOverlayViewBinding? = null
    private var listener: PlaylistOverlayEventListener? = null

    private val playlistData = mutableListOf<Details.Episode>()
    private var currentEpisode: Details.Episode? = null

    private val playlistItemAdapter by lazy {
        PlayerPlaylistOverlayAdapter()
    }

    init {
        binding = PlayerPlaylistOverlayViewBinding.inflate(LayoutInflater.from(context), this, true)

        doOnAttach {
            bindComponent()
            setupInitialAnimationState()
        }
    }

    private fun setupInitialAnimationState() {
        // Initially position views below the screen for slide-up animation
        binding?.tvTitle?.translationY = 100f
        binding?.rcvMain?.translationY = 200f
        binding?.tvTitle?.alpha = 0f
        binding?.rcvMain?.alpha = 0f
    }

    private fun animateViewsFromBottom() {
        val titleAnimator = ObjectAnimator.ofFloat(binding?.tvTitle, "translationY", 100f, 0f).apply {
            duration = 400
            interpolator = DecelerateInterpolator()
        }
        
        val titleAlphaAnimator = ObjectAnimator.ofFloat(binding?.tvTitle, "alpha", 0f, 1f).apply {
            duration = 400
            interpolator = DecelerateInterpolator()
        }

        val recyclerAnimator = ObjectAnimator.ofFloat(binding?.rcvMain, "translationY", 200f, 0f).apply {
            duration = 500
            startDelay = 100 // Slight delay for staggered effect
            interpolator = DecelerateInterpolator()
        }
        
        val recyclerAlphaAnimator = ObjectAnimator.ofFloat(binding?.rcvMain, "alpha", 0f, 1f).apply {
            duration = 500
            startDelay = 100
            interpolator = DecelerateInterpolator()
        }

        // Play all animations together
        AnimatorSet().apply {
            playTogether(titleAnimator, titleAlphaAnimator, recyclerAnimator, recyclerAlphaAnimator)
            start()
        }
    }

    private fun bindComponent() {
        binding?.clContainer?.onClickDelay { listener?.onClose() }

        binding?.rcvMain?.apply {
            playlistItemAdapter.eventListener = object : IEventListener<Details.Episode> {
                override fun onClickedItem(position: Int, data: Details.Episode) {
                    listener?.onClickItem(data)
                    listener?.onClose()
                }
            }
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            addItemDecoration(
                HorizontalItemDecoration(
                    spacingBetween = resources.getDimensionPixelSize(R.dimen.player_control_bottom_button_margin),
                    spacingEdge = resources.getDimensionPixelSize(R.dimen.margin_common_large)
                )
            )
            adapter = playlistItemAdapter

        }

        binding?.ibClose?.onClickDelay {
            listener?.onClose()
        }
    }

    fun setListener(playlistOverlayListener: PlaylistOverlayEventListener?) {
        this.listener = playlistOverlayListener
    }

    fun setPlaylistData(data: List<Details.Episode>) {
        playlistData.clear()
        playlistData.addAll(data)
        playlistItemAdapter.setCurrentEpisode(episode = currentEpisode)
        playlistItemAdapter.bind(data)
        
        // Auto scroll to current episode and center it
        currentEpisode?.let { currentEp ->
            val currentPosition = data.indexOfFirst { it.id == currentEp.id }
            if (currentPosition >= 0) {
                // Use a more robust centering approach
                binding?.rcvMain?.post {
                    scrollToPositionAndCenter(currentPosition)
                }
            }
        }
        
        // Animate views from bottom after data is set
        post {
            animateViewsFromBottom()
        }
    }

    fun setCurrentEpisode(episode: Details.Episode?) {
        this.currentEpisode = episode
    }

    fun showWithAnimation() {
        visibility = View.VISIBLE
        setupInitialAnimationState()
        post {
            animateViewsFromBottom()
        }
    }

    fun hideWithAnimation(onAnimationEnd: (() -> Unit)? = null) {
        val titleAnimator = ObjectAnimator.ofFloat(binding?.tvTitle, "translationY", 0f, 100f).apply {
            duration = 300
            interpolator = DecelerateInterpolator()
        }
        
        val titleAlphaAnimator = ObjectAnimator.ofFloat(binding?.tvTitle, "alpha", 1f, 0f).apply {
            duration = 300
            interpolator = DecelerateInterpolator()
        }

        val recyclerAnimator = ObjectAnimator.ofFloat(binding?.rcvMain, "translationY", 0f, 200f).apply {
            duration = 400
            startDelay = 50
            interpolator = DecelerateInterpolator()
        }
        
        val recyclerAlphaAnimator = ObjectAnimator.ofFloat(binding?.rcvMain, "alpha", 1f, 0f).apply {
            duration = 400
            startDelay = 50
            interpolator = DecelerateInterpolator()
        }

        AnimatorSet().apply {
            playTogether(titleAnimator, titleAlphaAnimator, recyclerAnimator, recyclerAlphaAnimator)
            addListener(object : android.animation.AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: android.animation.Animator) {
                    visibility = View.GONE
                    onAnimationEnd?.invoke()
                }
            })
            start()
        }
    }

    private fun scrollToPositionAndCenter(position: Int) {
        val recyclerView = binding?.rcvMain ?: return
        val layoutManager = recyclerView.layoutManager as? LinearLayoutManager ?: return
        
        // Wait for RecyclerView to be properly laid out before centering
        if (recyclerView.width == 0) {
            Logger.d("PlayerPlaylistOverlayView > RecyclerView not laid out yet, waiting for layout...")
            recyclerView.viewTreeObserver.addOnGlobalLayoutListener(object : android.view.ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    recyclerView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    Logger.d("PlayerPlaylistOverlayView > RecyclerView laid out, now centering...")
                    performCentering(position, recyclerView, layoutManager)
                }
            })
        } else {
            performCentering(position, recyclerView, layoutManager)
        }
    }
    
    private fun performCentering(position: Int, recyclerView: androidx.recyclerview.widget.RecyclerView, layoutManager: LinearLayoutManager) {
        val recyclerViewWidth = recyclerView.width
        val itemWidth = resources.getDimensionPixelSize(R.dimen.player_playlist_overlay_item_wrapper_width)
        val spacingBetween = resources.getDimensionPixelSize(R.dimen.player_control_bottom_button_margin)
        
        Logger.d("PlayerPlaylistOverlayView > performCentering - recyclerWidth: $recyclerViewWidth, itemWidth: $itemWidth")
        
        // Calculate center offset accounting for item decorations
        val effectiveItemWidth = itemWidth + spacingBetween
        val centerOffset = (recyclerViewWidth - effectiveItemWidth) / 2
        
        if (centerOffset >= 0) {
            // Traditional centering works
            Logger.d("PlayerPlaylistOverlayView > Using traditional centering with offset: $centerOffset")
            layoutManager.scrollToPositionWithOffset(position, centerOffset)
        } else {
            // Method 2: Use alternative centering for oversized items
            Logger.d("PlayerPlaylistOverlayView > Item too large ($effectiveItemWidth vs $recyclerViewWidth), using alternative centering")
            
            // Scroll to position first
            layoutManager.scrollToPosition(position)
            
            // Then manually center by calculating scroll offset
            recyclerView.post {
                val targetView = layoutManager.findViewByPosition(position)
                if (targetView != null) {
                    val viewCenter = (targetView.left + targetView.right) / 2
                    val recyclerCenter = recyclerViewWidth / 2
                    val scrollOffset = viewCenter - recyclerCenter
                    
                    Logger.d("PlayerPlaylistOverlayView > Manual centering - scrollOffset: $scrollOffset")
                    recyclerView.scrollBy(scrollOffset, 0)
                } else {
                    // Fallback to smooth scroll
                    Logger.d("PlayerPlaylistOverlayView > Fallback to smooth scroll")
                    recyclerView.smoothScrollToPosition(position)
                }
            }
        }
    }
}

interface PlaylistOverlayEventListener {
    fun onClickItem(episode: Details.Episode)
    fun onClose()
}