package com.fptplay.mobile.features.introduce

import android.annotation.SuppressLint
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.fptplay.mobile.HomeActivity
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.ActivityExtensions.startHome
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.DeeplinkUtils.addAdjustTrueLinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFacebookDeeplinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFirebaseDeeplinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFirebaseNotificationData
import com.fptplay.mobile.databinding.IntroduceFragmentBinding
import com.fptplay.mobile.databinding.SurveyFragmentBinding
import com.google.android.material.tabs.TabLayoutMediator
import com.google.gson.Gson
import com.xhbadxx.projects.module.domain.entity.fplay.common.TriggerEvent
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger
import javax.inject.Inject

@dagger.hilt.android.AndroidEntryPoint
class IntroduceFragment : BaseFragment<IntroduceViewModel.IntroduceState, IntroduceViewModel.IntroduceIntent>() {
    override val viewModel: IntroduceViewModel by activityViewModels()
    private var _binding: IntroduceFragmentBinding? = null
    private val binding get() = _binding!!
    override val hasEdgeToEdge: Boolean = true
    override val handleBackPressed: Boolean = true
    @Inject
    lateinit var sharedPreferences: SharedPreferences
    private val args: IntroduceFragmentArgs by navArgs()
    private val imageSliderAdapter: ImageSliderAdapter = ImageSliderAdapter()
    private var event:TriggerEvent.Event? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = IntroduceFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun initData() {
        event = try {
            Gson().fromJson(args.event, TriggerEvent.Event::class.java)
        } catch (e: Exception) {
            null
        }
        binding.viewPager.adapter = imageSliderAdapter
        if(event != null) {
            loadListImage(MainApplication.INSTANCE.applicationContext.resources.configuration.orientation)
        } else {
            startHome()
        }
    }

    override fun bindEvent() {
        binding.ivClose.setOnClickListener {
            startHome()
        }
    }

    @SuppressLint("InflateParams")
    private fun loadListImage(orientation:Int) {
        event?.let {
            val isPortrait = orientation == Configuration.ORIENTATION_PORTRAIT
            val portraitImages = it.media.images.portrait
            val portraitTabletImages = it.media.images.portraitTablet
            val landscapeImages = it.media.images.landscape
            val list = if (isPortrait){
                if(context.isTablet()) portraitTabletImages else portraitImages
            }else landscapeImages
            imageSliderAdapter.setImageList(list)
            if(list.size == 1) {
                binding.tabLayout.visibility = View.GONE
            } else {
                binding.tabLayout.visibility = View.VISIBLE
            }
            TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, _ ->
                tab.customView = LayoutInflater.from(requireContext())
                    .inflate(R.layout.introduce_indicator_item, null)
                tab.customView?.setOnClickListener {
                    //do nothing, just to prevent tab click
                }

            }.attach()
        }

    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        loadListImage(newConfig.orientation)
    }

    private fun startHome() {
        requireActivity().startHome(sharedPreferences, args.originalLink)
    }

    override fun IntroduceViewModel.IntroduceState.toUI() {

    }
}
