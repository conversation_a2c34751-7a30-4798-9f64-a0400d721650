package com.fptplay.mobile.features.loyalty.adapter

import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.extensions.getDisplayWidth
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.databinding.LoyaltyPrivilegeBlockBinding
import com.fptplay.mobile.databinding.LoyaltyPrivilegeBlockListBlockBinding
import com.fptplay.mobile.player.utils.isEnableAutoScreenRotationInSettings
import com.tear.modules.util.Utils.checkToShowContent
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.Structure
import com.xhbadxx.projects.module.domain.entity.fplay.loyalty.BlockPrivilege
import com.xhbadxx.projects.module.domain.entity.fplay.loyalty.BlockPrivilegeItem
import com.xhbadxx.projects.module.domain.entity.fplay.loyalty.BlockPrivilegeType
import com.xhbadxx.projects.module.domain.entity.fplay.loyalty.LoyGetListBlockPrivilegeEntity
import com.xhbadxx.projects.module.util.common.IEventListener
import timber.log.Timber

class PrivilegeAdapter: BaseAdapter<BlockPrivilege, RecyclerView.ViewHolder>() {

    var itemEventsListener: IEventListener<BaseObject>? = null
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if(viewType == 1) {
            BlockPrivilegeHorizontalViewHolder(
                LoyaltyPrivilegeBlockListBlockBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )
        } else {
            BlockPrivilegeVerticalViewHolder(
                LoyaltyPrivilegeBlockBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = differ.currentList[position]
        when (holder) {
            is BlockPrivilegeHorizontalViewHolder -> holder.bind(item)
            is BlockPrivilegeVerticalViewHolder -> holder.bind(item)
            else -> {}
        }
    }


    override fun getItemViewType(position: Int): Int {
        val item = differ.currentList[position]
        return if (item.blockType == BlockPrivilegeType.ListBlockHorizontal) {
            1
        } else {
            2
        }
    }

    fun refresh(data: List<LoyGetListBlockPrivilegeEntity.Block>) {
        bind(data)
    }

    inner class BlockPrivilegeHorizontalViewHolder(private val binding: LoyaltyPrivilegeBlockListBlockBinding) : RecyclerView.ViewHolder(binding.root) {

        val marginBetweenBlock = itemView.context.resources.getDimensionPixelSize(R.dimen.loyalty_privilege_list_block_item_margin_between)
        val marginEdge = itemView.context.resources.getDimensionPixelSize(R.dimen.loyalty_privilege_list_block_item_margin_between)
        val blockVisibleInScreen = 5
        val widthItem by lazy {
            val width = itemView.context.getDisplayWidth()

            val widthItem = if(itemView.context.isTablet()) {
                itemView.context.resources.getDimensionPixelSize(R.dimen.loyalty_privilege_list_block_item_width)
            } else {
                (width - (marginBetweenBlock * (blockVisibleInScreen-1) ) - (marginEdge * 2) ) / blockVisibleInScreen

            }
//            Timber.tag("tam-loyalty").d("widthItem $widthItem - width $width")
            widthItem
        }
        val horizontalBlockAdapter by lazy { PrivilegeHorizontalBlockAdapter(widthItem) }


        init {
            binding.rvPrivilegeBlock.apply {
                adapter = horizontalBlockAdapter
                addItemDecoration(
                    HorizontalItemDecoration(spacingBetween = marginBetweenBlock, spacingEdge = marginEdge)
                )
                layoutManager = LinearLayoutManager(itemView.context,
                    LinearLayoutManager.HORIZONTAL,false)

            }

            horizontalBlockAdapter.eventListener = object: IEventListener<BlockPrivilegeItem> {
                override fun onClickedItem(position: Int, data: BlockPrivilegeItem) {
                    itemEventsListener?.onClickedItem(position, data)
                }
            }

        }

        fun bind(data: BlockPrivilege) {
            horizontalBlockAdapter.bind(data.items)
        }

        inner class HorizontalItemDecoration(
            private val spacingBetween: Int,
            private val spacingEdge: Int = 0
        ): RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                val position = parent.getChildAdapterPosition(view)  // item position

                when (position) {
                    0 -> {
                        outRect.left =
                            spacingEdge
                        outRect.right =
                            spacingBetween
                    }
                    horizontalBlockAdapter.size() - 1 -> {
                        outRect.right = spacingEdge
                    }
                    else -> {
                        outRect.right = spacingBetween
                    }
                }

            }
        }
    }

    inner class BlockPrivilegeVerticalViewHolder(private val binding: LoyaltyPrivilegeBlockBinding) : RecyclerView.ViewHolder(binding.root){
        val marginBetweenBlock = itemView.context.resources.getDimensionPixelSize(R.dimen.loyalty_privilege_item_margin_between)
        val marginEdge = itemView.context.resources.getDimensionPixelSize(R.dimen.loyalty_privilege_item_margin_edge)
        private val privilegeItemAdapter by lazy { PrivilegeItemAdapter() }
        init {
            binding.rvPrivilegeItem.apply {
                layoutManager = LinearLayoutManager(itemView.context,
                    LinearLayoutManager.HORIZONTAL,false)
                adapter = privilegeItemAdapter
                addItemDecoration(PrivilegeItemDecoration(spacingBetween = marginBetweenBlock, spacingEdge = marginEdge, adapter = privilegeItemAdapter))
            }

            binding.tvViewMore.onClickDelay {
                item(absoluteAdapterPosition)?.let {
                    itemEventsListener?.onClickView(
                        position = absoluteAdapterPosition,
                        view = binding.tvViewMore,
                        data =  it
                    )

                }
            }

            privilegeItemAdapter.eventListener = object: IEventListener<BlockPrivilegeItem> {
                override fun onClickedItem(position: Int, data: BlockPrivilegeItem) {
                    itemEventsListener?.onClickedItem(position, data)
                }
            }
        }

        fun bind(data: BlockPrivilege) {
            binding.apply {
                tvHeader.checkToShowContent(data.name)
            }
            privilegeItemAdapter.bind(data.items)
        }


    }


    inner class PrivilegeItemDecoration(
        private val spacingBetween: Int,
        private val spacingEdge: Int = 0,
        private val adapter: RecyclerView.Adapter<*>
    ): RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            val position = parent.getChildAdapterPosition(view)  // item position
            val childCount = adapter.itemCount

            when (position) {
                0 -> {
                    outRect.left =
                        spacingEdge
                    outRect.right =
                        spacingBetween
                }
               childCount - 1 -> {
                    outRect.right = spacingEdge
                }
                else -> {
                    outRect.right = spacingBetween
                }
            }

        }
    }
}