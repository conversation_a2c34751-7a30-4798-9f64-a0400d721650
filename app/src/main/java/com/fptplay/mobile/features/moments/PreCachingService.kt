package com.fptplay.mobile.features.moments

import android.content.Context
import android.net.Uri
import androidx.annotation.OptIn
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.common.utils.Constants
import androidx.media3.common.MediaItem
import androidx.media3.exoplayer.offline.Downloader
import androidx.media3.exoplayer.offline.ProgressiveDownloader
import androidx.media3.common.StreamKey
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.exoplayer.dash.offline.DashDownloader
import androidx.media3.exoplayer.hls.offline.HlsDownloader
import com.xhbadxx.projects.module.util.logger.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.withContext

private const val TAG = "PreCachingService"

@OptIn(UnstableApi::class)
class PreCachingService(appContext: Context, params: WorkerParameters) : CoroutineWorker(appContext, params) {

    companion object {
        private const val PRE_CACHE_SIZE = 20 * 1024 * 1024L  //https://jira.fptplay.net/browse/ANRMOBI-2615 - edit 20Mb
    }

    private var cacheDataSourceFactory: CacheDataSource.Factory? = null
    private val simpleCache = MainApplication.simpleCache


    override suspend fun doWork(): Result = coroutineScope {
        cacheDataSourceFactory = DataSourceUtils.buildCacheDataSourceFactory(
            cache = simpleCache,
            applicationContext = applicationContext,
            listener = object : CacheDataSource.EventListener {
                override fun onCachedBytesRead(cacheSizeBytes: Long, cachedBytesRead: Long) {
                    //Logger.d("$TAG -> onCachedBytesRead. cacheSizeBytes:$cacheSizeBytes, cachedBytesRead: $cachedBytesRead")
                }

                override fun onCacheIgnored(reason: Int) {
                    //Logger.d("$TAG -> onCacheIgnored. reason:$reason")
                }

            }
        )

        val dataList = inputData.getStringArray(Constants.PRE_CACHING_KEY_LIST_DATA)

        val jobs = dataList?.map { data ->
            async {
                if (data.isNotEmpty()) {
                    val dataUri = Uri.parse(data)
                    cacheDataSourceFactory?.let {
                        val downloader = getDownloader(dataUri, it)
                        downloader?.let {
                            preCacheVideo(downloader, dataUri)
                        }
                    }
                }
            }
        }
        jobs?.joinAll()
        Result.success()
    }


    private suspend fun preCacheVideo(downloader: Downloader, uri: Uri) =
        withContext(Dispatchers.IO) {
            runCatching {
                // do nothing if already cache enough
                if (simpleCache?.isCached(uri.toString(), 0, PRE_CACHE_SIZE) == true) {
                    //Logger.d("$TAG -> video has been cached, return")
                    return@runCatching
                }

                //Logger.d("$TAG -> start pre-caching for uri: $uri")

                downloader.download { contentLength, bytesDownloaded, percentDownloaded ->
                    if (bytesDownloaded >= PRE_CACHE_SIZE) downloader.cancel()
                    //Logger.d("$TAG -> uri: $uri, contentLength: $contentLength, bytesDownloaded: $bytesDownloaded, percentDownloaded: $percentDownloaded")
                }
            }.onFailure {
                if (it is InterruptedException) return@onFailure

                Logger.d("$TAG -> Cache fail for uri: $uri with exception: $it}")
                it.printStackTrace()
            }.onSuccess {
                Logger.d("$TAG -> Cache success for uri: $uri")
            }
            Unit
        }



    private val cacheStreamKeys = emptyList<StreamKey>()

    private fun getDownloader(uri: Uri, cacheDataSourceFactory: CacheDataSource.Factory): Downloader? {
        val url = uri.toString()
        return when {
            url.contains(".m3u8") -> {
                HlsDownloader(
                    MediaItem.Builder()
                        .setStreamKeys(cacheStreamKeys)
                        .setUri(uri)
                        .build(),
                    cacheDataSourceFactory
                )
            }
            url.contains(".mpd") -> {
                DashDownloader(
                    MediaItem.Builder()
                        .setStreamKeys(cacheStreamKeys)
                        .setUri(uri)
                        .build(),
                    cacheDataSourceFactory
                )
            }
            url.contains(".mp4") -> {
                ProgressiveDownloader(
                    MediaItem.Builder()
                        .setStreamKeys(cacheStreamKeys)
                        .setUri(uri)
                        .build(),
                    cacheDataSourceFactory
                )
            }
            else -> null
        }
    }
}