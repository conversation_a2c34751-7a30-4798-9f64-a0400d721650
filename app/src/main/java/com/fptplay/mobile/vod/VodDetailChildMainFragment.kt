package com.fptplay.mobile.vod

import android.content.Intent
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.annotation.DrawableRes
import androidx.annotation.UiThread
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import coil.load
import com.android.billingclient.api.BillingClient
import com.fplay.module.downloader.model.VideoTaskState
import com.fplay.module.downloader.utils.VideoDownloadUtils
import com.fptplay.dial.connection.models.transfer_model.ActionEventType
import com.fptplay.dial.model.FAndroidTVDeviceInfo
import com.fptplay.dial.model.FAndroidTVDeviceInfoExternal
import com.fptplay.dial.model.FBoxDeviceInfoV2
import com.fptplay.dial.model.FSamsungTVDeviceInfo
import com.fptplay.dial.model.FSamsungTVDeviceInfoExternal
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavVodChildDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.block.BlockItemAdapter
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.extensions.isAirlineLayout
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.onClick
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.models.NextActionEvent
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.utils.CheckBeforePlayUtil
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.DateTimeUtils
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.NetworkUtils
import com.fptplay.mobile.common.utils.StringUtils
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.TabLayoutItemBinding
import com.fptplay.mobile.databinding.VodDetailChildMainFragmentBinding
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.comment.views.CommentView
import com.fptplay.mobile.features.comment_v2.CommentV2ViewModel
import com.fptplay.mobile.features.download.DownloadUtils
import com.fptplay.mobile.features.download.DownloadUtils.readyDownload
import com.fptplay.mobile.features.game_emoji.utils.GameEmojiUtils
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.features.pairing_control.PairingControlConnectionHelper
import com.fptplay.mobile.features.pairing_control.model.RemotePlayerState
import com.fptplay.mobile.features.payment.PaymentViewModel
import com.fptplay.mobile.features.payment.google_billing.BillingClientLifecycleV6
import com.fptplay.mobile.features.payment.google_billing.BillingUtils.isEnableGoogleBilling
import com.fptplay.mobile.features.payment.util.PaymentTrackingUtil
import com.fptplay.mobile.features.tracking_appsflyer.TrackingAppsFlyerProxy
import com.fptplay.mobile.player.PlayerUtils
import com.fptplay.mobile.player.PlayerView
import com.fptplay.mobile.features.tracking_ga4.TrackingGA4Proxy
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.vod.adapter.VodTablayoutAdapter
import com.fptplay.mobile.vod.data.VodCommentInfo
import com.fptplay.mobile.vod.dialog.addViewRating
import com.fptplay.mobile.vod.views.VodCommentTypeView
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.game.gamevod.GameVOD
import com.xhbadxx.projects.module.domain.entity.fplay.payment.PackagePlan
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class VodDetailChildMainFragment :
    BaseFragment<VodDetailViewModel.VodDetailState, VodDetailViewModel.VodDetailIntent>() {
    companion object {
        const val ACTION_FOLLOW = "follow"
        const val ACTION_DOWNLOAD = "download"
    }
    private var _binding: VodDetailChildMainFragmentBinding? = null
    private val binding get() = _binding!!

    override val viewModel: VodDetailViewModel by activityViewModels()
    private val viewModelCommentV2: CommentV2ViewModel by activityViewModels()
    private val paymentViewModel by activityViewModels<PaymentViewModel>()

    private var vodTablayoutAdapter: VodTablayoutAdapter? = null
    private val momentAdapter by lazy {
        BlockItemAdapter(
            binding.root.context,
            BlockItemAdapter.Type.VerticalSliderVideo
        )
    }
    private var isFollow = false
    private var curEpisode: Details.Episode? = null
    private var nextActionEvent: NextActionEvent? = null

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    private var paymentAlertDialog: AlertDialog? = null
    private var billingClientLifecycle: BillingClientLifecycleV6? = null
    private var selectedPlan: PackagePlan? = null

    @Inject
    lateinit var sharedPreferences: SharedPreferences


    private var isFirstTimePlay = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.saveDataDetail(null)
        viewModel.saveCurrentEpisode(null)
        viewModelCommentV2.saveMeta(null)
        viewModelCommentV2.saveDataCom(null)
        viewModelCommentV2.saveCommentPagePosition(null)
        viewModel.saveMomentDetail(null)
    }

    override fun onResume() {
        super.onResume()
        if (context.isTablet()) {
            viewModel.isFullScreen.value?.let {
                if (_binding != null) {
                    binding.clVodInfo.isVisible = !it.second
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = VodDetailChildMainFragmentBinding.inflate(inflater, container, false)
        Logger.d("VodDetailChildMainFragment >> onCreateView")
        return binding.root
    }

    override fun bindData() {
        if (viewModel.getDataDetail() == null) {
            lifecycleScope.launch(Dispatchers.Main) {
                viewModel.dispatchIntent(
                    VodDetailViewModel.VodDetailIntent.GetDetail(
                        viewModel.getId(),
                        isPlayerCalled = false
                    )
                )
            }
            viewModel.dispatchIntent(
                VodDetailViewModel.VodDetailIntent.GetVodCheckFollowSuggest(
                    viewModel.getId(),
                    "vod"
                )
            )
        } else {
            if(_binding != null) {
                updateComment(
                    viewModelCommentV2.getCommentTypeList()
                ) {
                    lifecycleScope.launch(Dispatchers.IO) {
                        delay(100)
                        try {
                            binding.vCommentType.post {
                                if (_binding != null) {
                                    binding.vCommentType.scrollToPosition(viewModelCommentV2.getLastCommentTypeClickPosition())
                                    viewModelCommentV2.saveLastCommentTypeClickPosition(0)
                                }
                            }
                        } catch (ex: Exception) {
                            ex.printStackTrace()
                        }
                    }
                }
                updateDetailUI()
                updateMomentUI()
            }
        }
    }

    override fun bindComponent() {

        binding.rcvMoment.apply {
            adapter = momentAdapter
            layoutManager = LinearLayoutManager(binding.root.context, LinearLayoutManager.HORIZONTAL, false)
        }
        binding.vpItem.isSaveFromParentEnabled = false
        initGoogleBilling()
    }

    override fun onDestroyView() {
        // Pairing Control
        MainApplication.INSTANCE.pairingConnectionHelper.removePlayerStateListener(listener = remotePlayerState)
        //
        super.onDestroyView()
        vodTablayoutAdapter = null
        _binding = null
    }

    override fun bindEvent() {
        binding.vCommentType.setTrackingInfo(trackingInfo)
        binding.vCommentType.setTooltipParentView(binding.clVodInfo)
        binding.vCommentType.setViewListener(object : VodCommentTypeView.VodCommentTypeViewListener {
            override fun onViewTouch() {
                viewModelCommentV2.dispatchIntent(CommentV2ViewModel.CommentIntent.CancelCommentSlideAnimation)
            }

            override fun onPageSelected(position: Int) {
                viewModelCommentV2.saveCommentPagePosition(position)
            }
        })

        if (context.isTablet()) {
            binding.root.onClickDelay { }
        }
        binding.tlTitle.onClickDelay { navigateToVodPeopleFragment() }
        binding.vDownTitle.onClickDelay { navigateToVodPeopleFragment() }
        binding.vCommentType.setItemClickListener(
            object : IEventListener<VodCommentInfo> {

                override fun onClickView(position: Int, view: View?, data: VodCommentInfo) {
                    viewModelCommentV2.saveLastCommentTypeClickPosition(position)
                    when (data.commentType) {
                        VodCommentInfo.VodCommentType.VOD_COMMENT_TYPE_NORMAL -> {
                            when(view?.id) {
                                R.id.tv_retry -> {
                                    viewModelCommentV2.dispatchIntent(
                                        CommentV2ViewModel.CommentIntent.GetCommentInfo(
                                            viewModel.getId(),
                                            isRetry = true
                                        )
                                    )
                                }
                                else -> {
                                    if (data.commentData.statusInfo.status == VodCommentInfo.Status.Success) {
                                        if(NetworkUtils.isNetworkAvailable()){
                                            navigateToCommentFragment()
                                        } else {
                                            showWarningDialog(<EMAIL>(R.string.error_no_intent))
                                        }
                                    }
                                }
                            }
                        }
                        VodCommentInfo.VodCommentType.VOD_COMMENT_TYPE_AI -> {
                            when(view?.id) {
                                R.id.ic_question_mark -> {
                                    binding.vCommentType.showTooltipView(position)
                                }

                                else -> {
                                    if (sharedPreferences.userLogin()) {
                                        if(NetworkUtils.isNetworkAvailable()){
                                            navigateToAICommentFragment()
                                        } else {
                                            showWarningDialog(<EMAIL>(R.string.error_no_intent))
                                        }
                                    } else {
                                        parentFragment?.parentFragment?.navigateToLoginWithParams()
                                    }
                                }
                            }
                        }
                    }
                }
            }
        )

        momentAdapter.eventListener = object : IEventListener<BaseObject> {
            override fun onClickedItem(position: Int, data: BaseObject) {
                when (PlayerUtils.getPlayingType()) {
                    is PlayerView.PlayingType.Cast -> {
                        MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                            navHostFragment = activity?.findNavHostFragment(),
                            onStopCastAndNavigate = {
                                MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
//                                checkBeforePlayUtil.navigateToSelectedContent(data, CheckBeforePlayUtil.SourceScreen.VodDetail)
                                checkBeforePlayUtil.navigateToSelectedContent(data, CheckBeforePlayUtil.NavExtraData.NavMomentData(
                                    sourceScreen = CheckBeforePlayUtil.SourceScreen.VodDetail,
                                    relatedId = viewModel.getId()
                                ))
                            },
                            onNavigate = {
//                                checkBeforePlayUtil.navigateToSelectedContent(data, CheckBeforePlayUtil.SourceScreen.VodDetail)
                                checkBeforePlayUtil.navigateToSelectedContent(data, CheckBeforePlayUtil.NavExtraData.NavMomentData(
                                    sourceScreen = CheckBeforePlayUtil.SourceScreen.VodDetail,
                                    relatedId = viewModel.getId()
                                ))
                            },
                            onCancel = {}
                        )
                    }
                    else -> {
//                        checkBeforePlayUtil.navigateToSelectedContent(data, CheckBeforePlayUtil.SourceScreen.VodDetail)
                        checkBeforePlayUtil.navigateToSelectedContent(data, CheckBeforePlayUtil.NavExtraData.NavMomentData(
                            sourceScreen = CheckBeforePlayUtil.SourceScreen.VodDetail,
                            relatedId = viewModel.getId()
                        ))
                    }
                }
            }
        }

        binding.llFollow.onClickDelay {
            if (isFollow) {
                //if (viewModel.getDataDetail()?.blockContent?.isComingSoon != true) {
                viewModel.dispatchIntent(
                    VodDetailViewModel.VodDetailIntent.GetVodDeleteFollowSuggest(
                        viewModel.getId(),
                        "vod"
                    )
                )
                //}
            } else {
                viewModel.dispatchIntent(
                    VodDetailViewModel.VodDetailIntent.GetVodAddFollowSuggest(
                        viewModel.getId(),
                        "vod"
                    )
                )
            }
        }
        binding.llShare.onClick(delayBetweenClick = if (context.isTablet()) 500L else 200L) {
            viewModel.getDataDetail()?.blockContent?.webUrl?.let {
                onShareLink(it)
            }
        }
        binding.llDownload.onClickDelay {
            viewModel.currentEpisode()?.let { episode ->
                findEpisodeIndex(episode)?.let { episodeIndex ->
                    viewModel.triggerDownloadClick(episode, episodeIndex, this.tag)
                }
            }
        }

        binding.tlMenu.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                if(_binding!=null){
                    binding.vpItem.currentItem = tab?.position ?: 0
                }
                lifecycleScope.launch(Dispatchers.Main) {
                    viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.DetailDataChanged)
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
        updateFollowButton()
        observeData()
        bindEventFragmentResult()

        // Pairing Control
        MainApplication.INSTANCE.pairingConnectionHelper.addPlayerStateListener(listener = remotePlayerState)
        //
    }

    override fun observeState() {
        super.observeState()
        viewModelCommentV2.state.observe(viewLifecycleOwner) {
            when (it) {
                is CommentV2ViewModel.CommentState.ResultCommentTypeLoadDone -> {
                    Logger.d("VODDetailFragment >> LogDetail observe comment type child")
                    updateComment(
                        viewModelCommentV2.getCommentTypeList()
                    )
                }

                is CommentV2ViewModel.CommentState.CommentTypeAutoSlideAnimation -> {
                    if(_binding != null) {
                        binding.vCommentType.scrollToPosition(position = it.targetPosition, isSmoothScroll = true)
                    }
                }

                else -> {}
            }
        }
        viewModel.playVod.observe(viewLifecycleOwner) {
            it?.run {
                binding.appBar.setExpanded(true, true)
            }
        }
        if (context.isTablet()) {
            viewModel.isFullScreen.observe(this) {
                it?.let {
                    if (_binding != null) {
                        binding.clVodInfo.isVisible = !it.second
                    }

                    viewModelCommentV2.getCommentPagePosition()?.let { position ->
                        binding.vCommentType.selectPage(position)

                    }
                }
            }
        }
        handlePaymentState()
        viewModel.playerChanged.observe(viewLifecycleOwner) {
            it?.let {
                handleWhenPlayerChanged(it)
            }
        }
    }

    private fun handleWhenPlayerChanged(it: Details.Episode?) {
        Logger.d("LogDetail observe playerChange")
        val lastEpisode = curEpisode
        curEpisode = it
        if (viewModel.isPlaylistContent()) { // Playlist
            binding.tvTitle.text = viewModel.getDataDetail()?.blockContent?.titleVietnam ?: ""

            if (lastEpisode != null && lastEpisode.vodId != curEpisode?.vodId) {
                Logger.d("LogDetail observe playerChange id: ${viewModel.getId()}")
                viewModel.dispatchIntent(
                    VodDetailViewModel.VodDetailIntent.GetDetail(
                        viewModel.getId(),
                        isPlayerCalled = false
                    )
                )
                viewModel.dispatchIntent(
                    VodDetailViewModel.VodDetailIntent.GetVodCheckFollowSuggest(
                        viewModel.getId(),
                        "vod"
                    )
                )
            }
        } else {
            if (viewModel.getDataDetail()?.blockContent?.episodeType == 0) {
                binding.tvTitle.text = viewModel.getDataDetail()?.blockContent?.titleVietnam ?: ""
            } else {
                binding.tvTitle.text = <EMAIL>(
                    R.string.vod_title_pattern,
                    viewModel.getDataDetail()?.blockContent?.titleVietnam ?: "",
                    curEpisode?.titleVietnam ?: ""
                )
            }
        }
        curEpisode?.let {
            viewModel.getDataDetail()?.let { detail ->
                viewModel.dispatchIntent(
                    VodDetailViewModel.VodDetailIntent.GetDownloadStage(
                        detail.blockContent.id,
                        it
                    )
                )
            }
        }
    }

    override fun VodDetailViewModel.VodDetailState.toUI() {
        when (this) {
            is VodDetailViewModel.VodDetailState.Loading -> {
                when (data) {
                    is VodDetailViewModel.VodDetailIntent.GetDetail -> {
                        showLoading()
                    }
                    else -> {}
                }
            }
            is VodDetailViewModel.VodDetailState.ResultDetail -> {
                hideLoading()
                Logger.d("VODDetailChildMainFragment >> ResultDetail")
                if ((viewModel.getDataDetail()?.blockContent?.id != data.blockContent.id) && !viewModel.isPlaylistContent()) {
                    viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.TriggerStopPlayer)
                }
                if (viewModel.isPlaylistContent()) {
                    viewModel.getPlaylist()?.videos?.let { this.data.blockEpisode.episodes = it }
                }
                viewModel.saveDataDetail(this.data)
                if (!isAirlineLayout()) {
                    val isPlayerCalled =
                        this.intent is VodDetailViewModel.VodDetailIntent.GetDetail && !this.intent.isPlayerCalled
                    if (!isPlayerCalled) {
                        this.data.blockMoment?.let {
                            if (it.id.isNotEmpty()) {
                                updateMomentData(it.id, it.style.id, it.itype, relatedId = viewModel.getId())
                            }
                        }
                    }
                }
                updateDetailUI(isPlayerCalled = this.intent is VodDetailViewModel.VodDetailIntent.GetDetail && this.intent.isPlayerCalled)
                updateFollowButton()
                if(this.intent is VodDetailViewModel.VodDetailIntent.GetDetail && this.intent.isPlayerCalled) {
                    viewModelCommentV2.dispatchIntent(
                        CommentV2ViewModel.CommentIntent.UpdateAiCommentData(
                            isSuccess = true,
                            vodId = viewModel.getDataDetail()?.blockContent?.id ?: "",
                            vodCommentInfo = if(viewModel.getDataDetail()?.blockContent?.isEnableSummaryComment == true) {
                                VodCommentInfo(
                                    userAvatar = "",
                                    commentType = VodCommentInfo.VodCommentType.VOD_COMMENT_TYPE_AI,
                                    commentData = VodCommentInfo.VodCommentData(
                                        commentAiContent = "",
                                        statusInfo = VodCommentInfo.CommentStatus(status = VodCommentInfo.Status.Success)
                                    )
                                )
                            } else {
                                null
                            }
                        )
                    )
                }

            }
            is VodDetailViewModel.VodDetailState.ResultRatingData->{
                Logger.d("trangtest ===== call api ResultRatingData")
                loadDataRating()
            }
            is VodDetailViewModel.VodDetailState.ResultStream -> {
                if (isAirlineLayout() && isFirstTimePlay) {
                    isFirstTimePlay = false
                    viewModel.currentEpisode()?.let {
                        viewModel.getDataDetail()?.let { detail ->
                            viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.CheckDownloadReadyForAirline(detail.blockContent.id, it))
                        }
                    }
                }
            }
            is VodDetailViewModel.VodDetailState.ResultVodCheckFollowSuggest -> {
                isFollow = data.status == 1
                updateFollowButton()
                viewModel.saveFollow(if (isFollow) "1" else "0")

                nextActionEvent?.let { event ->
                    if (event.action == NextActionEvent.Action.FOLLOW) {
                        invokeFollowAction(event)
                    }
                }
            }
            is VodDetailViewModel.VodDetailState.ResultVodAddFollowSuggest -> {
                isFollow = data.status == 1
                updateFollowButton()
                viewModel.saveFollow(if (isFollow) "1" else "0")

                // Pairing control
                if (PlayerUtils.getPlayingType() == PlayerView.PlayingType.Cast) {
                    val pairingConnection = MainApplication.INSTANCE.pairingConnectionHelper
                    if (pairingConnection.isSessionRunning) {
                        pairingConnection.sendEventActionEvent(type = if (isFollow) ActionEventType.ADD_FOLLOW else ActionEventType.REMOVE_FOLLOW)
                    }
                }
                //


                // Tracking
                sendLogAddFollow()
            }
            is VodDetailViewModel.VodDetailState.ResultVodDeleteFollowSuggest -> {
                isFollow = data.status != 1
                updateFollowButton()
                viewModel.saveFollow(if (isFollow) "1" else "0")

                // Pairing control
                if (PlayerUtils.getPlayingType() == PlayerView.PlayingType.Cast) {
                    val pairingConnection = MainApplication.INSTANCE.pairingConnectionHelper
                    if (pairingConnection.isSessionRunning) {
                        pairingConnection.sendEventActionEvent(type = if (isFollow) ActionEventType.ADD_FOLLOW else ActionEventType.REMOVE_FOLLOW)
                    }
                }
                //

                // Tracking
                sendLogDeleteFollow()
            }
            is VodDetailViewModel.VodDetailState.ResultDownloadStage -> {
                checkDownload(this.episode, this.downloadStage, this.percent)
            }
            is VodDetailViewModel.VodDetailState.ResultCheckDownloadReadyForAirline -> {
                checkShowChooseWatchOrDownloadDialog(this.episode,this.downloadStage)
            }
            is VodDetailViewModel.VodDetailState.ResultGameVod -> {
                updateRankingButton(
                    if(GameEmojiUtils.validGameEmojiV2(viewModel,data)) {
                        data.data
                    } else {
                        null
                    }
                )
            }
            is VodDetailViewModel.VodDetailState.ErrorRequiredLogin -> {
                when (data) {
                    is VodDetailViewModel.VodDetailIntent.UserRating->{
                        findNavController().navigateUp()
                        viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.TriggerPlayerState(true)) //play continue
                        parentFragment?.parentFragment?.navigateToLoginWithParams()
                    }
                    is VodDetailViewModel.VodDetailIntent.GetDetail,
                    is VodDetailViewModel.VodDetailIntent.CheckForDownload -> {
                        val extendsArgs:Bundle = bundleOf(Constants.VOD_DOWNLOAD_NAVIGATE_LOGIN_REQUEST_KEY to true)
                        parentFragment?.parentFragment?.navigateToLoginWithParams(title = this.message,extendsArgs = extendsArgs)
                    }
                    is VodDetailViewModel.VodDetailIntent.GetVodAddFollowSuggest->{
                        val extendsArgs:Bundle = bundleOf(
                            Constants.VOD_FOLLOW_NAVIGATE_LOGIN_REQUEST_KEY to true,
                            Constants.VOD_FOLLOW_NAVIGATE_LOGIN_VALUE to true,
                        )
                        parentFragment?.parentFragment?.navigateToLoginWithParams(title = this.message,extendsArgs = extendsArgs)
                    }
                    is VodDetailViewModel.VodDetailIntent.GetVodDeleteFollowSuggest -> {
                        val extendsArgs:Bundle = bundleOf(
                            Constants.VOD_FOLLOW_NAVIGATE_LOGIN_REQUEST_KEY to true,
                            Constants.VOD_FOLLOW_NAVIGATE_LOGIN_VALUE to false,
                        )
                        parentFragment?.parentFragment?.navigateToLoginWithParams(title = this.message,extendsArgs = extendsArgs)
                    }
                    else -> {}
                }
            }
            is VodDetailViewModel.VodDetailState.ResultListMoment -> {
                viewModel.saveMomentDetail(data)
                updateMomentUI()
            }
            is VodDetailViewModel.VodDetailState.Done -> {
                when (intent) {
                    is VodDetailViewModel.VodDetailIntent.GetDetail -> {}
                    else -> {}
                }
            }
            is VodDetailViewModel.VodDetailState.Error -> {
                when (data) {
                    is VodDetailViewModel.VodDetailIntent.GetDetail -> {
                        hideLoading()
                    }
                    is VodDetailViewModel.VodDetailIntent.GetGameVod -> {
                        updateRankingButton(null)
                    }
                    is VodDetailViewModel.VodDetailIntent.GetListMoment -> {
                        Timber.d("thien test api moment error")
                        viewModel.saveMomentDetail(null)
                        updateMomentUI()
                    }
                    else -> {}
                }
            }
            else -> {}
        }
    }
    private fun loadDataRating(){
        Logger.d("trangtest ============================ loadDataRating")
        binding.llRatingGroup.removeAllViews()
        viewModel.ratingData?.let { data->
            if(data.status == 1){//success
                data.data.content.forEach {item->
                    binding.llRatingGroup.addViewRating(data = item, userRate = data.data.rateOfUser,
                        sharedPreferences = sharedPreferences, userLogin = sharedPreferences.userLogin())
                    {
                        if(sharedPreferences.userLogin()) {
                            <EMAIL>?.parentFragment?.findNavController()?.navigateSafe(
                                VodDetailFragmentDirections.actionVodDetailFragmentToRatingBottomSheetDialogFragment(
                                    userRating = data.data.rateOfUser,
                                    countDes = item.countDescription,
                                    rateValue = item.avgRate
                                )
                            )
                        }else {
                            parentFragment?.parentFragment?.navigateToLoginWithParams()
                        }
                    }
                }
            }else{
                //khong get duoc data rating, khong hien thi
            }
        }


    }

    private fun navigateToVodPeopleFragment() {
        findNavController().navigateSafe(VodDetailChildMainFragmentDirections.actionVodDetailFragmentToVodPeopleFragment())
    }

    private fun navigateToCommentFragment() {
        findNavController().navigateSafe(
            NavVodChildDirections.actionGlobalDetailToCommentV2Fragment(contentId = viewModel.getId())
        )
    }

    private fun navigateToAICommentFragment() {
        findNavController().navigateSafe(
            VodDetailChildMainFragmentDirections.actionVodChildMainFragmentToSummaryCommentFragment(
                vodId = viewModel.getId(),
                firstLoad = viewModelCommentV2.getAiContent(viewModel.getDataDetail()?.blockContent?.id ?: "").isEmpty()
            )
        )
    }

    private fun updateDetailUI(isPlayerCalled: Boolean = false) {
        binding.cdlVod.show()
        viewModel.getDataDetail()?.let { vod ->
            if (!isPlayerCalled) {
                loadDataRating()
                binding.cdlVod.show()
                if (viewModel.isPlaylistContent()) {
                    binding.tvTitle.text = vod.blockContent.titleVietnam
                } else {
                    if (vod.blockContent.episodeType == 0) {
                        binding.tvTitle.text = vod.blockContent.titleVietnam
                    } else {
                        binding.tvTitle.text =
                            if (viewModel.currentEpisode() == null) vod.blockContent.<NAME_EMAIL>(
                                R.string.vod_title_pattern,
                                vod.blockContent.titleVietnam,
                                viewModel.currentEpisode()?.titleVietnam ?: ""
                            )
                    }
                }
                val text = StringUtils.getMetaText(
                    context = binding.root.context,
                    data = vod.blockContent
                )
                if (text.isNullOrBlank()) {
                    binding.tvVideoInfo.isVisible = false
                } else {
                    binding.tvVideoInfo.isVisible = true
                    binding.tvVideoInfo.text = text
                }

                if (vod.maturityRating.advisories.isBlank()) {
                    binding.tvAgeRestriction.gone()
                } else {
                    binding.tvAgeRestriction.text = vod.maturityRating.advisories
                    binding.tvAgeRestriction.show()
                }

                binding.tvShortDescription.apply {
                    isVisible = vod.blockContent.shortDescription.isNotBlank()
                    setText(vod.blockContent.shortDescription)
                }

//                val hideEpisodeTab = vod.blockContent.episodeType == 0 && vod.blockContent.episodeTotal == 1
                val hideEpisodeTab = false
                if (viewModel.isPlaylistContent()) {
                    if (vodTablayoutAdapter == null) {
                        updateVodTabLayout(vod, hideEpisodeTab)
                    }
                } else {
                    updateVodTabLayout(vod, hideEpisodeTab)
                }

                //handle appbar scroll in airline layout
                if (isAirlineLayout()) {
                    setOnScroll(scrollable = !vodTablayoutAdapter?.tabName.isNullOrEmpty())
                }
                lifecycleScope.launch(Dispatchers.Main) {
                    viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.DetailDataChanged)
                }
                viewModel.dispatchIntent(
                    VodDetailViewModel.VodDetailIntent.GetDownloadChaptersByMovieId(
                        viewModel.getId()
                    )
                )

                // Check stage download for current movie episode
                viewModel.getDataDetail()?.let { detail ->
                    curEpisode?.let {
                        viewModel.dispatchIntent(
                            VodDetailViewModel.VodDetailIntent.GetDownloadStage(
                                detail.blockContent.id,
                                it
                            )
                        )
                    }
                }

                updateRankingButton(viewModel.currentGameVod())
            }
        }
    }

    private fun updateFollowButton() {
        binding.ivFollow.isSelected = isFollow
        if (viewModel.getDataDetail()?.blockContent?.isComingSoon == true) {
            binding.ivFollow.isActivated = true
            binding.tvFollow.text = if (isFollow) {
                getString(R.string.vod_scheduled_premiere)
            } else getString(R.string.vod_schedule_premiere)
        } else {
            binding.ivFollow.isActivated = false
            binding.tvFollow.text = if (isFollow) {
                getString(R.string.vod_unfollow)
            } else getString(R.string.vod_follow)
        }
    }

    private fun updateVodTabLayout(vod: Details, hideEpisodeTab: Boolean) {
        vodTablayoutAdapter = VodTablayoutAdapter(
            binding.root.context,
            vod,
            childFragmentManager,
            lifecycle,
            isPlaylist = viewModel.isPlaylistContent(),
            isAirlineLayout = isAirlineLayout()
        )
        binding.vpItem.apply {
            adapter = vodTablayoutAdapter
            if (vodTablayoutAdapter?.tabName.isNullOrEmpty() && isAirlineLayout()) {
                this.hide()
                binding.flMenu.hide()
            }
        }

        TabLayoutMediator(binding.tlMenu, binding.vpItem) { tab, position ->
            if (context.isTablet()) {
                tab.customView = TabLayoutItemBinding.inflate(layoutInflater).root
            }
            tab.text = vodTablayoutAdapter?.tabName?.get(position)
            tab.view.setOnLongClickListener { true }
        }.attach()
    }

    private fun observeData() {
        viewModel.updateDownloadState.observe(viewLifecycleOwner) {
            it?.let { onDownloadState ->
                findEpisodeIndex(viewModel.currentEpisode())?.let { index ->
                    if (onDownloadState.chapterIdx.equals(index.toString())) {
                        updateDownloadButtonStatus(status = it.state)
                    }
                }
            }
        }
        viewModel.updateDownloadProcess.observe(viewLifecycleOwner) {
            it?.let { onDownloadProcess ->
                findEpisodeIndex(viewModel.currentEpisode())?.let { index ->
                    if (onDownloadProcess.chapterIdx.equals(index.toString())) {
                        updateDownloadButtonStatus(status = it.state, percentage = it.progress)
                    }
                }
            }
        }
    }

    private fun reloadDataRating(){
        Logger.d("trangtest get rating === reload in voddetail child main")
        viewModel.getDataDetail()?.let {
            viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.GetRating(itemId = it.blockContent.id, refId = it.blockContent.refId))
        }
    }
    private fun bindEventFragmentResult() {

        parentFragment?.parentFragment?.run {

            setFragmentResultListener(Constants.SUMMARY_COMMENT_TEXT_UPDATE_KEY){ _, bundle ->
                Logger.d("VodDetailChildMainFragment > setFragmentResultListener ${Constants.SUMMARY_COMMENT_TEXT_UPDATE_KEY}")
                val textUpdate = bundle.getString(Constants.SUMMARY_COMMENT_TEXT_UPDATE_VALUE, "")
                viewModelCommentV2.dispatchIntent(CommentV2ViewModel.CommentIntent.TextAIUpdate(
                    vodId = viewModel.getId(),
                    content = textUpdate
                ))
            }

            setFragmentResultListener(Utils.RATING_RESULT_EVENT){ _, bundle ->
                when(bundle.getString(Utils.RATING_EVENT, "")){
                    Utils.RATING_NEED_RELOAD->{
                        //reloadDataRating()
                        loadDataRating()
                    }
                    Utils.RATING_EXIT->{}
                    else ->{}
                }
            }
            setFragmentResultListener(Utils.DOWNLOAD_OPTION_DIALOG_DOWNLOAD_TYPE) { _, bundle ->
                val message =
                    when (bundle.getString(Utils.DOWNLOAD_OPTION_DIALOG_CHOSEN_OPTION_ID_KEY, "")) {
                        Utils.DOWNLOAD_OPTION_DIALOG_OPTION_WATCH_NOW_ID -> {
                            // Watch VOD
                            "Choose Watch now"
                        }
                        Utils.DOWNLOAD_OPTION_DIALOG_OPTION_DOWNLOAD_ID -> {
                            // Download VOD
                            binding.llDownload.performClick()
                            "Choose Download"
                        }
                        else -> {
                            "Choose something else"
                        }
                    }
                Timber.d(message)
            }

            setFragmentResultListener(Utils.VOD_ACTOR_FILM) { _, bundle ->
                val isBackFromFilmActor = bundle.getBoolean(Utils.VOD_ACTOR_FILM_KEY, false)
                if (isBackFromFilmActor) {
                    viewModel.triggerPlayVod(viewModel.getId())
                    viewModelCommentV2.dispatchIntent(CommentV2ViewModel.CommentIntent.RefreshCommentData(vodId = viewModel.getId()))
                }
            }
            setFragmentResultListener(Constants.LOGIN_SUCCESS) { _, bundle ->
                val isSuccess = bundle.getBoolean(Constants.LOGIN_SUCCESS_KEY, false)
                if (isSuccess) {
                    bindData()
                    if (context.isTablet()) {
                        viewModel.triggerPlayVod(viewModel.getId())
                    } else {
                        reloadDataRating()
                        viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.GetVodCheckFollowSuggest(viewModel.getId(), "vod"))
                        viewModelCommentV2.dispatchIntent(
                            CommentV2ViewModel.CommentIntent.GetCommentInfo(
                                viewModel.getId()
                            )
                        )
                    }
                    val extensionBundle = bundle.getBundle(Constants.EXTENDS_ARG_NAVIGATE_LOGIN_KEY)?:Bundle()
                    // Preview
                    val nextAction = if (viewModel.hasPreview()) {
                        val previewShouldNavigateToPayment = extensionBundle.getBoolean(Constants.PREVIEW_SHOULD_NAVIGATE_TO_PAYMENT_KEY,false)
                        if (previewShouldNavigateToPayment) {
                            NextActionEvent(NextActionEvent.Type.PREVIEW, NextActionEvent.Action.GO_TO_PAYMENT, viewModel.getId(), Bundle().apply {
                                putBoolean(Constants.PREVIEW_SHOULD_NAVIGATE_TO_PAYMENT_VALUE, true)
                            })
                        } else null
                    } else null
                    viewModel.saveNextActionEvent(action = nextAction)
                    //
                    val isHandleFollow = extensionBundle.getBoolean(Constants.VOD_FOLLOW_NAVIGATE_LOGIN_REQUEST_KEY,false)
                    val valueFollow = extensionBundle.getBoolean(Constants.VOD_FOLLOW_NAVIGATE_LOGIN_VALUE,false)
                    if(isHandleFollow){
                        nextActionEvent = NextActionEvent(NextActionEvent.Type.FOLLOW, NextActionEvent.Action.FOLLOW, viewModel.getId(), Bundle().apply {
                            putBoolean(Constants.VOD_FOLLOW_VALUE, valueFollow)
                        })
                    }
                    // case : download vod when login successful (not confirmed by production)
//                    val isHandleDownload = extensionBundle.getBoolean(Constants.VOD_DOWNLOAD_NAVIGATE_LOGIN_REQUEST_KEY,false)
//                    if(eventDownLoad){
//                        actionEventDownloadNext = ActionEventNext(type = ACTION_DOWNLOAD, eventId = viewModel.getId(), value = valueDownload)
//                    }
                    handleActionAfterLogin(extensionBundle)
                } else {
                    val extensionBundle = bundle.getBundle(Constants.EXTENDS_ARG_NAVIGATE_LOGIN_KEY)?:Bundle()
                    handleActionAfterLogin(extensionBundle)
                }
            }
        }
    }

    private fun handleActionAfterLogin(extensionBundle: Bundle) {
        val actionAfterLogin = extensionBundle.getString(Constants.ACTION_AFTER_LOGIN_KEY)
        when(actionAfterLogin) {
            Constants.ACTION_OPEN_COMMENT -> {
                if (_binding != null) {
                    binding.root.postDelayed( {
                        navigateToCommentFragment()
                    },500)
                }
            }
        }
    }

    //region actionNext
    private fun invokeFollowAction(actionEvent: NextActionEvent) {
        if(actionEvent.relatedContentId == viewModel.getId()) {
            if (actionEvent.data.getBoolean(Constants.VOD_FOLLOW_VALUE, false)) {
                viewModel.dispatchIntent(
                    VodDetailViewModel.VodDetailIntent.GetVodAddFollowSuggest(
                        viewModel.getId(),
                        "vod"
                    )
                )
            } else {
//                if (viewModel.getDataDetail()?.blockContent?.isComingSoon != true) {
                viewModel.dispatchIntent(
                    VodDetailViewModel.VodDetailIntent.GetVodDeleteFollowSuggest(
                        viewModel.getId(),
                        "vod"
                    )
                )
//                }
            }
        }
        nextActionEvent = null
    }

    //endregion actionNext
    private fun onClickDownloadButton() {
        viewModel.currentEpisode()?.let { episode ->
            findEpisodeIndex(episode)?.let { episodeIndex ->
                viewModel.triggerDownloadClick(episode, episodeIndex, binding.llDownload.tag)
            }
        }
    }
    // region Download
    private fun checkDownload(
        episode: Details.Episode,
        downloadStage: Int,
        percent: Float
    ) {
        Timber.tag("tamlog")
            .d("---checkDownload ${DownloadUtils.canDownload(episode, isAirlineLayout(), viewModel.isPlaylistContent(), profileEnableDownload = MultiProfileUtils.profileEnabledDownload(sharedPreferences))}")
        if (DownloadUtils.canDownload(episode, isAirlineLayout(), viewModel.isPlaylistContent(), profileEnableDownload = MultiProfileUtils.profileEnabledDownload(sharedPreferences))) {
            updateDownloadButtonStatus(downloadStage, if (percent > 0f) VideoDownloadUtils.getPercent(percent) else "")
        } else {
            updateDownloadButton(status = VideoTaskState.ERROR)
        }
    }

    private fun checkShowChooseWatchOrDownloadDialog(episode: Details.Episode,
                                                     downloadStage: Int) {
        if (DownloadUtils.canDownload(episode, isAirlineLayout(), viewModel.isPlaylistContent(), profileEnableDownload = MultiProfileUtils.profileEnabledDownload(sharedPreferences))) {
            if (downloadStage.readyDownload()) {
                if (isAirlineLayout() && (context.isTablet() || MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT)) {
                    showChooseWatchOrDownloadDialog()
                }
            }
        }
    }

    private fun showChooseWatchOrDownloadDialog() {
        VodDialogPopupHelper.needShowDialogPopup(VodDialogPopupHelper.PriorityDialog(VodDialogPopupHelper.Priority.NORMAL) {
            <EMAIL>?.parentFragment?.findNavController()?.navigateSafe(
                VodDetailFragmentDirections.actionVodDetailFragmentToDownloadOptionBottomSheetDialogFragment(
                    type = Utils.DOWNLOAD_OPTION_DIALOG_DOWNLOAD_TYPE,
                    title = getString(R.string.download_choose_dialog_title),
                    optionOneText = getString(R.string.download_choose_dialog_option_play),
                    optionOneValue = Utils.DOWNLOAD_OPTION_DIALOG_OPTION_WATCH_NOW_ID,
                    optionTwoText = getString(R.string.download_choose_dialog_option_down),
                    optionTwoValue = Utils.DOWNLOAD_OPTION_DIALOG_OPTION_DOWNLOAD_ID,
                )
            )
        })
    }

    @UiThread
    private fun updateDownloadButtonStatus(status: Int?, percentage: String = "") {
        if (status == null) {
            updateDownloadButton(status = VideoTaskState.ERROR)
            return
        }
        when (status) {
            VideoTaskState.DEFAULT -> {
                updateDownloadButton(R.drawable.ic_download, getString(R.string.download_button_download_text), status)
            }
            VideoTaskState.PREPARE,
            VideoTaskState.PENDING,
            VideoTaskState.START,
            VideoTaskState.DOWNLOADING -> {
                updateDownloadButton(
                    R.drawable.ic_downloading,
                    if (percentage.isNotEmpty()) // percentage > 0
                        getString(R.string.download_button_downloading_with_percentage_text, percentage)
                    else
                        getString(R.string.download_button_downloading_text),
                    status
                )
            }
            VideoTaskState.PAUSE -> {
                updateDownloadButton(R.drawable.ic_download, getString(R.string.download_button_pause_text), status)
            }
            VideoTaskState.ERROR -> {
                updateDownloadButton(
                    R.drawable.ic_download_error,
                    getString(R.string.download_button_error_text),
                    status
                )
            }
            VideoTaskState.SUCCESS -> {
                updateDownloadButton(
                    R.drawable.ic_downloaded,
                    getString(R.string.download_button_downloaded_text),
                    status
                )
            }
            else -> {
                updateDownloadButton(R.drawable.ic_download, getString(R.string.download_button_download_text), status)
            }
        }
    }

    @UiThread
    private fun updateDownloadButton(@DrawableRes icon: Int? = null, text: String? = null, status: Int) {
        _binding?.apply {
            if (icon == null || text == null) {
                llDownload.hide()
                return
            }
            if (!isAirlineLayout()) {
                if (status == VideoTaskState.DOWNLOADING || status == VideoTaskState.PREPARE || status == VideoTaskState.START || status == VideoTaskState.PENDING) {
                    animDownloading.show()
                    ivDownload.hide()
                    if (!animDownloading.isAnimating) {
                        animDownloading.playAnimation()
                    }
                } else {
                    animDownloading.hide()
                    ivDownload.show()
                    animDownloading.cancelAnimation()
                }
            }
            ivDownload.load(icon)
            tvDownload.text = text
            llDownload.tag = status
            llDownload.show()
        }
    }

    private fun findEpisodeIndex(episode: Details.Episode?): Int? {
        if(episode == null) return null
        val listEpisode = viewModel.getDataDetail()?.blockEpisode?.episodes ?: listOf()
        val episode = listEpisode.firstOrNull { it.id == episode.id }
        episode?.let {
            return it.id.toIntOrNull()
        }
        return null
    }


    private fun updateComment(
        commentData: List<VodCommentInfo>,
        callback: () -> Unit = {}
    ) {
        if (commentData.isNotEmpty()) {
            binding.vCommentType.show()
            binding.vCommentType.updateCommentData(
                commentTypeList = commentData,
            ) {
                callback.invoke()
            }
        } else {
            binding.vCommentType.hide()
        }
    }

    private fun updateNormalComment() {
        lifecycleScope.launch {
            binding.vCommentType.updateNormalCommentData(
                VodCommentInfo(
                    userAvatar = sharedPreferences.userAvatar(),
                    commentType = VodCommentInfo.VodCommentType.VOD_COMMENT_TYPE_NORMAL,
                    commentData = VodCommentInfo.VodCommentData(
                        commentMetaData = viewModelCommentV2.getMeta(),
                        commentPageData = viewModelCommentV2.getDataCom(),
                        statusInfo = VodCommentInfo.CommentStatus(
                            status = VodCommentInfo.Status.Success
                        )
                    )
                )
            )
        }

    }

    private fun updateCommentAIEnable(isEnable: Boolean, content: String = "") {
//        lifecycleScope.launch {
//            if (BuildConfig.FLAVOR == "dev") {
//                binding.vCommentType.updateAIComment(isAiEnable = true, content = content)
//            } else {
//                binding.vCommentType.updateAIComment(isAiEnable = isEnable, content = content)
//            }
//
//            lifecycleScope.launch(Dispatchers.IO) {
//                delay(100)
//                binding.vCommentType.post {
//                    binding.vCommentType.scrollToPosition(viewModelComment.getLastCommentTypeClickPosition())
//                    viewModelComment.saveLastCommentTypeClickPosition(0)
//                }
//            }
//
//        }
    }

    private fun updateMomentUI() {
        viewModel.getMomentDetail().let {
            momentAdapter.bind(it)
            binding.grMoment.isVisible = !it.isNullOrEmpty()
            binding.tvMoment.text = viewModel.getDataDetail()?.blockMoment?.name ?: ""
        }
    }

    private fun updateMomentData(
        structureId: String,
        blockType: String,
        type: String,
        customData: String = "",
        relatedId: String
    ) {
        Timber.d("VODDetailChildMainFragment >> ResultDetail >> updateMomentData")
        viewModel.dispatchIntent(
            VodDetailViewModel.VodDetailIntent.GetListMoment(
                structureId = structureId,
                userId = sharedPreferences.userId(),
                page = 1,
                perPage = MainApplication.INSTANCE.appConfig.numItemOfPage,
                blockType = blockType,
                type = type,
                watchingVersion = if (type == Constants.WATCHING_TYPE) "v1" else null,
                customData = customData,
                relatedId = relatedId
            )
        )
    }

    private fun onShareLink(url: String) {
        sendTrackingShare()
        val sendIntent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, url)
            putExtra(Intent.EXTRA_REPLACING, true)
            type = "text/plain"
        }
        val shareIntent = Intent.createChooser(sendIntent, getString(R.string.share))
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            shareIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
        }
        context?.startActivity(shareIntent)
    }
    private fun sendTrackingShare() {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "516",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = TrackingUtil.screen,
                event = "Share",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                itemId = viewModel.getId(),
                itemName = viewModel.getDataDetail()?.blockContent?.titleVietnam?:"",
                EpisodeID = viewModel.currentEpisode()?.realEpisodeId?: "",
                chapterId = viewModel.currentEpisode()?.id?: "",
                businessPlan = viewModel.getDataDetail()?.blockContent?.payment?.id ?: "",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                isRecommend = TrackingUtil.isRecommend
            )
        )
    }


    private fun setOnScroll(scrollable: Boolean) {
        if (_binding != null) {
            if (scrollable) {
                (binding.toolbar.layoutParams as? AppBarLayout.LayoutParams)?.scrollFlags =
                    AppBarLayout.LayoutParams.SCROLL_FLAG_SCROLL or AppBarLayout.LayoutParams.SCROLL_FLAG_EXIT_UNTIL_COLLAPSED
                (binding.appBar.layoutParams as? androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams)?.behavior =
                    AppBarLayout.Behavior()
            } else {
                (binding.toolbar.layoutParams as? AppBarLayout.LayoutParams)?.scrollFlags =
                    AppBarLayout.LayoutParams.SCROLL_FLAG_NO_SCROLL
                (binding.appBar.layoutParams as? androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams)?.behavior =
                    null
            }
        }
    }

    //region Tracking
    private fun sendLogAddFollow() {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "59",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                event = "Subscribed",
                itemId = viewModel.getDataDetail()?.blockContent?.id.toString(),
                itemName = viewModel.getDataDetail()?.blockContent?.titleVietnam.toString(),
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                screen = TrackingUtil.screen,
                folder = TrackingUtil.blockId,
                subMenuId = TrackingUtil.blockId,
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.itemIndex,
                blocKPosition = TrackingUtil.blockIndex
            )
        )
    }

    private fun sendLogDeleteFollow() {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "59",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                event = "Unsubscribed",
                itemId = viewModel.getDataDetail()?.blockContent?.id.toString(),
                itemName = viewModel.getDataDetail()?.blockContent?.titleVietnam.toString(),
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                screen = TrackingUtil.screen,
                folder = TrackingUtil.blockId,
                subMenuId = TrackingUtil.blockId,
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.itemIndex,
                blocKPosition = TrackingUtil.blockIndex
            )
        )
    }
    //endregion

    // region Google billing
    private fun handlePaymentState() {
        if (paymentViewModel.state.hasObservers()) paymentViewModel.resetState()
        paymentViewModel.state.observe(viewLifecycleOwner) { state ->
            when (state) {
                is PaymentViewModel.PaymentViewState.Loading -> showLoading()

                is PaymentViewModel.PaymentViewState.ResultPackagePlan -> {
                    if (state.data.plans.isNotEmpty()) {
                        if (isEnableGoogleBilling()) {
                            selectedPlan = state.data.plans[0]
                            val utmData = Utils.getLocalDataUtm(sharedPreferences, trackingInfo)
                            paymentViewModel.dispatchIntent(
                                PaymentViewModel.PaymentViewIntent.CreateGoogleBillingTransaction(
                                    planId = state.data.plans[0].id,
                                    affiliateSource = utmData.first,
                                    trafficId = utmData.second)
                            )
                            AdjustAllEvent.sendPackageRecommendClickEvent(packageId = selectedPlan?.planType?: "", packageName = selectedPlan?.name?: "")
                            PaymentTrackingUtil.sendTrackingClickBuyTVodMovie(
                                trackingProxy = trackingProxy,
                                trackingInfo = trackingInfo,
                                monthPrepaid = selectedPlan?.id?: "",
                                idPackage = selectedPlan?.planType?: "",
                                price = selectedPlan?.amountStr?: ""
                            )
                        }
                    }
                }

                is PaymentViewModel.PaymentViewState.ResultCreateGoogleBillingTransaction -> {
                    AdjustAllEvent.dataCur.transactionType = state.data.ggSkuType
                    if (state.data.status == 1) {
                        if (state.data.isPurchase) {
                            // Preview
                            viewModel.saveIsPauseAfterSeek(true)
                            viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.TriggerPausePreview)
                            //
                            billingClientLifecycle?.queryProductDetail(listOf(state.planId), state.data.ggSkuType)
                        } else {
                            showDialog(state.data.message) {
                                when (state.data.redirectLink) {
                                    BillingClientLifecycleV6.GOOGLE_BILLING_REDIRECT_SUBSCRIPTIONS -> {
                                        activity?.let {
                                            billingClientLifecycle?.openAccountSubscription("", it.packageName, it)
                                        }
                                    }
                                    BillingClientLifecycleV6.GOOGLE_BILLING_REDIRECT_DETAIL -> {
                                        activity?.let {
                                            billingClientLifecycle?.openAccountSubscription(
                                                state.data.latestPlanId,
                                                it.packageName,
                                                it
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        showDialog(state.data.message)
                    }
                }

                is PaymentViewModel.PaymentViewState.ResultVerifyGoogleBillingTransaction -> {
                    if (state.data.status == 1) {
                        selectedPlan?.let {
                            AdjustAllEvent.sendPaymentSuccessEvent(transactionId = state.orderId)
                            TrackingGA4Proxy.sendTrackingPaymentSuccess(it, TrackingGA4Proxy.PaymentMethod.direct)
                            TrackingAppsFlyerProxy.sendTrackingAppsFlyerPaymentSuccess(it)
                            PaymentTrackingUtil.sendTrackingRentMovieShow(
                                trackingProxy, trackingInfo,
                                PaymentTrackingUtil.rentSuccess, "",
                                it.displayValue ?: "", viewModel.getId(), it.id, it.amountStr ?: ""
                            )
                        }
                        viewModel.saveVipRequired(isRequired = false, requiredVip = null)
                        viewModel.triggerInitPlayer()
                    } else {
                        selectedPlan?.let {
                            AdjustAllEvent.sendPaymentFailEvent(transactionId = state.orderId)
                            TrackingGA4Proxy.sendTrackingPaymentFail(it, TrackingGA4Proxy.PaymentMethod.direct)
                            PaymentTrackingUtil.sendTrackingRentMovieShow(
                                trackingProxy,
                                trackingInfo,
                                PaymentTrackingUtil.rentFail,
                                "",
                                it.displayValue?:"",
                                viewModel.getId(),
                                it.id,
                            it.amountStr?:""
                            )
                        }
                        showDialog(state.data.message)
                    }
                }

                is PaymentViewModel.PaymentViewState.ResultRetryVerifyGoogleBillingTransaction -> {}

                is PaymentViewModel.PaymentViewState.RequiredLogin -> {
                    val extendsArgs: Bundle = Bundle().apply {
                        if (state.intent is PaymentViewModel.PaymentViewIntent.GetPackagePlan) {
                            if (state.intent.actionFromPreview) {
                                putBoolean(Constants.PREVIEW_SHOULD_NAVIGATE_TO_PAYMENT_KEY, true)
                            }
                        }
                    }
                    parentFragment?.parentFragment?.navigateToLoginWithParams(title = state.message, extendsArgs = extendsArgs)
                }

                is PaymentViewModel.PaymentViewState.Done -> hideLoading()

                else -> {}
            }
        }
    }

    private fun initGoogleBilling() {
        if (isEnableGoogleBilling() && activity != null) {
            billingClientLifecycle = BillingClientLifecycleV6.getInstance(requireActivity().applicationContext)
            billingClientLifecycle?.let { clientLifecycle ->
                viewLifecycleOwner.lifecycle.addObserver(clientLifecycle)

                clientLifecycle.purchasesInApp.observe(viewLifecycleOwner) { data ->
                    clientLifecycle.handlerPurchase(
                        data, BillingClientLifecycleV6.PurchaseType.ResponseInAppPurchase,
                        onPurchaseSuccess = object : BillingClientLifecycleV6.OnPurchaseSuccess {
                            override fun onPurchaseSuccess(
                                orderId: String,
                                planId: String,
                                googlePurchaseToken: String
                            ) {
                                val utmData = Utils.getLocalDataUtm(sharedPreferences, trackingInfo)
                                paymentViewModel.dispatchIntent(
                                    PaymentViewModel.PaymentViewIntent.RetryVerifyGoogleBillingTransaction(
                                        planId = planId,
                                        googlePurchaseToken = googlePurchaseToken,
                                        affiliateSource = utmData.first,
                                        trafficId = utmData.second
                                    )
                                )
                            }
                        },
                        onPurchasePending = null,
                        onPurchaseFail = null
                    )
                }
                clientLifecycle.purchasesSubs.observe(viewLifecycleOwner) { data ->
                    clientLifecycle.handlerPurchase(
                        data, BillingClientLifecycleV6.PurchaseType.ResponseSubsPurchase,
                        onPurchaseSuccess = object : BillingClientLifecycleV6.OnPurchaseSuccess {
                            override fun onPurchaseSuccess(
                                orderId: String,
                                planId: String,
                                googlePurchaseToken: String
                            ) {
                                val utmData = Utils.getLocalDataUtm(sharedPreferences, trackingInfo)
                                paymentViewModel.dispatchIntent(
                                    PaymentViewModel.PaymentViewIntent.RetryVerifyGoogleBillingTransaction(
                                        planId = planId,
                                        googlePurchaseToken = googlePurchaseToken,
                                        isRetry = 1,
                                        affiliateSource = utmData.first,
                                        trafficId = utmData.second
                                    )
                                )
                            }
                        },
                        onPurchasePending = null,
                        onPurchaseFail = null
                    )
                }
                clientLifecycle.purchaseUpdateEvent.observe(viewLifecycleOwner) { data ->
                    clientLifecycle.handlerPurchase(data, BillingClientLifecycleV6.PurchaseType.CurrentPurchase,
                        onPurchaseSuccess = object : BillingClientLifecycleV6.OnPurchaseSuccess {
                            override fun onPurchaseSuccess(
                                orderId: String,
                                planId: String,
                                googlePurchaseToken: String
                            ) {
                                // Preview
                                viewModel.saveIsPauseAfterSeek(false)
                                //
                                activity?.runOnUiThread {
                                    hideLoading()
                                    val utmData = Utils.getLocalDataUtm(sharedPreferences, trackingInfo)
                                    paymentViewModel.dispatchIntent(
                                        PaymentViewModel.PaymentViewIntent.VerifyGoogleBillingTransaction(
                                            orderId = orderId,
                                            planId = planId,
                                            googlePurchaseToken = googlePurchaseToken,
                                            affiliateSource = utmData.first,
                                            trafficId = utmData.second
                                        )
                                    )
                                }
                            }
                        },
                        onPurchasePending = object : BillingClientLifecycleV6.OnPurchasePending {
                            override fun onPurchasePending() {
                                activity?.runOnUiThread {
                                    showLoading()
                                }
                            }
                        },
                        onPurchaseFail = object : BillingClientLifecycleV6.OnPurchaseFail {
                            override fun onPurchaseFail(errorMessage: String) {
                                // Preview
                                viewModel.saveIsPauseAfterSeek(false)
                                //
                                activity?.runOnUiThread {
                                    hideLoading()
                                    showDialog(errorMessage)
                                }
                            }
                        })
                }
                clientLifecycle.productDetail.observe(viewLifecycleOwner) { data ->
                    if (data != null && data.productId.isNotBlank() && sharedPreferences.userId().isNotBlank()) {
                        activity?.let {
                            clientLifecycle.launchBillingFlowV6(it, data, 0, sharedPreferences.userId())
                        }
                    }
                }
                clientLifecycle.errorResponse.observe(viewLifecycleOwner) { data ->
                    // Preview
                    viewModel.saveIsPauseAfterSeek(false)
                    //
                    if (data.second.isNotBlank()) {
                        Toast.makeText(context, data.second, Toast.LENGTH_SHORT).show()
                    }
                    when (data.first) {
                        BillingClient.BillingResponseCode.USER_CANCELED -> {
                            selectedPlan?.let {
                                AdjustAllEvent.sendPaymentUserCancelEvent()
                                TrackingGA4Proxy.sendTrackingPaymentUserCancel(it.id, it.name?:"")
                                PaymentTrackingUtil.cancelBuyPackageOrRent(trackingProxy, trackingInfo,  promoCode = "",
                                    monthPrepaid = it.id, idMovie = viewModel.getId(), idService = it.planType, price = it.amountStr ?: "")
                            }
                        }
                        else -> {
                            selectedPlan?.let {
                                AdjustAllEvent.sendPaymentFailEvent(transactionId = "")
                                TrackingGA4Proxy.sendTrackingPaymentFail(it, TrackingGA4Proxy.PaymentMethod.direct)
                                PaymentTrackingUtil.sendTrackingRentMovieShow(trackingProxy, trackingInfo,
                                    PaymentTrackingUtil.rentFail, "", it.displayValue?:"", viewModel.getId(), it.id,
                                    it.amountStr?:"")
                            }
                        }
                    }
                }
            }
        }
    }

    private fun showDialog(message: String, onConfirm: (() -> Unit)? = null) {
        paymentAlertDialog?.dismiss()
        paymentAlertDialog = AlertDialog().apply {
            setMessage(message)
            setTextConfirm(<EMAIL>(R.string.text_rating_accept))
            setOnlyConfirmButton(true)
            setHandleBackPress(true)
            setListener(object : AlertDialogListener {
                override fun onConfirm() {
                    onConfirm?.invoke()
                }
            })
        }
        paymentAlertDialog?.show(childFragmentManager, "PaymentAlertDialog")
    }
    //endregion

    private fun showDownloadLoading() {
        binding.pbLoading?.root.show()
    }

    private fun hideDownloadLoading() {
        binding.pbLoading?.root.hide()
    }

    // region Game Emoji
    private fun updateRankingButton(data: GameVOD.GameVODData? = null) {
        //https://fptplay.vn/doandaidi/bang-xep-hang?idEvent=************************&fptplayId=97140135
//        val data = GameVOD.GameVODData(isLeaderBoard = true, leaderBoardLink = "https://fptplay.vn/doandaidi/bang-xep-hang")
        if (data != null && data.isLeaderBoard && data.leaderBoardLink.isNotBlank()) {

            binding.llRankGame.show()
//            val url = try {
//                Uri.parse(data.leaderBoardLink).buildUpon().apply {
//                    appendQueryParameter("idEvent", viewModel.getId())
//                    appendQueryParameter("fptplayId", sharedPreferences.userId())
//                }
//            } catch (e: Exception) {
//                null
//            }

            binding.llRankGame.onClickDelay {
//                if(url != null) {
//                    viewModel.dispatchIntent(
//                        VodDetailViewModel.VodDetailIntent.TriggerShowRankGame(
//                            url = url.build().toString()
//                        )
//                    )
//                }
                if (data.leaderBoardLink.isNotBlank()) {
                    viewModel.dispatchIntent(
                        VodDetailViewModel.VodDetailIntent.TriggerShowRankGame(
                            url = data.leaderBoardLink.toString()
                        )
                    )
                }
            }
        } else {
            binding.llRankGame.hide()
            binding.llRankGame.onClickDelay { /* do nothing */ }
        }
    }
    // endregion Game Emoji

    //region Pairing Control
    private val remotePlayerState = object : PairingControlConnectionHelper.RemotePlayerStateListener {
        override fun onStateChanged(state: RemotePlayerState) {}
        override fun onActionEvent(type: String) {
            when (PlayerUtils.getPlayingType()) {
                PlayerView.PlayingType.Cast -> {
                    when (MainApplication.INSTANCE.pairingConnectionHelper.getCurrentConnection()) {
                        is FBoxDeviceInfoV2 -> {
                            when (type) {
                                ActionEventType.ADD_FOLLOW -> {
                                    viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.GetVodAddFollowSuggest(viewModel.getId(), "vod"))
                                }
                                ActionEventType.REMOVE_FOLLOW -> {
//                                    if (viewModel.getDataDetail()?.blockContent?.isComingSoon != true) {
                                    viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.GetVodDeleteFollowSuggest(viewModel.getId(),"vod"))
//                                    }
                                }
                            }
                        }
                        is FSamsungTVDeviceInfo,
                        is FSamsungTVDeviceInfoExternal -> {
                            when (type) {
                                ActionEventType.ADD_FOLLOW -> {
                                    isFollow = true
                                    updateFollowButton()
                                }
                                ActionEventType.REMOVE_FOLLOW -> {
                                    isFollow = false
                                    updateFollowButton()
                                }
                            }
                        }
                        is FAndroidTVDeviceInfo -> {}
                        is FAndroidTVDeviceInfoExternal -> {
                            when (type) {
                                ActionEventType.ADD_FOLLOW -> {
                                    isFollow = true
                                    updateFollowButton()
                                }
                                ActionEventType.REMOVE_FOLLOW -> {
                                    isFollow = false
                                    updateFollowButton()
                                }
                            }
                        }
                    }
                }
                else -> {}
            }
        }

        override fun onCastSessionChanged() {}
    }
    private fun CommentView.addEllipsizeToSpannedOnLayout() {
            if (maxLines != -1 && lineCount > maxLines) {
                val endOfLastLine = layout.getLineEnd(maxLines - 1)
                val spannedDropLast3Chars = if(endOfLastLine > 3) text.subSequence(0, endOfLastLine - 3) else text
                val spannableBuilder = SpannableStringBuilder()
                    .append(spannedDropLast3Chars)
                    .append("…")
                text = spannableBuilder
            }
        }
    //endregion
}
