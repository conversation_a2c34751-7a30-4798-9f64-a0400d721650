<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_welcome"
    app:startDestination="@id/welcome_fragment">

    <include app:graph="@navigation/nav_login" />

    <fragment
        android:id="@+id/welcome_fragment"
        android:name="com.fptplay.mobile.welcome.WelcomeFragment"
        android:label="WelcomeFragment"
        tools:layout="@layout/welcome_fragment">
        <action
            android:id="@+id/action_welcome_fragment_to_landing_page_fragment"
            app:destination="@id/landing_page_fragment">
            <argument
                android:name="originalLink"
                android:defaultValue=""
                app:argType="string"
                app:nullable="true" />
        </action>
        <action android:id="@+id/action_global_to_survey_fragment"
            app:destination="@id/nav_survey">
            <argument
                android:name="originalLink"
                android:defaultValue=""
                app:argType="string"
                app:nullable="true" />
            <argument
                android:name="event"
                app:argType="string"
                app:nullable="false" />
        </action>
<!--        <deepLink-->
<!--            android:id="@+id/deepLinkFplay1"-->
<!--            android:autoVerify="true"-->
<!--            app:uri="fptplay.vn" />-->
<!--        <deepLink-->
<!--            android:id="@+id/deepLink2"-->
<!--            android:autoVerify="true"-->
<!--            app:uri="fptplay.vn/{screen}" />-->
<!--        <deepLink-->
<!--            android:id="@+id/deepLink3"-->
<!--            android:autoVerify="true"-->
<!--            app:uri="fptplay.vn/{screen}/{menu}" />-->
<!--        <deepLink-->
<!--            android:id="@+id/deepLink4"-->
<!--            android:autoVerify="false"-->
<!--            app:uri="fptplay.vn/{screen}/{menu}/{subMenu}" />-->
<!--        <deepLink-->
<!--            android:id="@+id/deepLinkFplay5"-->
<!--            android:autoVerify="true"-->
<!--            app:uri="fptplay.vn/{screen}/{menu}/{subMenu}/{id}" />-->
<!--        <deepLink-->
<!--            android:id="@+id/deepLinkFplay6"-->
<!--            android:autoVerify="true"-->
<!--            app:uri="fptplay.vn/{screen}/{menu}/{subMenu}/{id}/.*" />-->
        <deepLink
            android:id="@+id/deepLinkFplay1"
            android:autoVerify="true"
            app:uri="fptplay.vn/.*" />
        <deepLink
            android:id="@+id/deepLinkFplay2"
            android:autoVerify="true"
            app:uri="www.fptplay.vn/.*" />

        <deepLink
            android:id="@+id/deepLinkCustomScheme1"
            android:autoVerify="true"
            app:uri="fptplay://fptplay.vn" />
        <deepLink
            android:id="@+id/deepLinkCustomScheme2"
            android:autoVerify="true"
            app:uri="fptplay://fptplay.vn/{screen}" />
        <deepLink
            android:id="@+id/deepLinkCustomScheme3"
            android:autoVerify="true"
            app:uri="fptplay://fptplay.vn/{screen}/{menu}" />
        <deepLink
            android:id="@+id/deepLinkCustomScheme4"
            android:autoVerify="true"
            app:uri="fptplay://fptplay.vn/{screen}/{menu}/{subMenu}" />
        <deepLink
            android:id="@+id/deepLinkCustomScheme5"
            android:autoVerify="true"
            app:uri="fptplay://fptplay.vn/{screen}/{menu}/{subMenu}/{id}" />
        <deepLink
            android:id="@+id/deepLinkCustomScheme6"
            android:autoVerify="true"
            app:uri="fptplay://fptplay.vn/{screen}/{menu}/{subMenu}/{id}/.*" />


        <deepLink
            android:id="@+id/deepLinkBetaFplay1"
            android:autoVerify="true"
            app:uri="beta-dev.fptplay.vn" />
        <deepLink
            android:id="@+id/deepLinkBetaFplay2"
            android:autoVerify="true"
            app:uri="beta-dev.fptplay.vn/{screen}" />
        <deepLink
            android:id="@+id/deepLinkBetaFplay3"
            android:autoVerify="true"
            app:uri="beta-dev.fptplay.vn/{screen}/{menu}" />
        <deepLink
            android:id="@+id/deepLinkBetaFplay4"
            android:autoVerify="true"
            app:uri="beta-dev.fptplay.vn/{screen}/{menu}/{subMenu}" />
        <deepLink
            android:id="@+id/deepLinkBetaFplay5"
            android:autoVerify="true"
            app:uri="beta-dev.fptplay.vn/{screen}/{menu}/{subMenu}/{id}" />
        <deepLink
            android:id="@+id/deepLinkStagingFplay1"
            android:autoVerify="true"
            app:uri="staging.fptplay.vn/.*" />
        <deepLink
            android:id="@+id/deepLinkStagingFplay2"
            android:autoVerify="true"
            app:uri="www.staging.fptplay.vn/.*" />
        <deepLink
            android:id="@+id/deepLinkDevFplay1"
            android:autoVerify="true"
            app:uri="dev.fptplay.vn/.*" />
        <deepLink
            android:id="@+id/deepLinkDevFplay2"
            android:autoVerify="true"
            app:uri="www.dev.fptplay.vn/.*" />

        <deepLink
            android:id="@+id/deepLink7"
            android:autoVerify="true"
            app:uri="hotro.fptplay.vn/.*" />
        <argument
            android:name="screen"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="menu"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="subMenu"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="id"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="arg"
            android:defaultValue="true"
            app:argType="boolean" />

        <action android:id="@+id/action_welcome_fragment_to_out_of_service_region_fragment"
            app:destination="@id/out_of_service_fragment"/>
        <action
            android:id="@+id/action_global_to_introduce_fragment"
            app:destination="@id/introduce_fragment" />
        <action
            android:id="@+id/action_global_to_interaction_fragment"
            app:destination="@id/interaction_fragment" />
    </fragment>
    <fragment
        android:id="@+id/landing_page_fragment"
        android:name="com.fptplay.mobile.features.event_trigger.LandingPageFragment"
        android:label="LandingPageFragment"
        tools:layout="@layout/landing_page_fragment">
        <argument
            android:name="originalLink"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="event"
            app:argType="string"
            app:nullable="false" />
    </fragment>
    <action
        android:id="@+id/action_global_to_login"
        app:destination="@id/nav_login">
        <argument
            android:name="idToPlay"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="time_shift_limit"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="time_shift"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="navigationId"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="popupToId"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="popUpToInclusive"
            android:defaultValue="true"
            app:argType="boolean" />
    </action>

    <!--  region Download  -->
    <fragment
        android:id="@+id/downloadMainFragment"
        android:name="com.fptplay.mobile.features.download.fragment.DownloadMainFragment"
        android:label="DownloadMainFragment"
        tools:layout="@layout/download_main_fragment" />

    <fragment
        android:id="@+id/out_of_service_fragment"
        android:name="com.fptplay.mobile.features.out_of_service.OutOfServiceFragment"
        android:label="Out Of Service Region Fragment"
        tools:layout="@layout/out_of_service_region_fragment" >
        <action android:id="@+id/action_out_of_service_region_fragment_to_welcome_fragment"
            app:destination="@id/welcome_fragment"/>
    </fragment>

    <fragment
        android:id="@+id/introduce_fragment"
        android:name="com.fptplay.mobile.features.introduce.IntroduceFragment"
        android:label="IntroduceFragment"
        tools:layout="@layout/introduce_fragment">
        <argument
            android:name="originalLink"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="event"
            app:argType="string"
            app:nullable="false" />
    </fragment>
    <fragment
        android:id="@+id/interaction_fragment"
        android:name="com.fptplay.mobile.features.introduce.InteractionFragment"
        android:label="InteractionFragment"
        tools:layout="@layout/interaction_fragment">
        <argument
            android:name="originalLink"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="event"
            app:argType="string"
            app:nullable="false" />
    </fragment>
    <action
        android:id="@+id/action_global_to_downloadMainFragment"
        app:destination="@id/downloadMainFragment" />
    <action
        android:id="@+id/action_global_to_download_v2"
        app:destination="@id/nav_dowload_v2" />
    <include app:graph="@navigation/nav_dowload_v2" />

    <!--  endregion Download  -->

    <include app:graph="@navigation/nav_survey" />

</navigation>