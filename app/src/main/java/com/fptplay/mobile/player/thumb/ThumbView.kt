package com.fptplay.mobile.player.thumb

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.fptplay.mobile.R
import com.fptplay.mobile.databinding.PlayerThumbLayoutBinding
import com.fptplay.mobile.player.utils.visible
import com.xhbadxx.projects.module.util.common.Util.hide
import java.util.Locale

class ThumbView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    style: Int = 0
) :
    FrameLayout(context, attrs, style) {

    private var _binding: PlayerThumbLayoutBinding? = null
    private val binding get() = _binding!!

    init {
        _binding = PlayerThumbLayoutBinding.inflate(LayoutInflater.from(context), this, true)
        initViews()
    }

    private fun initViews() {

    }

    fun getImageThumb() = binding.ivThumbnail

    fun setThumbnail(bitmap: Bitmap?) {
        bitmap?.let {
            binding.ivThumbnail.visible()
            binding.ivThumbnail.setImageBitmap(it)
        } ?: kotlin.run {
            binding.ivThumbnail.hide()
        }
    }

    fun setTime(time: String, totalTime: String, isLoadThumbSuccess: Boolean) {
        binding.llTimeContainer.setBackgroundResource(if (isLoadThumbSuccess) Color.TRANSPARENT else R.drawable.player_thumb_seek_time_bg)
        binding.tvTime.text = time
        binding.tvTotalTime.text = String.format(
            Locale.getDefault(),
            context.getString(R.string.player_total_time_with_param),
            totalTime
        )
    }
}