// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    apply from: 'constants.gradle'
    apply from: 'metadata.gradle'

    repositories {
        google()
        mavenLocal()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.google.dagger:hilt-android-gradle-plugin:$hilt_dagger_gradle_version"
        classpath('androidx.navigation:navigation-safe-args-gradle-plugin:2.5.1')
        classpath 'com.google.gms:google-services:4.3.13'

        // Add the Crashlytics Gradle plugin
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.1'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

//allprojects {
//    repositories {
//        google()  // Google's Maven repository
//    }
//}

task clean(type: Delete) {
    delete rootProject.buildDir
}