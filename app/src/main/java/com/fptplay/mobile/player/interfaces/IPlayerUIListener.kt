package com.fptplay.mobile.player.interfaces

import androidx.annotation.OptIn
import androidx.media3.common.VideoSize
import androidx.media3.common.util.UnstableApi
import com.fplay.module.downloader.model.VideoTaskItem
import com.fptplay.mobile.player.model.PlayerBottomControlData
import com.fptplay.mobile.player.utils.PlayerConstants
import com.fptplay.mobile.player.utils.PlayerDebugViewData
import com.fptplay.mobile.vod.data.BuyPackageGuide
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.tear.modules.player.util.PlayerControlView

@OptIn(UnstableApi::class)
interface IPlayerUIListener {
    fun onNext(position: Int, curData: Details.Episode, isAuto: Boolean, isSendEventToRemote: Boolean)
    fun onPrevious(position: Int, curData: Details.Episode, isAuto: Boolean)
    fun onPlayItem(position: Int, episode: Details.Episode, isSendEventToRemote: Boolean) {}
    fun onPlayItemOffline(videoTaskItem: VideoTaskItem) {}
    fun onNextOffline(position: Int, curData: VideoTaskItem, isAuto: Boolean)
    fun onPreviousOffline(position: Int, curData: VideoTaskItem, isAuto: Boolean)
    fun onPlayToggle()
    fun onMulticam()
    fun onCast()
    fun onShare()
    fun onExpand(curResolutionId: String)
    fun onMore(moreData: List<PlayerBottomControlData>)
    fun onLiveChatClick()
    fun onSportInteractiveClick()
    fun onFullScreen(isFullscreen: Boolean, isLandscapeMode: Boolean)
    fun onSetting(bitrates: List<PlayerControlView.Data.Bitrate>?)
    fun onAudioAndSubtitle(tracks: List<PlayerControlView.Data.Track>?)
    fun onOpenAudioSelection(tracks: List<PlayerControlView.Data.Track>?)
    fun onOpenSubtitleSelection(tracks: List<PlayerControlView.Data.Track>?)
    fun onPlayerSpeed()
    fun onWatchCredit(isSendEventToRemote: Boolean)
    fun onRecommendClose()
    fun onRecommendWatchNow(related: Details.RelatedVod?)
    fun onRecommendPlayTrailer(related: Details.RelatedVod?)
    fun onEpisodeChangedFromBackground(pos: Int, currentDuration: Long) // Case: Background -> Foreground
    fun onEpisodeChangeInBackground(pos: Int) {} // Case: Change episode when click in notification
    fun onVideoSizeChanged(videoSize: VideoSize)
    fun onTracksInfoChanged()
    fun onSeekNext(duration:Long){}
    fun onSeekPrevious(duration:Long){}
    fun onActionSeek(duration:Long){}
    fun onReport(){}
    fun onAdsShow() {}
    fun onAdsHide() {}
    fun onPreviewPlayCompleted() {}
    fun onPictureInPictureModeChanged(isInPictureInPictureMode: Boolean)
    fun onClickBuyPackage() {}
    fun onShowBuyPackageGuide(buyPackageGuide: BuyPackageGuide) {}
    fun onOpenFrameOverlay(type: PlayerConstants.PlayerFrameOverlayType?) {}
    fun onCloseFrameOverlay(type: PlayerConstants.PlayerFrameOverlayType?) {}
}