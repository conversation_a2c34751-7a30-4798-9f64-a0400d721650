package com.fptplay.mobile.features.moments

import android.content.Context
import androidx.annotation.OptIn
import androidx.media3.common.util.UnstableApi
import androidx.media3.common.util.Util
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.datasource.FileDataSource
import androidx.media3.datasource.cache.Cache
import androidx.media3.datasource.cache.CacheDataSink
import androidx.media3.datasource.cache.CacheDataSource

internal object DataSourceUtils {

    @OptIn(UnstableApi::class)
    fun buildCacheDataSourceFactory(
        cache: Cache,
        applicationContext: Context,
        listener: CacheDataSource.EventListener
    ): CacheDataSource.Factory {
        val cacheSink = CacheDataSink.Factory()
            .setCache(cache)
        val upstreamFactory =
            DefaultDataSource.Factory(
                applicationContext, DefaultHttpDataSource.Factory().setUserAgent(
                    Util.getUserAgent(
                        applicationContext,
                        "exo"
                    )
                )
            )
        return CacheDataSource.Factory()
            .setCache(cache)
            .setCacheWriteDataSinkFactory(cacheSink)
            .setCacheReadDataSourceFactory(FileDataSource.Factory())
            .setUpstreamDataSourceFactory(upstreamFactory)
            .setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)
            .setEventListener(listener)
    }
}