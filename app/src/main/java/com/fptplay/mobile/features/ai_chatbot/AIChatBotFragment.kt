package com.fptplay.mobile.features.ai_chatbot

import android.app.Activity
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.DrawableRes
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppConfig
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppNativeErrorEventData
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppNativeEventListener
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppNativeLoadSuccessEventData
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppNativeMethodProvider
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppNativeSessionInitEventData
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppRenderEffectData
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppUIInterface
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppUsage
import com.fpl.plugin.mini_app_sdk.android_view.ResponseMetadata
import com.fpl.plugin.mini_app_sdk.android_view.Token
import com.fpl.plugin.mini_app_sdk.entity.MiniAppManifest
import com.fpl.plugin.mini_app_sdk.model.DisplayModeOptions
import com.fpl.plugin.mini_app_sdk.model.MiniAppDisplayInfo
import com.fpl.plugin.mini_app_sdk.model.MiniAppPlatformInfo
import com.fpl.plugin.mini_app_sdk.model.MiniAppUserInfo
import com.fpl.plugin.mini_app_sdk.model.OpenPopupData
import com.fpl.plugin.mini_app_sdk.model.OpenPopupType
import com.fpl.plugin.mini_app_sdk.model.RenderEffectOptions
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.hideKeyboard
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.showKeyboard
import com.fptplay.mobile.common.ui.bases.BaseActivity
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.databinding.AIChatBotFragmentBinding
import com.fptplay.mobile.features.ai_chatbot.chat_sesion.ChatBotAISession
import com.fptplay.mobile.features.ai_chatbot.chat_sesion.ChatBotAISession.ChatSessionRealtimeTrackerListener
import com.fptplay.mobile.features.ai_chatbot.utils.AIChatBotConstants
import com.fptplay.mobile.features.ai_chatbot.utils.AIChatBotProvider
import com.fptplay.mobile.features.ai_chatbot.utils.AIChatBotUtils
import com.fptplay.mobile.features.ai_chatbot.viewmodel.AIChatBotViewModel
import com.fptplay.mobile.features.mini_app.utils.MiniAppUtils.toJsonString
import com.fptplay.mobile.player.utils.gone
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject
import timber.log.Timber
import javax.inject.Inject
@AndroidEntryPoint
class AIChatBotFragment : BaseFragment<AIChatBotViewModel.AIChatBotState, AIChatBotViewModel.AIChatBotIntent>() {
    companion object {
        const val  MANIFEST_LOAD_TIME_OUT_MS  = 10_000L
        const val  DELAY_TIME_OPEN_DEEPLINK  = 2_000L
    }
    override val handleBackPressed = true
    override val viewModel: AIChatBotViewModel by viewModels()
    private var _binding: AIChatBotFragmentBinding? = null
    private val binding get() = _binding!!
    private lateinit var aiChatBotUtils:AIChatBotUtils

    @Inject
    lateinit var trackingProxy: TrackingProxy
    override val hasEdgeToEdge: Boolean = false

    @Inject
    lateinit var trackingInfo: Infor

    @Inject
    lateinit var chatSession: ChatBotAISession

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = AIChatBotFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        AIChatBotProvider.clearDataRemote()
        chatSession.stopChatSessionTracker()
        chatSession.removeChatBotAITrackingCallback()
        _binding = null
    }

    override fun showPageErrorV2OneButton(
        parentView: View?,
        toolbarTitle: String?,
        errorTitle: String?,
        errorMessage: String,
        @DrawableRes navigationIcon: Int?,
        @DrawableRes backgroundIcon: Int?,
        btnText: String?,
        onBtnClickBeforeRetryLoadPage: (() -> Unit)?,
        onCustomRetryLoadPageFun: (() -> Unit)?,
        @DrawableRes btnBackgroundRes: Int?,
        @DrawableRes backgroundColor: Int?
    ) {
        binding.flErrorPage.show()
        super.showPageErrorV2OneButton(
            parentView = binding.flErrorPage,
            toolbarTitle,
            errorTitle,
            errorMessage,
            navigationIcon,
            backgroundIcon,
            btnText,
            onBtnClickBeforeRetryLoadPage,
            onCustomRetryLoadPageFun,
            btnBackgroundRes,
            backgroundColor = backgroundColor ?: R.color.ai_chatbot_error_screen_background
        )
    }

    override fun hidePageErrorV2(parentView: View?) {
        binding.flErrorPage.hide()
        super.hidePageErrorV2(binding.flErrorPage)
    }

    override fun initData() {
        aiChatBotUtils = AIChatBotUtils(trackingProxy = trackingProxy, trackingInfo = trackingInfo)
        chatSession.startChatSessionTracker()
    }

    override fun backHandler() {
        findNavController().navigateUp()
    }

    override fun bindData() {
        hidePageErrorV2()
        aiChatBotUtils.evaluateChatBotAIAvailability(sharedPreferences = sharedPreferences) { result ->
            when (result) {
                AIChatBotUtils.AiChatResult.Success -> {
                    triggerOpenAIChatBox()
                }
                AIChatBotUtils.AiChatResult.RequiredLogin ->{
                    navigateToLoginWithParams(requestRestartApp = true)
                }
                AIChatBotUtils.AiChatResult.NotSupported -> {
                    showWarningDialog(
                        textTitle = getString(R.string.ai_chat_bot_not_support_title),
                        message = getString(R.string.ai_chat_bot_not_support_des),
                        textConfirm = getString(R.string.close),
                        isConfirmEnable = true,
                        isExitEnable = true,
                        isCancelOutside = true,
                        onConfirm = {
                            backHandler()
                        }
                    )
                }

                AIChatBotUtils.AiChatResult.NoInternet -> {
                    showNoInternetUI()
                }

                AIChatBotUtils.AiChatResult.KidProfileNotSupported -> {
                    showWarningDialog(
                        textTitle = getString(R.string.multi_profile_kid_not_supported_title),
                        message = MainApplication.INSTANCE.appConfig.profileMsgRestrictContentVi.ifBlank {getString(R.string.multi_profile_kid_not_supported_des)},
                        textConfirm = getString(R.string.all_string_known),
                        isConfirmEnable = true,
                        isExitEnable = true,
                        isCancelOutside = true,
                        onConfirm = {
                            backHandler()
                        }
                    )
                }
            }
            // send log enter chatbot fail
            if (result !is AIChatBotUtils.AiChatResult.Success){
                aiChatBotUtils.sendLogEnterChatBot(chatSession = chatSession.getChatSession(), status = false)
            }
        }
    }
    override fun bindComponent() {
        bindToolbar()
        bindMiniApp()
    }
    override fun bindEvent() {
        binding.toolbar.setCloseButtonOnClick {
            backHandler()
        }
        chatSession.addChatBotAITrackingCallback(chatBotAITrackingCallback)
    }

    private fun bindToolbar(){
        binding.toolbar.title = AIChatBotProvider.currentAiChatBotConfig.title?.takeIf { it.isNotEmpty() } ?: getString(R.string.ai_chat_bot_title)
        binding.toolbar.startIcon?.let {
            aiChatBotUtils.loadDrawableAIChatBoxUrl(
                context = requireContext(),
                it,
                iconUrl = AIChatBotProvider.currentAiChatBotConfig.iconUrl,
                size = context?.resources?.getDimensionPixelSize(R.dimen.ic_ai_chat_box_toolbar_size)?:0
            )
        }
    }

    private fun sessionInit(){
        chatSession.resumeChatSessionTracker()
    }

    private val chatBotAITrackingCallback = object : ChatSessionRealtimeTrackerListener {
        override fun onStartChatSessionTracker(logSession: Long) {
            Timber.d("$tag onStartChatSessionTracker")
        }

        override fun onResumeChatSessionTracker(logSession: Long) {
            Timber.d("$tag onResumeChatSessionTracker")
        }

        override fun onStopChatSessionTracker(logSession: Long) {
            Timber.d("$tag onStopChatSessionTracker")
        }
    }

    private fun triggerOpenAIChatBox(){
        viewModel.dispatchIntent(AIChatBotViewModel.AIChatBotIntent.GetManifest)
    }

    private fun bindMiniApp() {
        binding.miniAppView.prepareMiniAppconfig(
            miniAppConfig = MiniAppConfig(
                manifestLoadTimeoutMs = MANIFEST_LOAD_TIME_OUT_MS
            )
        )
        binding.miniAppView.prepareMiniAppUsage(
            miniAppUsage = MiniAppUsage(
                scope = viewLifecycleOwner.lifecycleScope,
                activityForPermissionContract = requireActivity(),
                miniAppNativeMethodProvider = miniAppNativeMethodProvider,
                miniAppNativeEventListener = miniAppNativeEventListener,
                miniAppUIController = miniAppUIController,
            )
        )
    }

    private fun loadAIChatBoxMiniApp(manifest: MiniAppManifest, force: Boolean = false) {
        showLoadingUi()
        binding.miniAppView.loadManifest(
            miniAppManifest = manifest,
            force = force
        )
    }

    private val miniAppNativeEventListener = object : MiniAppNativeEventListener {
        override fun loadSuccess(requestId: String, data: MiniAppNativeLoadSuccessEventData) {
            Timber.d("$tag miniAppNativeEventListener >> load success")
            showMiniAppView()
            aiChatBotUtils.startDelaySendLogEnterChatSession(
                lifecycleOwner = viewLifecycleOwner,
                chatSession = chatSession.getChatSession())
        }

        override fun loadManifestTimeout(manifest: MiniAppManifest?) {
            Timber.d("$tag loadManifestTimeout: ${manifest?.scope?.megaApp?.sourceUrl}")
            showTimeoutUi(manifest = manifest, force = true)
        }

        override fun sessionInit(requestId: String, data: MiniAppNativeSessionInitEventData) {
            Timber.d("$tag sessionInit: ${data}")
            lifecycleScope.launch {
                sessionInit()
            }
        }

        override fun processError(
            requestId: String,
            manifest: MiniAppManifest?,
            data: MiniAppNativeErrorEventData
        ) {
            super.processError(requestId, manifest, data)
            Timber.d("$tag processError: ${data}")
            lifecycleScope.launch {
                processErrorMiniApp(miniAppManifest = manifest,data = data)
            }
        }

    }

    private val miniAppNativeMethodProvider = object : MiniAppNativeMethodProvider {
        override fun closeFullScreen(requestId: String) {

        }

        override fun genApiToken(
            requestId: String,
            responseCallback: (Token, ResponseMetadata) -> Unit
        ) {
            Timber.d("$tag  genApiToken: $requestId")
            viewModel.getPartnerToken(requestId,responseCallback = { token, responseMetadata ->
                Timber.d("$tag  genApiToken : $requestId >> token :$token >> responseMetadata : $responseMetadata")
                responseCallback(token, responseMetadata)
            })
        }

        override fun getDisplayInfo(requestId: String): MiniAppDisplayInfo {
            Timber.d("$tag getDisplayInfo: $requestId")
            return MiniAppDisplayInfo(
                width = binding.miniAppView.width,
                height = binding.miniAppView.height,
                mode = MiniAppDisplayInfo.Mode.Default.value,
                success = true
            )
        }

        override fun getPlatformInfo(requestId: String): MiniAppPlatformInfo {
            Timber.d("$tag  getPlatformInfo: $requestId")
            return MiniAppPlatformInfo(
                platform = "android",
                version = Build.VERSION.SDK_INT.toString(),
                deviceModel = Build.MODEL,
                isEmulator = false,
                deviceType = if(context?.isTablet() == true) MiniAppPlatformInfo.DEVICE_TYPE_TABLET else MiniAppPlatformInfo.DEVICE_TYPE_PHONE
            )
        }

        override fun getUserId(): String {
            Timber.d("$tag  getUserId: ${viewModel.getUserId()}")
            return viewModel.getUserId()
        }

        override fun getUserInfo(
            requestId: String,
            responseCallback: (MiniAppUserInfo?, ResponseMetadata) -> Unit
        ) {
            Timber.d("$tag getUserInfo: $requestId")
            viewModel.getUserInfo(
                requestId = requestId,
                responseCallback = { userInfo, responseMetadata ->
                    Timber.d("$tag  getUserInfo: $userInfo >> responseMetadata :$responseMetadata")
                    responseCallback(userInfo, responseMetadata)
                }
            )
        }

        override fun hideKeyboard(requestId: String) {
            hideKeyboard()
        }

        override fun interactContent(requestId: String, action: String) {

        }

        @Deprecated("Use [getUserInfo] instead")
        override suspend fun onProvideUserInfo(): MiniAppUserInfo {
            return MiniAppUserInfo()
        }

        override fun openDeeplink(url: String) {
            Timber.d("$tag openDeeplink url: $url")
            closeMiniAppAndOpenDeepLink(url = url)
        }

        override fun openFullScreen(requestId: String) {
            Timber.d("$tag openFullScreen requestId: $requestId")

        }

        override fun openPopUp(requestId: String, type: OpenPopupType, options: OpenPopupData?) {
            Timber.d("$tag openPopUp: type: ${type.type} - options: $options")
            when (type) {
                OpenPopupType.TOAST -> {
                    options?.data?.firstOrNull()?.let {
                        lifecycleScope.launch(Dispatchers.Main) {
                            showSnackBarMessage(
                                activity = requireActivity(),
                                text = options.title?:"",
                                iconUrl = it.icon,
                            )
                        }
                    }
                }

                OpenPopupType.WARNING -> {
                    options?.data?.firstOrNull()?.let {
                        showWarningDialog(
                            textTitle = options.title,
                            message = it.title ?: "",
                            isOnlyConfirmButton = true,
                            textConfirm = it.button,
                        )
                    }
                }

                else -> {}
            }
        }

        override fun renderEffect(
            requestId: String,
            effectData: MiniAppRenderEffectData,
            customAnimation: RenderEffectOptions?
        ) {}

        override fun requestLogin(requestId: String) {
            Timber.d("$tag requestLogin: $requestId")
            lifecycleScope.launch {
                navigateToLoginWithParams(requestRestartApp = true)
            }
        }

        override fun setDisplayMode(
            requestId: String,
            mode: MiniAppDisplayInfo.Mode,
            options: DisplayModeOptions?
        ): MiniAppDisplayInfo {
            Timber.d("$tag requestLogin: $requestId")
            return MiniAppDisplayInfo(
                success = false,
                mode = MiniAppDisplayInfo.Mode.Default.value,
                width = binding.miniAppView.width,
                height = binding.miniAppView.height
            )
        }

        override fun showKeyboard(requestId: String) {
            showKeyboard()
        }

        override fun sendLog(
            requestId: String,
            type: String,
            message: String,
            timestamp: Long,
            metadata: JSONObject?
        ) {
            Timber.d("$tag sendLog: type: ${type} - message: $message - timestamp : $timestamp -- metadata : ${metadata.toJsonString()}")
            lifecycleScope.launch {
                aiChatBotUtils.sendLogAIChatBot(
                    requestId = requestId,
                    type = type,
                    message = message,
                    timestamp = timestamp,
                    metadata = metadata,
                    chatSession = chatSession.getChatSession()
                )
            }
        }
    }

    private val miniAppUIController = object : MiniAppUIInterface {
        override fun onCloseMiniApp() {
            Timber.d("$tag onCloseMiniApp")
            lifecycleScope.launch {
                findNavController().popBackStack()
            }
        }
    }

    override fun AIChatBotViewModel.AIChatBotState.toUI() {
        when (this) {
            is AIChatBotViewModel.AIChatBotState.Loading -> {
                when (intent) {
                    is AIChatBotViewModel.AIChatBotIntent.GetManifest -> {
                        showLoadingUi()
                    }

                    else -> {}
                }
            }

            is AIChatBotViewModel.AIChatBotState.Error -> {
                when (intent) {
                    is AIChatBotViewModel.AIChatBotIntent.GetManifest -> {
                        showTimeoutUi()
                    }

                    else -> {}
                }
            }

            is AIChatBotViewModel.AIChatBotState.ErrorNotInternet ->{
                when (intent) {
                    is AIChatBotViewModel.AIChatBotIntent.GetManifest -> {
                        showNoInternetUI()
                    }

                    else -> {}
                }
            }

            is AIChatBotViewModel.AIChatBotState.ErrorRequiredLogin ->{
                when (intent) {
                    is AIChatBotViewModel.AIChatBotIntent.GetManifest -> {
                        navigateToLoginWithParams(requestRestartApp = true)
                    }
                    else -> {}
                }
            }
            is AIChatBotViewModel.AIChatBotState.Done -> {
                when (intent) {
                    is AIChatBotViewModel.AIChatBotIntent.GetManifest -> {
                        hideLoadingUI()
                    }

                    else -> {}
                }
            }

            is AIChatBotViewModel.AIChatBotState.ResultManifest -> {
                loadAIChatBoxMiniApp(manifest = data)
            }
            else -> {}
        }
    }

    private fun closeMiniAppAndOpenDeepLink(url: String) {
        binding.miniAppView.apply {
            postDelayed({
                try {
                    if (<EMAIL>) {
                        Timber.e("$tag open DeepLink Mini app >> url : $url")
                        findNavController().navigateUp().run {
                            aiChatBotUtils.openDeepLinkFromChatBot(url = url)
                        }
                    }
                } catch (e: Exception) {
                    Timber.e("$tag closeMiniAppAndOpenDeepLink >> url : $url")
                }
            }, DELAY_TIME_OPEN_DEEPLINK)
        }
    }

    private fun showTimeoutUi(manifest: MiniAppManifest? = null,force: Boolean = false) {
        binding.flErrorPage.show()
        showPageErrorV2OneButton(
            navigationIcon = -1,
            errorTitle = getString(R.string.error_layout_title),
            errorMessage = aiChatBotUtils.errorMessage(
                context = requireContext(),
                message = getString(R.string.error_layout_troubleshoot)
            ),
            onCustomRetryLoadPageFun = {
                manifest?.let { loadAIChatBoxMiniApp(manifest = it, force = force) }?:bindData()
            }
        )
        // send log status fail
        aiChatBotUtils.sendLogEnterChatBot(chatSession = chatSession.getChatSession(), status = false)
    }

    private fun showNoInternetUI(manifest: MiniAppManifest? = null){
        showPageErrorV2OneButton(
            navigationIcon = -1,
            errorTitle = getString(R.string.welcome_no_internet_title),
            errorMessage = aiChatBotUtils.errorMessage(
                context = requireContext(),
                message = getString(R.string.error_layout_troubleshoot)
            ),
            backgroundIcon = R.drawable.ic_no_internet,
            onCustomRetryLoadPageFun = {
                manifest?.let { loadAIChatBoxMiniApp(manifest = it, force = true) }?:bindData()
            }
        )
    }

    private fun showLoadingUi() {
        binding.lvAiChatBot.post {
            binding.lvAiChatBot.show()
            hidePageErrorV2()
        }
    }

    private fun hideLoadingUI(){
        binding.lvAiChatBot.gone()
    }

    private fun showMiniAppView() {
        binding.miniAppView.post {
            binding.lvAiChatBot.gone()
            hidePageErrorV2()
        }
    }

    private fun showSnackBarMessage(
        activity: Activity,
        text: String,
        iconUrl: String?= null,
        action: String = "",
        actionClickListener: (() -> Unit)? = null
    ) {
        if (activity is BaseActivity) {
            activity.showSnackBarMessage(
                text = text,
                action = action,
                iconUrl = iconUrl,
                iconLocal = null,
                actionClickListener = actionClickListener,
            )
        } else {
            showSnackbar(text)
        }
    }

    private fun showKeyboard() {
        _binding?.let {
            binding.root.showKeyboard()
        }
    }

    private fun hideKeyboard() {
        _binding?.let {
            binding.root.hideKeyboard()
        }
    }

    private fun processErrorMiniApp(miniAppManifest: MiniAppManifest?, data:MiniAppNativeErrorEventData){
        if(data.code !=null) {
            when(data.code){
                AIChatBotConstants.ERROR_CODE_AUTH_FAILED,
                AIChatBotConstants.ERROR_CODE_NETWORK_ERROR ->{
                    miniAppManifest?.let { loadAIChatBoxMiniApp(manifest = it, force = true) }?:bindData()
                }
            }
        }
    }
}