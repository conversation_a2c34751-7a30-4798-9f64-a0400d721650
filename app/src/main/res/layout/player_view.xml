<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.tear.modules.player.exo.ExoPlayerView
        android:id="@+id/epv_player"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:useController="false" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_image_hover"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toBottomOf="@id/epv_player"
        app:layout_constraintEnd_toEndOf="@id/epv_player"
        app:layout_constraintStart_toStartOf="@id/epv_player"
        app:layout_constraintTop_toTopOf="@id/epv_player"/>

    <ViewStub
        android:id="@+id/vt_show_matrix_ip"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="@id/epv_player"
        app:layout_constraintBottom_toBottomOf="@id/epv_player"
        app:layout_constraintStart_toStartOf="@id/epv_player"
        app:layout_constraintEnd_toEndOf="@id/epv_player"
        android:layout="@layout/viewstub_show_matrix_ip" />

    <ViewStub
        android:id="@+id/vt_show_ip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout="@layout/viewstub_show_ip"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/vt_limit_ccu"
        android:layout="@layout/viewstub_limit_ccu"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="@id/epv_player"
        app:layout_constraintBottom_toBottomOf="@id/epv_player"
        app:layout_constraintStart_toStartOf="@id/epv_player"
        app:layout_constraintEnd_toEndOf="@id/epv_player" />

    <com.fptplay.mobile.features.ads.view.AdsView
        android:id="@+id/ads_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="@id/epv_player"
        app:layout_constraintBottom_toBottomOf="@id/epv_player"
        app:layout_constraintStart_toStartOf="@id/epv_player"
        app:layout_constraintEnd_toEndOf="@id/epv_player"
        />

    <com.fptplay.mobile.player.PlayerUIView
        android:id="@+id/playerUI"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/epv_player"
        app:layout_constraintEnd_toEndOf="@id/epv_player"
        app:layout_constraintStart_toStartOf="@id/epv_player"
        app:layout_constraintTop_toTopOf="@id/epv_player" />

    <ViewStub
        android:id="@+id/vt_debug_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout="@layout/view_stub_player_debug_info" />

</androidx.constraintlayout.widget.ConstraintLayout>