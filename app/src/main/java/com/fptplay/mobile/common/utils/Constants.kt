package com.fptplay.mobile.common.utils

object Constants {
    const val RELOAD_PAGE = "reloadPage"
    const val SHORT_VIDEO_ID = "ShortVideoID"
    const val SHORT_VIDEO_CHAPTER = "ShortVideoChapter"

    const val WARNING_REQUEST_KEY = "WarningKey"
    const val WARNING_RESULT_EXTRA = "WarningResultExtra"
    const val WARNING_RESULT = "WarningResult"

    const val POP_UP_TO_ID = "popupToId"
    const val POP_UP_TO_INCLUSIVE = "popUpToInclusive"
    const val NAVIGATION_ID = "navigationId"
    const val ID_TO_PLAY = "idToPlay"
    const val TIME_SHIFT_LIMIT = "timeShiftLimit"
    const val TIME_SHIFT = "timeShift"
    const val ENTER_REGISTER = "enterRegister"
    const val CHECK_REQUIRE_VIP = "checkRequireVip"
    const val IS_PLAYLIST = "isPlaylist"
    const val REQUEST_RESTART_APP = "requestRestartApp"
    const val REQUEST_FROM_ONBOARDING = "requestFromOnBoarding"
    const val LOGIN_NAVIGATE_EXTRA_DATA = "loginNavigateExtraData"
    const val PREVIOUS_BACK_STACK_ENTRY_ID = "previousBackStackEntryId"
    const val IS_SELECTION_ON_BOARDING = "isSelectionOnBoarding"
    const val IS_FROM_LOGIN_SCREEN = "isFromLoginScreen"

    const val HOME_TYPE = "home"
    const val LIVE_TV_TYPE = "livetv"
    const val TV_CHANNEL = "tvchannel"
    const val PAGE_TYPE = "page"
    const val VOD_TYPE = "vod"
    const val FOLLOW_TYPE = "follow"
    const val COMING_SOON = "coming_soon"
    const val FOLLOW_CHANNEL_TYPE = "follow_channel"
    const val WATCHING_TYPE = "watching"
    const val COMMENT_VOD_TYPE = "vod"
    const val COMMENT_EVENT_TYPE = "event"

    const val SPORT_INTERACTIVE_LOG_KIBANA = "sport_interactive_log_kibana"
    const val SPORT_INTERACTIVE_LOG_SCREEN = "sport_interactive_log_screen"
    const val SPORT_INTERACTIVE_LOG_EVENT = "sport_interactive_log_event"
    const val SPORT_INTERACTIVE_LOG_ITEM_NAME = "sport_interactive_log_item_name"

    //tablet
    const val LOGIN_SHOW = "login_show"
    const val LOGIN_SUCCESS = "login_success"
    const val LOGIN_SUCCESS_KEY = "login_success_key"
    const val HAVE_CHANGE_DATA = "have_change_data"

    const val LOGIN_SUCCESS_FOR_DIALOG = "login_success_for_dialog"
    const val LOGIN_DELETE_DEVICE = "login_delete_device"
    const val LOGIN_DELETE_DEVICE_STATUS = "login_delete_device_status"
    const val LOGIN_DELETE_DEVICE_STATUS_SUCCESS = "login_delete_device_status_success"
    const val LOGIN_DELETE_DEVICE_STATUS_FAIL = "login_delete_device_status_fail"
    const val LOGIN_DELETE_DEVICE_STATUS_ON_LOGIN = "login_delete_device_status_login"

    //device_manager
    const val REFRESH_DATA = "refresh_data"

    const val WATCHING_VERSION = "v1"

    const val LANDING_PAGE_FRAGMENT_HOME_TYPE = "home"
    const val LANDING_PAGE_FRAGMENT_LIVE_TV_TYPE = "livetv"
    const val LANDING_PAGE_FRAGMENT_VOD_TYPE = "vod"
    const val LANDING_PAGE_FRAGMENT_EVENT_TYPE = "event"
    const val LANDING_PAGE_FRAGMENT_EVENT_TV_TYPE = "eventtv"
    const val LANDING_PAGE_FRAGMENT_OVERLOAD_TYPE = ""

    const val HIGHLIGHT_LIVE_TYPE = "live"
    const val HIGHLIGHT_COMING_TYPE = "coming"

    const val CHAT_EVENT_REAL_TIME_TYPE = "realtime"

    //endregion
    //region Zendesk help center
    const val HELP_CENTER_ARTICLES_FOR_CATEGORY_IDS = 360001108971L
    const val FIREBASE_NOTIFICATION_TYPE_ZENDESK = "firebase-zendesk"
    //endregion

    //Notification
    const val NOTIFICATION_BUNDLE_KEY = "notification-bundle-key"

    //Ratio Width Image
    const val HORIZONTAL_IMAGE_RATIO = 1.78
    const val VERTICAL_IMAGE_RATIO = 0.67

    //Inbox And Notification
    const val INBOX_MESSAGE_ID_KEY = "inbox-message-id-key"
    const val INBOX_MESSAGE_TITLE_KEY = "inbox-message-title-key"
    const val INBOX_MESSAGE_TIMESTAMP_KEY = "inbox-message-timestamp-key"
    const val NOTIFICATION_FIRE_STORE_BUNDLE_KEY = "notification-fire-store-bundle-key"
    const val NOTIFICATION_FIRE_STORE_ID_KEY = "notification-fire-store-id-key"
    const val NOTIFICATION_FIRE_STORE_TYPE_KEY = "notification-fire-store-type-key"
    const val NOTIFICATION_FIRE_STORE_TITLE_KEY = "notification-fire-store-title-key"
    const val NOTIFICATION_FIRE_STORE_IMAGE_KEY = "notification-fire-store-image-key"
    const val NOTIFICATION_FIRE_STORE_HAVE_DATA_KEY = "notification-fire-store-have-data-key"

    //FireBase Messaging
    const val FIREBASE_NOTIFICATION_NEW_ACTION = "firebase-notification-new-action"
    const val FIREBASE_NOTIFICATION_NEW = "firebase-notification-new"
    const val FIREBASE_NOTIFICATION_TYPE = "firebase-notification-type"
    const val FIREBASE_NOTIFICATION_TYPE_ID = "firebase-notification-type-id"
    const val FIREBASE_NOTIFICATION_URL = "firebase-notification-url"
    const val FIREBASE_NOTIFICATION_TITLE = "firebase-notification-title"
    const val FIREBASE_NOTIFICATION_FILED_TITLE = "title"
    const val FIREBASE_NOTIFICATION_FILED_MESSAGE = "message"
    const val FIREBASE_NOTIFICATION_FILED_IMAGE = "image"
    const val FIREBASE_NOTIFICATION_FILED_URL = "url"
    const val FIREBASE_NOTIFICATION_FILED_MESSAGE_ID = "message_id"
    const val FIREBASE_NOTIFICATION_FILED_TYPE = "type"
    const val FIREBASE_NOTIFICATION_FILED_TYPE_ID = "type_id"
    const val FIREBASE_NOTIFICATION_FILED_TYPE_TICKET = "ticket"
    const val FIREBASE_NOTIFICATION_FILED_TYPE_LIVE_TV = "livetv"
    const val FIREBASE_NOTIFICATION_FILED_TYPE_VOD = "vod"
    const val FIREBASE_NOTIFICATION_FILED_TYPE_EVENT_TV = "eventtv"
    const val FIREBASE_NOTIFICATION_FILED_TYPE_EVENT = "event"

    const val FIREBASE_NOTIFICATION_FILED_TYPE_CATEGORY = "category"
    const val FIREBASE_NOTIFICATION_FILED_TYPE_PREMIERE = "premiere"
    const val FIREBASE_NOTIFICATION_TYPE_LOYALTY = "loyalty"

    const val GROUP_CHAT_ROOM_ID_KEY = "group-chat-room-id-key"
    const val GROUP_CHAT_NOTIFICATION_TYPE = "rcon"

    //endregion
    //region Detail VOD Constants
    const val HORIZONTAL_IMAGE_TYPE = "horizontal"
    const val VERTICAL_IMAGE_TYPE = "vertical"
    var currentImageType: String = HORIZONTAL_IMAGE_TYPE
    const val SOURCE_PROVIDER_HBO_GO = "hbo"


    //Detail
    const val DETAIL_VOD_TYPE_GROUP_KEY = "detail-vod-type-group-key"
    const val DETAIL_VOD_BUNDLE_KEY = "detail-vod-bundle-key"
    const val DETAIL_VOD_ID_KEY = "detail-vod-id-key"
    const val DETAIL_VOD_TITLE_KEY = "detail-vod-title-key"
    const val DETAIL_VOD_IMAGE_KEY = "detail-vod-image-key"
    const val DETAIL_VOD_SHOW_DIALOG_ASK_PLAY_FROM_HISTORY_KEY = "detail-vod-show-dialog-ask-play-from-history-key"
    const val DETAIL_VOD_JUST_CHANGE_BITRATE_KEY = "detail-vod-just-change-bitrate-key"
    const val DETAIL_VOD_JUST_CHANGE_BITRATE_TRACKING_KEY = "detail-vod-just-change-bitrate-tracking-key"
    const val DETAIL_VOD_LOCK_PLAYER_CONTROL_KEY = "detail-vod-lock-player-control-key"
    const val DETAIL_VOD_IS_NEEDED_RESET_ADS_KEY = "detail-vod-is-need-reset-ads-key"
    const val DETAIL_VOD_ACTION_KEY = "detail-vod-action-key"
    const val DETAIL_VOD_INDEX_KEY = "detail-vod-index-key"

    //Detail TV
    const val DETAIL_TV_BUNDLE_KEY = "detail-tv-bundle-key"
    const val DETAIL_TV_CHANNEL_SCHEDULES_KEY = "detail-tv-channel-schedules-key"
    const val DETAIL_TV_CHANNEL_ID_KEY = "detail-tv-channel-id-key"
    const val DETAIL_TV_CHANNEL_THUMB = "detail-tv-channel-thumb"
    const val DETAIL_TV_JUST_CHANGE_BITRATE_KEY = "detail-tv-just-change-bitrate-key"
    const val DETAIL_TV_JUST_CHANGE_BITRATE_TRACKING_KEY = "detail-tv-just-change-bitrate-tracking-key"
    const val DETAIL_TV_LOCK_PLAYER_CONTROL_KEY = "detail-tv-lock-player-control-key"
    const val DETAIL_TV_IS_NEEDED_RESET_ADS_KEY = "detail-tv-is-need-reset-ads-key"
    const val DETAIL_TV_ACTION_KEY = "detail-tv-action-key"

    const val MEGA_SERVICE_3G_URL = "https://fptplay.vn/dich-vu/3g"

    const val FOXPAY_SYSTEM_TOKEN_STAG = "Basic ZnB0X3BsYXlfc2RrOjVyUWJXaHAqI2hIdyEmaVk=" //Staging
    const val FOXPAY_SYSTEM_TOKEN_PRO = "Basic ZnB0X3BsYXlfc2RrOjhaYlZpeEt6QkJyOUlsN2M5YWg1" //Product
    const val SDK_FOXPAY_STAGING = 2
    const val SDK_FOXPAY_PRODUCTION = 3
    const val FOXPAY_DIRECT_HOME = 0
    const val FOXPAY_DIRECT_PHONE_CARD = 1

    //region ToolTip
    const val TOOLTIP_SETTING_PLAYER = "tooltip-setting-player"
    const val TOOLTIP_REGISTER_PACKAGE = "tooltip-register-package"
    const val TOOLTIP_HOME = "tooltip-click-into-tool-tip-home"
    const val TOOLTIP_TV = "tooltip-click-into-tool-tip-tv"
    const val TOOLTIP_SPORT_INTERACTIVE = "tooltip-sport-interactive"
    const val TOOLTIP_SUMMARY_COMMENT = "tooltip-summary-comment"

    //endregion ToolTip

    //region FloatingButton
    const val FLOATING_BUTTON_BOTTOM_RIGHT = "BR"
    const val FLOATING_BUTTON_BOTTOM_LEFT = "BL"
    const val FLOATING_BUTTON_ON = "1"
    //endregion FloatingButton

    //category
    const val CATEGORY_OF_CATEGORIES_BUNDLE_KEY = "cfc-bundle-key"
    const val CATEGORY_OF_CATEGORIES_STYLE_KEY = "cfc-style-key"
    const val CATEGORY_OF_CATEGORIES_TYPE_KEY = "cfc-type-key"
    const val CATEGORY_OF_CATEGORIES_ID_KEY = "cfc-id-key"
    const val CATEGORY_OF_CATEGORIES_TITLE_KEY = "cfc-name-key"
    // Player
    const val PLAYER_RATIO = 16.0 / 9
    const val PLAYER_SCALED_RATIO = 0.65f
    const val PLAYER_HOVER_IMAGE_WIDTH = 1600
    const val PLAYER_HOVER_IMAGE_HEIGHT = 900
    //Download
    const val SELECT_QUALITY_DOWNLOAD = "select-quality-download"
    const val SELECT_QUALITY_DOWNLOAD_KEY = "select-quality-download-key"
    const val SCREEN_SETTING_DOWNLOAD_TO_POP_UP_QUALITY = "setting-download"
    const val SCREEN_VOD_TO_POP_UP_QUALITY = "vod"
    //Loyalty
    const val TYPE_ACCESS = "type_access"
    const val TYPE_CLOSE = "type_close"

    // loyalty
    const val LOYALTY_GOT_IT_BRAND_SELECT = "loyalty_got_it_brand_select"

    const val GIFT_USING_YET = "1"
    const val GIFT_USED = "2"
    const val GIFT_EXPIRE = "3"
    const val GIFT_TRANSPORT = "4"
    const val CONTRACT_APPLY = "contract_apply"
    const val SELECT_POS_CONTRACT_KEY = "select_pos_contract_key"
    const val PRICE_SELECTION = "price_selection"
    const val SELECT_POS_COINS_KEY = "select_pos_coins_key"
    const val API_SUCCESS = "0"
    const val API_ERROR = "1"
    const val API_ERROR_FRONT_IMAGE = "2"
    const val API_ERROR_BACK_IMAGE = "3"
    const val API_EKYC_FAIL = "1"
    const val API_EKYC_PENDING = "2"
    const val EXCHANGE_GIFT_SUCCESS = "0"
    const val EXCHANGE_GIFT_FAILED = "2"
    const val VND = "đ"
    const val MALE = "MALE"
    const val FEMALE = "FEMALE"
    const val ID_CHIP_FRONT = "cccd_chip_front"
    const val ID_CHIP_BACK = "chip_back"
    const val ID_12_FRONT = "cccd_12_front"
    const val ID_12_BACK = "new_back"
    const val ID_09_FRONT = "cmnd_09_front"
    const val ID_09_BACK = "old_back"
    const val NOT_VALUE = "N/A"

    // loyalty
    const val LIST_MEGA_APP_KEY = "list_mega_app_KEY"
    const val LIST_MEGA_APP = "list_mega_app"
    const val URL_JSON_MEGA_APP_KEY = "url_json_mega_app_key"
    const val URL_JSON_MEGA_APP = "url_json_mega_app"
    const val SORT_BY = "oldest"
    const val SORT_BY_DESCENDING = "lastest"

    //fptplay shop
    const val STRUCTURE_ITEM_TYPE_FPT_PLAY_SHOP = "fptplayshop"

    // mini app
    const val MINI_APP_SDK_SUPPORTED = "2.2.1"

    // region account
    const val ACCOUNT_TOOLBAR_HANDLE_BACK_COMPLETELY_EVENT = "account_toolbar_handle_back_event"
    const val ACCOUNT_DELETE_HANDLE_BACK_COMPLETELY_EVENT = "account_delete_handle_back_event"
    const val ACCOUNT_DELETE_HANDLE_BACK_COMPLETELY_EVENT_MESSAGE = "account_delete_handle_back_event_message"

    const val ACCOUNT_TOOLBAR_UPDATE_TITLE_EVENT = "account_toolbar_update_title_event"
    const val ACCOUNT_TOOLBAR_UPDATE_TITLE_KEY_TITLE = "account_toolbar_update_title_key_title"
    // endregion account

    // region SportInteractive
    const val SPORT_INTERACTIVE_NAVIGATE_REQUEST_KEY = "sport_interactive_navigate_request_key"
    const val SPORT_INTERACTIVE_NAVIGATE_REQUEST_CONTENT_ID = "sport_interactive_navigate_request_content_id"
    const val SPORT_INTERACTIVE_NAVIGATE_REQUEST_ITEM_TYPE = "sport_interactive_navigate_request_item_type"

    const val SPORT_INTERACTIVE_OPEN_REQUEST = "sport_interactive_open_request"
    const val SPORT_INTERACTIVE_OPEN_REQUEST_VALUE = "sport_interactive_open_request_value"

    const val SPORT_INTERACTIVE_OPEN_REQUEST_PADDING_START = "sport_interactive_open_request_start_padding"
    const val SPORT_INTERACTIVE_OPEN_REQUEST_PADDING_END = "sport_interactive_open_request_end_padding"
    const val SPORT_INTERACTIVE_OPEN_REQUEST_ROTATION = "sport_interactive_open_request_rotation"

    const val SPORT_INTERACTIVE_CLOSE_REQUEST = "sport_interactive_close_request"
    // endregion SportInteractive

    // region Follow
    const val PREMIERE_FOLLOW_NAVIGATE_LOGIN_REQUEST_KEY = "premiere_follow_navigate_login_request_key"
    const val PREMIERE_FOLLOW_NAVIGATE_LOGIN_VALUE = "premiere_follow_navigate_login_value"

    const val VOD_FOLLOW_NAVIGATE_LOGIN_REQUEST_KEY = "vod_follow_navigate_login_request_key"
    const val VOD_FOLLOW_NAVIGATE_LOGIN_VALUE = "vod_follow_navigate_login_value"

    const val VOD_DOWNLOAD_NAVIGATE_LOGIN_REQUEST_KEY = "vod_download_navigate_login_request_key"

    const val VOD_FOLLOW_VALUE = "vod_follow_value"
    const val PREMIERE_FOLLOW_VALUE = "premiere_follow_value"

    const val EXTENDS_ARG_NAVIGATE_LOGIN_KEY= "extends_args_login_key"

    // endregion Follow
    // region Home
    const val HOME_BOOK_ITEM_NAVIGATE_LOGIN_TYPE_KEY = "home_book_item_navigate_login_type_key"
    const val HOME_BOOK_ITEM_NAVIGATE_LOGIN_ID_KEY = "home_book_item_navigate_login_id_key"
    const val HOME_BOOK_ITEM_VALUE = "home_book_item_value"

    // endregion home

    const val EVENT_LOG_ADD_ALARM = "AddAlarm"
    const val EVENT_LOG_REMOVE_ALARM = "RemoveAlarm"

    const val GROUP_BY_INTERNET = "group_by_internet"

    // report player
    const val PLAYER_REPORT_DETAIL_KEY = "account_toolbar_handle_back_event"

    // region Preview
    const val PREVIEW_SHOULD_NAVIGATE_TO_PAYMENT_KEY = "should_navigate_to_payment_key"
    const val PREVIEW_SHOULD_NAVIGATE_TO_PAYMENT_VALUE = "should_navigate_to_payment_value"

    // endregion

    // region PiP
    const val PLAYER_PIP_BROADCAST_EVENT = "player_pip_broadcast_event"
    const val PLAYER_PIP_BROADCAST_EVENT_TYPE = "player_pip_broadcast_event_type"
    const val PLAYER_PIP_BROADCAST_EVENT_SOURCE = "player_pip_broadcast_event_source"
    // endregion

    const val EVENT_END_TIME_REMOVE_ITEM_KEY = "event_end_time_remove_item_key"
    const val EVENT_END_TIME_REMOVE_ITEM_VALUE = "event_end_time_remove_item_value"


    const val INTERVAL_TIME_TO_SHOW_SAVE_UTM_DEEPLINK_IN_DAYS = 30L

    // region pladio
    const val PLADIO_TOOLBAR_HANDLE_BACK_COMPLETELY_EVENT = "pladio_toolbar_handle_back_event"

    // region moment
    const val MOMENT_AUTO_COMMENT = "moment_auto_comment"
    const val MOMENT_COMMENT_DATA = "moment_comment_data"
    const val MOMENT_COMMENT_PARENT_ID = "moment_comment_parent_id"
    const val MOMENT_ITEM_IN_ROW = 1
    const val MOMENT_PRELOAD_OFFSET = 2
    const val MOMENT_FEATURE_SHORT_VOD = "short_vod"
    // endregion

    // region Chat Mini App
    const val ACTION_AFTER_LOGIN_KEY = "action_after_login"
    const val ACTION_OPEN_CHAT = "action_open_chat"
    const val ACTION_OPEN_COMMENT = "action_open_comment"
    // endregion

    // region PreCaching
    const val PRE_CACHING_KEY_LIST_DATA = "pre_caching_key_list_data"
    // endregion

    // region Summary Comment
    const val SUMMARY_COMMENT_TEXT_UPDATE_KEY = "summary_comment_text_update_key"
    const val SUMMARY_COMMENT_TEXT_UPDATE_VALUE = "summary_comment_text_update_key"
    //endregion

    const val SHORT_VIDEO_TAB_ID_DEFAULT = "other"

    const val TAB_ID_NEED_RELOAD = "tab_id_need_reload"
    const val NEED_RELOAD = "need_reload"
}

sealed class PageId(val id: String) {
    object HomePageId : PageId("home")
    object TvPageId : PageId("channel")
    object SportPageId : PageId("sport")
    object HboPageId : PageId("hbogo")
    object AppPageId : PageId("app")
    object ShortVideoPageId : PageId("short-videos")
    object PladioPageId : PageId("m-podcast")
    object PladioSearchPageId : PageId("m-search")
    object LibraryPageId : PageId("library")
    data class OtherPageId(val _id: String) : PageId(_id)
}

sealed class PageProvider(val pageId: PageId) {
    object Home : PageProvider(PageId.HomePageId)
    object Sport : PageProvider(PageId.SportPageId)
    object HBO : PageProvider(PageId.HboPageId)
    object TV : PageProvider(PageId.TvPageId)
    object Pladio : PageProvider(PageId.PladioPageId){}
    object PladioSearch : PageProvider(PageId.PladioSearchPageId){}
    object Library : PageProvider(PageId.LibraryPageId){}

    data class Other(val _id: String) : PageProvider(PageId.OtherPageId(_id))
}

enum class PlayerPipEventType(val value: Int) {
    USER_LEAVE_HINT(0),
    PIP_OPEN(1),
    PIP_CLOSE(2)
}