package com.fptplay.mobile.player.utils

import androidx.media3.common.C
import androidx.media3.common.Format
import com.tear.modules.player.util.PlayerControlView
import com.tear.modules.player.util.Track
import com.tear.modules.player.util.TrackType
import com.xhbadxx.projects.module.util.logger.Logger
import androidx.media3.common.TrackGroup
import androidx.media3.common.util.UnstableApi

@UnstableApi
object TrackHelper {

    var currentAudioTrack : PlayerControlView.Data.Track ?= null
    var currentSubtitleTrack : PlayerControlView.Data.Track ?= null

    fun checkIfNoTrackSelected(data : ArrayList<PlayerControlView.Data.Track>) : ArrayList<PlayerControlView.Data.Track> {
        val result = arrayListOf<PlayerControlView.Data.Track>().apply { addAll(data) }
        var isExistAudioDefault = false
        var isExistSubDefault = false

        var audioCount = 0
        var subCount = 0
        result.forEach {
            when (it.type) {
                TrackType.AUDIO.ordinal -> {
                    audioCount += 1
                    if (it.id == "Tắt âm thanh") isExistAudioDefault = true
                }
                TrackType.TEXT.ordinal -> {
                    subCount += 1
                    if (it.id == "Tắt phụ đề" || it.name == "Tắt phụ đề") isExistSubDefault = true
                }
            }
        }

        // Filter track if the same "Type" && the same "Track Group Index"
        val filterList = result.distinctBy { Pair(it.type, it.trackGroupIndex) }
        result.clear()
        result.addAll(filterList)

        // Empty audio track list if have once track
        val countAudioTrack = result.filter { track -> track.type == TrackType.AUDIO.ordinal }.size
        if (countAudioTrack == 1) {
            result.removeAll { track -> track.type == TrackType.AUDIO.ordinal }
        }

        // Add default subtitle track
        if (!isExistSubDefault && subCount > 0) {
            result.add(0, PlayerControlView.Data.Track(id = "Tắt phụ đề", name = "Tắt phụ đề", type = TrackType.TEXT.ordinal, isSelected = false))
        }

        // Check track selected => if not, set track = off
        var hasOneAudioTrackSelected = false
        var hasOneSubtitleTrackSelected = false
        result.forEach {
            when (it.type) {
                TrackType.AUDIO.ordinal -> {
                    if (it.isSelected) hasOneAudioTrackSelected = true
                }
                TrackType.TEXT.ordinal -> {
                    if (it.isSelected) hasOneSubtitleTrackSelected = true
                }
            }
        }
        result.forEach {
            if (it.type == TrackType.AUDIO.ordinal && it.id == "Tắt âm thanh" && !hasOneAudioTrackSelected) {
                it.isSelected = true
            }
            if (it.type == TrackType.TEXT.ordinal && (it.id == "Tắt phụ đề" || it.name == "Tắt phụ đề") && !hasOneSubtitleTrackSelected) {
                it.isSelected = true
            }
        }
        // end
        return result
    }

    fun List<PlayerControlView.Data.Track>.findTrackById(id: String, type: Int) : PlayerControlView.Data.Track? {
        forEach { item ->
            if (item.id == id && item.type == type) {
                return item
            } else if ((item.id == "Tắt phụ đề" || item.name == "Tắt phụ đề") && item.id == id && item.type == 10002) {
                return item
            }
        }
        return null
    }

    fun List<PlayerControlView.Data.Track>.findTrackByName(name: String, type: Int) : PlayerControlView.Data.Track? {
        forEach { item ->
            if (item.name == name && item.type == type) {
                return item
            } else if ((item.id == "Tắt âm thanh" || item.name == "Tắt âm thanh") && item.name == name) {
                return item
            }
        }
        return null
    }

    fun Map<TrackType, List<Track>>.mapToDataTrack() : List<PlayerControlView.Data.Track> {
        val dataTrack = mutableListOf<PlayerControlView.Data.Track>()
        val dataVideoTrack = mutableListOf<PlayerControlView.Data.Track>()
        val dataAudioTrack = mutableListOf<PlayerControlView.Data.Track>()
        val dataTextTrack = mutableListOf<PlayerControlView.Data.Track>()
        val dataDefaultTrack = mutableListOf<PlayerControlView.Data.Track>()
        for ((trackType, listTrack) in this) {
            listTrack.forEachIndexed{ index, item ->
                when (trackType) {
                    TrackType.VIDEO -> if (item.name != "Không xác định") {
                        dataVideoTrack.add(PlayerControlView.Data.Track(id = index.toString(), name = item.name, iconVip = "", trackGroupIndex = item.trackGroupIndex, trackIndex = item.trackIndex, isSelected = item.selected, type = trackType.ordinal))
                    }
                    TrackType.AUDIO -> if (item.name != "Không xác định") {
                        dataAudioTrack.add(PlayerControlView.Data.Track(id = index.toString(), name = item.name, iconVip = "", trackGroupIndex = item.trackGroupIndex, trackIndex = item.trackIndex, isSelected = item.selected, type = trackType.ordinal))
                    }
                    TrackType.TEXT -> if (item.name != "Không xác định") {
                        dataTextTrack.add(PlayerControlView.Data.Track(id = index.toString(), name = item.name, iconVip = "", trackGroupIndex = item.trackGroupIndex, trackIndex = item.trackIndex, isSelected = item.selected, type = trackType.ordinal))
                    }
                    TrackType.DEFAULT -> if (item.name != "Không xác định") {
                        dataDefaultTrack.add(PlayerControlView.Data.Track(id = index.toString(), name = item.name, iconVip = "", trackGroupIndex = item.trackGroupIndex, trackIndex = item.trackIndex, isSelected = item.selected, type = trackType.ordinal))
                    }
                }
            }
        }
        if (dataAudioTrack.size > 1) {
            dataAudioTrack.add(0, PlayerControlView.Data.Track(id = "Tắt âm thanh", name = "Tắt âm thanh", iconVip = "", trackGroupIndex = 0, trackIndex = 0, isSelected = false, type = TrackType.AUDIO.ordinal))
            dataTrack.addAll(dataAudioTrack)
        }
        if (dataTextTrack.size > 0) {
            dataTextTrack.add(0, PlayerControlView.Data.Track(id = "Tắt phụ đề", name = "Tắt phụ đề", iconVip = "", trackGroupIndex = 0, trackIndex = 0, isSelected = false, type = TrackType.TEXT.ordinal))
        }
        dataTrack.addAll(dataVideoTrack)
        dataTrack.addAll(dataTextTrack)
        dataTrack.addAll(dataDefaultTrack)

        // Check track selected => if not, set track = off
        var hasOneAudioTrackSelected = false
        var hasOneSubtitleTrackSelected = false
        dataTrack.forEach {
            when (it.type) {
                TrackType.AUDIO.ordinal -> {
                    if (it.isSelected) hasOneAudioTrackSelected = true
                }
                TrackType.TEXT.ordinal -> {
                    if (it.isSelected) hasOneSubtitleTrackSelected = true
                }
            }
        }
        dataTrack.forEach {
            if (it.type == TrackType.AUDIO.ordinal && it.id == "Tắt âm thanh" && !hasOneAudioTrackSelected) {
                it.isSelected = true
            }
            if (it.type == TrackType.TEXT.ordinal && (it.id == "Tắt phụ đề" || it.name == "Tắt phụ đề") && !hasOneSubtitleTrackSelected) {
                it.isSelected = true
            }
        }
        // end

        return dataTrack
    }

//    fun updateCurrentPlayingInfo(tracksInfo: TracksInfo) {
//        // Reset current playing info
//        currentAudioTrack = null
//        currentSubtitleTrack = null
//        //
//
//        var trackGroupIndex = 0
//        var lastTrackType = -1
//        for (index in 0 until tracksInfo.trackGroupInfos.size) {
//            tracksInfo.trackGroupInfos[index]?.let { trackGroupInfo ->
//                // Group level information.
//                val trackType = trackGroupInfo.trackType
//                if (lastTrackType != trackType) {
//                    trackGroupIndex = 0
//                }
//
//                val trackInGroupIsSelected: Boolean = trackGroupInfo.isSelected
//                val group: TrackGroup = trackGroupInfo.trackGroup
//
//                when (trackType) {
//                    C.TRACK_TYPE_AUDIO -> {
//                        if (trackInGroupIsSelected) {
//                            for (i in 0 until group.length) {
//                                // Individual track information.
//                                val isSelected: Boolean = trackGroupInfo.isTrackSelected(i)
//                                if (isSelected) {
//                                    currentAudioTrack = PlayerControlView.Data.Track(trackGroupIndex = trackGroupIndex, trackIndex = i, type = TrackType.AUDIO.ordinal)
//                                }
//                            }
//                        }
//
//                    }
//                    C.TRACK_TYPE_TEXT -> {
//                        if (trackInGroupIsSelected) {
//                            for (i in 0 until group.length) {
//                                // Individual track information.
//                                val isSelected: Boolean = trackGroupInfo.isTrackSelected(i)
//                                if (isSelected) {
//                                    currentSubtitleTrack = PlayerControlView.Data.Track(trackGroupIndex = trackGroupIndex, trackIndex = i, type = TrackType.TEXT.ordinal)
//                                }
//                            }
//                        }
//                    }
//                    else -> {}
//                }
//                lastTrackType = trackType
//                trackGroupIndex += 1
//            }
//        }
//
//        if (currentAudioTrack == null) {
//            currentAudioTrack = PlayerControlView.Data.Track(id = "Tắt âm thanh", name = "Tắt âm thanh", type = TrackType.AUDIO.ordinal)
//        }
//        if (currentSubtitleTrack == null) {
//            currentSubtitleTrack = PlayerControlView.Data.Track(id = "Tắt phụ đề", name = "Tắt phụ đề", type = TrackType.TEXT.ordinal)
//        }
//    }
}