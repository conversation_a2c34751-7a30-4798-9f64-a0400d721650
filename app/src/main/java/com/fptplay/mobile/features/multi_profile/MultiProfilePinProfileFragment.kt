package com.fptplay.mobile.features.multi_profile

import android.graphics.Rect
import android.os.Bundle
import android.text.InputFilter
import android.text.InputType
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.EditText
import android.widget.Toast
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.ViewCompat
import androidx.core.view.marginBottom
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.getDisplayHeight
import com.fptplay.mobile.common.extensions.hideKeyboard
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.extensions.showSnackBar
import com.fptplay.mobile.common.ui.bases.BaseFullDialogFragment
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.common.utils.ZendeskUtils
import com.fptplay.mobile.databinding.MultiProfilePinProfileFragmentBinding
import com.fptplay.mobile.features.ads.utils.AdsUtils
import com.fptplay.mobile.features.login.LoginViewModel
import com.fptplay.mobile.features.multi_profile.utils.EditProfileScreenType
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.features.multi_profile.utils.PinProfileScreenType
import com.fptplay.mobile.features.multi_profile.utils.ResetPasswordScreenTargetType
import com.fptplay.mobile.features.multi_profile.views.PasswordProfileView
import com.fptplay.mobile.player.utils.PlayerPiPHelper
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.visible
import com.google.android.material.snackbar.Snackbar
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.otp.UserOtpType
import com.xhbadxx.projects.module.domain.entity.fplay.user.CheckPassword
import com.xhbadxx.projects.module.domain.entity.fplay.user.CheckPasswordUserType
import com.xhbadxx.projects.module.domain.entity.fplay.user.CheckProfilePin
import com.xhbadxx.projects.module.domain.entity.fplay.user.LoginProfile
import com.xhbadxx.projects.module.domain.entity.fplay.user.Profile
import com.xhbadxx.projects.module.domain.entity.fplay.user.UpdateProfile
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject


@AndroidEntryPoint
class MultiProfilePinProfileFragment :
    BaseFullDialogFragment<MultiProfileViewModel.MultiProfileState, MultiProfileViewModel.MultiProfileIntent>() {

    override val hasEdgeToEdge = true

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor
    private val safeArgs: MultiProfilePinProfileFragmentArgs by navArgs()

    override val viewModel: MultiProfileViewModel by activityViewModels()

    private var _binding: MultiProfilePinProfileFragmentBinding? = null
    private val binding get() = _binding!!

    private var globalLayoutListener: ViewTreeObserver.OnGlobalLayoutListener? = null

    private val avatarSize by lazy {
        Utils.getSizeInPixel(
            context = requireActivity(),
            resId = R.dimen.multi_profile_pin_profile_avatar_size
        )
    }

    private val requiredPinLength by lazy { 4 }
    private val requiredPasswordLength by lazy { 6 }

    private var edtLayout: EditText? = null
    private var oldType = ""
    private var oldId = ""
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.MultiProfilePinProfileBottomSheetDialogTheme)
    }
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = MultiProfilePinProfileFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }


    override fun onStop() {
        super.onStop()
        binding.root.viewTreeObserver.removeOnGlobalLayoutListener(globalLayoutListener)

    }

    override fun onDestroyView() {
        super.onDestroyView()
        edtLayout?.hideKeyboard()
        _binding = null
        edtLayout = null
    }

    override fun bindComponent() {
        binding.edtPin.filters = arrayOf(InputFilter.LengthFilter(requiredPinLength))
        binding.edtPassword.setMaxLength(requiredPasswordLength)
        binding.edtPassword.setTypeInputEditText(InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_VARIATION_PASSWORD)
        binding.edtPassword.setHint(getString(R.string.multi_profile_password_hint))
        binding.edtPassword.setError(null)

        binding.apply {
            tvErrorPin.gone()
            when (safeArgs.targetScreen) {
                PinProfileScreenType.VerifyPinLoginProfile -> {
                    tvHeader.text = safeArgs.title.ifBlank {
                        getString(R.string.multi_profile_pin_profile_verify_pin_title)
                    }
                    tvDes.text = safeArgs.description.ifBlank {
                        getString(R.string.multi_profile_pin_login_profile_des)
                    }

                    // hide password layout
                    edtPassword.gone()
                    tvResetPassword.gone()

                    //show profile avatar
                    vAvatar.visible()

                    groupEditPin.visible()
                    tvForgetPin.visible()
                    btnConfirmPin.gone()
                }

                PinProfileScreenType.ResetPinProfileAndLogin,
                PinProfileScreenType.UpdatePinProfile -> {
                    tvHeader.text = safeArgs.title.ifBlank {
                        getString(R.string.multi_profile_pin_profile_create_pin_title)
                    }
                    tvDes.text = safeArgs.description.ifBlank {
                        getString(R.string.multi_profile_pin_profile_des)
                    }


                    // hide password layout
                    edtPassword.gone()
                    tvResetPassword.gone()

                    //show profile avatar
                    vAvatar.visible()

                    groupEditPin.visible()
                    tvForgetPin.gone()
                    btnConfirmPin.visible()

                }

                PinProfileScreenType.VerifyPasswordForgetPin,
                PinProfileScreenType.VerifyPasswordEditProfile,
                PinProfileScreenType.VerifyPasswordLoginProfile -> {
                    tvHeader.text = safeArgs.title.ifBlank {
                        getString(R.string.multi_profile_password_title)
                    }
                    tvDes.text = safeArgs.description.ifBlank {
                        getString(R.string.multi_profile_password_des)
                    }

                    // hide pin layout
                    groupEditPin.gone()
                    tvForgetPin.gone()

                    //hide profile avatar
                    vAvatar.gone()

                    edtPassword.visible()
                    btnConfirmPin.visible()
                    tvResetPassword.visible()

                }

            }

        }

        edtLayout =  when (safeArgs.targetScreen) {
            PinProfileScreenType.VerifyPinLoginProfile,
            PinProfileScreenType.ResetPinProfileAndLogin,
            PinProfileScreenType.UpdatePinProfile -> binding.edtPin

            PinProfileScreenType.VerifyPasswordEditProfile,
            PinProfileScreenType.VerifyPasswordForgetPin,
            PinProfileScreenType.VerifyPasswordLoginProfile -> binding.edtPassword.passwordEdittext
        }

    }


    override fun bindData() {

//        binding.vAvatar.bindProfile(
//            url = safeArgs.profileAvatar,
//            width = avatarSize,
//            height = avatarSize,
//            kidProfile = MultiProfileUtils.isProfileKid(profileType = safeArgs.profileType),
//            privateProfile = MultiProfileUtils.isProfilePrivate(pinType = safeArgs.profilePinType)
//        )

        val profile = viewModel.newestProfileData ?: Profile()
        binding.vAvatar.bindProfileItem(
            profileItem = profile,
            width = avatarSize,
            height = avatarSize
        )
        oldType = sharedPreferences.profileType()
        oldId = sharedPreferences.profileId()
    }

    override fun bindEvent() {
        binding.vOutside.onClickDelay {
            Timber.tag("tam-multiProfile").e("click outside")
            handleBack()
        }
        binding.ivClose.onClickDelay {
            handleBack()
        }

        binding.flContain.onClickDelay {
            edtLayout?.hideKeyboard()
        }

        binding.tvResetPassword.onClickDelay {
//            edtLayout?.hideKeyboard()
//            AlertDialog().apply {
//                setShowTitle(true)
//                setTextTitle(<EMAIL>(R.string.multi_profile_switch_account_alert_title))
//                setMessage(<EMAIL>(R.string.multi_profile_switch_account_alert_des))
//                setTextConfirm(<EMAIL>(R.string.logout))
//                setListener(object : AlertDialogListener {
//                    override fun onConfirm() {
//                        viewModel.dispatchIntent(MultiProfileViewModel.MultiProfileIntent.Logout)
//                    }
//                })
//                isCancelable = false
//            }.show(<EMAIL>, "PopupSwitchAccount")

            viewModel.dispatchIntent(MultiProfileViewModel.MultiProfileIntent.RequestValidateUserPin(type = UserOtpType.LoginForgotPass))
//            val targetScreen = when(safeArgs.targetScreen) {
//                PinProfileScreenType.VerifyPasswordLoginProfile -> ResetPasswordScreenTargetType.LoginProfile
//                PinProfileScreenType.VerifyPasswordForgetPin -> ResetPasswordScreenTargetType.CreatePin
//                PinProfileScreenType.VerifyPasswordEditProfile -> ResetPasswordScreenTargetType.EditProfile
//                else -> null
//            } ?. let {
//
//                findNavController().navigate(
//                    MultiProfilePinProfileFragmentDirections.actionPinProfileToProfileVerifyOtpDialog(
//                        targetScreen = it,
//                        userPhone = sharedPreferences.userPhone(),
//                        countryCode = LoginViewModel.country.code
//                    )
//                )
//            }
        }

        binding.tvForgetPin.onClickDelay {
            viewModel.dispatchIntent(MultiProfileViewModel.MultiProfileIntent.GetUserInfo)

        }

        binding.edtPin.apply {
            doAfterTextChanged { editable ->
                Timber.tag("tam-multiProfile").i("edtPin onTextChange")
                binding.tvErrorPin.gone()
                editable?.toString()?.let {
                    updateConfirmButtonBackground()
                    if (it.length == requiredPinLength) {
                        hideKeyboard()
                        if(safeArgs.targetScreen == PinProfileScreenType.VerifyPinLoginProfile)
                            checkPin(screen = safeArgs.targetScreen, pinValue = text.toString())
                    }
                }
            }


//            setOnEditorActionListener { _, actionId, _ ->
//                if (actionId == EditorInfo.IME_ACTION_DONE) {
//                    val code = text.toString()
//                    if (code.length == requiredPinLength) {
//                        hideKeyboard()
//                        checkPin(screen = safeArgs.targetScreen, pinValue = text.toString())
//                    }
//                }
//                false
//            }
        }

        binding.edtPassword.passwordEdittext.doAfterTextChanged {
            Timber.tag("tam-multiProfile").i("edtPassword doAfterTextChanged")
            updateConfirmButtonBackground()
        }

        binding.edtPassword.onPasswordContentChangedListener = object: PasswordProfileView.PasswordContentChangedListener {
            override fun onContentChanged(oldText: String, newText: String) {
                // method strictly called when content changed, not just have action changed
                if (newText.length == requiredPasswordLength) {
                    binding.edtPassword.passwordEdittext.hideKeyboard()
                }
            }
        }

//        bindEventKeyBoardChange()
    }


    private fun bindEventKeyBoardChange() {
        binding.apply {
            if (globalLayoutListener != null) {
                root.viewTreeObserver.removeOnGlobalLayoutListener(globalLayoutListener)
            }
            globalLayoutListener = ViewTreeObserver.OnGlobalLayoutListener {
                val displayRect = Rect().apply { root.getWindowVisibleDisplayFrame(this) }
                Timber.tag("tam-multiProfile")
                    .d("globalLayoutListener ${activity?.getDisplayHeight() ?: 0} - ${displayRect.bottom}")
                val insets = activity?.let {
                    ViewCompat.getRootWindowInsets(it.window.decorView)
                }
                val keypadHeight = (activity?.getDisplayHeight() ?: 0) - displayRect.bottom

                if (keypadHeight > 200) {
                    Timber.tag("tam-multiProfile")
                        .d("open keyboard $keypadHeight - ${flContain.marginBottom}")
                    if (flContain.marginBottom != keypadHeight) {
                        Timber.tag("tam-multiProfile").i("open keyboard change margin bottom")
                        val param = flContain.layoutParams as ViewGroup.MarginLayoutParams
                        param.setMargins(0, 0, 0, keypadHeight)
                        flContain.layoutParams = param
//                        val param = layoutInput.layoutParams as ViewGroup.MarginLayoutParams
//                        param.setMargins(0, 0, 0, keypadHeight)
//                        layoutInput.layoutParams = param

                    } else return@OnGlobalLayoutListener
                } else {
                    Timber.tag("tam-multiProfile")
                        .d("close keyboard  $keypadHeight - ${flContain.marginBottom}")
                    val marginBottom = 0
                    if (flContain.marginBottom != marginBottom) {
                        Timber.tag("tam-multiProfile").i("close keyboard change margin bottom")
                        val param = flContain.layoutParams as ViewGroup.MarginLayoutParams
                        param.setMargins(0, 0, 0, marginBottom)
                        flContain.layoutParams = param
//                        val param = layoutInput.layoutParams as ViewGroup.MarginLayoutParams
//                        param.setMargins(0, 0, 0, marginBottom)
//                        layoutInput.layoutParams = param
                    } else return@OnGlobalLayoutListener

                }
            }

            root.viewTreeObserver.addOnGlobalLayoutListener(globalLayoutListener)

        }

    }

    private fun moveLayout(layout: View, keypadHeight: Int, marginWithoutKeypad: Int = 0) {
        Timber.tag("tam-multiProfile")
            .d("moveLayout $layout : $keypadHeight - ${layout.marginBottom}")
        if (keypadHeight > 200) {
            val displayRect = Rect().apply { layout.getWindowVisibleDisplayFrame(this) }
            Timber.tag("tam-multiProfile").d("open keyboard $keypadHeight - ${layout.marginBottom}")
            if (layout.marginBottom != keypadHeight) {
                Timber.tag("tam-multiProfile").i("open keyboard change margin bottom")
                val param = layout.layoutParams as ViewGroup.MarginLayoutParams
                param.setMargins(0, 0, 0, keypadHeight)
                layout.layoutParams = param

            } else return
        } else {
            Timber.tag("tam-multiProfile")
                .d("close keyboard  $keypadHeight - ${layout.marginBottom}")
            if (layout.marginBottom != marginWithoutKeypad) {
                Timber.tag("tam-multiProfile").i("close keyboard change margin bottom")
                val param = layout.layoutParams as ViewGroup.MarginLayoutParams
                param.setMargins(0, 0, 0, marginWithoutKeypad)
                layout.layoutParams = param
            } else return

        }
    }


    private fun updateConfirmButtonBackground() {
        val password = binding.edtPassword.password
        val pin = binding.edtPin.text

        val isEnable = when (safeArgs.targetScreen) {
            PinProfileScreenType.VerifyPinLoginProfile,
            PinProfileScreenType.ResetPinProfileAndLogin,
            PinProfileScreenType.UpdatePinProfile ->
                (pin?.length ?: 0) == requiredPinLength

            PinProfileScreenType.VerifyPasswordEditProfile,
            PinProfileScreenType.VerifyPasswordForgetPin,
            PinProfileScreenType.VerifyPasswordLoginProfile ->
                (password?.length ?: 0) >= requiredPasswordLength

        }
        if (isEnable) {
            binding.btnConfirmPin.isEnabled = true
            binding.btnConfirmPin.background = AppCompatResources.getDrawable(
                requireContext(),
                R.drawable.account_rounded_btn_background_enable
            )
            binding.btnConfirmPin.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.app_content_text_color
                )
            )
            binding.btnConfirmPin.onClickDelay {
                when (safeArgs.targetScreen) {
                    PinProfileScreenType.VerifyPinLoginProfile,
                    PinProfileScreenType.ResetPinProfileAndLogin -> checkPin(
                        safeArgs.targetScreen,
                        binding.edtPin.text.toString()
                    )

                    PinProfileScreenType.VerifyPasswordEditProfile,
                    PinProfileScreenType.VerifyPasswordForgetPin ,
                    PinProfileScreenType.VerifyPasswordLoginProfile -> checkPin(
                        safeArgs.targetScreen,
                        binding.edtPassword.password.toString()
                    )

                    PinProfileScreenType.UpdatePinProfile -> {
                        setFragmentResult(
                            Utils.PROFILE_UPDATE_PIN_EVENT,
                            bundleOf(Utils.PROFILE_UPDATE_PIN_VALUE to binding.edtPin.text.toString())
                        )
                        handleBack()
                    }
                }

            }
        } else {
            binding.btnConfirmPin.isEnabled = false
            binding.btnConfirmPin.background = AppCompatResources.getDrawable(
                requireContext(),
                R.drawable.account_rounded_btn_background_disable
            )
            binding.btnConfirmPin.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.app_content_text_disable_color
                )
            )
            binding.btnConfirmPin.setOnClickListener(null)
        }
    }

    private fun checkPin(screen: PinProfileScreenType, pinValue: String) {
        val intent = when (screen) {
            PinProfileScreenType.VerifyPinLoginProfile -> {
                MultiProfileViewModel.MultiProfileIntent.CheckPin(profileId = safeArgs.profileId, pinCode = pinValue)
            }

            PinProfileScreenType.ResetPinProfileAndLogin -> {
                MultiProfileViewModel.MultiProfileIntent.UpdateProfile(profileId = safeArgs.profileId, pinCode = pinValue)

            }
            PinProfileScreenType.VerifyPasswordLoginProfile -> {
                MultiProfileViewModel.MultiProfileIntent.CheckPassword(profileId = safeArgs.profileId, password = pinValue, checkPasswordType = CheckPasswordUserType.Profile)

            }

            PinProfileScreenType.VerifyPasswordForgetPin -> {
                MultiProfileViewModel.MultiProfileIntent.CheckPassword(profileId = safeArgs.profileId, password = pinValue, checkPasswordType = CheckPasswordUserType.Profile)
            }

            PinProfileScreenType.VerifyPasswordEditProfile -> {
                MultiProfileViewModel.MultiProfileIntent.CheckPassword(profileId = safeArgs.profileId, password = pinValue, checkPasswordType = CheckPasswordUserType.Profile)
            }

            PinProfileScreenType.UpdatePinProfile -> {
                null
            }
        }
        if(intent != null) {
            viewModel.dispatchIntent(intent)
        }

    }

    fun handleBack() {
        findNavController().navigateUp()
    }

    override fun MultiProfileViewModel.MultiProfileState.toUI() {
        when (this) {
            is MultiProfileViewModel.MultiProfileState.Loading -> {
                showLoadingView()
            }

            is MultiProfileViewModel.MultiProfileState.ErrorNoInternet -> {
                processError(intent = intent, message = message, errorState = this)
            }

            is MultiProfileViewModel.MultiProfileState.ErrorRequiredLogin -> {
                processError(intent = intent, message = message, errorState = this)
            }

            is MultiProfileViewModel.MultiProfileState.Error -> {
                processError(intent = intent, message = message, errorState = this)
            }

            is MultiProfileViewModel.MultiProfileState.ResultLogout -> {
                if (data.isSuccess()) {
                    AdsUtils.saveUserType(sharedPreferences, false)
                    trackingProxy.sendEvent(
                        InforMobile(
                            infor = trackingInfo,
                            logId = "180",
                            appId = TrackingUtil.currentAppId,
                            appName = TrackingUtil.currentAppName,
                            screen = "Logout",
                            event = "Logout"
                        )
                    )
                    //reset session and user phone when logout
                    Utils.clearUserData(sharedPreferences)
                    // Zendesk
                    ZendeskUtils.updateZendeskIdentity(userLogin = false, userToken = "", userTokenType = "")

                    // Pairing control
                    MainApplication.INSTANCE.pairingConnectionHelper.let {
                        if (it.isConnected) {
                            it.disconnect()
                        }
                    }
                    //

                    // Picture in Picture
                    PlayerPiPHelper.saveSupportPictureInPicture(sharedPreferences, isSupport = false)

                    MultiProfileUtils.restartHome(activity)

                } else {
                    Toast.makeText(context, errorMessage(data.message), Toast.LENGTH_SHORT).show()
                }
            }
            is MultiProfileViewModel.MultiProfileState.ResultUpdateProfile -> {
                processResultUpdateProfile(safeArgs.profileId, data)
            }
            is MultiProfileViewModel.MultiProfileState.ResultCheckPassword -> {
                processResultCheckPassword(safeArgs.profileId, data)
            }
            is MultiProfileViewModel.MultiProfileState.ResultCheckPin -> {
                processResultCheckPin(safeArgs.profileId, data)
            }

            is MultiProfileViewModel.MultiProfileState.ResultLoginProfile -> {
                processResultLoginProfile(data, pinScreenSource)
            }
            is MultiProfileViewModel.MultiProfileState.ResultGetUserInfo -> {
                processGetUserInfoBeforeForgetPin()
            }
            is MultiProfileViewModel.MultiProfileState.ResultValidateUserPin -> {
                if(data.status == "1" && data.errorCode == "0" ) {
                    when(safeArgs.targetScreen) {
                        PinProfileScreenType.VerifyPasswordLoginProfile -> ResetPasswordScreenTargetType.LoginProfile
                        PinProfileScreenType.VerifyPasswordForgetPin -> ResetPasswordScreenTargetType.CreatePin
                        PinProfileScreenType.VerifyPasswordEditProfile -> ResetPasswordScreenTargetType.EditProfile
                        else -> null
                    } ?. let {
                        val navOptions = NavOptions.Builder()
                            .setPopUpTo(safeArgs.popUpToId, safeArgs.popUpToInclusive)
                            .build()

                        findNavController().navigate(
                            MultiProfilePinProfileFragmentDirections.actionPinProfileToProfileVerifyOtpDialog(
                                targetScreen = it,
                                userPhone = sharedPreferences.userPhone(),
                                countryCode = LoginViewModel.country.code,
                                verifyToken = data.verifyToken,
                                popUpToId = safeArgs.popUpToId,
                                popUpToInclusive = safeArgs.popUpToInclusive
                            ), navOptions
                        )
                    }
                }
                else {
                    binding.root.showSnackBar(title = data.msg, duration = Snackbar.LENGTH_LONG)
                }
            }
            is MultiProfileViewModel.MultiProfileState.Done -> {
                hideLoadingView()
            }
            else -> {}
        }
    }


    private fun processResultCheckPassword(profileId: String, data: CheckPassword) {
        Timber.tag("tam-multiProfile").d("processResultCheckPassword $data")
        if(!data.isSuccess()) {
            binding.edtPassword.setError(errorMessage(data.message))

//            if(data.message.isNotBlank()) {
//                binding.edtPassword.setError(data.message)
//            } else {
//                binding.edtPassword.setError(context?.getString(R.string.multi_profile_pin_profile_wrong_password_msg))
//            }
            return
        }
        binding.edtPassword.setError(null)
        when(safeArgs.targetScreen) {
            PinProfileScreenType.VerifyPasswordLoginProfile -> {
                viewModel.dispatchIntent(MultiProfileViewModel.MultiProfileIntent.LoginProfile(profileId = profileId, pinScreenSource = safeArgs.targetScreen))
            }

            PinProfileScreenType.VerifyPasswordForgetPin -> {
                val navOptions = NavOptions.Builder()
                    .setPopUpTo(safeArgs.popUpToId, safeArgs.popUpToInclusive)
                    .build()

                findNavController().navigate(
                    MultiProfilePinProfileFragmentDirections.actionMultiProfilePinProfileFragmentSelf(
                        targetScreen = PinProfileScreenType.ResetPinProfileAndLogin,
                        title = getString(R.string.multi_profile_pin_profile_create_pin_title),
                        description = getString(R.string.multi_profile_pin_reset_pin_des),
                        profileId = safeArgs.profileId,
                        isFromLoginScreen = safeArgs.isFromLoginScreen,
                        isSelectionOnBoarding = safeArgs.isSelectionOnBoarding,
                        popUpToId = safeArgs.popUpToId,
                        popUpToInclusive = safeArgs.popUpToInclusive
                    ), navOptions
                )

            }
            PinProfileScreenType.VerifyPasswordEditProfile -> {
                // navigate to edit profile
                findNavController().navigate(
                    MultiProfilePinProfileFragmentDirections.actionPinProfileToEditProfile(
                        targetScreen = EditProfileScreenType.EditProfile,
                        profileId = safeArgs.profileId
                    )
                )
            }
            else -> {}

        }

    }

    private fun processResultCheckPin(profileId: String, data: CheckProfilePin) {
        if(!data.isSuccess()) {
            binding.tvErrorPin.text =  errorMessage(data.message)

//            if(data.message.isNotBlank()) {
//                binding.tvErrorPin.text = data.message
//
//            } else {
//                binding.tvErrorPin.setText(R.string.multi_profile_pin_profile_wrong_pin_msg)
//            }
            binding.tvErrorPin.visible()
            return
        }

        viewModel.dispatchIntent(MultiProfileViewModel.MultiProfileIntent.LoginProfile(profileId = profileId, pinScreenSource = safeArgs.targetScreen))

    }

    private fun processResultUpdateProfile(profileId: String, data: UpdateProfile) {
        if(!data.isSuccess()) {
            binding.tvErrorPin.text = errorMessage(data.message)

//            if(data.message.isNotBlank()) {
//                binding.tvErrorPin.text = data.message
//
//            } else {
//                binding.tvErrorPin.setText(R.string.multi_profile_pin_profile_create_pin_fail_msg)
//            }
            binding.tvErrorPin.visible()
            return
        }
        val message = data.message.ifBlank {
            getString(R.string.multi_profile_pin_profile_create_pin_success_msg)
        }
        binding.root.showSnackBar(title = message, duration = Snackbar.LENGTH_LONG)

        viewModel.dispatchIntent(MultiProfileViewModel.MultiProfileIntent.LoginProfile(profileId = profileId, pinScreenSource = safeArgs.targetScreen))

    }

    private fun processGetUserInfoBeforeForgetPin() {
        fun navigateToVerifyPinSettingFun() {
            val navOptions = NavOptions.Builder()
                .setPopUpTo(safeArgs.popUpToId, safeArgs.popUpToInclusive)
                .build()

            findNavController().navigate(
                MultiProfilePinProfileFragmentDirections.actionMultiProfilePinProfileFragmentSelf(
                    targetScreen = PinProfileScreenType.VerifyPasswordForgetPin,
                    title = getString(R.string.multi_profile_password_title),
                    description = getString(R.string.multi_profile_password_forget_pin_des),
                    profileId = safeArgs.profileId,
                    isFromLoginScreen = safeArgs.isFromLoginScreen,
                    isSelectionOnBoarding = safeArgs.isSelectionOnBoarding,
                    popUpToId = safeArgs.popUpToId,
                    popUpToInclusive = safeArgs.popUpToInclusive
                ), navOptions
            )

        }

        fun navigateToCreatePinSettingFun() {
            val targetScreen = when(safeArgs.targetScreen) {
                PinProfileScreenType.VerifyPinLoginProfile -> ResetPasswordScreenTargetType.CreatePin
                else -> null
            } ?. let {

                findNavController().navigate(
                    MultiProfilePinProfileFragmentDirections.actionPinProfileToProfileResetPasswordDialog(
                        createPinSetting = true,
                        targetScreen = it,
                        verifyToken = ""
                    )
                )
            }

        }
        if(sharedPreferences.allowPin()) {
            showAlertDialog(
                title = getString(R.string.f_id_create_pin_setting_title),
                message = getString(R.string.f_id_create_pin_setting_description),
                textConfirm = getString(R.string.f_id_create_pin_setting_confirm_text),
                textClose = getString(R.string.f_id_create_pin_setting_close_text),
                onConfirm = {
                    navigateToCreatePinSettingFun()
                },
                isCancelled = true,
                showTitle = true
            )

        } else {

            if(sharedPreferences.shouldShowPopUpPin(sharedPreferences.userId())) {
                sharedPreferences.setShouldShowPopUpPin(sharedPreferences.userId(), false)
                showWarningMessageDialog(
                    title = getString(R.string.f_id_update_pin_setting_title),
                    message = getString(R.string.f_id_update_pin_setting_description),
                    textConfirm = getString(R.string.mini_app_alert_dialog_error_confirm_text),
                    onConfirm = {
                        navigateToVerifyPinSettingFun()
                    }
                )
            } else {
                navigateToVerifyPinSettingFun()
            }
        }


    }
    private fun processResultLoginProfile(data: LoginProfile, sourceScreen: PinProfileScreenType?) {
        if(sourceScreen == null) {
            // request not come from pin screen
            return
        }
        if(!data.isSuccess()) {
            if(safeArgs.targetScreen == PinProfileScreenType.ResetPinProfileAndLogin) {
                // if login fail (login only after success create pin profile), navigate back to layout check pin profile
                binding.root.showSnackBar(title = getString(R.string.multi_profile_pin_profile_create_pin_success_msg))
                val navOptions = NavOptions.Builder()
                    .setPopUpTo(safeArgs.popUpToId, safeArgs.popUpToInclusive)
                    .build()

                findNavController().navigate(
                    MultiProfilePinProfileFragmentDirections.actionMultiProfilePinProfileFragmentSelf(
                        targetScreen = PinProfileScreenType.VerifyPinLoginProfile,
                        title = getString(R.string.multi_profile_pin_profile_verify_pin_title),
                        description = getString(R.string.multi_profile_pin_login_profile_des),
                        profileId = safeArgs.profileId,
                        isFromLoginScreen = safeArgs.isFromLoginScreen,
                        isSelectionOnBoarding = safeArgs.isSelectionOnBoarding,
                        popUpToId = safeArgs.popUpToId,
                        popUpToInclusive = safeArgs.popUpToInclusive
                    ), navOptions
                )


            } else {
                binding.root.showSnackBar(title = errorMessage(data.message))
            }
            return
        }
        MainApplication.INSTANCE.isOpenOnBoardingAndWithProfile = false // update status first open app for onboard
        MultiProfileUtils.saveProfileSelectionShownTime(sharedPreferences)
        if (sharedPreferences.getEnableProfileOnboarding() == "1" && data.data.statusOnboarding == "0") {
            // navigate to onboarding
            MultiProfileUtils.switchProfile(
                activity = activity,
                sharedPreferences = sharedPreferences,
                profile = data.data,
                restartAppAfterSwitch = false
            )
            navigateToMultiProfileOnBoarding(data.data.id)
        } else {
            if (safeArgs.isFromLoginScreen){
                /**
                 * Note : callback to login profile success for Login Fragment
                 * **/
                MultiProfileUtils.sendEventProfileOnboardingIfSwitchProfileWithPin(
                    activity = activity,
                    profile = data.data,
                    fragment = this,
                    sharedPreferences = sharedPreferences,
                    sourceChange = "${this.javaClass.simpleName} - backhandler",
                    oldProfileId = oldId,
                    oldProfileType = oldType
                )
                findNavController().navigateUp()
            }else{
                MultiProfileUtils.switchProfile(
                    activity = activity,
                    sharedPreferences = sharedPreferences,
                    profile = data.data,
                    restartAppAfterSwitch = true,
                    restartKeepIntent = safeArgs.isSelectionOnBoarding
                )
            }
        }
    }

    private fun navigateToMultiProfileOnBoarding(profileId:String){
        findNavController().navigate(MultiProfilePinProfileFragmentDirections.actionPinProfileToOnboardingProfile(
            profileId = profileId,isFromLoginScreen = safeArgs.isFromLoginScreen
        ))
    }

    private fun processError(intent: MultiProfileViewModel.MultiProfileIntent?, message: String, errorState: MultiProfileViewModel.MultiProfileState) {
        when(intent) {
            is MultiProfileViewModel.MultiProfileIntent.RequestValidateUserPin,
            is MultiProfileViewModel.MultiProfileIntent.CheckPin,
            is MultiProfileViewModel.MultiProfileIntent.CheckPassword,
            is MultiProfileViewModel.MultiProfileIntent.GetUserInfo,
            is MultiProfileViewModel.MultiProfileIntent.UpdateProfile -> {
                if(errorState is MultiProfileViewModel.MultiProfileState.ErrorNoInternet) {
                    showWarningMessageDialog(
//                        title = getString(R.string.f_id_update_pin_setting_title),
                        message = message,
                        textConfirm = getString(R.string.alert_close),
                    )

                } else {
                    binding.root.showSnackBar(title = errorMessage(message))
                }
            }
            is MultiProfileViewModel.MultiProfileIntent.LoginProfile-> {
                if(safeArgs.targetScreen == PinProfileScreenType.ResetPinProfileAndLogin) {
                    // if login fail (login only after success create pin profile), navigate back to layout check pin profile
                    binding.root.showSnackBar(title = requireContext().getString(R.string.multi_profile_pin_profile_create_pin_success_msg))
                    val navOptions = NavOptions.Builder()
                        .setPopUpTo(safeArgs.popUpToId, safeArgs.popUpToInclusive)
                        .build()
                    findNavController().navigate(
                        MultiProfilePinProfileFragmentDirections.actionMultiProfilePinProfileFragmentSelf(
                            targetScreen = PinProfileScreenType.VerifyPinLoginProfile,
                            title = getString(R.string.multi_profile_pin_profile_verify_pin_title),
                            description = getString(R.string.multi_profile_pin_login_profile_des),
                            profileId = safeArgs.profileId,
                            isFromLoginScreen = safeArgs.isFromLoginScreen,
                            isSelectionOnBoarding = safeArgs.isSelectionOnBoarding,
                            popUpToId = safeArgs.popUpToId,
                            popUpToInclusive = safeArgs.popUpToInclusive
                        ), navOptions
                    )


                } else {
                    binding.root.showSnackBar(title = errorMessage(message))
                }
            }
            else -> {}

        }
    }


    private fun errorMessage(message: String?): String {
        return if(message.isNullOrBlank()) {
            getString(R.string.multi_profile_login_profile_error)
        } else{
            message
        }
    }


    protected fun showWarningMessageDialog(title:String?  =null, message: String, textConfirm: String? = null, onConfirm: (() -> Unit)? = null, isCancelled: Boolean = false) {
        AlertDialog().apply {
            setShowTitle(true)
            setTextTitle(title?:<EMAIL>(R.string.notification))
            setOnlyConfirmButton(true)
            setHandleBackPress(isCancelled)
            setMessage(message)
            setTextConfirm(textConfirm?:<EMAIL>(R.string.confirm))
            setListener(object : AlertDialogListener {
                override fun onConfirm() {
                    onConfirm?.invoke()
                }
            })
            isCancelable = false
        }.show(<EMAIL>, "AlertDialog")
    }

}