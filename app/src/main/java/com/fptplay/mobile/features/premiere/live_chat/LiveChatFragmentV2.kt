package com.fptplay.mobile.features.premiere.live_chat

import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateInterpolator
import android.widget.FrameLayout
import androidx.core.os.bundleOf
import androidx.core.view.OnApplyWindowInsetsListener
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isGone
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppEvent
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppManagement
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppNativeEventListener
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppNativeLoadSuccessEventData
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppNativeMethodProvider
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppRenderEffectData
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppUIInterface
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppUsage
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppView
import com.fpl.plugin.mini_app_sdk.android_view.ResponseMetadata
import com.fpl.plugin.mini_app_sdk.android_view.Token
import com.fpl.plugin.mini_app_sdk.entity.MiniAppManifest
import com.fpl.plugin.mini_app_sdk.model.DisplayModeOptions
import com.fpl.plugin.mini_app_sdk.model.MiniAppDeviceInfo
import com.fpl.plugin.mini_app_sdk.model.MiniAppDisplayInfo
import com.fpl.plugin.mini_app_sdk.model.MiniAppPlatformInfo
import com.fpl.plugin.mini_app_sdk.model.MiniAppUserInfo
import com.fpl.plugin.mini_app_sdk.model.OpenPopupData
import com.fpl.plugin.mini_app_sdk.model.OpenPopupType
import com.fpl.plugin.mini_app_sdk.model.RenderEffectOptions
import com.fpl.plugin.mini_app_sdk.model.WebViewStateUpdate
import com.fptplay.mobile.BuildConfig
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.extensions.hideKeyboard
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.runOnUiThread
import com.fptplay.mobile.common.extensions.showKeyboard
import com.fptplay.mobile.common.ui.bases.BaseActivity
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.view.GlobalSnackbarManager
import com.fptplay.mobile.common.ui.view.SnackbarManager
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.DeeplinkUtils
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.TrackingConstants
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils.isEmulator
import com.fptplay.mobile.common.utils.Utils.isTablet
import com.fptplay.mobile.databinding.FragmentLiveChatV2Binding
import com.fptplay.mobile.features.pladio.util.context
import com.fptplay.mobile.features.premiere.PremiereViewModel
import com.fptplay.mobile.features.premiere.live_chat.data.LiveChatOption
import com.fptplay.mobile.features.premiere.live_chat.utils.ChatMiniAppUtil
import com.fptplay.mobile.features.premiere.live_chat.utils.ChatMiniAppUtil.mapToLiveChatOption
import com.fptplay.mobile.features.premiere.live_chat.utils.ChatMiniAppUtil.mapToMiniAppManifest
import com.fptplay.mobile.features.premiere.live_chat.utils.ChatMiniAppUtil.mapToOpenPopupData
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.fptplay.mobile.features.premiere.live_chat.utils.ChatMiniAppUtil.toLiveResponseMetaDataSendLog
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import org.json.JSONObject
import javax.inject.Inject

@AndroidEntryPoint
class LiveChatFragmentV2 :
    BaseFragment<LiveChatV2ViewModel.LiveChatV2State, LiveChatV2ViewModel.LiveChatV2Intent>() {

    override val viewModel: LiveChatV2ViewModel by activityViewModels()

    private val premiereViewModel by activityViewModels<PremiereViewModel>()

    private var _binding: FragmentLiveChatV2Binding? = null
    private val binding get() = _binding!!

    private var chatDisplayMode = MiniAppDisplayInfo.Mode.Default
    private var isPlayerFullScreen = false

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var trackingInfo: Infor

    private val safeArgs: LiveChatFragmentV2Args by navArgs()

    private var latestWebStatus : Pair<String,Boolean>? = null // Pair(event, isLanscape)

    @Inject
    lateinit var trackingProxy: TrackingProxy


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        _binding = FragmentLiveChatV2Binding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onStop() {
        super.onStop()
        triggerEvent(ChatMiniAppUtil.WEB_VIEW_STATE_UPDATE, displayMode = ChatMiniAppUtil.DISPLAY_HIDDEN_MODE, isLandscape = MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.getMiniApp()?.let {
            binding.flMiniAppView.removeView(it.second)
        }
        _binding = null
    }

    override fun bindComponent() {
        super.bindComponent()
        bindMiniAppView()
    }

    override fun bindEvent() {
        super.bindEvent()
        binding.apply {
            ivClose.setOnClickListener {
                setFragmentResult(ChatMiniAppUtil.LIVE_CHAT_RESULT, bundleOf(ChatMiniAppUtil.LIVE_CHAT_EVENT to ChatMiniAppUtil.ENABLE_LIVE_CHAT) )
                findNavController().popBackStack()
            }

            premiereViewModel.isFullScreen.observe(viewLifecycleOwner) {
                it?.run {
                    // isFullscreen, isLandscape
                    isPlayerFullScreen = it.first
                    updateTitleVisibility(it.first, it.second)
                    triggerEvent(ChatMiniAppUtil.WEB_VIEW_STATE_UPDATE, isLandscape = it.second)

                    if (isPlayerFullScreen) {
                        try {
                            hideKeyboard()
                        } catch (ex: Exception) {
                            ex.printStackTrace()
                        }
                    } else {
                        if (isTablet && !it.second) {
                            hideKeyboard()
                        }
                    }
                }
            }
            // Sử dụng cho trường hợp Landscape FullScreen
            ViewCompat.setOnApplyWindowInsetsListener(flMiniAppView,
                object: OnApplyWindowInsetsListener {
                    private var lastBottom = 0
                    override fun onApplyWindowInsets(
                        v: View,
                        insets: WindowInsetsCompat
                    ): WindowInsetsCompat {
                        val imeInsets = insets.getInsets(WindowInsetsCompat.Type.ime())
                        val isKeyboardVisible = insets.isVisible(WindowInsetsCompat.Type.ime())
                        if (isKeyboardVisible) {
                            // Điều chỉnh padding dưới để nhường chỗ cho bàn phím
                            if (imeInsets.bottom != 0 && isPlayerFullScreen) {
                                lastBottom = imeInsets.bottom
                            } else {
                                lastBottom = 0
                            }
                        } else {
                            // Khôi phục khi bàn phím ẩn
                            lastBottom = 0
                        }
                        v.setPadding(imeInsets.left, imeInsets.top, imeInsets.right, lastBottom)
                        return insets
                    }
                }
            )

            parentFragment?.parentFragment?.setFragmentResultListener(ChatMiniAppUtil.RESULT_OPTION_KEY) { _, bundle ->
                val reportSuccess = bundle.getBoolean(ChatMiniAppUtil.RESULT_OPTION_SUCCESS, false)
                if (reportSuccess) {
                    viewModel.liveChatOption?.let {
                        triggerEvent(ChatMiniAppUtil.ACTION_EVENT, option = it)
                    }
                }
            }
        }
    }

    private fun updateTitleVisibility(isFullScreen: Boolean, isLandscape: Boolean) {
        if (isFullScreen && isLandscape) {
            binding.clTitle.hide()
        } else {
            binding.clTitle.show()
        }
    }

    private fun triggerEvent(
        event: String,
        isLandscape: Boolean = false,
        option: LiveChatOption? = null,
        displayMode: String? = null
    ) {
        val result = when (event) {
            ChatMiniAppUtil.WEB_VIEW_STATE_UPDATE -> {
                viewModel.getMiniApp()?.second?.triggerEvent(
                    MiniAppEvent.WebViewConfigurationUpdated(
                        WebViewStateUpdate(
                            displayMode = displayMode ?: if (isPlayerFullScreen) ChatMiniAppUtil.DISPLAY_FULL_MODE else ChatMiniAppUtil.DISPLAY_DEFAULT_MODE,
                            orientation = if (isLandscape)
                                ChatMiniAppUtil.ORIENTATION_LANDSCAPE_STATE
                            else
                                ChatMiniAppUtil.ORIENTATION_PORTRAIT_STATE
                        )
                    )
                )
                latestWebStatus = Pair(event, isLandscape)
            }

            ChatMiniAppUtil.ACTION_EVENT -> {
                option?.let {
                    viewModel.getMiniApp()?.second?.triggerEvent(
                        MiniAppEvent.OpenPopUpResponse(
                            option.mapToOpenPopupData()
                        )
                    )
                }
            }

            ChatMiniAppUtil.KEYDOWN_EVENT,
            ChatMiniAppUtil.STREAM_INFO_UPDATE,
            ChatMiniAppUtil.VIDEO_INFO_UPDATE -> {
            }

            else -> {}
        }
    }

    override fun bindData() {
        super.bindData()
        if (viewModel.premiereId.isNotEmpty() && viewModel.premiereId != premiereViewModel.getPremiereId()) {
            viewModel.getMiniApp()?.let {
                viewModel.saveMiniApp(loadSuccess = false, it.second)
            }
        }
        if (viewModel.getMiniApp()?.first == true) return
        viewModel.dispatchIntent(
            LiveChatV2ViewModel.LiveChatV2Intent.GetMiniAppManifest(
                context = requireContext(),
                force = !(viewModel.getMiniApp()?.first ?: false)
            )
        )
    }

    override fun LiveChatV2ViewModel.LiveChatV2State.toUI() {
        when (this) {
            is LiveChatV2ViewModel.LiveChatV2State.ResultMiniAppManifest -> {
                if (data.code == ChatMiniAppUtil.API_SUCCESS_CODE.toString()) {
                    viewModel.premiereId = premiereViewModel.getPremiereId()
                    val manifest = data.data.mapToMiniAppManifest(viewModel.premiereId)
                    loadMiniAppManifest(manifest, force)
                }
            }

            is LiveChatV2ViewModel.LiveChatV2State.ErrorRequiredLogin -> {
                requireLogin()
            }

            else -> {}
        }
    }

    private fun loadMiniAppManifest(data: MiniAppManifest, force: Boolean = false) {
        viewModel.getMiniApp()?.second?.loadManifest(
            miniAppManifest = data,
            force = force
        )
    }

    private fun bindMiniAppView() {
        if (viewModel.premiereId != premiereViewModel.getPremiereId()) {
            viewModel.clearMiniApp()
        }
        val miniApp = viewModel.getMiniApp()?.let {
            binding.clTitle.hide()
            it
        } ?: kotlin.run {
            binding.clTitle.show()
            Pair(false, MiniAppView(binding.context))
        }
        miniApp.apply {
            try {
                if (second.parent != null) {
                    (second.parent as ViewGroup).removeView(second)
                }
                second.apply {
                    prepareMiniAppUsage(
                        MiniAppUsage(
                            scope = viewLifecycleOwner.lifecycleScope,
                            activityForPermissionContract = requireActivity(),
                            miniAppNativeMethodProvider = miniAppNativeMethodProvider,
                            miniAppNativeEventListener = miniAppNativeEventListener,
                            miniAppUIController = miniAppUIController
                        )
                    )
                    layoutParams = FrameLayout.LayoutParams(
                        FrameLayout.LayoutParams.MATCH_PARENT,
                        FrameLayout.LayoutParams.MATCH_PARENT
                    )
                }
                viewModel.saveMiniApp(first, second)
                binding.flMiniAppView.addView(second)
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }
    }

    private val miniAppNativeMethodProvider = object : MiniAppNativeMethodProvider {
        override fun openDeeplink(url: String) {
            // process deeplink or open browser
            DeeplinkUtils.parseDeepLinkAndExecute(
                deeplink = url,
                useWebViewInApp = false,
                trackingInfo = trackingInfo,
                isDeeplinkCalledInApp = true
            )
        }

        override fun genApiToken(
            requestId: String,
            responseCallback: (Token, ResponseMetadata) -> Unit
        ) {
            viewModel.getPartnerToken(responseCallback)
        }

        override fun requestLogin(requestId: String) {
            lifecycleScope.launch {
                requireLogin()
            }
        }

        override fun setDisplayMode(
            requestId: String,
            mode: MiniAppDisplayInfo.Mode,
            options: DisplayModeOptions?
        ): MiniAppDisplayInfo {
//            setChatDisplayInfo(mode)
            return getChatDisplayInfo()
        }

        override fun getDisplayInfo(requestId: String): MiniAppDisplayInfo {
            return getChatDisplayInfo()
        }

        override fun showKeyboard(requestId: String) {
            showKeyboard()
        }

        override fun hideKeyboard(requestId: String) {
            hideKeyboard()
        }

        override fun getPlatformInfo(requestId: String): MiniAppPlatformInfo {
            return MiniAppPlatformInfo(
                platform = "android",
                version = Build.VERSION.SDK_INT.toString(),
                deviceModel = Build.MODEL,
                isEmulator = isEmulator(),
                deviceType = if(context?.isTablet() == true) MiniAppPlatformInfo.DEVICE_TYPE_TABLET else MiniAppPlatformInfo.DEVICE_TYPE_PHONE
            )
        }

        override fun getDeviceInfo(requestId: String): MiniAppDeviceInfo {
            return MiniAppDeviceInfo(
                deviceId = sharedPreferences.androidId(),
                version = BuildConfig.VERSION_NAME
            )
        }

        override fun getUserId(): String {
            return sharedPreferences.userId()
        }

        override fun getUserInfo(
            requestId: String,
            responseCallback: (MiniAppUserInfo?, ResponseMetadata) -> Unit
        ) {
            viewModel.getUserInfo(
                requestId = requestId,
                responseCallback = { userInfo, responseMetadata ->
                    responseCallback(userInfo, responseMetadata)
                }
            )
        }

        override fun openFullScreen(requestId: String) {
        }

        override fun openPopUp(requestId: String, type: OpenPopupType, options: OpenPopupData?) {
            options?.let {
                handleOpenPopUp(type, it.mapToLiveChatOption())
            }
        }

        override fun renderEffect(
            requestId: String,
            effectData: MiniAppRenderEffectData,
            customAnimation: RenderEffectOptions?
        ) {}

        override fun closeFullScreen(requestId: String) {
        }

        override fun interactContent(requestId: String, action: String) {
        }

        override fun sendLog(
            requestId: String,
            type: String,
            message: String,
            timestamp: Long,
            metadata: JSONObject?
        ) {
            sendLogLiveChatDeeplinkInteractive(
                requestId = requestId,
                type = type,
                message = message,
                timestamp = timestamp,
                metadata = metadata
            )
        }
        @Deprecated("Use [getUserInfo] instead")
        override suspend fun onProvideUserInfo(): MiniAppUserInfo {
            return MiniAppUserInfo()
        }
    }

    private fun requireLogin() {
        val extras = Bundle()
        extras.putString(Constants.ACTION_AFTER_LOGIN_KEY, Constants.ACTION_OPEN_CHAT)
        parentFragment?.parentFragment?.navigateToLoginWithParams(extendsArgs = extras)
    }

    private fun setChatDisplayInfo(mode: MiniAppDisplayInfo.Mode) {
        if (chatDisplayMode != mode) {
            chatDisplayMode = mode
        }
    }

    private fun getChatDisplayInfo(): MiniAppDisplayInfo {
        var width = -1
        var height = -1
        if (_binding != null) {
            width = binding.flMiniAppView.measuredWidth
            height = binding.flMiniAppView.measuredHeight
        }
        return MiniAppDisplayInfo(
            success = true,
            mode = chatDisplayMode.value,
            width = width,
            height = height
        )
    }

    private fun handleOpenPopUp(type: OpenPopupType, option: LiveChatOption) {
        runOnUiThread {
            var actionId = -1
            val args = bundleOf()
            viewModel.liveChatOption = option
            when (type) {
                OpenPopupType.REPORT -> {
                    actionId = safeArgs.reportAction
                }

                OpenPopupType.BOTTOM_SHEET -> {
                    actionId = safeArgs.optionAction
                }

                OpenPopupType.TOAST -> {
                    openSnackbar(option)
                }

                else -> {}
            }
            if (actionId != -1) {
                parentFragment?.parentFragment?.findNavController()?.navigateSafe(
                    resId = actionId,
                    args = args,
                    NavOptions.Builder().build()
                )
            }
        }
    }

    private fun openSnackbar(option: LiveChatOption) {
        if (option.actions.isNotEmpty()) {
            val actions = option.actions.toMutableList()
            val firstItem = actions[0].copy(selected = true)
            actions[0] = firstItem
            showSnackbarMessage(
                text = firstItem.title,
                action = firstItem.button
            ) {
                triggerEvent(
                    event = ChatMiniAppUtil.ACTION_EVENT,
                    option = option.copy(actions = actions)
                )
            }
        }
    }

    private fun showSnackbarMessage(
        text: String,
        action: String = "",
        actionClickListener: (() -> Unit)? = null
    ) {
        val config = SnackbarManager.Builder(requireActivity())
            .text(text)
            .action(action, actionClickListener)
            .fullWidth(action.isNotBlank())
            .paddingVertical(
                if (isPlayerFullScreen)
                    binding.root.resources.getDimensionPixelSize(R.dimen.snackbar_message_padding_vertical_when_full_screen)
                else
                    binding.root.resources.getDimensionPixelSize(R.dimen.snackbar_message_padding_vertical_default)
            )
            .position(SnackbarManager.Position.BOTTOM_CENTER)
        GlobalSnackbarManager.showMessageWithItem(item = config.build())
    }

    private fun showKeyboard() {
        _binding?.let {
            binding.root.showKeyboard()
        }
    }

    private fun hideKeyboard() {
        _binding?.let {
            binding.root.hideKeyboard()
        }
    }

    private val miniAppUIController = object : MiniAppUIInterface {
        override fun onCloseMiniApp() {
            setFragmentResult(ChatMiniAppUtil.LIVE_CHAT_RESULT, bundleOf(ChatMiniAppUtil.LIVE_CHAT_EVENT to ChatMiniAppUtil.ENABLE_LIVE_CHAT) )
            findNavController().popBackStack()
        }
    }

    private val miniAppNativeEventListener = object : MiniAppNativeEventListener {
        override fun loadSuccess(
            requestId: String,
            data: MiniAppNativeLoadSuccessEventData
        ) {
            viewModel.getMiniApp()?.let {
                viewModel.saveMiniApp(loadSuccess = data.isSuccess, it.second)
            }
            latestWebStatus?.let {
                triggerEvent(it.first,it.second)
            }
        }
    }

    fun View.fadeOut(duration: Long = 200L, onEnd: (() -> Unit)? = null) {
        animate()
            .alpha(0f)
            .setDuration(duration)
            .setInterpolator(AccelerateInterpolator()) // animation nhanh dần
            .withEndAction {
                visibility = View.GONE // Ẩn view sau khi animation kết thúc
                onEnd?.invoke() // Gọi callback onEnd nếu có
            }
            .start()
    }
    private fun sendLogLiveChatDeeplinkInteractive(
        requestId: String,
        type: String,
        message: String,
        timestamp: Long,
        metadata: JSONObject?
    ){
        val responseMetadata = metadata.toLiveResponseMetaDataSendLog()
        if (responseMetadata!=null){
            trackingProxy.sendEvent(
                infor = InforMobile(
                    infor = trackingInfo,
                    appId = TrackingUtil.currentAppId,
                    appSource = premiereViewModel.getDataDetail()?.appId ?:"",
                    appName = TrackingUtil.currentAppName,
                    logId = responseMetadata.logId,
                    screen = responseMetadata.screen,
                    event = responseMetadata.event,
                    url =  responseMetadata.url,
                    isLive = TrackingUtil.isLive,
                    itemId = premiereViewModel.getDataDetail()?.id ?: "",
                    itemName = premiereViewModel.getDataDetail()?.title ?: "",
                    EpisodeID = premiereViewModel.getPremiereId(),
                    startTime = TrackingUtil.startTime,
                    isLinkDRM = if(premiereViewModel.getDataDetail()?.isDrm == true) "1" else "0",
                    blocKPosition = TrackingUtil.blockIndex,
                    subMenuId = TrackingUtil.blockId,
                    refItemId = TrackingUtil.contentPlayingInfo.refId,
                    refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                    refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                    businessPlan = premiereViewModel.getDataDetail()?.payment?.requireVipPlan?: "",
                    isRecommend = TrackingUtil.isRecommend,
                    status = "None",
                    playerName = TrackingUtil.playerName
                )
            )
        }
    }
}
