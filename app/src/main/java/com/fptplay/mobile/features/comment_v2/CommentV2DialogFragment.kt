package com.fptplay.mobile.features.comment_v2

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.ui.bases.BaseDialogFragment
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.databinding.CommentV2DialogFragmentBinding
import com.fptplay.mobile.features.comment_v2.utils.CommentV2Util
import com.fptplay.mobile.features.moments.ShortVideoLikeCommentCache
import com.fptplay.mobile.features.short_video.DataCacheObject
import com.fptplay.mobile.features.short_video.ShortVideosContentFragment

class CommentV2DialogFragment: BaseDialogFragment<CommentV2ViewModel.CommentState, CommentV2ViewModel.CommentIntent>() {

    private var _binding: CommentV2DialogFragmentBinding? = null
    private val binding get() = _binding!!

    override val viewModel: CommentV2ViewModel by activityViewModels()

    private val safeArgs: CommentV2DialogFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = CommentV2DialogFragmentBinding.inflate(inflater,container,false)
        return binding.root
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = object : Dialog(requireContext(), R.style.FullDialogFragmentTheme) {
            override fun onBackPressed() {
                backHandler()
            }
        }
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        return dialog
    }

    override fun bindComponent() {
        val commentFragment = CommentV2Fragment().apply {
            val args = CommentV2FragmentArgs(contentId = safeArgs.contentId, episodeId = safeArgs.episodeId, openInDialog = true)
            arguments = args.toBundle()
        }
        childFragmentManager.beginTransaction()
            .replace(R.id.comment_dialog_container, commentFragment)
            .commitNow()
    }

    override fun bindEvent() {
        super.bindEvent()
        binding.root.onClickDelay {
            backHandler()
        }
        setFragmentResultListener(CommentV2Util.COMMENT_V2_RESULT) { _, bundle ->
            val result = bundle.getString(CommentV2Util.COMMENT_V2_KEY)
            when(result) {
                CommentV2Util.COMMENT_REQUIRED_LOGIN -> {
                    DataCacheObject.dataLikeCommentCache = ShortVideoLikeCommentCache(
                        dataActive = false,
                        tabId = DataCacheObject.dataCache.getCurTabId()?:"",
                        actionStatus = 1,
                        itemId = safeArgs.contentId,
                        chapterId = safeArgs.episodeId,
                        statusLikeLocal = ""
                    )
                    try {
                        if(parentFragment?.parentFragment is ShortVideosContentFragment) { //viewModel.isTabHomeLayout
                            parentFragment?.parentFragment?.navigateToLoginWithParams(isDirect = false, displayInDialogForMobile = true)
                        } else navigateToLoginWithParams(isDirect = false, displayInDialogForMobile = true)
                    } catch (ex: Exception) {
                        ex.printStackTrace()
                    }
                }
            }
        }
    }

    override fun CommentV2ViewModel.CommentState.toUI() {

    }
}