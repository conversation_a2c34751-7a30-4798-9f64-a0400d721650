package com.fptplay.mobile.common.extensions

import android.app.Activity
import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.Toast
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentContainerView
import androidx.navigation.NavOptions
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import androidx.window.layout.WindowMetricsCalculator
import com.fptplay.mobile.HomeActivity
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.ActivityExtensions.hideSystemBar
import com.fptplay.mobile.common.ui.popup.loading.LoadingView
import com.fptplay.mobile.common.utils.DeeplinkUtils.addAdjustTrueLinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addClevertapDeeplinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFacebookDeeplinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFirebaseDeeplinkData
import com.fptplay.mobile.common.utils.DeeplinkUtils.addFirebaseNotificationData
import com.fptplay.mobile.features.event_trigger.data.EventButton
import com.fptplay.mobile.features.mega.apps.airline.AirlineActivity
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import timber.log.Timber
import java.lang.Integer.min

object ActivityExtensions {
    fun FragmentActivity.findNavHostFragment(): NavHostFragment? {
        return try {
            findViewById<FragmentContainerView>(R.id.nav_host_fragment).getFragment() as? NavHostFragment
        } catch (ex: Exception) {
            ex.printStackTrace()
            null
        }
    }

    fun FragmentActivity.navigateToScreen(screenId: Int, bundle: Bundle? = null, navOptions: NavOptions? = null): Boolean {
        findNavHostFragment()?.let {
            it.navController.navigate(screenId, bundle, navOptions)
            return true
        }
        return false
    }

    fun FragmentActivity.showLoading(loadingId: Int) {
        hideLoading(loadingId) {
            findViewById<View>(android.R.id.content).rootView?.let { rootView ->
                val params =
                    RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, RelativeLayout.LayoutParams.MATCH_PARENT)
                        .apply {
                            addRule(RelativeLayout.CENTER_HORIZONTAL)
                            addRule(RelativeLayout.ALIGN_PARENT_BOTTOM)
                        }
                val loadingView: LoadingView by lazy { LoadingView(this) }
                loadingView.id = loadingId
                try {
                    Timber.d("***show loadingggg on view $rootView")
                    (rootView as? ViewGroup)?.addView(loadingView, params)
                } catch (ex: IllegalStateException) {
                    ex.printStackTrace()
                }
            }
        }
    }

    fun FragmentActivity.hideLoading(loadingId: Int, block: () -> Unit = {}) {
        try {
            findViewById<View>(android.R.id.content).rootView?.let { rootView ->
                findViewById<LoadingView>(loadingId)?.run {
                    Timber.d("***Hide loadingggg")
                    (rootView as? ViewGroup)?.removeView(this)
                }
            }
        } finally {
            block()
        }
    }

    fun FragmentActivity.isAirlineLayout(): Boolean {
        return this is AirlineActivity
    }

    //
    private fun Fragment.checkToDismissPopup(): Int {
        val navController = activity?.findNavHostFragment()?.findNavController()
        return when (val curFragmentId = navController?.currentDestination?.id ?: -1) {
            R.id.pairing_control_device_bottom_sheet,
            //
            R.id.player_report_dialog_fragment,
            //
            R.id.downloadOptionBottomSheetDialogFragment,
            R.id.downloadFinishBottomSheetDialogFragment,
            R.id.download_quality_dialog_fragment_v2,
            R.id.download_setting_dialog_fragment_v2,
            R.id.vod_option_dialog_fragment,
            //
            R.id.player_option_dialog_fragment,
            R.id.option_dialog_fragment -> {
                curFragmentId
            }
            else -> -1
        }
    }

    fun Fragment.checkToDismissFragmentDialogInPlayer() {
        try {
            val fragmentDismiss = checkToDismissPopup()
            if (fragmentDismiss != -1) {
                findNavController().popBackStack()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun FragmentActivity.checkAppInBackground(): Boolean {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val appProcesses = activityManager.runningAppProcesses ?: return false
        val packageName = packageName
        for (appProcess in appProcesses) {
            if (appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND && appProcess.processName == packageName) {
                return false
            }
        }
        return true
    }

    fun FragmentActivity.openSettingForApp() {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            val uri: Uri = Uri.fromParts("package", this.packageName, null)
            intent.data = uri
            startActivity(intent)
        } catch (ex: Exception) {
            ex.printStackTrace()
            Toast.makeText(this, getString(R.string.text_all_app_store_unavailable), Toast.LENGTH_SHORT).show()
        }
    }

    fun FragmentActivity.hideSystemBar() {
        Logger.d("-----fun hide system bar")
        window?.run {
            ViewCompat.getWindowInsetsController(decorView)?.let { windowInsetsController ->
                decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                Logger.d("-----fun hide system bar > run case 1")
                // Configure the behavior of the hidden system bars
                windowInsetsController.systemBarsBehavior =
                    WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
                // Hide both the status bar and the navigation bar
                windowInsetsController.hide(WindowInsetsCompat.Type.systemBars())
            } ?: kotlin.run {
                Logger.d("-----fun hide system bar > run case 2")
                decorView.systemUiVisibility = (
                        View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                                or View.SYSTEM_UI_FLAG_FULLSCREEN
                                or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                        )
            }
        }
    }

    fun FragmentActivity.showSystemBar() {
        Logger.d("-----fun show system bar")
        window?.run {
            ViewCompat.getWindowInsetsController(decorView)?.let { windowInsetsController ->
                decorView.systemUiVisibility = View.STATUS_BAR_VISIBLE
                windowInsetsController.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
                // Hide both the status bar and the navigation bar
                windowInsetsController.show(WindowInsetsCompat.Type.systemBars())
            } ?: kotlin.run {
                decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE

            }
        }
    }

    fun FragmentActivity.setSystemBarVisibilityChangeListener(action: () -> Unit) {
        Logger.d("-----run function setSystemBarVisibilityChangeListener")
        window.decorView.setOnSystemUiVisibilityChangeListener { visibility ->
            Logger.d("-----run function setSystemBarVisibilityChangeListener > visibility: $visibility")
            if (visibility and View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN == 0) {
                // The system bars are visible
                // You can re-hide the navigation bar here if needed
                Logger.d("-----run function setSystemBarVisibilityChangeListener > visibility > run action")
                action.invoke()
            }
        }
    }

    fun FragmentActivity.removeSystemBarVisibilityChangeListener() {
        Logger.d("-----run function removeSystemBarVisibilityChangeListener")
        window.decorView.setOnSystemUiVisibilityChangeListener(null)
    }

    fun FragmentActivity?.isInPiPMode(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            this?.isInPictureInPictureMode ?: false
        } else {
            false
        }
    }

    fun FragmentActivity?.startHome(sharedPreferences:SharedPreferences, deeplink:String? = null) {
        this?.let {
            startActivity(
            Intent(
                this,
                HomeActivity::class.java
            ).apply {
                if(sharedPreferences.isProfileChangeWhenOpenApp()) {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                }
                addFirebaseNotificationData(it.intent)
                addClevertapDeeplinkData(it.intent)
                addFirebaseDeeplinkData(it.intent)
                addFacebookDeeplinkData(it.intent)
                addAdjustTrueLinkData(it.intent)
                if(deeplink!= null) putExtra("originalLink", deeplink)
            })
            it.finish()
        }
    }

}