<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/black"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />
    
    <com.google.android.material.tabs.TabLayout
        android:layout_marginHorizontal="@dimen/_6sdp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:id="@+id/tab_layout"
        android:layout_width="@dimen/_236sdp"
        android:layout_height="@dimen/_1sdp"
        android:layout_marginTop="@dimen/_22sdp"
        app:tabIndicatorFullWidth="false"
        app:tabGravity="fill"
        app:tabMode="fixed"
        app:tabBackground="@color/transparent"
        android:background="@color/transparent"
        app:tabPaddingStart="@dimen/_1sdp"
        app:tabPaddingEnd="@dimen/_1sdp"
        app:tabIndicatorColor="@color/white"
        app:tabIndicatorGravity="center"
        app:tabIndicatorHeight="@dimen/_1sdp" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/_22sdp"
        android:layout_height="@dimen/_22sdp"
        android:layout_marginTop="@dimen/_27sdp"
        android:padding="@dimen/_6sdp"
        android:scaleType="fitXY"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:srcCompat="@drawable/ic_x_close" />

</androidx.constraintlayout.widget.ConstraintLayout>
