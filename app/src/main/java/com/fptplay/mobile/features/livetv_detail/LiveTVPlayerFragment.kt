package com.fptplay.mobile.features.livetv_detail

import android.animation.LayoutTransition
import android.annotation.SuppressLint
import android.content.res.Configuration
import android.os.*
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.fplay.module.downloader.model.VideoTaskItem
import com.fptplay.dial.connection.FConnectionManager
import com.fptplay.dial.connection.android_tv.model.FAndroidTVCustomMessageResponse
import com.fptplay.dial.connection.android_tv.model.FAndroidTVCustomPlayContentResponse
import com.fptplay.dial.connection.android_tv.model.FAndroidTVCustomStopSessionResponse
import com.fptplay.dial.connection.models.FAndroidTVObjectReceiver
import com.fptplay.dial.connection.models.FAndroidTVObjectReceiverExternal
import com.fptplay.dial.connection.models.FBoxObjectReceiver
import com.fptplay.dial.connection.models.FSamsungObjectReceiver
import com.fptplay.dial.connection.models.FSamsungObjectReceiverExternal
import com.fptplay.dial.connection.models.ObjectReceiver
import com.fptplay.dial.connection.models.transfer_model.*
import com.fptplay.dial.model.DeviceInfo
import com.fptplay.dial.model.FBoxDeviceInfoV2
import com.fptplay.dial.scanner.interfaces.FScannerAwakeListener
import com.fptplay.mobile.BuildConfig
import com.fptplay.mobile.HomeActivity
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.*
import com.fptplay.mobile.common.extensions.ActivityExtensions.checkToDismissFragmentDialogInPlayer
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.ActivityExtensions.isInPiPMode
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.log.LivePlaybackLogger
import com.fptplay.mobile.common.log.data.LoadType
import com.fptplay.mobile.common.log.data.LogEnterPlayerScreenSource
import com.fptplay.mobile.common.log.data.LogStreamInfo
import com.fptplay.mobile.common.log.data.LogUserTimeInfo
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertInfoDialog
import com.fptplay.mobile.common.utils.*
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.Navigation.navigateToRequiredBuyPackage
import com.fptplay.mobile.common.utils.StringUtils.truncateString
import com.fptplay.mobile.databinding.LivetvPlayerFragmentBinding
import com.fptplay.mobile.features.adjust.AbandonedReason
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.adjust.Source
import com.fptplay.mobile.features.adjust.SourcePage
import com.fptplay.mobile.features.adjust.WatchType
import com.fptplay.mobile.features.ads.AdsTvcListener
import com.fptplay.mobile.features.ads.tracking_ads.AdsTrackingProxy
import com.fptplay.mobile.features.ads.utils.AdsUtils
import com.fptplay.mobile.features.livetv_detail.LiveTVDetailViewModel.LiveTVDetailIntent.*
import com.fptplay.mobile.features.livetv_detail.LiveTVDetailViewModel.LiveTVDetailState.*
import com.fptplay.mobile.features.livetv_detail.data.LiveTvPlayerInfo
import com.fptplay.mobile.features.livetv_detail.data.LiveTvPreviewPlayerInfo
import com.fptplay.mobile.features.mqtt.MqttConnectManager
import com.fptplay.mobile.features.mqtt.MqttUtil
import com.fptplay.mobile.features.mqtt.MqttUtil.DEFAULT_MODE
import com.fptplay.mobile.features.mqtt.MqttUtil.mapToPublisher
import com.fptplay.mobile.features.mqtt.model.MqttContentType
import com.fptplay.mobile.features.mqtt.model.Publisher
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.features.pairing_control.Utils.isFailure
import com.fptplay.mobile.features.pairing_control.Utils.isMyMessage
import com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning
import com.fptplay.mobile.features.pairing_control.Utils.isSuccess
import com.fptplay.mobile.features.pairing_control.Utils.shouldShowStopSessionToast
import com.fptplay.mobile.features.pairing_control.model.CastingItemState
import com.fptplay.mobile.features.payment.google_billing.BillingUtils
import com.fptplay.mobile.features.payment.util.PaymentTrackingUtil
import com.fptplay.mobile.features.preview_timer.PreviewTimer
import com.fptplay.mobile.features.report.utils.PlayerReportUtils
import com.fptplay.mobile.player.PlayerUtils
import com.fptplay.mobile.features.sport_interactive.SportInteractiveViewModel
import com.fptplay.mobile.features.user_realtime_playing.UserRealtimePlayingTracker
import com.fptplay.mobile.player.PlayerView
import com.fptplay.mobile.player.config.PlayerConfigBuilder
import com.fptplay.mobile.player.dialog.data.LiveMoreData
import com.fptplay.mobile.player.handler.PlayerHandler
import com.fptplay.mobile.player.interfaces.IPlayerUIListener
import com.fptplay.mobile.player.retry.PlayerGetStreamRetryHandler
import com.fptplay.mobile.player.retry.PlayerPiPRetryHandler
import com.fptplay.mobile.player.retry.PlayerRetryHandler
import com.fptplay.mobile.player.utils.TrackHelper.findTrackById
import com.fptplay.mobile.player.utils.TrackHelper.findTrackByName
import com.fptplay.mobile.player.utils.afterMeasured
import com.fptplay.mobile.player.utils.gone
import com.google.android.exoplayer2.video.VideoSize
import com.tear.modules.player.exo.ExoPlayerProxy
import com.tear.modules.player.util.IPlayer
import com.tear.modules.player.util.PlayerControlView
import com.tear.modules.player.util.PlayerUtils.getHdrType
import com.tear.modules.player.util.TrackType
import com.tear.modules.player.util.UnValidResponseCode
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.CommonInfor
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.RequiredVip
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.domain.entity.fplay.live.TvChannel
import com.xhbadxx.projects.module.domain.entity.fplay.live.TvChannelDetail
import com.xhbadxx.projects.module.domain.entity.fplay.live.TvSchedule
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.xhbadxx.projects.module.util.common.Util
import com.xhbadxx.projects.module.util.common.Util.fromBase64Default
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.safeInflate
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@AndroidEntryPoint
class LiveTVPlayerFragment : BaseFragment<LiveTVDetailViewModel.LiveTVDetailState, LiveTVDetailViewModel.LiveTVDetailIntent>() {

    override val handleConfigurationChange = true

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    @Inject
    lateinit var userRealtimePlayingTracker: UserRealtimePlayingTracker

    @Inject
    lateinit var playerRetryHandler: PlayerRetryHandler

    @Inject
    lateinit var playerGetStreamRetryHandler: PlayerGetStreamRetryHandler

    @Inject
    lateinit var playerPiPRetryHandler: PlayerPiPRetryHandler

    // Tracking Ads
    private val trackingAds by lazy { AdsTrackingProxy(trackingProxy, trackingInfo) }

    private val TAG = this::class.java.simpleName

    override val viewModel: LiveTVDetailViewModel by activityViewModels()

    val sportInteractiveViewModel: SportInteractiveViewModel by activityViewModels()

    private var shouldUseSchedule = false

    private var _binding: LivetvPlayerFragmentBinding? = null
    private val binding get() = _binding!!

    private val livetvPlayerInfo by lazy {
        LiveTvPlayerInfo()
    }

    private var isPlayerScaled = false

    // Tracking
    private var handlerTrackingHeartBeat: Handler? = null
    private var runnableTrackingHeartBeat = Runnable {
        sendTrackingHeartBeat()
    }
    private var userClickChangeBitrate = false
    private var beginSeekTime: Long = 0

    private val livePlaybackLogger: LivePlaybackLogger by lazy {
        LivePlaybackLogger(
            sharedPreferences = sharedPreferences,
            trackingProxy = trackingProxy,
            trackingInfo = trackingInfo,
            details = null
        ).apply {
            enterLivePlayback(
                LogEnterPlayerScreenSource(
                    appId = TrackingUtil.currentAppId,
                    appName = TrackingUtil.currentAppName,
                    subMenuId = TrackingUtil.blockId,
                    blockId = TrackingUtil.blockId,
                    blockPosition = TrackingUtil.blockIndex,
                )
            )
            setScreen(TrackingUtil.screen)
        }
    }

    //
    // Retry task
    private var countDownTimerRetry: CountDownTimer? = null
    private var playerRetryDialog: AlertInfoDialog? = null
    //

    // Retry when internet available
    private var isPlayerErrorByInternet = false
    //

    // Show Ip And Matrix Ip
    private var tvShowIp: TextView? = null
    private var cdtShowIp: CountDownTimer? = null
    private var rlShowMatrixIp: RelativeLayout? = null
    private var cdtShowMatrixIp: CountDownTimer? = null
    //

    //log kibana

    // Pairing Control
    private val pairingScanner by lazy { MainApplication.INSTANCE.pairingScannerHelper }
    private val pairingConnection by lazy { MainApplication.INSTANCE.pairingConnectionHelper }
    //

    //fix tạm cho sprint này - optimize sau
    private var screenStartLog41:String  = ""
    private var isPlayTimeShift:Boolean = false
    private var subMenuIdWhenStartPlay = ""

    // region preview
    private val liveTvPreviewPlayerInfo by lazy {
        LiveTvPreviewPlayerInfo()
    }

    private var previewEndTimeDialog: AlertDialog? = null

    private var isShowRequiredLogin: Boolean = true

    @Inject
    lateinit var previewTimer : PreviewTimer
    private var isPreviewEnded : Boolean = false
    // endregion preview

    //MQTT
    private var mqttPublisher : Publisher? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setCurrentChannelDetail(null)
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = LivetvPlayerFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onPause() {
        //
        checkToDismissFragmentDialogInPlayer()
        //
        super.onPause()
    }

    override fun onDestroyView() {
        cdtShowIp?.cancel()
        cdtShowMatrixIp?.cancel()
        when (PlayerUtils.getPlayingType()) {
            PlayerView.PlayingType.Local -> {
                // Tracking
                if (isPlayTimeShift)
                    sendTrackingStopTimeShift()
                else
                    sendTrackingStopIPTV()
                //
                binding.player.stop(force = true)

                //MQTT
                publishEndToTopic()
            }
            PlayerView.PlayingType.Cast -> {
                binding.player.stopPlayerLocal(isClearRequest = true)
            }
        }
        //
        binding.player.setPlayerEventsListener(listener = null)
        //
        removeIntervalUpdateProgress()
        removeTrackingHeartBeat()
        stopCountDownTimerRetry()
        trackingInfo.updatePlayingSession(0)
        TrackingUtil.resetIsRecommend()
        userRealtimePlayingTracker.clearData()
        // Pairing Control
        removePairingControlListener()
        // Player error handler
        removePlayerRetryHandlerListener()
        // Player get stream error handler
        removePlayerGetStreamRetryListener()
        // remove preview timer
        previewTimer.removePreviewTimerListener()

        viewModel.cancelGetStreamJob()

        if (MainApplication.INSTANCE.sharedPreferences.shouldShowTooltip(Constants.TOOLTIP_SETTING_PLAYER)) {
            MainApplication.INSTANCE.sharedPreferences.setShouldShowTooltip(Constants.TOOLTIP_SETTING_PLAYER, shouldShow = false)
        }
        _binding = null
        super.onDestroyView()
    }

    override fun bindOrientationStateChange(newConfig: Configuration) {
        super.bindOrientationStateChange(newConfig)
        if (_binding != null) {
            binding.player.configurationChanged(newConfig)
        }
    }


    override fun initData() {
        viewModel.saveVipRequired(isRequired = false)
        viewModel.dispatchIntent(GetPublicIp)
        viewModel.saveChannelBitrateId(bitrateId = sharedPreferences.bitrateLive())
        if(!shouldUseSchedule) {
            viewModel.resetSchedule()
        }
    }

    override fun bindComponent() {
        binding.player.initPlayer(fragmentActivity = activity, viewLifecycleOwner = viewLifecycleOwner, screenType = PlayerHandler.ScreenType.Live, viewModel = viewModel, fragmentManager = childFragmentManager)
        Utils.setStateWhenShowLogin(this.parentFragment, object : OnShowLoginListener{
            override fun onShow() {
                binding.player.pause()
            }

            override fun onHide() {
                binding.player.play()
            }
        })
    }

    override fun bindEvent() {
        observeData()
        bindEventFragmentResult()
        bindEventInternetListener()
        // Pairing Control
        addPairingControlListener()
        // Player error handler
        setPlayerRetryHandlerListener()
        // Player get stream retry
        setPlayerGetStreamRetryListener()
        // set preview timer
        previewTimer.setPreviewTimerListener(previewTimerListener = previewTimerListener)

        binding.player.apply {
            // Player UI Events
            setPlayerUIListener(object : IPlayerUIListener {
                override fun onNext(position: Int, curData: Details.Episode, isAuto: Boolean, isSendEventToRemote: Boolean) {}

                override fun onPrevious(position: Int, curData: Details.Episode, isAuto: Boolean) {}
                override fun onNextOffline(position: Int, curData: VideoTaskItem, isAuto: Boolean) {}
                override fun onPreviousOffline(position: Int, curData: VideoTaskItem, isAuto: Boolean) {}

                override fun onActionSeek(duration: Long) {
                    beginSeekTime = System.currentTimeMillis()
                }

                override fun onPlayToggle() {
                    // Tracking
                    if (!binding.player.isPlaying() && isPlayTimeShift) {
                        livePlaybackLogger.logPauseTimeShift(
                            streamInfo = getStreamInfoForLogger(),
                            userTimeInfo = LogUserTimeInfo(
                                realtimePlaying = getRealTimePlayingForLog()
                            )
                        )
                    } else if (binding.player.isPlaying() && isPlayTimeShift) {
                        livePlaybackLogger.logResumeTimeShift(
                            streamInfo = getStreamInfoForLogger(),
                            userTimeInfo = LogUserTimeInfo(
                                realtimePlaying = getRealTimePlayingForLog()
                            )
                        )
                    }
                    //
                }

                override fun onMulticam() {}

                override fun onCast() {
                    // Pairing control
                    if (sharedPreferences.userLogin()) {
                        if (!pairingConnection.isConnected) { // Navigate to search devices
                            navigateToPairingControl(type = 0)
                        } else {
                            navigateToPairingControl(type = 3)
                        }
                    } else {
                        parentFragment?.parentFragment?.navigateToLoginWithParams(navigationId = R.id.nav_pairing_control_host)
                    }
                    //

                }

                override fun onShare() {
                    // Send tracking
                    sendTracking(logId = "516", event = "Share")
                }

                override fun onExpand(curResolutionId: String) {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_EXPAND)
                            viewModel.savePlayerOptionsCurItem(idCurItem = curResolutionId)
                            parentFragment?.findNavController()?.navigateSafe(LiveTVDetailFragmentDirections.actionLiveTvListChannelFragmentToPlayerOptionDialogFragment())
                        }
                        PlayerView.PlayingType.Cast -> {
                            pairingConnection.showToast(getString(R.string.pairing_cast_not_support))
                        }
                    }
                }
                override fun onMore() {
                    // Feature removed
//                    viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_MORE)
//                    when (liveType) {
//                        is PlayerView.LiveType.LiveTv -> parentFragment?.findNavController()?.navigate(LiveTVDetailFragmentDirections.actionLiveTvListChannelFragmentToOptionDialogFragment())
//                        else -> {}
//                    }
                }

                override fun onLiveChatClick() { }

                override fun onSportInteractiveClick() { }

                override fun onFullScreen(isFullscreen: Boolean, isLandscapeMode: Boolean) {
                    if (context.isTablet()) {
                        viewModel.triggerFullScreen(binding.player.isFullscreen(), isLandscapeMode)
                        if (isLandscapeMode) {
                            if (isFullscreen) {
                                view?.run {
                                    (this as? ViewGroup)?.layoutTransition?.enableTransitionType(LayoutTransition.CHANGING)
                                    val lp = ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.MATCH_PARENT, ConstraintLayout.LayoutParams.MATCH_PARENT)
                                    layoutParams = lp
                                }
                            } else {
                                tabletPlayerLayout()
                            }
                        } else {
                            if (isFullscreen) {
                                view?.run {
                                    (this as? ViewGroup)?.layoutTransition?.enableTransitionType(LayoutTransition.CHANGING)
                                    val lp = ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.MATCH_PARENT, ConstraintLayout.LayoutParams.MATCH_PARENT)
                                    layoutParams = lp
                                }
                            } else {
                                resetPlayerLayout()
                            }
                        }
                    } else {
                        viewModel.triggerFullScreen(binding.player.isFullscreen(), isLandscapeMode)
                        if (binding.player.isFullscreen()) {
                            view?.run {
                                val lp = ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.MATCH_PARENT, ConstraintLayout.LayoutParams.MATCH_PARENT)
                                layoutParams = lp
                            }
                        } else {
                            resetPlayerLayout()
                            // Live chat
                            isPlayerScaled = false
                        }
                    }
                }

                override fun onPlayerSpeed() {

                }

                override fun onSetting(bitrates: List<PlayerControlView.Data.Bitrate>?) {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            if (!bitrates.isNullOrEmpty()) {
                                viewModel.saveCurrentPlayerBitrates(bitrates = bitrates)
                                viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_BITRATE)
                                val bitrateIndex = binding.player.playerData.bitrateIndex ?: 0
                                viewModel.savePlayerOptionsCurItem(idCurItem = bitrates[if (bitrateIndex < 0) 0 else bitrateIndex].id)
                                parentFragment?.findNavController()?.navigateSafe(LiveTVDetailFragmentDirections.actionLiveTvListChannelFragmentToPlayerOptionDialogFragment())
                            }
                        }
                        PlayerView.PlayingType.Cast -> {
                            pairingConnection.showToast(getString(R.string.pairing_cast_not_support))
                        }

                    }
                }

                override fun onAudioAndSubtitle(tracks: List<PlayerControlView.Data.Track>?) {
                    viewModel.saveTracks(tracks = tracks)
                    viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_SUBTITLE)
                    parentFragment?.findNavController()?.navigateSafe(LiveTVDetailFragmentDirections.actionLiveTvListChannelFragmentToPlayerOptionDialogFragment())
                }
                override fun onWatchCredit(isSendEventToRemote: Boolean) {}
                override fun onRecommendClose() {}
                override fun onRecommendWatchNow(related: Details.RelatedVod?) {}
                override fun onRecommendPlayTrailer(related: Details.RelatedVod?) {}
                override fun onEpisodeChangedFromBackground(pos: Int, currentDuration: Long) {}
                override fun onVideoSizeChanged(videoSize: VideoSize) {
                    sendTrackingChangeResolution()
                    userClickChangeBitrate = false
                }

                override fun onTracksInfoChanged() {
                    // Logic save tracks history
                    updateLogicSaveTrack()
                    //
                }

                override fun onReport() {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            if (sharedPreferences.userLogin()) {
                                navigateToReportPlayerVod()
                            } else {
                                // parentFragment?.parentFragment?.navigateToLoginWithParams(navigationId = R.id.action_vod_detail_fragment_to_player_report_dialog_fragment, isDirect = false)
                                parentFragment?.parentFragment?.navigateToLoginWithParams(isDirect = false)
                            }
                        }
                        PlayerView.PlayingType.Cast -> {
                            pairingConnection.showToast(getString(R.string.pairing_cast_not_support))
                        }

                    }
                }

                override fun onAdsShow() {
                    super.onAdsShow()
                    checkToDismissFragmentDialogInPlayer()
                }

                override fun onPictureInPictureModeChanged(isInPictureInPictureMode: Boolean) {
                    if (!isInPictureInPictureMode) {
                        // Handle Player Retry
                        val isPlayerRetryProcessing = playerPiPRetryHandler.isProcessing()
                        if (isPlayerRetryProcessing && _binding?.player?.isPlaying() != true) {
                            when (val processData = playerPiPRetryHandler.getProcessData()) {
                                is PlayerPiPRetryHandler.PlayerRetryData -> {
                                    playerRetryHandlerListener.onShowPlayerError(
                                        shouldCountDown = processData.shouldCountDown,
                                        countdownTimeMs = playerPiPRetryHandler.getCurrentTickMs(),
                                        code = processData.code,
                                        responseCode = processData.responseCode
                                    )
                                }

                                is PlayerPiPRetryHandler.PlayerGetStreamRetryData -> {
                                    playerGetStreamRetryListener.onShowGetStreamError(
                                        shouldCountDown = processData.shouldCountDown,
                                        countdownTimeMs = playerPiPRetryHandler.getCurrentTickMs(),
                                        errorMessage = processData.errorMessage,
                                    )
                                }
                            }
                        }
                        playerPiPRetryHandler.stopRetryFlow()
                    }
                    // Hide popup
                    if (isInPictureInPictureMode) {
                        checkToDismissFragmentDialogInPlayer()
                        //
                        tvShowIp?.hide()
                        //
                    }
                }
            })

            // Player Events
            setPlayerEventsListener(object : IPlayer.IPlayerCallback {
                var startBufferTime = 0L
                override fun onBandwidth(message: String) {
                    Logger.d("$TAG onBandwidth")
                    //
                    userRealtimePlayingTracker.playerEvents.onBandwidth(message = message)
                    playerRetryHandler.playerEvents.onBandwidth(message = message)
                }

                override fun onBuffering() {
                    Logger.d("$TAG onBuffering")

                    //
                    userRealtimePlayingTracker.playerEvents.onBuffering()
                    playerRetryHandler.playerEvents.onBuffering()
                }

                override fun startBuffering() {
                    Logger.d("$TAG startBuffering")
                    sendTrackingBuffering("StartBuffering", binding.player.getTrackingBandWith(), "0")
                    startBufferTime = System.currentTimeMillis()
                }

                override fun endBuffering() {
                    Logger.d("$TAG endBuffering")
                    sendTrackingBuffering("EndBuffering", binding.player.getTrackingBandWith(), (System.currentTimeMillis() - startBufferTime).toString())
                }

                override fun onPause() {
                    Logger.d("$TAG onPause")
                    //
                    userRealtimePlayingTracker.playerEvents.onPause()
                    playerRetryHandler.playerEvents.onPause()
                }

                override fun onEnd() {
                    Logger.d("$TAG onEnd")
                    // Auto next tv schedule when watching timeshift
                    if (viewModel.isPlayingTimeshift()) {
                        viewModel.getCurrentPlayingTvSchedule()?.let {
                            val nextTvSchedule = it.second.findNextTvSchedule(curTvSchedule = viewModel.playSchedule.value?.first)
                            if (nextTvSchedule.second != null) {
                                viewModel.triggerPlaySchedule(nextTvSchedule.second!!)
                            } else {
                                if (nextTvSchedule.first) { // schedule end day
                                    // Get next list TvSchedule in next date and play first tv schedule
                                    val currentScheduleMilliseconds = it.first
                                    val currentMillisecondsNextDate = DateTimeUtils.getNextDate(currentScheduleMilliseconds)
                                    viewModel.dispatchIntent(
                                        GetTVSchedule(
                                            channelId = viewModel.getChannelId(),
                                            page = 1,
                                            perPage = 100,
                                            day = DateTimeUtils.formatTimeToYourFormat(currentMillisecondsNextDate, "dd-MM-yyy"),
                                            isFirstCall = false,
                                            currentMilliseconds = currentMillisecondsNextDate,
                                            isPlayerCalled = true
                                        )
                                    )
                                }
                            }
                        }
                    }
                    //
                    userRealtimePlayingTracker.playerEvents.onEnd()
                    playerRetryHandler.playerEvents.onEnd()
                }

                override fun onErrorCodec(
                    code: Int,
                    name: String,
                    detail: String,
                    responseCode: Int,
                    isDrm: Boolean,
                    codec: IPlayer.CodecType
                ) {
                    Logger.d("$TAG onErrorCodec")

                    if (isPlayingPreview()) {
                        handleEndPreviewOnError()
                        previewTimer.playerEvents.onErrorCodec(code, name, detail, responseCode, isDrm, codec)
                        return
                    }
                    if (isPlayingVipTrailerInEnablePreview()) return

                    <EMAIL>(resetInstreamAd = false)
                    //
                    userRealtimePlayingTracker.playerEvents.onErrorCodec(code, name, detail, responseCode, isDrm, codec)
                    playerRetryHandler.playerEvents.onErrorCodec(code, name, detail, responseCode, isDrm, codec)
                }

                override fun onError6006(code: Int, name: String, detail: String) {
                    Logger.d("$TAG onError6006")

                    if (isPlayingPreview()) {
                        handleEndPreviewOnError()
                        previewTimer.playerEvents.onError6006(code = code, name = name, detail = detail)
                        return
                    }
                    if (isPlayingVipTrailerInEnablePreview()) return

                    userRealtimePlayingTracker.playerEvents.onError6006(code = code, name = name, detail = detail)
                    playerRetryHandler.playerEvents.onError6006(code = code, name = name, detail = detail)
                }

                override fun onError6006WhenPreview(code: Int, name: String, detail: String, responseCode: Int) {
                    Logger.d("$TAG onError6006WhenPreview")

                    if (isPlayingPreview()) {
                        handleEndPreviewOnError()
                        previewTimer.playerEvents.onError6006WhenPreview(code = code, name = name, detail = detail, responseCode = responseCode)
                        return
                    }
                    if (isPlayingVipTrailerInEnablePreview()) return

                    userRealtimePlayingTracker.playerEvents.onError6006WhenPreview(code = code, name = name, detail = detail, responseCode = responseCode)
                    playerRetryHandler.playerEvents.onError6006WhenPreview(code = code, name = name, detail = detail, responseCode = responseCode)
                }



                override fun onError(code: Int, name: String, detail: String, error403: Boolean, responseCode: Int) {
                    Logger.d("$TAG onError")
                    isPlayerErrorByInternet = code == 2001
                    //

                    if(code in 6000..6006) {
                        livePlaybackLogger.logDrmKeyLoadedFailed(
                            streamInfo = getStreamInfoForLogger()
                        )
                    }

                    if (isPlayingPreview()) {
                        handleEndPreviewOnError()
                        previewTimer.playerEvents.onError(code, name, detail, error403, responseCode)
                        return
                    }
                    if (isPlayingVipTrailerInEnablePreview()) return

                    <EMAIL>(resetInstreamAd = false)
                    //
                    userRealtimePlayingTracker.playerEvents.onError(code, name, detail, error403, responseCode)
                    playerRetryHandler.playerEvents.onError(code, name, detail, error403, responseCode)
                }

                override fun onErrorBehindInLive(code: Int, name: String, detail: String) {
                    super.onErrorBehindInLive(code, name, detail)
                    Logger.d("$TAG onErrorBehindLiveWindow")
                    //
                    if (isPlayingPreview()) {
                        handleEndPreviewOnError()
                        previewTimer.playerEvents.onErrorBehindInLive(code, name, detail)
                        return
                    }
                    if (isPlayingVipTrailerInEnablePreview()) return

                    <EMAIL>(resetInstreamAd = false)
                    //
                    userRealtimePlayingTracker.playerEvents.onErrorBehindInLive(code, name, detail)
                    playerRetryHandler.playerEvents.onErrorBehindInLive(code, name, detail)
                }

                override fun onDrmKeysLoaded() {
                    Logger.d("$TAG onDrmKeysLoaded")
                    livePlaybackLogger.logDrmKeyLoadedSuccess(
                        loadedTime = System.currentTimeMillis() - viewModel.getPrepareSourceTimeInMs(),
                        streamInfo = getStreamInfoForLogger()
                    )
                }

                override fun onPrepare() {
                    Logger.d("$TAG onPrepare")
                    TrackingUtil.saveStreamProfile("")
                    livePlaybackLogger.updateStreamProfile(streamProfile = "")

                    initTrackingHeartBeat()

                    //Tracking
                    if (viewModel.getVipRequired()?.first == false || binding.player.getPlayerConfig().isPlayPreview) {
                        livePlaybackLogger.logPlayAttempt(getStreamInfoForLogger(), viewModel.getIsStreamRetry() || playerRetryHandler.getIsAutoRetry())
                    }
                    livePlaybackLogger.setEnterOnPrepareTime(System.currentTimeMillis())
                    viewModel.savePrepareSourceTimeInMs(System.currentTimeMillis())


//                    startInstreamAds()

                    //
                    userRealtimePlayingTracker.playerEvents.onPrepare()

                    // Must set up before trigger onPrepare event
                    playerRetryHandler.playerEvents.onPrepare()
                }

                override fun onReady() {
                    Logger.d("$TAG onReady")
                    if (_binding != null) {
                        binding.player.onShowThumb(isShow = false)
                    }
                    // Logic save tracks history
                    updateLogicSaveTrack()
                    //
                    setupUserRealtimePlayingTracker()
                    //
                    if (binding.player.getPlayerConfig().isPlayPreview) {
                        setupDataForPreviewTimer()
                        previewTimer.playerEvents.onReady()
                    }
                    userRealtimePlayingTracker.playerEvents.onReady()
                    playerRetryHandler.playerEvents.onReady()

                    if (viewModel.isPlayingTimeshift()) {
                        sendTrackingStartTimeShift()
                    } else {
                        sendTrackingStartIptv()
                    }

                    if ((viewModel.getVipRequired()?.first == false && _binding != null) || binding.player.getPlayerConfig().isPlayPreview) {
                        val adsStartTime = viewModel.getPreAdsStartTimeInMs()
                        val adsEndTime = viewModel.getPreAdsEndTimeInMs()

                        livePlaybackLogger.logStartFirstFrame(
                            logStreamInfo = getStreamInfoForLogger(),
                            loadType = if(viewModel.getIsStreamRetry()) LoadType.Retry else LoadType.Initial,
                            startFromAds = viewModel.getIsPreAds(),
                            clickTime = viewModel.getClickTimeToPlay(),
                            startFirstFrameTime = System.currentTimeMillis(),
                            timePreAds = if (adsStartTime != 0L && adsEndTime != 0L && adsStartTime <= adsEndTime)
                                adsEndTime - adsStartTime else 0

                        )
                    }
                    viewModel.saveClickTimeToPlay(0)
                    viewModel.saveIsPreAds(false)
                    viewModel.savePreAdsStartTimeInMs(0)
                    viewModel.savePreAdsEndTimeInMs(0)

                }

                override fun onStart() {
                    Logger.d("$TAG onStart")
                    //
                    userRealtimePlayingTracker.playerEvents.onStart()
                    playerRetryHandler.playerEvents.onStart()

                    if(beginSeekTime != 0L) {
                        val seekTime = System.currentTimeMillis() - beginSeekTime
                        livePlaybackLogger.logSeekTimeShift(
                            streamInfo = getStreamInfoForLogger(),
                            timeSpendForSeekAction = seekTime,

                        )
                        beginSeekTime = 0
                    }
                }



                override fun onStop() {
                    Logger.d("$TAG onStop")
                    //
                    userRealtimePlayingTracker.playerEvents.onStop()
                    playerRetryHandler.playerEvents.onStop()

                    if (binding.player.getPlayerConfig().isPlayPreview) {
                        previewTimer.playerEvents.onStop()
                    }
                }

                override fun onFetchBitrateSuccess(bitrates: ArrayList<IPlayer.Bitrate>) {
                    Logger.d("$TAG => OnFetchBitrateSuccess: $bitrates")
                    if (!viewModel.isPlayingTimeshift()) {
                        mappingBitrate(playerDataBitrate = bitrates)
                        processChangeBitrate()
                    }
                    //
                    userRealtimePlayingTracker.playerEvents.onFetchBitrateSuccess(bitrates)
                    playerRetryHandler.playerEvents.onFetchBitrateSuccess(bitrates)
                }


                override fun onFetchBitrateAll(
                    bitrates: ArrayList<IPlayer.Bitrate>,
                    audioTracks: List<PlayerControlView.Data.Track>?
                ) {
                    val data = bitrates.joinToString(";") { data -> data.dataTracking() }
                    TrackingUtil.saveStreamProfile(data)
                    livePlaybackLogger.updateStreamProfile(streamProfile = data)
                }

                override fun onPlay() {
                    //
                    userRealtimePlayingTracker.playerEvents.onPlay()
                    playerRetryHandler.playerEvents.onPlay()
                }

                override fun onResume() {
                    //
                    userRealtimePlayingTracker.playerEvents.onResume()
                    playerRetryHandler.playerEvents.onResume()
                }

                override fun onRelease() {
                    //
                    userRealtimePlayingTracker.playerEvents.onRelease()
                    playerRetryHandler.playerEvents.onRelease()

                    if (binding.player.getPlayerConfig().isPlayPreview) {
                        previewTimer.playerEvents.onRelease()
                    }
                }
            })
        }
    }

    private fun handleEndPreviewOnError() {
        if (!isPreviewEnded) {
            Logger.d("PreviewTimer - End preview")
            isPreviewEnded = true
            sendTrackingStopIPTV()
            requiredBuyPackageForPreview()
            binding.player.exitFullscreenMode()
            previewTimer.clearData()
            if(NetworkUtils.isNetworkAvailable()) {
                playTrailer(viewModel.getVipRequired()?.second?.livePreviewInfo?.trailerUrl, viewModel.getVipRequired()?.second?.pingMqtt ?: false)
            }
        }
    }

    private fun bindEventInternetListener() { // TODO
        MainApplication.INSTANCE.networkDetector.observe(this) {
            Logger.d("$TAG -> NetworkDetector: $it")
            it?.let { hasInternet ->
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
                        if(_binding != null) {
                            Logger.d("$TAG -> NetworkDetector: isPlayerErrorByInternet: $isPlayerErrorByInternet $hasInternet")
                            if (isPlayerErrorByInternet && hasInternet) {
                                if(isPlayingPreview() || isPlayingVipTrailerInEnablePreview() || !sharedPreferences.userLogin()) {
                                    playTrailer(viewModel.getVipRequired()?.second?.livePreviewInfo?.trailerUrl, viewModel.getVipRequired()?.second?.pingMqtt ?: false)
                                } else {
                                    if(viewModel.isPlayingTimeshift()) {
                                        getScheduleStream(isRetry = true)
                                    }else{
                                        getChannelStream(isRetry = true)
                                    }
                                }
                            }
                        }
                    }
                    PlayerView.PlayingType.Cast -> {}
                }
                isPlayerErrorByInternet = false
            }
        }
    }

    private fun isPlayingPreview(): Boolean {
        return binding.player.getPlayerConfig().isPlayPreview
    }

    private fun isPlayingVipTrailerInEnablePreview(): Boolean {
        return viewModel.hasPreview && binding.player.isPlayingVipTrailer()
    }

    private fun bindEventFragmentResult() {
        parentFragment?.run {
            setFragmentResultListener(Utils.OPTION_DIALOG_BITRATE_KEY) { _, bundle ->
                val bitrateId = bundle.getString(Utils.OPTION_DIALOG_BITRATE_ID_KEY, "")

                if (bitrateId.isNotEmpty()) {
                    userClickChangeBitrate = true
                    if (viewModel.isPlayingTimeshift()) {
                        getScheduleStream(isRetry = false)
                    } else {
                        val oldBitrate = viewModel.getChannelBitrateId()
                        viewModel.saveChannelBitrateId(bitrateId = bitrateId)
                        //
                        sharedPreferences.saveBitrateLive(bitrateId = bitrateId)
                        //
                        if (!processChangeBitrate()) {
                            stopTvcAds()
                            getChannelStream(isRetry = false)
                        }

                        // Tracking
                        sendTrackingChangeVideoQuality(oldBitrate)
                    }

                }
            }

            setFragmentResultListener(Utils.OPTION_DIALOG_SUBTITLE_KEY) { _, bundle ->
                val subtitleId = bundle.getString(Utils.OPTION_DIALOG_SUBTITLE_ID_KEY, "")
                if (subtitleId.isNullOrEmpty()) {
                    return@setFragmentResultListener
                }
                viewModel.getTracks()?.findTrackById(id = subtitleId, type = TrackType.TEXT.ordinal)?.let {
                    // Save setting
                    sharedPreferences.saveSubLive(it.id)

                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            binding.player.changeTrack(track = it)
                        }
                        PlayerView.PlayingType.Cast -> {
                            getCurrentChannelDetail()?.let { tvChannelDetail ->
                                pairingConnection.sendEventChangeTrack(id = tvChannelDetail.id, refId = tvChannelDetail.refId, selectId = it.id, type = "sub")
                            }
                        }
                    }

                    // Tracking
                    sendTrackingChangeSubtitlesAudio(value = it.name, isChangeAudio = false)
                }
            }

            setFragmentResultListener(Utils.OPTION_DIALOG_AUDIO_TRACK_KEY){ _, bundle ->
                val audioTrackName = bundle.getString(Utils.OPTION_DIALOG_AUDIO_TRACK_ID_KEY, "")
                if (audioTrackName.isNullOrEmpty()) {
                    return@setFragmentResultListener
                }
                viewModel.getTracks()?.findTrackByName(name = audioTrackName, type = TrackType.AUDIO.ordinal)?.let {
                    // Save setting
                    sharedPreferences.saveAudioLive(it.name)

                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            binding.player.changeTrack(track = it)
                        }
                        PlayerView.PlayingType.Cast -> {
                            getCurrentChannelDetail()?.let { tvChannelDetail ->
                                pairingConnection.sendEventChangeTrack(id = tvChannelDetail.id, refId = tvChannelDetail.refId, selectId = it.id, type = "audio")
                            }
                        }
                    }

                    // Tracking
                    sendTrackingChangeSubtitlesAudio(value = it.name, isChangeAudio = true)
                }
            }

            setFragmentResultListener(Utils.OPTION_DIALOG_EXPAND_KEY) { _, bundle ->
                val id = bundle.getInt(Utils.OPTION_DIALOG_EXPAND_ID_KEY, 0)
                val pos = bundle.getInt(Utils.OPTION_DIALOG_EXPAND_POS_KEY, 0)
                binding.player.setResizeMode(resizeMode = id, position = pos)
            }

            setFragmentResultListener(Utils.OPTION_DIALOG_MORE_KEY) { _, bundle ->
                val id = bundle.getString(Utils.OPTION_DIALOG_MORE_ID_KEY, "")
                try {
                    when (LiveMoreData.Type.valueOf(id)) {
                        LiveMoreData.Type.Share -> {
                            binding.player.onShareLink(url = getCurrentChannelDetail()?.websiteUrl ?: "")
                        }
                        LiveMoreData.Type.Follow -> {
                            viewModel.dispatchIntent(AddFollow(id = viewModel.getChannelId()))
                        }
                        LiveMoreData.Type.UnFollow -> {
                            viewModel.dispatchIntent(DeleteFollow(id = viewModel.getChannelId()))
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }


            setFragmentResultListener(Utils.PAIRING_DIALOG_TYPE) { _, bundle ->
                val type = bundle.getInt(Utils.PAIRING_DIALOG_TYPE_KEY, 0)

                when (type) {
                    0 -> {
                        try {
                            pairingConnection.let {
                                it.getSelectDevice()?.run {
                                    when (this) {
                                        is FBoxDeviceInfoV2 -> { // Only logic for BoxC
                                            if (this.response.state == "running") {
                                                it.connect(this)
                                            } else {
                                                pairingScanner.awake(deviceInfo = this, lifecycleScope = lifecycleScope, callback = object : FScannerAwakeListener {
                                                    override fun awakeDeviceCallBack(deviceInfo: DeviceInfo?, isSuccess: Boolean, isRunning: Boolean) {
                                                        if (isRunning) {
                                                            if (deviceInfo is FBoxDeviceInfoV2) {
                                                                it.setSelectDevice(data = FBoxDeviceInfoV2(device = <EMAIL>, response = deviceInfo.response))
                                                                it.getSelectDevice()?.let { selectDevice ->
                                                                    it.connect(selectDevice)
                                                                }
                                                            }
                                                        } else {
                                                            it.showToast(message = binding.root.context.getString(R.string.pairing_control_waiting_connection, pairingConnection.getReceiverName()))
                                                            it.showToast(message = binding.root.context.getString(R.string.pairing_cast_title_connect_error))
                                                        }
                                                    }
                                                }
                                                )
                                            }
                                        }
                                        else -> {
                                            it.connect(this)
                                        }
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                    1 -> {}
                    2 -> {}
                    else -> {}
                }
            }

            setFragmentResultListener(Utils.PAIRING_CONTROL_NAVIGATE_TYPE) { _, bundle ->
                val type = bundle.getString(Utils.PAIRING_CONTROL_NAVIGATE_TYPE_KEY, "")
                if (!type.isNullOrBlank()) {
                    try {
                        pairingConnection.getCastingItemMapped()?.let {
                            val id = getCurrentChannelDetail()?.id
                            val typeItem = ItemType.LiveTV
                            if (it.id != id) {
                                if (ItemType.valueOf(type) == typeItem) {
                                    viewModel.triggerPlayChannel(TvChannel(id = it.id))
                                }
                            }
                        }
                    } catch (_: Exception) { }
                }
            }

            setFragmentResultListener(Constants.WARNING_REQUEST_KEY) {_, bundle ->
                Logger.d("Reset schedule")
                if (viewModel.isPlayingTimeshift()) {
                    shouldUseSchedule = bundle.getBoolean(Constants.WARNING_RESULT)
                }
            }
            setFragmentResultListener(Utils.OPTION_DIALOG_USER_REPORT_KEY) { _, bundle ->
                val isReported = bundle.getBoolean(Utils.OPTION_DIALOG_USER_REPORT_STATUS, false)
                val message = bundle.getString(Utils.OPTION_DIALOG_USER_REPORT_MESSAGE, "")
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
                        if(isReported) {
                            viewModel.dispatchIntent(
                                TriggerShowMsgUserReport(isReported = isReported, message = message
                                )
                            )
                        }
                    }
                    PlayerView.PlayingType.Cast -> {}
                }
            }
            setFragmentResultListener(Utils.DEEPLINK_NOT_SUPPORTED_CONFIRM_EVENT) { _, _ ->
                val navController = (activity as? HomeActivity)?.navHostFragment?.navController
                val navigation = if(navController?.graph?.id == R.id.nav_home_main) { navController } else { null }
                navigation?.navigate(NavHomeMainDirections.actionGlobalToHomeMainFragment())

            }
        }

    }

    private fun getStreamInfoForLogger(): LogStreamInfo {
        return LogStreamInfo(
            bitrate = binding.player.getTrackingBitrate(),
            bandwidth = binding.player.getTrackingBandWith(),
            streamBandwidth = binding.player.debugViewData.videoInfo.getBitrateRawValue(),
            videoQuality = kotlin.run {
                val isTimeshift = viewModel.isPlayingTimeshift()
                getBitrateNameForTracking(
                    isTimeshift,
                    getBitrateIdForTracking(isTimeshift),
                    tvSchedule = viewModel.playSchedule.value?.first
                )
            },
            audioName = binding.player.currentSelectedAudioTrack?.name ?: "",
            audioBandwidth = binding.player.debugViewData.audioInfo.getBitrateRawValue(),

            url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
            urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getExtraForUrlMode(sharedPreferences)}",
            status = if (binding.player.getPlayerConfig().isPlayPreview) LogStreamInfo.LogPlaybackStatus.Preview else LogStreamInfo.LogPlaybackStatus.None,
        )
    }

    override fun LiveTVDetailViewModel.LiveTVDetailState.toUI() {
        when (this) {
            is Loading -> {
                when (intent) {
                    is GetTvChannelDetail -> {
                        //
//                        binding.loading.visible()
                        viewModel.saveChannelId(channelId = intent.id)
                        //
                    }
                    else -> Unit
                }
            }
            is Error -> {
                when (intent) {
                    is GetTvChannelDetail -> {
                        showWarningDialog(message = this.message)
                    }
                    is GetTvChannelStream -> {
                        //
                        notifyGetStreamError(errorMessage = this.message)
                    }
                    is GetTvScheduleStream -> {
                        //
                        notifyGetStreamError(errorMessage = this.message)
                    }
                    else -> {}
                }
            }
            is ErrorByInternet -> {
                when (intent) {
                    is GetTvChannelStream,
                    is GetTvScheduleStream -> {
                        isPlayerErrorByInternet = true
                        showWarningDialog(message = this.message)
                    }
                    is GetTvChannelDetail -> {
                        showWarningDialog(message = this.message)
                    }
                    else -> {}
                }

                //
                resetGetStreamRetryStep()
            }
            is ErrorRequiredLogin -> {
                Logger.d("$TAG ErrorRequiredLogin: Navigate to login")
                when (intent) {
                    is GetTvChannelStream,
                    is GetTvScheduleStream -> {
                        requiredLogin?.let {
                            if (isShowRequiredLogin || !it.enablePreview) {
                                parentFragment?.parentFragment?.navigateToLoginWithParams(title = this.message)
                            }
                            if (it.enablePreview) { // Show only once for content with preview
                                isShowRequiredLogin = false
                            }
                            playTrailer(it.trailerUrl, pingStart = it.pingMqtt)
                        }
                        //
                        resetGetStreamRetryStep()
                    }
                    else -> {}
                }
            }
            is ErrorRequiredVip -> {
                Logger.d("$TAG ErrorRequiredVip")
                viewModel.saveVipRequired(isRequired = true, requiredVip = requiredVip)
                when (intent) {
                    is GetTvChannelStream -> {
                        handleRequiredVip(requiredVip)
                    }
                    is GetTvScheduleStream -> {
                        viewModel.saveVipRequired(isRequired = true, requiredVip = requiredVip)
                        parentFragment?.parentFragment?.navigateToRequiredBuyPackage(
                            title = requiredVip?.requireVipTitle ?: "",
                            message = requiredVip?.requireVipDescription ?: "",
                            titleNegative = requiredVip?.btnSkip ?: "",
                            titlePosition = requiredVip?.btnActive ?: "",
                            packageId = requiredVip?.requireVipPlan ?: "",
                            continueWatch = true
                        )
//                        showDialogRequireVip(message = requiredVip?.requireVipName?:"", vipPlan = requiredVip?.requireVipDescription?:"")

                        //
                    }
                    else -> {}
                }
                resetGetStreamRetryStep()
            }
            is Done -> {
                if (intent is GetTvChannelDetail) {
                    binding.loading.gone()
                }
            }
            is ResultTriggerPlayerLayout -> {
                if (context.isTablet()) {
                    adjustPlayerLayoutTablet(isScale = isScale)
                } else {
                    adjustPlayerLayout(isScale = isScale)
                }
            }
            is ResultSwitchPlayerMode -> {
                if (_binding != null) {
                    if (this.modeFullscreen) {
                        binding.player.enterFullscreenMode()
                    } else {
                        binding.player.exitFullscreenMode()
                    }
                }
            }
            is ResultCheckFollow -> {
                viewModel.saveUserFollow(userFollowed = data.isSuccess())
            }
            is ResultTvChannelDetail -> {
                //
                //region save data for log Adjust
                AdjustAllEvent.dataCur.contentId = data.id
                AdjustAllEvent.dataCur.contentName = data.aliasName
                AdjustAllEvent.dataCur.contentType = data.tracking.contentType
                AdjustAllEvent.dataCur.genre = ""
                AdjustAllEvent.dataCur.watchType = if (data.isVipProfile) WatchType.paid else WatchType.free
                AdjustAllEvent.dataCur.sourcePage = SourcePage.detail_module
                AdjustAllEvent.dataCur.source = Source.package_recommend
                //endregion
                getCurrentChannelDetail()?.let {
                    if (it.id != data.id) {
                        _binding?.player?.playerData?.resetBitrates()

                        isShowRequiredLogin = true

                        // Reset preview timer
                        previewTimer.clearData()
                    }
                }
                //
                if(MultiProfileUtils.isCurrentProfileKid(sharedPreferences) && !data.isKid) {
                    val navController = (activity as? HomeActivity)?.navHostFragment?.navController
                    val navigation = if(navController?.graph?.id == R.id.nav_home_main) { navController } else { null }
                    navigation?.navigate(NavHomeMainDirections.actionGlobalToDeeplinkNotSupportedDialog(
                        titlePositive = getString(R.string.multi_profile_kid_not_supported_content_title_confirm)
                    ))
                    return

                }
                setCurrentChannelDetail(channelDetail = data)
                livePlaybackLogger.updateChannelDetail(data)
                livePlaybackLogger.enterLivePlayback(
                    LogEnterPlayerScreenSource(
                        appId = TrackingUtil.currentAppId,
                        appName = TrackingUtil.currentAppName,
                        subMenuId = TrackingUtil.blockId,
                        blockId = TrackingUtil.blockId,
                        blockPosition = TrackingUtil.blockIndex,
                    )
                )
                initTvcAds(trackingAds)
                // Tracking
                //sendTrackingStartIptv()

                Handler(Looper.getMainLooper()).postDelayed({
                    if (viewModel.playSchedule.value != null) { // Case: If user tap play schedule before load cur stream => PlaySchedule
                        if (MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE && context.isTablet()){
                            getChannelStream(isRetry = false)
                        } else {
                            getScheduleStream(isRetry = false)
                        }
                    } else {
                        getChannelStream(isRetry = false)
                    }
                }, 50)

                //
                resetGetStreamRetryStep()
            }
            is ResultGetHighLightDetail -> {

            }
            is ResultTvChannelStream -> {
                //
                resetGetStreamRetryStep()
                liveTvPreviewPlayerInfo.resetData()
                //
                livePlaybackLogger.updateStreamInfo(data)
                viewModel.saveIsStreamRetry(intent.isRetry)
                //
                Logger.d("$TAG ResultTvChannelStream: $data")
                binding.player.playerData.refreshLiveState(isLive = true)
                livetvPlayerInfo.apply {
                    playScheduleStream = false
                    stream = data
                    drmKey = key
                }

                binding.player.apply {
                    setInComingRequest(
                        request = buildLiveTvRequest(
                            details = getCurrentChannelDetail(),
                            autoProfile = getCurrentChannelDetail()?.autoProfile ?: "",
                            stream = data,
                            isMulticast = isMulticast(),
                            headers = Utils.getHeaderForPlayerRequest(data.streamSession),
                            drmKey = key?.drmKeyBase64?.fromBase64Default(),
                        ),
                        isRunningBackgroundAudio = getCurrentChannelDetail()?.bgAudio == "1",
                        tvDetail = getCurrentChannelDetail()
                    )
                }

                // Check play in cast
                // if playing, not send event, check required vip
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
                        binding.player.onShowThumb(isShow = false)
                        binding.player.stop(force = true)
                        playPrerollAd()

                        //MQTT
                        publishStartToTopic(contentType = MqttContentType.LiveTV, pingStart = data.pingMqtt, drmPartner = data.merchant, mqttMode = data.mqttMode)
                    }
                    PlayerView.PlayingType.Cast -> {
                        // Case: Receive signal change channel from remote.
                        // Not apply, following logic from product team. When remote device change channel, local device stop player.

                        // Case: Change local -> send signal to remote
                        binding.player.preparePlayerLiveCast(
                            playerConfig = PlayerConfigBuilder()
                                .setEnableReportPlayer(false)
                                .setSupportAudioMode(isAudioMode = data.isAudio, backgroundAudioOverlay = data.audioBackgroundImageUrl)
                                .setEnableReportPlayer(isShownButtonReport())
                                .build(),
                            details = getCurrentChannelDetail(),
                            autoProfile = getCurrentChannelDetail()?.autoProfile ?: "",
                            data = data,
                            isMulticast = isMulticast(),
                            onEvents = object : PlayerHandler.OnEvents {}
                        )
                        binding.player.stopPlayerLocal(force = true, isClearRequest = true)
                        sendPlayContentMessage()

                        binding.player.onShowThumb(isShow = true, url = getCurrentChannelDetail()?.thumb ?: "")
                        //

                    }
                }
                //

//                playPrerollAd {
//                    viewModel.dispatchIntent(
//                        TriggerPreparePlayer(
//                            details = getCurrentChannelDetail(),
//                            bitrateId = viewModel.getBitrateId(),
//                            stream = data,
//                            liveType = PlayerView.LiveType.LiveTv
//                        )
//                    )
//                }

            }
            is PreparePlayer -> {
                trackingInfo.updatePlayingSession(time = System.currentTimeMillis())
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
                        cdtShowIp?.onFinish()
                        setupPlayerRetryHandler(channelId = getCurrentChannelDetail()?.id ?: "", streamId = getCurrentChannelDetail()?.autoProfile ?: "")
                        binding.player.preparePlayerLive(
                            playerConfig = PlayerConfigBuilder()
                                .setEnableReportPlayer(isShownButtonReport())
                                .setSupportAudioMode(isAudioMode = stream.isAudio, backgroundAudioOverlay = stream.audioBackgroundImageUrl)
                                .build(),
                            details = getCurrentChannelDetail(),
                            autoProfile = getCurrentChannelDetail()?.autoProfile ?: "",
                            data = stream,
                            isMulticast = isMulticast(),
                            onEvents = object : PlayerHandler.OnEvents {
                                override fun showError(reason: String) {
                                    Logger.d("$TAG showError $reason")
                                    showWarningDialog(message = reason)
                                }

                                override fun navigateToRequiredVip(
                                    planId: String,
                                    fromSource: String,
                                    idToPlay: String,
                                    vipBackground: String,
                                    title: String,
                                    description: String,
                                    btnActive: String,
                                    btnSkip: String,
                                    trailerUrl: String,
                                ) {
                                    AdjustAllEvent.sendPackageRecommendDisplayEvent(true)
                                    navigateToRequiredBuyPackage(
                                        title = title,
                                        message = description,
                                        titlePosition = btnActive,
                                        titleNegative = btnSkip,
                                        packageId = planId,
                                        isDirect = false
                                    )

                                    playTrailer(trailerUrl = trailerUrl, viewModel.getVipRequired()?.second?.pingMqtt ?: false)
                                }

                                override fun navigateToRequireLogin(message: String, idToPlay: String) {
                                    parentFragment?.parentFragment?.navigateToLoginWithParams(title = message, idToPlay = idToPlay, isDirect = true)
                                }

                                override fun sendTrackingPingPlayCcu(
                                    actionType: CommonInfor.PingStreamActionType,
                                    message: String,
                                    showFingerprint: Boolean,
                                    type: String
                                ) {
                                    // Send Tracking
                                    if (actionType == CommonInfor.PingStreamActionType.PING_STREAM_SHOW_ACTION) {
                                        sendTrackingShowIpSuccess(event = "ShowSuccessfully", errorMessage = "", typeId = type, message = message)
                                    } else if (actionType == CommonInfor.PingStreamActionType.PING_STREAM_RECEIVER_ACTION) {
                                        sendTrackingReceiveShowIp(event = "ReceivedSuccessfully", errorMessage = "", typeId = type, message = message)
                                    }
                                }

                                override fun hideShowIp() {
                                    tvShowIp?.hide()
                                }
                            },
                            onCastSessionEvents = object : PlayerHandler.OnCastSessionEvents {
                                override fun checkCastSession(): Boolean {
                                    return pairingConnection.isConnected
                                }
                            },
                            processShowIp = { duration, x, y ->
                                if (!activity.isInPiPMode()) {
                                    viewLifecycleOwner.lifecycleScope.launch {
                                        val ipPublic = viewModel.getPublicIp()
                                        withContext(Dispatchers.Main) {
                                            try {
                                                val root = binding.player.getViewStubShowIP().safeInflate()
                                                if (root != null) tvShowIp = (root as TextView)
                                                tvShowIp?.let { tvShowIp ->
                                                    val value = String.format("%s - %s", ipPublic, sharedPreferences.userId())
                                                    tvShowIp.text = value
                                                    tvShowIp.x = 0f
                                                    tvShowIp.y = 0f
                                                    tvShowIp.afterMeasured {
                                                        context?.let {
                                                            val newX = binding.player.width * (x / 100f) - tvShowIp.width
                                                            val newY = binding.player.height * (y / 100f) - tvShowIp.height
                                                            tvShowIp.x = if (newX >= 0) newX else 0f
                                                            tvShowIp.y = if (newY >= 0) newY else 0f
                                                        }
                                                    }
                                                    tvShowIp.show()
                                                    cdtShowIp?.cancel()
                                                    cdtShowIp = object : CountDownTimer(TimeUnit.SECONDS.toMillis(if (duration == 0L) 60 else duration), 1000) {
                                                        override fun onTick(p0: Long) {}
                                                        override fun onFinish() {
                                                            tvShowIp.hide()
                                                        }
                                                    }.start()
                                                    binding.player.sendTrackingPingPlayCcu(
                                                        actionType = CommonInfor.PingStreamActionType.PING_STREAM_SHOW_ACTION,
                                                        showFingerprint = true, message = value
                                                    )
                                                }
                                            } catch (e: Exception) {
                                                e.printStackTrace()
                                            }
                                        }
                                    }
                                }
                            },
                            processShowMatrixIp = {
                                // Mobile platform: Disable actively show ip logic
                                /*viewLifecycleOwner.lifecycleScope.launch {
                                    val ipPublic = viewModel.getPublicIp()
                                    withContext(Dispatchers.Main) {
                                        try {
                                            val userId = sharedPreferences.userId()
                                            val root = binding.player.getViewStubShowMatrix().safeInflate()
                                            if (root != null) rlShowMatrixIp = (root as RelativeLayout)
                                            rlShowMatrixIp?.let { rlShowMatrixIp ->
                                                val value = String.format("%s - %s", ipPublic, userId)
                                                for (index in 0 until rlShowMatrixIp.childCount) {
                                                    val tvIp = rlShowMatrixIp.getChildAt(index)
                                                    if (tvIp is TextView) tvIp.text = value
                                                }
                                                rlShowMatrixIp.show()
                                                cdtShowMatrixIp?.cancel()
                                                cdtShowMatrixIp = object : CountDownTimer(TimeUnit.SECONDS.toMillis(60), 1000) {
                                                    override fun onTick(p0: Long) {}
                                                    override fun onFinish() {
                                                        rlShowMatrixIp.hide()
                                                    }
                                                }.start()
                                                binding.player.sendTrackingPingPlayCcu(
                                                    actionType = CommonInfor.PingStreamActionType.PING_STREAM_SHOW_ACTION,
                                                    showFingerprint = true, message = "$value - FullScreen"
                                                )
                                            }
                                        } catch (e: Exception) {
                                            e.printStackTrace()
                                        }
                                    }
                                }*/
                            },
                            processReloadStream = {
                                tryRetry()
                            },
                            sportInteractiveViewModel = sportInteractiveViewModel,
                            headers = Utils.getHeaderForPlayerRequest(stream.streamSession),
                            drmKey = drmKey?.drmKeyBase64?.fromBase64Default()
                        )

                    }
                    PlayerView.PlayingType.Cast -> {}
                }
            }
            is ResultGetTVSchedule -> {
                // Get next list TvSchedule in next date and play first tv schedule
                if (isPlayerCalled) {
                    // save current schedule playlist
                    viewModel.saveCurrentPlayingTvSchedule(Pair(this.currentMilliseconds, this.data))
                    //
                    val schedule = data.firstOrNull()
                    schedule?.let {
                        viewModel.triggerPlaySchedule(data = schedule)
                    }
                }
            }
            is ResultTvScheduleStream -> {
                Logger.d("$TAG ResultTvScheduleStream: $data")
                //
                resetGetStreamRetryStep()
                liveTvPreviewPlayerInfo.resetData()
                //
                livePlaybackLogger.updateStreamInfo(streamInfo = data, isTimeShift = true)
                viewModel.saveIsStreamRetry(intent.isRetry)
                //
                binding.player.playerData.refreshLiveState(isLive = viewModel.playSchedule.value?.first?.liveState == 2)
                livetvPlayerInfo.apply {
                    playScheduleStream = true
                    stream = data
                    drmKey = null
                }
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
                        binding.player.onShowThumb(isShow = false)
                        playPrerollAd()

                        //MQTT
                        publishStartToTopic(contentType = MqttContentType.TimeShift, pingStart = data.pingMqtt, drmPartner = data.merchant, mqttMode = data.mqttMode)
                    }
                    PlayerView.PlayingType.Cast -> {
                        sendPlayContentMessage()
                        binding.player.onShowThumb(isShow = true, url = getCurrentChannelDetail()?.thumb ?: "")
                    }
                }
//                playPrerollAd {
//                    viewModel.dispatchIntent(
//                        TriggerPrepareSchedulePlayer(
//                            tvSchedule = getCurrentSchedule(),
//                            stream = data
//                        )
//                    )
//                }

            }
            is PrepareSchedulePlayer -> {
                trackingInfo.updatePlayingSession(time = System.currentTimeMillis())
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
                        val scheduleData = viewModel.playSchedule.value?.first
                        setupPlayerRetryHandler(
                            channelId = scheduleData?.id ?: "",
                            streamId = if (scheduleData?.bitrates.isNullOrEmpty()) {
                                ""
                            } else {
                                scheduleData?.bitrates?.last()?.id ?: ""
                            },
                        )
                        binding.player.prepareSchedulePlayer(
                            playerConfig = PlayerConfigBuilder()
                                .setEnableReportPlayer(getCurrentChannelDetail()?.enableReport?:false)
                                .setSupportAudioMode(isAudioMode = data.isAudio, backgroundAudioOverlay = data.audioBackgroundImageUrl)
                                .build(),
                            details = getCurrentChannelDetail(),
                            tvSchedule = viewModel.playSchedule.value?.first,
                            data = data,
                            sportInteractiveViewModel = sportInteractiveViewModel,
                            headers = Utils.getHeaderForPlayerRequest(data.streamSession)
                        )
                    }
                    PlayerView.PlayingType.Cast -> {}
                }
            }
            is ResultTriggerBuyPackageForPreview -> {
                handleBuyPackageForPreview()
            }
            else -> Unit
        }
    }

    //region Ping Drm
    //endregion

    //region Multicast
    private fun isMulticast() = false // viewModel.getBitrateId().contains("Multicast")
    private fun multicastUrl() = if (isMulticast()) {
        currentChannelDetail?.multicast ?: ""
    } else ""
    //endregion


    private fun observeData() {
        viewModel.initPlayer.observe(viewLifecycleOwner) {
            it?.run {
                viewModel.saveIsPlayingTimeshift(isPlayingTimeshift = shouldUseSchedule)
                binding.player.updateIsPlayingTimeShift(isPlayingTimeshift = shouldUseSchedule)
                viewModel.saveClickTimeToPlay(System.currentTimeMillis())
                getChannelDetail(id = viewModel.getChannelId())
            }
        }
        viewModel.playSchedule.observe(viewLifecycleOwner) { data ->
            viewModel.saveClickTimeToPlay(System.currentTimeMillis())

            data?.first?.let { tvSchedule ->
                // Tracking
                if (isPlayTimeShift) {
                    sendTrackingStopTimeShift()
                } else {
                    sendTrackingStopIPTV()
                }

                //MQTT
                publishEndToTopic()

                val stateTVChannelSchedule = DateTimeUtils.checkTimeWithCurrentTime(startTime = tvSchedule.startTime.toLong(), endTime = tvSchedule.endTime.toLong())
                viewModel.saveIsPlayingTimeshift(isPlayingTimeshift = stateTVChannelSchedule != 3)
                //
                binding.player.updateIsPlayingTimeShift(isPlayingTimeshift = viewModel.isPlayingTimeshift())
                stopTvcAds()

                // Stop player when change program
                binding.player.stop(force = true)
                binding.player.stopServices()
                //

                TrackingUtil.resetPosition()

                if(stateTVChannelSchedule != 3) {
                    getScheduleStream(isRetry = false)
                }else {
                    // Reset bitrate when change schedule
                    viewModel.saveChannelBitrateId(bitrateId = sharedPreferences.bitrateLive())

                    getChannelStream(isRetry = false)
                }

            }
        }
        viewModel.playChannel.observe(viewLifecycleOwner) { dataTriggerPlayChannel ->
            //Play channel from channel list (object TVChannel)
            val tvChannel = dataTriggerPlayChannel?.channel
            viewModel.saveClickTimeToPlay(System.currentTimeMillis())

            Timber.d("****** run trigger play channel ${viewModel.getCurChannel()} - $tvChannel")
            tvChannel?.run {
                Timber.d("****** run trigger -  ${viewModel.getCurChannel() == this}")

                if (viewModel.getCurChannel() == this && !dataTriggerPlayChannel.isForce)
                    return@run

                Timber.d("****** run trigger play over return == ${viewModel.isPlayingTimeshift()}")
                // Tracking
                if (isPlayTimeShift) {
                    sendTrackingStopTimeShift()
                } else {
                    sendTrackingStopIPTV()
                }

                //MQTT
                publishEndToTopic()

                TrackingUtil.resetPosition()
                TrackingUtil.idRelated = viewModel.getCurChannel()?.id ?: ""
                viewModel.saveCurChannel(this)
                viewModel.saveIsPlayingTimeshift(isPlayingTimeshift = false)
                //
                viewModel.saveHaveSwitchChannel(haveSwitch = true)
                binding.player.updateIsPlayingTimeShift(isPlayingTimeshift = false)

                stopTvcAds(resetPrerollAd = true, resetInstreamAd = true)
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
                        binding.player.stop(force = true)
                        binding.player.stopServices()
                    }
                    PlayerView.PlayingType.Cast -> {}
                }

                // Reset bitrate when change channel
                viewModel.saveChannelBitrateId(bitrateId = sharedPreferences.bitrateLive())

                getChannelDetail(this.id)

            }
        }
        viewModel.preparePlayPreview.observe(viewLifecycleOwner) { info ->
            info?.let {
                trackingInfo.updatePlayingSession(time = System.currentTimeMillis())
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
                        cdtShowIp?.onFinish()
                        setupPlayerRetryHandler(
                            channelId = getCurrentChannelDetail()?.id ?: "",
                            streamId = getCurrentChannelDetail()?.autoProfile ?: ""
                        )
                        isPreviewEnded = false
                        Logger.d("KhaiLog || Event: preparePlayPreview || PlayingSession: ${System.currentTimeMillis()}")
                        binding.player.preparePlayerLivePreview(
                            playerConfig = PlayerConfigBuilder()
                                .setEnableReportPlayer(isShownButtonReport())
                                .setEnableSharePlayer(false)
                                .setIsPlayPreview(isPlayPreview = true)
                                .build(),
                            details = getCurrentChannelDetail(),
                            autoProfile = getCurrentChannelDetail()?.autoProfile ?: "",
                            liveTvPreviewPlayerInfo = info,
                            onEvents = object : PlayerHandler.OnEvents {
                                override fun showError(reason: String) {
                                    Logger.d("$TAG showError $reason")
                                    showWarningDialog(message = reason)
                                }

                                override fun navigateToRequiredVip(
                                    planId: String,
                                    fromSource: String,
                                    idToPlay: String,
                                    vipBackground: String,
                                    title: String,
                                    description: String,
                                    btnActive: String,
                                    btnSkip: String,
                                    trailerUrl: String,
                                ) {
                                    navigateToRequiredBuyPackage(
                                        title = title,
                                        message = description,
                                        titlePosition = btnActive,
                                        titleNegative = btnSkip,
                                        packageId = planId,
                                        isDirect = false
                                    )
                                }

                                override fun navigateToRequireLogin(
                                    message: String,
                                    idToPlay: String
                                ) {
                                    parentFragment?.parentFragment?.navigateToLoginWithParams(
                                        title = message,
                                        idToPlay = idToPlay,
                                        isDirect = true
                                    )
                                }

                                override fun sendTrackingPingPlayCcu(
                                    actionType: CommonInfor.PingStreamActionType,
                                    message: String,
                                    showFingerprint: Boolean,
                                    type: String
                                ) {
                                    // Send Tracking
                                    if (actionType == CommonInfor.PingStreamActionType.PING_STREAM_SHOW_ACTION) {
                                        sendTrackingShowIpSuccess(
                                            event = "ShowSuccessfully",
                                            errorMessage = "",
                                            typeId = type,
                                            message = message
                                        )
                                    } else if (actionType == CommonInfor.PingStreamActionType.PING_STREAM_RECEIVER_ACTION) {
                                        sendTrackingReceiveShowIp(
                                            event = "ReceivedSuccessfully",
                                            errorMessage = "",
                                            typeId = type,
                                            message = message
                                        )
                                    }
                                }

                                override fun hideShowIp() {
                                    tvShowIp?.hide()
                                }
                            },
                            onCastSessionEvents = object : PlayerHandler.OnCastSessionEvents {
                                override fun checkCastSession(): Boolean {
                                    return pairingConnection.isConnected
                                }
                            },
                            processShowIp = { duration, x, y ->
                                if (!activity.isInPiPMode()) {
                                    viewLifecycleOwner.lifecycleScope.launch {
                                        val ipPublic = viewModel.getPublicIp()
                                        withContext(Dispatchers.Main) {
                                            try {
                                                val root =
                                                    binding.player.getViewStubShowIP().safeInflate()
                                                if (root != null) tvShowIp = (root as TextView)
                                                tvShowIp?.let { tvShowIp ->
                                                    val value = String.format(
                                                        "%s - %s",
                                                        ipPublic,
                                                        sharedPreferences.userId()
                                                    )
                                                    tvShowIp.text = value
                                                    tvShowIp.x = 0f
                                                    tvShowIp.y = 0f
                                                    tvShowIp.afterMeasured {
                                                        context?.let {
                                                            val newX =
                                                                binding.player.width * (x / 100f) - tvShowIp.width
                                                            val newY =
                                                                binding.player.height * (y / 100f) - tvShowIp.height
                                                            tvShowIp.x = if (newX >= 0) newX else 0f
                                                            tvShowIp.y = if (newY >= 0) newY else 0f
                                                        }
                                                    }
                                                    tvShowIp.show()
                                                    cdtShowIp?.cancel()
                                                    cdtShowIp = object : CountDownTimer(
                                                        TimeUnit.SECONDS.toMillis(if (duration == 0L) 60 else duration),
                                                        1000
                                                    ) {
                                                        override fun onTick(p0: Long) {}
                                                        override fun onFinish() {
                                                            tvShowIp.hide()
                                                        }
                                                    }.start()
                                                    binding.player.sendTrackingPingPlayCcu(
                                                        actionType = CommonInfor.PingStreamActionType.PING_STREAM_SHOW_ACTION,
                                                        showFingerprint = true, message = value
                                                    )
                                                }
                                            } catch (e: Exception) {
                                                e.printStackTrace()
                                            }
                                        }
                                    }
                                }
                            },
                            processShowMatrixIp = {
                                // Mobile platform: Disable actively show ip logic
                                /*viewLifecycleOwner.lifecycleScope.launch {
                                    val ipPublic = viewModel.getPublicIp()
                                    withContext(Dispatchers.Main) {
                                        try {
                                            val userId = sharedPreferences.userId()
                                            val root = binding.player.getViewStubShowMatrix().safeInflate()
                                            if (root != null) rlShowMatrixIp = (root as RelativeLayout)
                                            rlShowMatrixIp?.let { rlShowMatrixIp ->
                                                val value = String.format("%s - %s", ipPublic, userId)
                                                for (index in 0 until rlShowMatrixIp.childCount) {
                                                    val tvIp = rlShowMatrixIp.getChildAt(index)
                                                    if (tvIp is TextView) tvIp.text = value
                                                }
                                                rlShowMatrixIp.show()
                                                cdtShowMatrixIp?.cancel()
                                                cdtShowMatrixIp = object : CountDownTimer(TimeUnit.SECONDS.toMillis(60), 1000) {
                                                    override fun onTick(p0: Long) {}
                                                    override fun onFinish() {
                                                        rlShowMatrixIp.hide()
                                                    }
                                                }.start()
                                                binding.player.sendTrackingPingPlayCcu(
                                                    actionType = CommonInfor.PingStreamActionType.PING_STREAM_SHOW_ACTION,
                                                    showFingerprint = true, message = "$value - FullScreen"
                                                )
                                            }
                                        } catch (e: Exception) {
                                            e.printStackTrace()
                                        }
                                    }
                                }*/
                            },
                            processReloadStream = {
                                tryRetry()
                            },
                            sportInteractiveViewModel = sportInteractiveViewModel,
                            headers = Utils.getHeaderForPlayerRequest(info.streamSession),
                            drmKey = info.drmKey?.drmKeyBase64?.fromBase64Default()
                        )
                    }

                    PlayerView.PlayingType.Cast -> {}
                }
                // PairingControl
                // disconnect pairingControl when play preview
                pairingConnection.disconnect()
                //
            }
        }
        viewModel.preparePlayTrailer.observe(viewLifecycleOwner) { url ->
            url?.let {
                trackingInfo.updatePlayingSession(time = System.currentTimeMillis())
                Logger.d("$TAG preparePlayTrailer")
                setupPlayerRetryHandler(channelId = getCurrentChannelDetail()?.id ?: "", streamId = getCurrentChannelDetail()?.autoProfile ?: "")
                binding.player.onShowThumb(isShow = false)
                binding.player.updateScreenType(screenType = PlayerHandler.ScreenType.Live)
                binding.player.updateIsPlayingTimeShift(isPlayingTimeshift = false)
                binding.player.preparePlayerVipTrailer(
                    playerConfig = PlayerConfigBuilder()
                        .setEnableReportPlayer(isShownButtonReport())
                        .setEnableSharePlayer(false)
                        .build(),
                    url = it,
                    linkToShare = getCurrentChannelDetail()?.websiteUrl ?: ""
                )
            }
        }
        MqttConnectManager.INSTANCE.messageArrived()?.let {
            try {
                it.observe(viewLifecycleOwner) { messages ->
                    if (messages.isNotEmpty()) {
                        val latestMessage = messages[0]
                        val publisher = latestMessage.mapToPublisher()
                        publisher?.let { pub ->
                            if (pub.action == MqttUtil.ACTION_LIMIT_CCU) {
                                MqttConnectManager.INSTANCE.sendLogLimitCCU(pub, latestMessage.topic)
                            }
                        }

                    }
                }
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }
    }

    private fun resetPlayerLayout() {
        view?.run {
            val width = context.getDisplayWidth()
            val height = context.getDisplayHeight()
            val realWidth = if (width < height) width else height
            layoutParams = ConstraintLayout.LayoutParams(realWidth, (realWidth / Constants.PLAYER_RATIO).toInt())
        }
    }

    private fun tabletPlayerLayout() {
        view?.run {
            val width = context.getDisplayWidth()
            val height = context.getDisplayHeight()
            var realWidth = if (width > height) width else height
            realWidth = (realWidth * (Constants.PLAYER_SCALED_RATIO)).toInt()
            layoutParams = ConstraintLayout.LayoutParams(realWidth, (realWidth / Constants.PLAYER_RATIO).toInt())
        }
    }


    private fun showEndEventWarning(message: String) {
        AlertDialog().apply {
            setTextTitle(<EMAIL>().getString(R.string.notification))
            setShowTitle(true)
            setMessage(message)
            setOnlyConfirmButton(true)
            setTextConfirm(<EMAIL>().getString(R.string.understood))
            setHandleBackPress(true)
            setListener(object : AlertDialogListener{
                override fun onConfirm() {
                    findNavController().popBackStack()
                }
            })
        }.show(childFragmentManager, TAG)
    }

    private fun showPlayerErrorWithRetryDialog(message: String) {
        if (playerRetryDialog != null) {
            playerRetryDialog?.dismissAllowingStateLoss()
            playerRetryDialog = null
        }
        if (playerRetryDialog == null) {
            playerRetryDialog = AlertInfoDialog().apply {
                setTextTitle(title = <EMAIL>(R.string.notification))
                setMessage(text = message)
                setUserId(text = sharedPreferences.userId())
                setDeviceName(text = "${Util.manufacturer()} ${Util.model()}")
                setMac(text = Util.deviceId(<EMAIL>()))
                setVersion(text = "iZiOS ${BuildConfig.VERSION_NAME}")
                setTime(text = DateTimeUtils.addTimeWithCurrentLocationAndFormat(currentMilliseconds = System.currentTimeMillis(), timeFormat = "dd/MM/yyyy - HH:mm"))
                setType(text = if (viewModel.isPlayingTimeshift()) "LiveTV Timeshift" else "LiveTV")
                setMessageContent(text = kotlin.run {
                    if (viewModel.isPlayingTimeshift()) {
                        "${getCurrentChannelDetail()?.name ?: ""}\n${DateTimeUtils.addTimeWithCurrentLocationAndFormat(currentMilliseconds = (viewModel.playSchedule.value?.first?.startTime ?: 0) * 1000L, timeFormat = "dd/MM/yyyy HH:mm")}"
                    } else getCurrentChannelDetail()?.name ?: ""
                })
                setTextExit(text = <EMAIL>(R.string.all_exit))
                setTextConfirm(text = <EMAIL>(R.string.all_retry))
                setListener(object : AlertDialogListener {
                    override fun onExit() {
                        stopCountDownTimerRetry()
                    }

                    override fun onConfirm() {
                        tryRetry()
                        stopCountDownTimerRetry()
                    }
                })
            }
            playerRetryDialog?.show(childFragmentManager, "PlayerErrorWithRetryDialog")
        }
    }

    private fun showPlayerErrorDialog(message: String) {
        AlertInfoDialog().apply {
            setTextTitle(title = <EMAIL>(R.string.notification))
            setMessage(text = message)
            setUserId(text = sharedPreferences.userId())
            setDeviceName(text = "${Util.manufacturer()} ${Util.model()}")
            setMac(text = Util.deviceId(<EMAIL>()))
            setVersion(text = "iZiOS ${BuildConfig.VERSION_NAME}")
            setTime(text = DateTimeUtils.addTimeWithCurrentLocationAndFormat(currentMilliseconds = System.currentTimeMillis(), timeFormat = "dd/MM/yyyy - HH:mm"))
            setType(text = if (viewModel.isPlayingTimeshift()) "LiveTV Timeshift" else "LiveTV")
            setMessageContent(text = kotlin.run {
                if (viewModel.isPlayingTimeshift()) {
                    "${getCurrentChannelDetail()?.name ?: ""}\n${DateTimeUtils.addTimeWithCurrentLocationAndFormat(currentMilliseconds = (viewModel.playSchedule.value?.first?.startTime ?: 0) * 1000L, timeFormat = "dd/MM/yyyy HH:mm")}"
                } else getCurrentChannelDetail()?.name ?: ""
            })
            setTextExit(text = <EMAIL>(R.string.all_exit))
            setTextConfirm(text = <EMAIL>(R.string.all_retry))
            setListener(object : AlertDialogListener {
                override fun onConfirm() {
                    tryRetry()
                }
            })
        }.show(childFragmentManager, "PlayerErrorDialog")
    }

    private fun List<TvSchedule>.findNextTvSchedule(curTvSchedule: TvSchedule?): Pair<Boolean, TvSchedule?> { // Pain<curTvScheduleEndDay, data>
        this.forEachIndexed { index, item ->
            if (item.id == curTvSchedule?.id) {
                return if (index < size - 1) {
                    Pair(false, this[index + 1])
                } else Pair(true, null)
            }
        }
        return Pair(false, null)
    }
    private fun getChannelDetail(id: String) {
        viewModel.dispatchIntent(GetTvChannelDetail(id = id))
        checkFollow()
    }

    private fun checkFollow() {
        viewModel.dispatchIntent(CheckFollow(id = viewModel.getChannelId()))
    }

    private fun getChannelStream(delay: Long = 0L, isRetry: Boolean = false) {
        viewModel.saveVipRequired(isRequired = false)
        //
        val channelDetail = getCurrentChannelDetail()

        viewModel.dispatchIntent(
            GetTvChannelStream(
                id = channelDetail?.id ?: "",
                bitrateId = channelDetail?.autoProfile ?: "",
                delay = delay,
                enablePreview = "1",
                isRetry = isRetry
            )
        )
    }

    /**
     * Get schedule stream
     */
    private fun getScheduleStream(delay: Long = 0L, isRetry: Boolean = false) {
        //
        fun TvSchedule?.findBitrateId(): String {
            var userBitrateId = viewModel.getScheduleBitrateId()
            if (this == null) {
                userBitrateId = ""
            } else {
                if (bitrates.isNullOrEmpty()) userBitrateId = ""
                else {
                    val result = bitrates.firstOrNull { it.id == userBitrateId }
                    if (result == null) {
                        userBitrateId = bitrates[bitrates.lastIndex].id
                    }
                }
            }
            // If save bitrate id from schedule so wrong something, because currently Schedule' stream has only one.
             viewModel.saveScheduleBitrateId(bitrateId = userBitrateId)
            return userBitrateId
        }
        //
        val schedule = viewModel.playSchedule.value?.first
        schedule?.let {
            val stateTVChannelSchedule = DateTimeUtils.checkTimeWithCurrentTime(startTime = it.startTime.toLong(), endTime = it.endTime.toLong())
            if (stateTVChannelSchedule != 3) { // PROGRAM != LIVE
                binding.player.playerData.bindBitrateFromSchedule(data = schedule.bitrates)
            }
        }
        viewModel.dispatchIntent(
            GetTvScheduleStream(
                scheduleId = schedule?.id ?: "",
                bitrateId = schedule.findBitrateId(),
                delay = delay,
                isRetry = isRetry
            )
        )
    }
    //endregion

    //region Bind -> Data Player Control
    /**
     * Bind live state to display for user
     */
    private fun PlayerControlView.Data.refreshLiveState(isLive: Boolean = true) {
        this.isLive = isLive
    }

    /**
     * Reset bitrates when change channel
     */
    private fun PlayerControlView.Data.resetBitrates() {
        bitrates = listOf()
    }

    /**
     * Bind all bitrates to display for user
     */
    private fun PlayerControlView.Data.bindBitrateFromTv(data: List<TvChannelDetail.Bitrate>?) {
        //
        fun List<TvChannelDetail.Bitrate>?.getBitrateIndex(): Int {
            if (this.isNullOrEmpty()) return -1
            val userBitrateIndex = this.indexOfFirst { it.id == viewModel.getChannelBitrateId() }
            return if (userBitrateIndex >= 0 && userBitrateIndex < this.size) userBitrateIndex
            else this.size - 1
        }
        //
        if (data.isNullOrEmpty()) this.bitrates = emptyList()
        else {
            bitrates = data.map { bitrate ->
                PlayerControlView.Data.Bitrate(
                    id = bitrate.id,
                    name = bitrate.name,
                    iconVip = "",
                    type = bitrate.type()
                )
            }
            bitrateIndex = data.getBitrateIndex()
        }
    }

    /**
     * Bind all bitrates to display bitrate for user
     */
    private fun PlayerControlView.Data.bindBitrateFromSchedule(data: List<TvSchedule.Bitrate>?) {
        //
        fun List<TvSchedule.Bitrate>?.getBitrateIndex(): Int {
            if (this.isNullOrEmpty()) return -1
            val userBitrateIndex = this.indexOfFirst { it.id == viewModel.getScheduleBitrateId() }
            return if (userBitrateIndex >= 0 && userBitrateIndex < this.size) userBitrateIndex
            else this.size - 1
        }
        //
        if (data.isNullOrEmpty()) this.bitrates = emptyList()
        else {
            bitrates = data.map { bitrate ->
                PlayerControlView.Data.Bitrate(
                    id = bitrate.id,
                    name = bitrate.name,
                    iconVip = ""
                )
            }
            bitrateIndex = data.getBitrateIndex()
        }
    }

    private fun TvChannelDetail?.findBitrateId(): String {
        var userBitrateId = viewModel.getChannelBitrateId()
        if (this == null) {
            userBitrateId = ""
        } else {
            if (appBitrates.isNullOrEmpty()) userBitrateId = ""
            else {
                val result = appBitrates.firstOrNull { it.id == userBitrateId }
                if (result == null) {
                    userBitrateId = appBitrates[appBitrates.lastIndex].id
                }
            }
        }
        viewModel.saveChannelBitrateId(bitrateId = userBitrateId)
        return userBitrateId
    }

    private fun mappingBitrate(playerDataBitrate: ArrayList<IPlayer.Bitrate>) {
        val cacheListBitrateApi = mutableListOf<TvChannelDetail.Bitrate>()
        cacheListBitrateApi.addAll(getCurrentChannelDetail()?.apiBitrates ?: listOf())

        val processBitrateList = mutableListOf<TvChannelDetail.Bitrate>().apply {
            addAll(cacheListBitrateApi.map {
                playerDataBitrate.firstOrNull { bitrate -> bitrate.name == it.id }?.let { _ ->
                    it.copy()
                } ?: TvChannelDetail.Bitrate(
                    id = it.name,
                    name = it.name + "p",
                    requiredPayment = true, // can't found from API map
                )
            }.filter { bitrate -> !bitrate.requiredPayment })

            // Always add auto profile
            cacheListBitrateApi.firstOrNull { bitrate -> bitrate.type() == "auto_default" }?.let { autoBitrate ->
                add(autoBitrate)
            }
        }

        getCurrentChannelDetail()?.appBitrates?.apply {
            clear()
            addAll(processBitrateList)
        }

        Logger.d("$TAG => MappingBitrate => => Bitrates: ${getCurrentChannelDetail()?.appBitrates}")
    }

    private fun processChangeBitrate() : Boolean {
        _binding?.let {
            val bitrateId = getCurrentChannelDetail()?.findBitrateId() ?: ""
            //
            binding.player.playerData.bindBitrateFromTv(data = getCurrentChannelDetail()?.appBitrates)
            //
            val result = binding.player.changeBitrate(
                playerRequestId = playerRequestId(),
                key = bitrateId
            )
            Logger.d("$TAG => ProcessChangeBitrate => BitrateId: $bitrateId => Bitrates: ${binding.player.playerData.bitrates} => Result: $result")
            return result
        } ?: return true
    }

    private fun playerRequestId(): String {
        return getCurrentChannelDetail()?.id ?: ""
    }


    companion object {
        private var currentChannelDetail: TvChannelDetail? = null

        //
        fun setCurrentChannelDetail(channelDetail: TvChannelDetail?) {
            this.currentChannelDetail = channelDetail
            //save data for tracking log kibana
            currentChannelDetail?.let {
                TrackingUtil.resetDataPlaying()
                TrackingUtil.setIdContentAndRefId(idContent = it.id, refId = it.refId)
            }
        }

        fun getCurrentChannelDetail() = currentChannelDetail
        //
    }

    //region Tracking
    private fun initTrackingHeartBeat() {
        removeTrackingHeartBeat()
        if (handlerTrackingHeartBeat == null) {
            Looper.getMainLooper()?.run {
                handlerTrackingHeartBeat = Handler(this)
            }
        }
        handlerTrackingHeartBeat?.run {
            post(runnableTrackingHeartBeat)
        }
    }

    private fun removeTrackingHeartBeat() {
        handlerTrackingHeartBeat?.removeCallbacks(runnableTrackingHeartBeat)
        handlerTrackingHeartBeat = null
    }

    @SuppressLint("SimpleDateFormat")
    private fun sendTrackingHeartBeat() {
        if (binding.player.isPlaying() && ((viewModel.getVipRequired()?.first != true) || binding.player.getPlayerConfig().isPlayPreview)) {
            if (pairingConnection.isConnected) {
                return
            }
            trackingProxy.sendEvent(
                InforMobile(
                    infor = trackingInfo,
                    logId = "111",
                    appId = TrackingUtil.currentAppId,
                    appName = TrackingUtil.currentAppName,
                    screen = if (binding.player.getPlayerConfig().isPlayPreview) "PingPreviewLive" else "PingChannel",
                    event = "Ping",
                    itemId = getCurrentChannelDetail()?.id?:"",
                    chapterId = "",
                    itemName = getCurrentChannelDetail()?.name?:"",
                    url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                    boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                    dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                    elapsedTimePlaying = (binding.player.currentDuration() / 1000).toString(),
                    realTimePlaying = getRealTimePlayingForLog(),
                    videoQuality = kotlin.run {
                        val isTimeshift = viewModel.isPlayingTimeshift()
                        getBitrateNameForTracking(isTimeshift, getBitrateIdForTracking(isTimeshift), tvSchedule = viewModel.playSchedule.value?.first)
                    },
                    audio = binding.player.currentSelectedAudioTrack?.name ?: "",
                    subtitle = binding.player.currentSelectedSubtitleTrack?.name ?: "",
                    streamProfile = TrackingUtil.getStreamProfile(),
                    subMenuId = TrackingUtil.blockId,
                    businessPlan = getCurrentChannelDetail()?.vipPlan ?: "",
                    isLinkDRM = getCurrentChannelDetail()?.isVerimatrix?.toString() ?:"",
                    CDNName = "",
                    Bitrate = binding.player.getTrackingBitrate(),
                    Resolution = binding.player.getVideoSize(),
                    drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                    refItemId = TrackingUtil.contentPlayingInfo.refId,
                    refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                    refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                    bandwidth =  binding.player.getTrackingBandWith(),
                    streamBandwidth = binding.player.debugViewData.videoInfo.getBitrateRawValue(),
                    streamBandwidthAudio = binding.player.debugViewData.audioInfo.getBitrateRawValue(),
                    dimension = TrackingUtil.getDimensionForTracking(width = sharedPreferences.getDisplayWidth(), height = sharedPreferences.getDisplayHeight()),

                    videoCodec = sharedPreferences.videoCodecInfo(),
                    videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                    audioCodec = sharedPreferences.audioCodecInfo(),

                    deviceFingerprint = sharedPreferences.deviceFingerprint(),
                    urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getExtraForUrlMode(sharedPreferences)}",

                    totalByteLoaded = binding.player.getPlayer()?.currentByteLoaded()?.toString() ?: "",
                    streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",

                    hdrType = "${MainApplication.INSTANCE.applicationContext.getHdrType()}",
                    position = TrackingUtil.position,
                    isRecommend = TrackingUtil.isRecommend,
                    appSource = getCurrentChannelDetail()?.appId ?: ""

                )
            )
        }

        handlerTrackingHeartBeat?.run {
            postDelayed(runnableTrackingHeartBeat, 60000)
        }
    }

    /**
     * Send log ping for start and stop action
     */
    private fun sendTracking111(stop:Boolean = false){ // for log start and stop
        if (sharedPreferences.userLogin() && ((viewModel.getVipRequired()?.first != true) || binding.player.getPlayerConfig().isPlayPreview)) {
            if(pairingConnection.isConnected) return
            trackingProxy.sendEvent(
                InforMobile(
                    infor = trackingInfo,
                    logId = "111",
                    appId = TrackingUtil.currentAppId,
                    appName = TrackingUtil.currentAppName,
                    screen = if (binding.player.getPlayerConfig().isPlayPreview) "PingPreviewLive" else "PingChannel",
                    event = "Ping",
                    itemId = getCurrentChannelDetail()?.id?:"",
                    chapterId = "",
                    itemName = getCurrentChannelDetail()?.name?:"",
                    url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                    boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                    dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                    elapsedTimePlaying = (binding.player.currentDuration() / 1000).toString(),
                    realTimePlaying = if(stop && !binding.player.isPlayingVipTrailer()) userRealtimePlayingTracker.getRealtimePlaying().toString() else getRealTimePlayingForLog(),
                    videoQuality = kotlin.run {
                        val isTimeshift = viewModel.isPlayingTimeshift()
                        getBitrateNameForTracking(isTimeshift, getBitrateIdForTracking(isTimeshift), tvSchedule = viewModel.playSchedule.value?.first)
                    },
                    audio = binding.player.currentSelectedAudioTrack?.name ?: "",
                    subtitle = binding.player.currentSelectedSubtitleTrack?.name ?: "",
                    streamProfile = TrackingUtil.getStreamProfile(),
                    subMenuId = if(stop) subMenuIdWhenStartPlay else TrackingUtil.blockId,
                    businessPlan = getCurrentChannelDetail()?.vipPlan ?: "",
                    isLinkDRM = getCurrentChannelDetail()?.isVerimatrix?.toString() ?:"",
                    CDNName = "",
                    Bitrate = binding.player.getTrackingBitrate(),
                    Resolution = binding.player.getVideoSize(),
                    drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                    refItemId = TrackingUtil.contentPlayingInfo.refId,
                    refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                    refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                    bandwidth =  binding.player.getTrackingBandWith(),
                    streamBandwidth = binding.player.debugViewData.videoInfo.getBitrateRawValue(),
                    streamBandwidthAudio = binding.player.debugViewData.audioInfo.getBitrateRawValue(),
                    dimension = TrackingUtil.getDimensionForTracking(width = sharedPreferences.getDisplayWidth(), height = sharedPreferences.getDisplayHeight()),

                    videoCodec = sharedPreferences.videoCodecInfo(),
                    videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                    audioCodec = sharedPreferences.audioCodecInfo(),

                    deviceFingerprint = sharedPreferences.deviceFingerprint(),
                    urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getExtraForUrlMode(sharedPreferences)}",

                    totalByteLoaded = binding.player.getPlayer()?.currentByteLoaded()?.toString() ?: "",
                    streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",

                    hdrType = "${MainApplication.INSTANCE.applicationContext.getHdrType()}",
                    idRelated = TrackingUtil.idRelated,
                    position = TrackingUtil.position,
                    isRecommend = TrackingUtil.isRecommend,
                    appSource = getCurrentChannelDetail()?.appId ?: ""
                )
            )
        }
    }

    private fun sendTrackingShownPopup(message: String = "") {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "191",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "Popup",
                event = "ShowPopup",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                chapterId = "",
                itemId = getCurrentChannelDetail()?.id?:"",
                itemName = message
            )
        )
    }

    private fun sendTrackingRequestPackage() {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "412",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "IPTVRequestPackage",
                event = "IPTVRequestPackage",
                itemId = getCurrentChannelDetail()?.id ?: "",
                itemName = getCurrentChannelDetail()?.name ?: "",
                groupChannel = TrackingUtil.blockId,
                businessPlan = getCurrentChannelDetail()?.vipPlan ?: "",
                isLinkDRM = isLinkDRM(),
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getExtraForUrlMode(sharedPreferences)}",
                subMenuId = TrackingUtil.blockId,
                key = TrackingUtil.key.rawValue,
            )
        )
    }


    private fun sendTrackingBuffering(event: String, bandwidth: String, bufferLength: String) {
        if(pairingConnection.isConnected) return
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "112",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "Buffering",
                event = event,
                bandwidth = bandwidth,
                bufferLength = bufferLength,
                chapterId = "",
                itemId = getCurrentChannelDetail()?.id?:"",
                itemName = getCurrentChannelDetail()?.name?:"",
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                elapsedTimePlaying = (binding.player.currentDuration() / 1000).toString(),
                realTimePlaying = getRealTimePlayingForLog(),
                subMenuId = TrackingUtil.blockId,
                businessPlan = getCurrentChannelDetail()?.vipPlan ?: "",
                isLinkDRM = getCurrentChannelDetail()?.isVerimatrix?.toString() ?:"",
                CDNName = "",
                Bitrate = binding.player.getTrackingBitrate(),
                Resolution = binding.player.getVideoSize(),
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getExtraForUrlMode(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.position,
                appSource = getCurrentChannelDetail()?.appId ?: ""
            )
        )
    }

    private fun sendTrackingChangeResolution() {
        if(pairingConnection.isConnected) return
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "113",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "ChangeResolution",
                event = "ChangeResolution",
                bandwidth = binding.player.getTrackingBandWith(),
                isManual = if (userClickChangeBitrate) "1" else "0",
                itemId = getCurrentChannelDetail()?.id?:"",
                chapterId = "",
                itemName = getCurrentChannelDetail()?.name?:"",
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                elapsedTimePlaying = (binding.player.currentDuration() / 1000).toString(),
                realTimePlaying = getRealTimePlayingForLog(),
                videoQuality = kotlin.run {
                    val isTimeshift = viewModel.isPlayingTimeshift()
                    getBitrateNameForTracking(isTimeshift, getBitrateIdForTracking(isTimeshift), tvSchedule = viewModel.playSchedule.value?.first)
                },
                audio = binding.player.currentSelectedAudioTrack?.name ?: "",
                subtitle = binding.player.currentSelectedSubtitleTrack?.name ?: "",
                subMenuId = TrackingUtil.blockId,
                businessPlan = getCurrentChannelDetail()?.vipPlan ?: "",
                isLinkDRM = getCurrentChannelDetail()?.isVerimatrix?.toString() ?:"",
                CDNName = "",
                Bitrate = binding.player.getTrackingBitrate(),
                Resolution = binding.player.getVideoSize(),
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getExtraForUrlMode(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.position
            )
        )
    }

    private fun getBitrateIdForTracking(isTimeshift: Boolean): String {
        return if (isTimeshift) viewModel.getScheduleBitrateId() else viewModel.getChannelBitrateId()
    }

    private fun getBitrateNameForTracking(isTimeshift: Boolean, bitrateId: String, tvSchedule: TvSchedule ?= null): String {
        if (isTimeshift) {
            tvSchedule?.let {
                if (it.bitrates.isEmpty()) {
                    return ""
                } else {
                    val result = it.bitrates.firstOrNull { bitrate -> bitrate.id == bitrateId}
                    return result?.name ?: ""
                }
            } ?: kotlin.run { return "" }
        } else {
            getCurrentChannelDetail()?.let {
                if (it.appBitrates.isEmpty()) {
                    return ""
                } else {
                    val result = it.appBitrates.firstOrNull { bitrate -> bitrate.id == bitrateId}
                    return result?.name ?: ""
                }
            } ?: kotlin.run { return "" }
        }
    }
    //endregion

    //region Logic save tracks history
    private fun updateLogicSaveTrack() {
        if (_binding != null) {
            val tracks = binding.player.playerData.tracks ?: listOf()
            val listSubtitle = tracks.filter { item -> (item.type == TrackType.TEXT.ordinal || ((item.id == "Tắt phụ đề" || item.name == "Tắt phụ đề") && item.type == 10002))}
            val listAudio = tracks.filter { item -> (item.type == TrackType.AUDIO.ordinal) }

            listSubtitle.findLast { it.isSelected }?.let { selectedSub ->
                tracks.findTrackById(id = sharedPreferences.subLive(), type = TrackType.TEXT.ordinal)?.let { historyTrack ->
                    if (selectedSub.id != historyTrack.id) {
                        binding.player.changeTrack(historyTrack)
                    }
                }
            }

            listAudio.findLast { it.isSelected }?.let { selectedAudio ->
                tracks.findTrackByName(name = sharedPreferences.audioLive(), type = TrackType.AUDIO.ordinal)?.let { historyTrack ->
                    if (selectedAudio.name != historyTrack.name) {
                        binding.player.changeTrack(historyTrack)
                    }
                }
            }
        }
    }
    //endregion

    // region Ads


    private fun initTvcAds(adsTrackingProxy: AdsTrackingProxy) {
//        currentChannelDetail?.enableAds == 1
        if (currentChannelDetail?.enableAds == 1 && AdsUtils.canRequestAds(requireContext(), sharedPreferences, disableOnProfileKids = false)) {
            // filter content show ads for profile kids from api by field enableAds, not hard code from client
            binding.player.initAdsController(adsTrackingProxy)
            binding.player.tvcAdsListener = tvcAdsListener
        }
    }

    private fun playPrerollAd() {
        if (currentChannelDetail?.enableAds == 1 && AdsUtils.canRequestAds(requireContext(), sharedPreferences, disableOnProfileKids = false)) {
            // filter content show ads for profile kids from api by field enableAds, not hard code from client
            viewModel.saveIsPreAds(true)
            val contentId = currentChannelDetail?.id
            binding.player.playPrerollAds(
                websiteUrl = getCurrentChannelDetail()?.websiteUrl ?: "",
                contentId = contentId ?: "",
                contentListStructureId = if(contentId != null) listOf(contentId) else null
            )
            viewModel.savePreAdsStartTimeInMs(System.currentTimeMillis())
        } else {
            tvcAdsListener.onPrerollCompleted()
        }
    }

    private fun startInstreamAds() {
        if (currentChannelDetail?.enableAds == 1 && AdsUtils.canRequestAds(requireContext(), sharedPreferences, disableOnProfileKids = false)) {
            // filter content show ads for profile kids from api by field enableAds, not hard code from client

            val channelId = currentChannelDetail?.id ?: ""
            binding.player.startInstreamLiveTV(
                channelId = channelId,
                contentId = channelId,
                contentListStructureId = listOf(channelId)
            )
        }
    }

    private fun stopTvcAds(resetPrerollAd: Boolean = false, resetInstreamAd: Boolean = true) {
        binding.player.stopTvcAds(resetPrerollAd, resetInstreamAd)
    }

    private val tvcAdsListener by lazy {
        object : AdsTvcListener() {
            override fun onPrerollCompleted() {
                Logger.d("$TAG onPrerollCompleted: $livetvPlayerInfo")
                if (viewModel.canPreview) {
                    Logger.d("$TAG trigger play preview: $livetvPlayerInfo")
                    viewModel.triggerPreparePlayPreview(liveTvPreviewPlayerInfo)
                } else {
                    livetvPlayerInfo.apply {
                        if (stream != null) {
                            if (playScheduleStream) {
                                viewModel.dispatchIntent(
                                    TriggerPrepareSchedulePlayer(
                                        tvSchedule = viewModel.playSchedule.value?.first,
                                        stream = stream!!,
                                    )
                                )
                            } else {
                                viewModel.dispatchIntent(
                                    TriggerPreparePlayer(
                                        details = getCurrentChannelDetail(),
                                        bitrateId = getCurrentChannelDetail()?.autoProfile ?: "",
                                        stream = stream!!,
                                        drmKey = drmKey
                                    )
                                )
                            }
                        }
                    }
                }
                viewModel.savePreAdsEndTimeInMs(System.currentTimeMillis())
            }

            override fun onOutStreamInteractivePopupLoaded(dataJson: String, userInteract: Boolean) {
            }
        }

    }

    // endregion Ads

    //region Tracking

    private fun sendTracking(
        logId: String,
        screen: String = if (pairingConnection.isConnected) "Casting" else TrackingUtil.screen,
        event: String,
        errorCode: String = "",
        errorMessage: String = "",
        issueId: String = "",
    ) {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = logId,
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                appSource = getCurrentChannelDetail()?.appId ?: "",
                screen = screen,
                event = event,
                chapterId = "",
                itemId = getCurrentChannelDetail()?.id ?: "",
                itemName = getCurrentChannelDetail()?.name ?: "",
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                groupChannel = TrackingUtil.blockId,
                blocKPosition = TrackingUtil.blockIndex,
                realTimePlaying = getRealTimePlayingForLog(),
                isLive = TrackingUtil.isLive,
                errorCode = errorCode,
                errorMessage = errorMessage,
                errUrl = (binding.player.getPlayer() as? ExoPlayerProxy)?.tracking?.urlError ?: "",
                errHeader = (binding.player.getPlayer() as? ExoPlayerProxy)?.tracking?.responseHeaderWhenError ?: "",
                multicast = livetvPlayerInfo.stream?.url ?: "",
                videoQuality = getBitrateIdForTracking(isTimeshift = viewModel.isPlayingTimeshift()),
                businessPlan = getCurrentChannelDetail()?.vipPlan ?: "",
                drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                isRoot = sharedPreferences.isDeviceRooted(),
                widevineLevel = sharedPreferences.widevineLevel(),
                buildNumber = sharedPreferences.buildNumber(),
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getExtraForUrlMode(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                issueId = issueId,
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.position,
                isRecommend = TrackingUtil.isRecommend
            )
        )
    }
    private fun sendTrackingStartIptv() {
        isPlayTimeShift = false
        if(pairingConnection.isConnected) return
        Logger.d("trangtest start iptv vipPlan = ${getCurrentChannelDetail()?.vipPlan ?: ""}")
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "41",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = if (pairingConnection.isConnected) "Casting" else TrackingUtil.screen,
                event = "StartChannel",
                itemId = getCurrentChannelDetail()?.id ?: "",
                itemName = getCurrentChannelDetail()?.name ?: "",
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                blocKPosition = TrackingUtil.blockIndex,
                multicast = livetvPlayerInfo.stream?.url ?: "",
                videoQuality = getBitrateIdForTracking(isTimeshift = viewModel.isPlayingTimeshift()),
                groupChannel = TrackingUtil.blockId,
                businessPlan = getCurrentChannelDetail()?.vipPlan ?: "",
                isLinkDRM = isLinkDRM(),
                key = TrackingUtil.key.rawValue,
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),
                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getExtraForUrlMode(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                subMenuId = TrackingUtil.blockId,
                status = if (binding.player.getPlayerConfig().isPlayPreview) "Preview" else "None",
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.position,
                isRecommend = TrackingUtil.isRecommend
            )
        )
        sendTracking111()
        screenStartLog41 = TrackingUtil.screen
        subMenuIdWhenStartPlay = TrackingUtil.blockId
    }

    private fun sendTrackingStopIPTV() {
        if(pairingConnection.isConnected) return
        //log Adjust
        AdjustAllEvent.sendVideoViewEvent(
            watchDuration = if (binding.player.isPlayingVipTrailer()) getRealTimePlayingForLog() else userRealtimePlayingTracker.getRealtimePlaying().toString(),
            isComplete = if(binding.player.getPlayer()?.isEnd() == true) "true" else "false",
            abandonedReason = AbandonedReason.user_initiated
        )
        sendTracking111(stop = true)
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "42",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = if (pairingConnection.isConnected) "Casting" else screenStartLog41.ifBlank { TrackingUtil.screen },
                event = "StopChannel",
                itemId = getCurrentChannelDetail()?.id ?: "",
                itemName = getCurrentChannelDetail()?.name ?: "",
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                blocKPosition = TrackingUtil.blockIndex,
                realTimePlaying = if (binding.player.isPlayingVipTrailer()) getRealTimePlayingForLog() else userRealtimePlayingTracker.getRealtimePlaying().toString(),
                multicast = livetvPlayerInfo.stream?.url ?: "",
                videoQuality = getBitrateIdForTracking(isTimeshift = viewModel.isPlayingTimeshift()),
                groupChannel = TrackingUtil.blockId,
                businessPlan = getCurrentChannelDetail()?.vipPlan ?: "",
                key = TrackingUtil.key.rawValue,
                isLinkDRM = isLinkDRM(),
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),
                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getExtraForUrlMode(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY)
                    ?: "",
                subMenuId = subMenuIdWhenStartPlay,
                status = if (binding.player.getPlayerConfig().isPlayPreview) "Preview" else "None",
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.position,
                isRecommend = TrackingUtil.isRecommend
            )
        )
        TrackingUtil.setTrackingKey(TrackingUtil.TrackingKey.CHANNEL_LIST)
        TrackingUtil.resetIsRecommend() //reset when stop
    }

    private fun sendTrackingStartTimeShift() {
        isPlayTimeShift = true
        if(pairingConnection.isConnected) return
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "43",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "BroadcastSchedule",
                event = "StartTimeshift",
                itemId = getCurrentChannelDetail()?.id ?: "",
                itemName = getCurrentChannelDetail()?.name ?: "",
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                blocKPosition = TrackingUtil.blockIndex,
                multicast = livetvPlayerInfo.stream?.url ?: "",
                videoQuality = getBitrateIdForTracking(isTimeshift = viewModel.isPlayingTimeshift()),
                groupChannel = TrackingUtil.blockId,
                businessPlan = getCurrentChannelDetail()?.vipPlan ?: "",
                isLinkDRM = getCurrentChannelDetail()?.isVerimatrix?.toString() ?:"",
                refItemId = TrackingUtil.contentPlayingInfo.refId,

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getExtraForUrlMode(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                subMenuId = TrackingUtil.blockId,
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.position
            )
        )
        sendTracking111()
        subMenuIdWhenStartPlay = TrackingUtil.blockId
    }

    private fun isLinkDRM(): String {
        return if(getCurrentChannelDetail()?.isVerimatrix == true) "1" else "0"
    }

    private fun sendTrackingStopTimeShift() {
        if(pairingConnection.isConnected) return
        sendTracking111(stop = true)
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "44",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "BroadcastSchedule",
                event = "StopTimeshift",
                itemId = getCurrentChannelDetail()?.id ?: "",
                itemName = getCurrentChannelDetail()?.name ?: "",
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                blocKPosition = TrackingUtil.blockIndex,
                realTimePlaying = userRealtimePlayingTracker.getRealtimePlaying().toString(),
                multicast = livetvPlayerInfo.stream?.url ?: "",
                videoQuality = getBitrateIdForTracking(isTimeshift = viewModel.isPlayingTimeshift()),
                groupChannel = TrackingUtil.blockId,
                businessPlan = getCurrentChannelDetail()?.vipPlan ?: "",
                isLinkDRM = getCurrentChannelDetail()?.isVerimatrix?.toString() ?:"",
                refItemId = TrackingUtil.contentPlayingInfo.refId,

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getExtraForUrlMode(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                subMenuId = subMenuIdWhenStartPlay,
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.position
            )
        )
    }


    private fun sendTrackingChangeVideoQuality(bitrateId: String) {
        if(pairingConnection.isConnected) return
        AdjustAllEvent.sendQualityChangeEvent(getBitrateIdForTracking(isTimeshift = viewModel.isPlayingTimeshift()))
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "416",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "ChangeVideoQuality",
                event = "ChangeVideoQuality",
                itemId = getCurrentChannelDetail()?.id ?: "",
                itemName = getBitrateIdForTracking(isTimeshift = viewModel.isPlayingTimeshift()),
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                blocKPosition = TrackingUtil.blockIndex,
                multicast = livetvPlayerInfo.stream?.url ?: "",
                videoQuality = getBitrateNameForTracking(isTimeshift = viewModel.isPlayingTimeshift(), bitrateId = bitrateId, tvSchedule = viewModel.playSchedule.value?.first),
                groupChannel = TrackingUtil.blockId,
                businessPlan = getCurrentChannelDetail()?.vipPlan ?: "",
                isLinkDRM = getCurrentChannelDetail()?.isVerimatrix?.toString() ?:"",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getExtraForUrlMode(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                subMenuId = TrackingUtil.blockId,
                status = if (binding.player.getPlayerConfig().isPlayPreview) "Preview" else "None",
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.position,
                isRecommend = TrackingUtil.isRecommend
            )
        )
    }

    private fun sendTrackingChangeSubtitlesAudio(value: String, isChangeAudio: Boolean) {
        if(pairingConnection.isConnected) return
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "518",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = TrackingUtil.screen,
                event = if (isChangeAudio) "ChangeAudio" else "ChangeSubtitles",
                itemId = getCurrentChannelDetail()?.id ?: "",
                itemName = value,
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                blocKPosition = TrackingUtil.blockIndex,
                multicast = livetvPlayerInfo.stream?.url ?: "",
                groupChannel = TrackingUtil.blockId,
                businessPlan = getCurrentChannelDetail()?.vipPlan ?: "",
                isLinkDRM = getCurrentChannelDetail()?.isVerimatrix?.toString() ?:"",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getExtraForUrlMode(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                subMenuId = TrackingUtil.blockId,
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.position,
                isRecommend = TrackingUtil.isRecommend
            )
        )
    }

    private fun sendTrackingReceiveShowIp(event: String, errorMessage: String, typeId: String, message: String) {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "26",
                appId = "DIAL",
                appName = "FingerPrint",
                screen = "Announcement",
                errorMessage = errorMessage,
                event = event,
                itemId = typeId,
                itemName = message,
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
            )
        )
    }

    private fun sendTrackingShowIpSuccess(event: String, errorMessage: String, typeId: String, message: String) {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "29",
                appId = TrackingUtil.currentAppId,
                appName = "FingerPrint",
                screen = "Announcement",
                errorMessage = errorMessage,
                event = event,
                itemId = typeId,
                itemName = message,
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
            )
        )
    }
    //endregion

    //region Adjust -> Player Layout
    private fun adjustPlayerLayout(isScale: Boolean) {
        if (MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            if (isScale) {
                view?.run {
                    val lp = ConstraintLayout.LayoutParams(
                        0,
                        0
                    )
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.matchConstraintPercentWidth = Constants.PLAYER_SCALED_RATIO
                    layoutParams = lp
                }
            } else {
                view?.run {
                    val lp = ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.MATCH_PARENT, ConstraintLayout.LayoutParams.MATCH_PARENT)
                    layoutParams = lp
                }
            }
        }
    }

    private fun adjustPlayerLayoutTablet(isScale: Boolean) {
        if (isScale) {
            if (viewModel.isFullScreen.value?.first == true && viewModel.isFullScreen.value?.second == true) {
                view?.run {
                    val lp = ConstraintLayout.LayoutParams(0, 0)
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.matchConstraintPercentWidth = Constants.PLAYER_SCALED_RATIO
                    layoutParams = lp
                }
            }
        } else {
            view?.run {
                if (viewModel.isFullScreen.value?.second == true) { // landscape
                    if (viewModel.isFullScreen.value?.first == true) { // fullscreen
                        layoutParams = ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.MATCH_PARENT, ConstraintLayout.LayoutParams.MATCH_PARENT)
                    } else {
                        tabletPlayerLayout()
                    }
                } else { // portrait
                    if (viewModel.isFullScreen.value?.first == true) {
                        layoutParams = ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.MATCH_PARENT, ConstraintLayout.LayoutParams.MATCH_PARENT)
                    } else {
                        resetPlayerLayout()
                    }
                }
            }
        }
    }
    //endregion


    // region Pairing Control
    private fun safeUpdateUI(block: () -> Unit = {}) {
        _binding?.let {
            block()
        }
    }
    /**
     * type: 0 -> Device Bottom Sheet
     * 1 -> Pairing with pin code
     * 2 -> Device Management
     * 3 -> Popup Remote Control
     */
    private fun navigateToPairingControl(type: Int) {
        val navController = (activity as? HomeActivity)?.navHostFragment?.navController
        val navigation = if(navController?.graph?.id == R.id.nav_home_main) { navController } else { null }
        navigation?.navigateSafe(NavHomeMainDirections.actionGlobalToPairingControl(type = type))
    }

    private fun sendPlayContentMessage() {
        if (livetvPlayerInfo.playScheduleStream) {
            safeUpdateUI {
//                pairingConnection.setSessionRunning(isRunning = false)
                binding.player.updateIsSupportCast(isSupport = false)
            }
            MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                navHostFragment = activity?.findNavHostFragment(),
                onStopCastAndNavigate = {
                    MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                }
            )
            return
        }
        pairingConnection.getCastingItemMapped()?.let {
            val id = getCurrentChannelDetail()?.id
            if (id == it.id) {
                when (pairingConnection.getCastingItemState()) {
                    CastingItemState.CAN_CAST -> {
                        if (pairingConnection.isSessionRunning) {
                            return
                        }
                    }
                    CastingItemState.NOT_SUPPORT -> {
                        safeUpdateUI {
//                            pairingConnection.setSessionRunning(isRunning = false)
                            binding.player.updateIsSupportCast(isSupport = false)
                        }
                        MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                            navHostFragment = activity?.findNavHostFragment(),
                            onStopCastAndNavigate = {
                                MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                            }
                        )
                        return
                    }
                    else -> { }
                }
            }
        }
        if (viewModel.getVipRequired()?.first == false) {
            safeUpdateUI { binding.player.updateIsSupportCast(isSupport = true) }
            getCurrentChannelDetail()?.let { tvChannel ->
                pairingConnection.sendEventPlayContent(
                    id = tvChannel.id,
                    highlightId = tvChannel.id,
                    refId = tvChannel.refId,
                    appId = "",
                    title = tvChannel.name,
                    image = tvChannel.thumb,
                    indexChapter = "",
                    isPlay = "1",
                    isLive = "1",
                    timeWatched = "0",
                    type = "livetv",
                    deepLink = tvChannel.websiteUrl,
                    token = sharedPreferences.accessToken(),
                    userId = sharedPreferences.userId(),
                    userPhone = sharedPreferences.userPhone(),
                    username = sharedPreferences.displayName(),
                    autoProfiles = kotlin.run {
                        try {
                            listOf(tvChannel.autoProfile)
                        } catch (e: Exception) {
                            listOf()
                        }
                    },
                    currentAutoProfile = tvChannel.autoProfile
                )
            }

        } else {
            safeUpdateUI {
//                pairingConnection.setSessionRunning(isRunning = false)
                binding.player.updateIsSupportCast(isSupport = false)
            }
        }
    }

    private fun addPairingControlListener() {
        pairingConnection.apply {
            addConnectionListener(connectionListener)
            addCommunicationListener(communicationListener)
        }
    }

    private fun removePairingControlListener() {
        pairingConnection.apply {
            removeConnectionListener(connectionListener)
            removeCommunicationListener(communicationListener)
        }
    }


    private val connectionListener = object : FConnectionManager.FConnectionListener {
        override fun onConnectError(errorCode: Int, message: String) {
            removeIntervalUpdateProgress()
            if (errorCode != 2) {
                if (livetvPlayerInfo.playScheduleStream) {
                    getScheduleStream(isRetry = false)
                } else {
                    getChannelStream(isRetry = false)
                }
            }
            runOnUiThread {
                binding.player.onShowThumb(isShow = false)
                pairingConnection.showToast(message = if (errorCode == 2) binding.root.context.getString(R.string.pairing_cast_description_connect_error_disable_cast, pairingConnection.getReceiverName().truncateString()) else binding.root.context.getString(R.string.pairing_cast_title_connect_error))

                //
                triggerStartAds()
                //
            }
        }

        override fun onConnectSuccess(message: String) {
            runOnUiThread {
                pairingConnection.showToast(message = binding.root.context.getString(R.string.pairing_cast_title_connect_success, pairingConnection.getReceiverName()))
                sendPlayContentMessage()
                binding.player.run {
                    stopServices()
                    stopPlayerLocal(force = true, isClearRequest = true)
                    onShowThumb(isShow = true, url = getCurrentChannelDetail()?.thumb ?: "")
                }

                //
                triggerStopAds()
                //
            }

            // Send tracking
            sendTracking(logId = "516", event = "CastToDevice")
        }

        override fun onDisconnectError(message: String) {
        }

        override fun onDisconnectSuccess(message: String) {
            runOnUiThread {
                removeIntervalUpdateProgress()
                pairingConnection.showToast(message = binding.root.context.getString(R.string.pairing_cast_title_disconnect_success, pairingConnection.getReceiverName()))
                if (viewModel.canPreview) {
                    Logger.d("$TAG trigger play preview: $livetvPlayerInfo")
                    viewModel.triggerPreparePlayPreview(liveTvPreviewPlayerInfo)
                } else {
                    if (livetvPlayerInfo.playScheduleStream) {
                        getScheduleStream(isRetry = false)
                    } else {
                        // Tracking
                        //sendTrackingStartIptv()
                        getChannelStream(isRetry = false)
                    }
                }
                binding.player.run {
                    onShowThumb(isShow = false)
                }

                //
                triggerStartAds()
                //
            }
        }
    }


    private val communicationListener = object : FConnectionManager.FCommunicationListener {
        override fun onMessage(message: ObjectReceiver) {
            runOnUiThread {
                when (message) {
                    is FBoxObjectReceiver -> {
                        when (message.data) {
                            is ReceivePlayContent -> {
                                (message.data as? ReceivePlayContent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            startIntervalUpdateProgress()
                                        }
                                    }
                                    if (result.isFailure() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            if (result == "-1") { // Box Iptv (ref_id=-1) => Not stop session, continue play current content (if exist)
                                                MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                                    navHostFragment = activity?.findNavHostFragment(),
                                                    message = binding.root.context.getString(R.string.pairing_control_content_receiver_not_support_message_with_error_code, "#CP001"),
                                                    onCancel = {
                                                        findNavController().popBackStack()
                                                    },
                                                    onStopCastAndNavigate = {
                                                        MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                                    },
                                                    allowCancelByOutside = false
                                                )
                                            } else {
                                                safeUpdateUI {
                                                    pairingConnection.setSessionRunning(isRunning = false)
                                                    binding.player.updateIsSupportCast(isSupport = false)
                                                }
                                                MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                                    navHostFragment = activity?.findNavHostFragment(),
                                                    message = binding.root.context.getString(R.string.pairing_control_content_receiver_not_support_message),
                                                    onStopCastAndNavigate = {
                                                        MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                                    }
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveSetTrack -> {
                                (message.data as? ReceiveSetTrack)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage() && isMyContentId(data?.id)) {
                                        if (isSessionRunning()) {
                                            getCurrentChannelDetail()?.let {
                                                pairingConnection.sendEventGetStatusWatching(id = it.id, refId = it.refId)
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveStopSession -> {
                                (message.data as? ReceiveStopSession)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (!isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                            if (shouldShowStopSessionToast()) {
                                                runOnUiThread {
                                                    pairingConnection.showToast(binding.root.context.getString(R.string.pairing_cast_title_player_stop_toast, pairingConnection.getReceiverName()))
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveExceptionEvent -> {
                                (message.data as? ReceiveExceptionEvent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                        }
                                    }
                                }
                            }
                        }
                    }
                    is FSamsungObjectReceiver,
                    is FSamsungObjectReceiverExternal -> {
                        when (message.data) {
                            is ReceivePlayContent -> {
                                (message.data as? ReceivePlayContent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            startIntervalUpdateProgress()
                                        }
                                    }
                                    if (result.isFailure() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                                navHostFragment = activity?.findNavHostFragment(),
                                                message = binding.root.context.getString(R.string.pairing_control_content_receiver_not_support_message),
                                                onStopCastAndNavigate = {
                                                    MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                                }
                                            )
                                        }
                                    }
                                }
                            }
                            is ReceiveStopSession -> {
                                (message.data as? ReceiveStopSession)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (!isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                            if (shouldShowStopSessionToast()) {
                                                runOnUiThread {
                                                    pairingConnection.showToast(binding.root.context.getString(R.string.pairing_cast_title_player_stop_toast, pairingConnection.getReceiverName()))
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveExceptionEvent -> {
                                (message.data as? ReceiveExceptionEvent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                        }
                                    }
                                }
                            }
                        }
                    }
                    is FAndroidTVObjectReceiver -> {
                        when (message.data) {
                            is FAndroidTVCustomMessageResponse -> {
                                (message.data as? FAndroidTVCustomMessageResponse)?.let {
                                    if (!isSessionRunning()) {
                                        removeIntervalUpdateProgress()
                                        if (shouldShowStopSessionToast()) {
                                            runOnUiThread {
                                                pairingConnection.showToast(binding.root.context.getString(R.string.pairing_cast_title_player_stop_toast, pairingConnection.getReceiverName()))
                                            }
                                        }
                                    }
                                }
                            }
                            is FAndroidTVCustomPlayContentResponse -> {
                                (message.data as? FAndroidTVCustomPlayContentResponse)?.run {
                                    if (result.isFailure()) {
                                        if (isSessionRunning()) {
                                            safeUpdateUI {
                                                pairingConnection.setSessionRunning(isRunning = false)
                                                binding.player.updateIsSupportCast(isSupport = false)
                                            }
                                            MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                                navHostFragment = activity?.findNavHostFragment(),
                                                message = binding.root.context.getString(R.string.pairing_control_content_receiver_not_support_message),
                                                onStopCastAndNavigate = {
                                                    MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                                }
                                            )
                                        }
                                    }
                                }
                            }
                            is FAndroidTVCustomStopSessionResponse -> {
                                (message.data as? FAndroidTVCustomStopSessionResponse)?.run {
                                    if (result.isSuccess()) {
                                        if (!isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                            if (shouldShowStopSessionToast()) {
                                                runOnUiThread {
                                                    pairingConnection.showToast(binding.root.context.getString(R.string.pairing_cast_title_player_stop_toast, pairingConnection.getReceiverName()))
                                                }
                                            }
                                        }
                                        // Tracking
                                        //sendTracking(logId = "52", event = "StopMovie")
                                    }
                                }
                            }
                        }
                    }
                    is FAndroidTVObjectReceiverExternal -> {
                        when (message.data) {
                            is ReceivePlayContent -> {
                                (message.data as? ReceivePlayContent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            startIntervalUpdateProgress()
                                        }
                                    }
                                    if (result.isFailure() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                                navHostFragment = activity?.findNavHostFragment(),
                                                message = binding.root.context.getString(R.string.pairing_control_content_receiver_not_support_message),
                                                onStopCastAndNavigate = {
                                                    MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                                }
                                            )
                                        }
                                    }
                                }
                            }
                            is ReceiveStopSession -> {
                                (message.data as? ReceiveStopSession)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (!isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                            if (shouldShowStopSessionToast()) {
                                                runOnUiThread {
                                                    pairingConnection.showToast(binding.root.context.getString(R.string.pairing_cast_title_player_stop_toast, pairingConnection.getReceiverName()))
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            is ReceiveExceptionEvent -> {
                                (message.data as? ReceiveExceptionEvent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                        }
                                    }
                                }
                            }
                            else -> {}
                        }
                    }
                    else -> {}
                }
            }
        }

        override fun onSendMessageCallback(
            messageType: Int,
            isSuccess: Boolean,
            errorCode: Int,
            errorMessage: String
        ) {

        }
    }

    //region Handle progress countdown
    private var numGetTrackRequest = 0
    private var maxTrackRequest = 3


    private var handlerProgress: Handler? = null
    private var runnableProgress = Runnable {
        intervalUpdateProgress()
    }

    private fun startIntervalUpdateProgress() {
        numGetTrackRequest = 0
        removeIntervalUpdateProgress()
        if (handlerProgress == null) {
            Looper.getMainLooper()?.run {handlerProgress = Handler(this) }
        }
        handlerProgress?.post(runnableProgress)
    }
    private fun intervalUpdateProgress() {
        handlerProgress?.run {
            pairingConnection.getRemoteData()?.let {
                if (it.remotePlayer.tracks == null
                    && numGetTrackRequest < maxTrackRequest
                    && (it.remotePlayer.currentDuration % (5 * 1000L) == 0L))
                {
                    numGetTrackRequest += 1
                    triggerGetStatusWatching()
                    postDelayed(runnableProgress, 1000)
                }
            } ?: kotlin.run {
                pairingConnection.sendEventGetBoxInfo()
            }
        }
    }

    private fun removeIntervalUpdateProgress() {
        handlerProgress?.removeCallbacks(runnableProgress)
        handlerProgress = null
    }

    private fun triggerGetStatusWatching() {
        getCurrentChannelDetail()?.let {
            pairingConnection.sendEventGetStatusWatching(id = it.id, refId = it.refId)
        }
    }
    //endregion

    private fun isMyContentId(remoteId: String?): Boolean {
        if (remoteId == null) return false
        return remoteId == getCurrentChannelDetail()?.id
    }

    /**
     * Trigger stop all ads when cast connect
     */
    private fun triggerStopAds() {
        stopTvcAds()
    }

    /**
     * Trigger startads when cast connect
     */
    private fun triggerStartAds() {
        // Don't need do anything.
    }

    // endregion Pairing Control

    //region Report Player
    private fun startToReportPlayer() {
        val playerReportDetails =
            PlayerReportUtils.ReportPlayerLiveTV.mappingDataReportDetailTV(
                tvChannelDetail = getCurrentChannelDetail(),
                isTimeShift = viewModel.isPlayingTimeshift(),
                tvScheduleDetail = viewModel.playSchedule.value?.first
            )
        parentFragment?.findNavController()
            ?.navigateSafe(directions = LiveTVDetailFragmentDirections.actionLiveTvListChannelFragmentToReportPlayerDialogFragment(),
                extendArgs = Bundle().apply {
                    putSerializable(
                        Constants.PLAYER_REPORT_DETAIL_KEY,
                        playerReportDetails
                    )
                })
    }

    private fun isShownButtonReport(): Boolean {
        return if (viewModel.isPlayingTimeshift()) viewModel.playSchedule.value?.first?.enableReport
            ?: false
        else getCurrentChannelDetail()?.enableReport ?: false
    }

    private fun navigateToReportPlayerVod() {
        when (PlayerUtils.getPlayingType()) {
            PlayerView.PlayingType.Local -> {
                if (findNavController().currentDestination?.id == R.id.livetv_detail_fragment) {
                    if (NetworkUtils.isNetworkAvailable()) {
                        startToReportPlayer()
                    } else {
                        viewModel.dispatchIntent(
                            TriggerShowMsgUserReport(
                                isReported = false,
                                message = <EMAIL>(R.string.report_vod_error_no_internet_message)
                            )
                        )
                    }
                }
            }
            PlayerView.PlayingType.Cast -> {}
        }
    }
    //endregion


    private fun getRealTimePlayingForLog(): String{
        val extraId = if (viewModel.isPlayingTimeshift()) { viewModel.playSchedule.value?.first?.id ?: "-1" } else { if (binding.player.isPlayingVipTrailer()) "-2" else "-1" }
        return userRealtimePlayingTracker.getRealTimePlaying(contentId = viewModel.getChannelId(), extraId = extraId).toString()
    }

    //region Handle User Realtime Playing
    private fun setupUserRealtimePlayingTracker() {
        if (isCalculateUserRealtimePlaying()) {
            val extraId = if (viewModel.isPlayingTimeshift()) { viewModel.playSchedule.value?.first?.id ?: "-1" } else { "-1" }
            userRealtimePlayingTracker.setData(contentId = viewModel.getChannelId(), extraId = extraId)
        }
    }
    private fun isCalculateUserRealtimePlaying(): Boolean {
        return viewModel.getVipRequired()?.first == false || _binding?.player?.getPlayerConfig()?.isPlayPreview ?: false || isPlayingVipTrailerInEnablePreview()
    }
    //endregion


    //region Handle Player Error
    private fun setupPlayerRetryHandler(channelId: String, streamId: String) {
        playerRetryHandler.setupData(channelId = channelId, streamId = streamId)
    }

    private fun setPlayerRetryHandlerListener() {
        playerRetryHandler.setListener(listener = playerRetryHandlerListener)
    }

    private fun removePlayerRetryHandlerListener() {
        playerRetryHandler.removeListener()
    }

    private val playerRetryHandlerListener = object : PlayerRetryHandler.PlayerRetryHandlerListener {
        override fun getRealtimePlayingMs(): Long {
            return userRealtimePlayingTracker.getRealtimePlaying() * 1000L
        }

        override fun onRetryGetStream() {
            tryRetry()
        }

        override fun onShowPlayerError(shouldCountDown: Boolean, countdownTimeMs: Long, code: Int, responseCode: Int) {
            showPlayerError(shouldCountDown = shouldCountDown, countdownTimeMs = countdownTimeMs, code = code, responseCode = responseCode)
        }

        override fun onSendTrackingPlayerError(
            isAutoRetry: Boolean,
            screen: String,
            errorCode: String,
            errorMessage: String
        ) {
            sendTrackingPlayerError(isAutoRetry = isAutoRetry, screen = screen, errorCode = errorCode, errorMessage = errorMessage)
        }

        override fun onClearAudioUserHistory() {
            sharedPreferences.saveAudioLive("")
        }
    }

    private fun showPlayerError(shouldCountDown: Boolean, countdownTimeMs: Long, code: Int, responseCode: Int) {
        if (activity.isInPiPMode()) {
            playerPiPRetryHandler.startRetryFlow(
                processData = PlayerPiPRetryHandler.PlayerRetryData(
                    shouldCountDown = shouldCountDown,
                    countdownTimeMs = countdownTimeMs,
                    code = code,
                    responseCode = responseCode
                ),
                onCompleted = {
                    tryRetry()
                }
            )
        } else {
            if (shouldCountDown) {
                showPlayerErrorWithRetryDialog(message = getErrorMessage(code, responseCode))
                stopCountDownTimerRetry()
                startCountDownTimerRetry(timeCountDown = countdownTimeMs / 1000L)
            } else {
                showPlayerErrorDialog(message = getErrorMessage(code, responseCode))
            }
        }
    }

    //region Retry Task
    private fun tryRetry(delay: Long = 0L) {
        binding.player.stop(force = true)
        if (viewModel.isPlayingTimeshift()) {
            getScheduleStream(delay = delay, isRetry = true)
        } else {
            getChannelStream(delay = delay, isRetry = true)
        }
    }

    private fun getErrorMessage(code: Int, responseCode: Int): String {
        val message =
            MainApplication.INSTANCE.appConfig.msgPlayerError.ifBlank {
                <EMAIL>().getString(R.string.msg_player_error)
            }
        return if(responseCode != UnValidResponseCode) {
            "$message (Mã lỗi ${code}-${responseCode})"

        } else {
            "$message (Mã lỗi $code)"
        }
    }

    private fun startCountDownTimerRetry(timeCountDown: Long) {
        if (countDownTimerRetry == null) {
            countDownTimerRetry = object : CountDownTimer(timeCountDown * 1000 + 1000, 1000) {
                override fun onTick(millis: Long) {
                    if (playerRetryDialog?.isShow() == true) {
                        playerRetryDialog?.updateTextConfirm("${<EMAIL>(R.string.all_retry)} (${millis / 1000})")
                    }
                }

                override fun onFinish() {
                    stopCountDownTimerRetry()
                    playerRetryDialog?.dismissAllowingStateLoss()
                    tryRetry()
                }
            }
            countDownTimerRetry?.start()
        }
    }

    private fun stopCountDownTimerRetry() {
        if (countDownTimerRetry != null) {
            countDownTimerRetry?.cancel()
            countDownTimerRetry = null
        }
    }
    //endregion

    //region Tracking player retry
    private fun sendTrackingPlayerError(
        isAutoRetry: Boolean,
        screen: String,
        errorCode: String,
        errorMessage: String
    ) {
        sendTracking(
            logId = TrackingConstants.EVENT_LOG_ID_ERROR_STREAM_VOD,
            screen = screen,
            event = "PlaybackError",
            errorCode = errorCode,
            errorMessage = errorMessage
        )
        if (!isAutoRetry) {
            sendTrackingShowPopupRetry(screen, errorCode, errorMessage)
        }
    }

    private fun sendTrackingShowPopupRetry(
        screen: String,
        errorCode: String,
        errorMessage: String
    ) {
        sendTracking(
            logId = "17",
            screen = screen,
            event = "Error",
            errorCode = errorCode,
            errorMessage = errorMessage,
            issueId = TrackingUtil.createIssueId()
        )
    }
    //endregion

    //endregion

    //region Handle Get Stream Error
    private fun setPlayerGetStreamRetryListener() {
        playerGetStreamRetryHandler.setListener(listener = playerGetStreamRetryListener)
    }

    private fun removePlayerGetStreamRetryListener() {
        playerGetStreamRetryHandler.setListener(listener = null)
    }

    private fun resetGetStreamRetryStep() {
        playerGetStreamRetryHandler.resetGetStreamRetryStep()
    }

    private fun notifyGetStreamError(errorMessage: String) {
        playerGetStreamRetryHandler.notifyGetStreamError(errorMessage = errorMessage)
    }

    private val playerGetStreamRetryListener = object : PlayerGetStreamRetryHandler.PlayerGetStreamRetryListener {
        override fun onRetryGetStream() {
            tryRetry(delay = PlayerGetStreamRetryHandler.FIRST_TIME_DELAY_TIMES_MS)
        }

        override fun onShowGetStreamError(
            shouldCountDown: Boolean,
            countdownTimeMs: Long,
            errorMessage: String
        ) {
            if (activity.isInPiPMode()) {
                playerPiPRetryHandler.startRetryFlow(
                    processData = PlayerPiPRetryHandler.PlayerGetStreamRetryData(
                        shouldCountDown = shouldCountDown,
                        countdownTimeMs = countdownTimeMs,
                        errorMessage = errorMessage
                    ),
                    onCompleted = {
                        tryRetry()
                    }
                )
            } else {
                if (shouldCountDown) {
                    showPlayerErrorWithRetryDialog(message = errorMessage)
                    stopCountDownTimerRetry()
                    startCountDownTimerRetry(timeCountDown = countdownTimeMs / 1000L)
                } else {
                    showPlayerErrorDialog(message = errorMessage)
                }
            }
        }

        override fun onSendTrackingGetStreamError(
            isAutoRetry: Boolean,
            screen: String,
            errorMessage: String
        ) {
            sendTracking(
                logId = "17",
                screen = screen,
                event = "Error",
                errorCode = AppErrorConstants.GET_LIVE_STREAM_CODE,
                errorMessage = errorMessage,
                issueId = TrackingUtil.createIssueId(),
            )
        }

    }

    //endregion

    //region preview
    private fun handleRequiredVip(requiredVip: RequiredVip?) {
//                if (enablePreview) {
//                    if (available > 0) {
//                        if (urlH265.isNotEmpty() || url.isNotEmpty()) {
//                            // Play luồng preview
//                        } else {
//                            // Play luồng mua gói của preview data.urlTrailer (urlTrailer)
//                        }
//                    } else {
//                        // Play luồng mua gói của preview data.urlTrailer (urlTrailer)
//                    }
//                } else {
//                    / / Play luồng mua gói cũ (urlTrailer)
//                }
        Logger.d("$TAG handleRequiredVip")
        requiredVip?.let {
            if (viewModel.canPreview) {
                saveLivePreviewInfo(it)
                playPreview()

                //MQTT
                publishStartToTopic(contentType = MqttContentType.PreviewChannel, requiredVip.pingMqtt)

                return
            }
            if (!viewModel.hasPreview) {
                playTrailer(it.trailerUrl, pingStart = it.pingMqtt)
                parentFragment?.parentFragment?.navigateToRequiredBuyPackage(
                    title = it.requireVipTitle,
                    message = it.requireVipDescription,
                    titleNegative = it.btnSkip,
                    titlePosition = it.btnActive,
                    packageId = it.requireVipPlan,
                    continueWatch = true
                )
                return
            }
            playTrailer(it.livePreviewInfo?.trailerUrl, pingStart =  it.pingMqtt)
            requiredBuyPackageForPreview()
        }
    }

    private fun saveLivePreviewInfo(requiredVip: RequiredVip) {
        val ttlPreview = requiredVip.livePreviewInfo?.ttlPreviewInSecond ?: -1
        liveTvPreviewPlayerInfo.apply {
            playScheduleStream = false
            url = requiredVip.livePreviewInfo?.url ?: ""
            urlH265 = ""
            drmKey = null
            requiredVipTitle = requiredVip.livePreviewInfo?.previewTitle ?: ""
            requiredVipDescription = requiredVip.livePreviewInfo?.previewMessage ?: ""
            available = requiredVip.livePreviewInfo?.available ?: false
            merchant = requiredVip.livePreviewInfo?.merchant ?: ""
            streamSession = requiredVip.livePreviewInfo?.streamSession ?: ""
            session = requiredVip.livePreviewInfo?.session?: ""
            pingSession = requiredVip.livePreviewInfo?.pingSession?: ""
            pingEnable = requiredVip.livePreviewInfo?.pingEnable?: false
            pingEnc = requiredVip.livePreviewInfo?.pingEnc?: false
            pingQnet = requiredVip.livePreviewInfo?.pingQnet?: 0
            overlayLogo = requiredVip.livePreviewInfo?.overlayLogo?: ""
            ttlPreviewInSecond = if (ttlPreview != 0) ttlPreview else PreviewTimer.DEFAULT_PREVIEW_END_TIME
        }
    }

    private fun playTrailer(trailerUrl: String? = "", pingStart: Boolean = false) {
        Logger.d("$TAG handlePlayTrailer: $trailerUrl")
        if (!trailerUrl.isNullOrBlank()) {
            viewModel.triggerPreparePlayTrailer(trailerUrl)
        }

        //MQTT
        publishStartToTopic(contentType = MqttContentType.Trailer, pingStart = pingStart)
    }

    private fun requiredBuyPackageForPreview() {
        val requiredBuyPackageTitle = getRequiredBuyPackageTitle()
        val requiredBuyPackageMessage = getRequiredBuyPackageMessage()
        previewEndTimeDialog?.dismissAllowingStateLoss()
        previewEndTimeDialog = AlertDialog().apply {
            setShowTitle(true)
            setTextTitle(
                requiredBuyPackageTitle
            )
            setMessage(
                requiredBuyPackageMessage
            )
            setTextConfirm(BillingUtils.getTextBuyPackageChannel(binding.root.context))
            setTextExit(<EMAIL>(R.string.close))
            setOnlyConfirmButton(false)
            setListener(object : AlertDialogListener {
                override fun onExit() {
                    previewEndTimeDialog?.dismissAllowingStateLoss()
                }
                override fun onConfirm() {
                    handleBuyPackageForPreview()
                    previewEndTimeDialog?.dismissAllowingStateLoss()
                }
            })
            isCancelable = false
        }
        previewEndTimeDialog?.show(childFragmentManager, "PreviewEndTimeDialog")
        sendTrackingShownPopup(requiredBuyPackageMessage)
        sendTrackingRequestPackage()
    }

    private fun getRequiredBuyPackageTitle(): String {
        val requiredBuyPackageTitle = viewModel.getVipRequired()?.second?.livePreviewInfo?.previewTitle ?: ""
        return requiredBuyPackageTitle.ifBlank { <EMAIL>(R.string.title_buy_package_for_live_preview) }
    }

    private fun getRequiredBuyPackageMessage(): String {
        val requiredBuyPackageMessage = viewModel.getVipRequired()?.second?.livePreviewInfo?.previewMessage ?: ""
        return requiredBuyPackageMessage.ifBlank { <EMAIL>(R.string.message_buy_package_for_live_preview) }
    }

    private fun handleBuyPackageForPreview() {
        TrackingUtil.savePreviewProcessItem(vodId = viewModel.getChannelId(), episodeId = "")
        val requireVipPlan = viewModel.getVipRequired()?.second?.livePreviewInfo?.requiredVipPlan ?: ""
        parentFragment?.parentFragment?.findNavController()?.navigateSafe(
            NavHomeMainDirections.actionGlobalToPayment(
                idToPlay = viewModel.getChannelId(),
                extraId = "",
                bitrateId = getCurrentChannelDetail()?.autoProfile ?:"",
                type = "livetv",
                viewType = BillingUtils.PACKAGE_VIEW_TYPE_PREVIEW,
                packageType = requireVipPlan,
                popupToId = R.id.nav_tv_detail
            )
        )
        AdjustAllEvent.sendPackageRecommendClickEvent(packageId = requireVipPlan, packageName = viewModel.getVipRequired()?.second?.requireVipName?: "")
        PaymentTrackingUtil.accessViewListPackagePayment(trackingProxy, trackingInfo)
    }

    private fun playPreview() {
        Logger.d("$TAG handlePlayPreview")

        binding.player.playerData.refreshLiveState(isLive = true)

        binding.player.onShowThumb(isShow = false)
        binding.player.stop(force = true)
        playPrerollAd()
    }

    private fun setupDataForPreviewTimer() {
        previewTimer.setData(liveTvPreviewPlayerInfo.ttlPreviewInSecond)
    }

    private val previewTimerListener = object: PreviewTimer.PreviewTimerListener {
        override fun onEndPreviewTime() {
            handleEndPreviewOnError()
        }
    }

    //endregion preview

    //MQTT
    private fun publishStartToTopic(contentType: MqttContentType = MqttContentType.LiveTV, pingStart: Boolean = false, drmPartner: String = "", mqttMode: Int = DEFAULT_MODE) {
        if (mqttPublisher != null) {
            publishToTopic(MqttUtil.ACTION_END)
        }
        if (pingStart) {
            publishToTopic(MqttUtil.ACTION_START, contentType, drmPartner, mqttMode)
        }
    }
    private fun publishEndToTopic() {
        publishToTopic(MqttUtil.ACTION_END)
    }

    private fun publishToTopic(action: String, contentType: MqttContentType = MqttContentType.LiveTV, drmPartner: String = "", mqttMode: Int = DEFAULT_MODE) {
        if (action == MqttUtil.ACTION_END) {
            mqttPublisher?.let { publisher ->
                MqttConnectManager.INSTANCE.publishToTopic(
                    publisher = publisher.copy(action = action, createdTime = System.currentTimeMillis()),
                    type = publisher.itemType.id,
                    typeId = publisher.itemId
                )
                mqttPublisher = null
            } ?: kotlin.run { return }
        } else if (action == MqttUtil.ACTION_START) {
            mqttPublisher = Publisher(
                action = action,
                createdTime = System.currentTimeMillis(),
                isRetry = 0,
                uid = sharedPreferences.userId(),
                contract = "",
                netMode = NetworkUtils.getNetworkMode(),
                appVer = BuildConfig.VERSION_NAME,
                profileId = sharedPreferences.profileId(),
                contentType = contentType.value.toString(),
                playlistId = "",
                chapterId = "",
                episodeId ="",
                itemId = viewModel.getChannelId(),
                refPlaylistId ="",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refEpisodeId = TrackingUtil.contentPlayingInfo.refEpisodeId,
                appSource = getCurrentChannelDetail()?.appId ?: "",
                isLinkDrm = isLinkDRM(),
                mode = mqttMode.toString(),
                sourceProvider = getCurrentChannelDetail()?.sourceProvider ?: "",
                drmPartner = drmPartner,
                businessPlan = getCurrentChannelDetail()?.vipPlan ?: "",
                isFree = "",
            ).apply {
                itemType = ItemType.LiveTV
                MqttConnectManager.INSTANCE.publishToTopic(
                    publisher = this,
                    type = itemType.id,
                    typeId = itemId
                )
            }
        }
    }

}