package com.fptplay.mobile.features.survey

import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.common.SendSurvey
import com.xhbadxx.projects.module.domain.entity.fplay.common.SurveyQuestion
import com.xhbadxx.projects.module.domain.repository.fplay.CommonRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class SurveyViewModel @Inject constructor(
    private val commonRepository: CommonRepository
) : BaseViewModel<SurveyViewModel.SurveyIntent, SurveyViewModel.SurveyState>() {

    var dataStart: DataStart = DataStart()
    var dataQuestion: SurveyQuestion? = null
        private set

    sealed class SurveyState : ViewState {
        data class Loading(val intent: SurveyIntent? = null) : SurveyState()
        data class Error(val message: String, val intent: SurveyIntent? = null) : SurveyState()
        data class ErrorNoInternet(val message: String, val intent: SurveyIntent? = null) : SurveyState()
        data class ErrorRequiredLogin(val message: String, val intent: SurveyIntent? = null) : SurveyState()
        data class Done(val intent: SurveyIntent? = null) : SurveyState()
        data class GetSurveyQuestionResult(val data: SurveyQuestion) : SurveyState()
        data class SendSurveyResult(val data: SendSurvey) : SurveyState()
        object Init : SurveyState()
        // Add more states as needed for your survey feature
    }

    sealed class SurveyIntent : ViewIntent {
        data class GetSurveyQuestion(val type: String, val surveyId: String, val eventId: String) : SurveyIntent()
        data class SendSurvey(val type: String, val surveyId: String, val eventId: String, val question: List<SurveyQuestion.Question>) : SurveyIntent()
        // Add more intents as needed for your survey feature
    }

    override fun dispatchIntent(intent: SurveyIntent) {
        safeLaunch {
            when (intent) {
                is SurveyIntent.GetSurveyQuestion -> {
                    commonRepository.getSurveyQuestion(type = intent.type, surveyId = intent.surveyId, eventId = intent.eventId).collect {
                        _state.value = it.reduce(intent = intent) { _, data ->
                            dataQuestion = data
                            SurveyState.GetSurveyQuestionResult(data)
                        }
                    }
                }
                is SurveyIntent.SendSurvey -> {
                    commonRepository.sendSurvey(type = intent.type, surveyId = intent.surveyId, eventId = intent.eventId, questions = intent.question).collect {
                        _state.value = it.reduce(intent = intent) { _, data ->
                            SurveyState.SendSurveyResult(data)
                        }
                    }
                }
                else -> {
                    // Handle other intents if necessary
                }
            }
        }
    }

    override fun <T> Result<T>.reduce(
        intent: SurveyIntent?,
        successFun: (Boolean, T) -> SurveyState
    ): SurveyState {
        return when (this) {
            is Result.Init -> SurveyState.Loading(intent = intent)
            is Result.Success -> {
                successFun(this.isCached, this.successData)
            }
            is Result.UserError.RequiredLogin -> SurveyState.ErrorRequiredLogin(message = this.message, intent = intent)
            is Result.Error -> {
                if (this is Result.Error.Intenet) {
                    SurveyState.ErrorNoInternet(message = this.message, intent = intent)
                } else {
                    SurveyState.Error(message = this.message, intent = intent)
                }
            }
            Result.Done -> SurveyState.Done(intent = intent)
        }
    }

    data class DataStart(
        val title: String = "",
        val description: String = "",
        val icon: String = "",
        val surveyId: String = "",
        val eventId: String = "",
        val typeEvent: String = ""
    )
}
