package com.fptplay.mobile.player.dialog

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.adapter.BaseViewHolder
import com.fptplay.mobile.databinding.DialogPlayerOptionItemBinding
import com.fptplay.mobile.player.dialog.data.AudioTrackPlayerData
import com.fptplay.mobile.player.dialog.data.BitratePlayerData
import com.fptplay.mobile.player.dialog.data.ExpandPlayerData
import com.fptplay.mobile.player.dialog.data.SubtitlePlayerData
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.visible
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.common.PlayerSpeed
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.image.ImageProxy

class PlayerOptionAdapter : BaseAdapter<BaseObject, RecyclerView.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return OptionViewHolder(DialogPlayerOptionItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is OptionViewHolder) {
            holder.bind(differ.currentList[position])
        }
    }

    private inner class OptionViewHolder(private val binding: DialogPlayerOptionItemBinding) : BaseViewHolder(binding) {

        init {
            binding.llContainer.setOnClickListener {
                if (absoluteAdapterPosition >= 0 && absoluteAdapterPosition < size()) {
                    eventListener?.onClickedItem(position = absoluteAdapterPosition, data = differ.currentList[absoluteAdapterPosition])
                }
            }
        }

        fun bind(data: BaseObject) {
            when (data) {
                is BitratePlayerData -> {
                    // Set title
                    binding.tvTitle.apply {
                        text = data.title
                        isSelected = data.isSelected
                    }
                    binding.ivSelected.isVisible = data.isSelected

                    // Set max vip image
                    if (data.isVipRequired) {
                        binding.ivMaxVipRequire.show()
                        if (data.vipImage.isNotBlank()) {
                            ImageProxy.load(
                                context = binding.root.context,
                                url = data.vipImage,
                                width = binding.ivMaxVipRequire.width,
                                height = binding.ivMaxVipRequire.height,
                                target = binding.ivMaxVipRequire
                            )
                        }
                    } else {
                        binding.ivMaxVipRequire.hide()
                    }
                }
                is SubtitlePlayerData -> {
                    // Set title
                    binding.tvTitle.apply {
                        text = data.name
                        isSelected = data.isSelected
                    }
                    binding.ivSelected.isVisible = data.isSelected

                    // Set max vip image
                    if (data.isVipRequired) {
                        binding.ivMaxVipRequire.show()
                        if (data.vipImage.isNotBlank()) {
                            ImageProxy.load(
                                context = binding.root.context,
                                url = data.vipImage,
                                width = binding.ivMaxVipRequire.width,
                                height = binding.ivMaxVipRequire.height,
                                target = binding.ivMaxVipRequire
                            )
                        }
                    } else {
                        binding.ivMaxVipRequire.hide()
                    }
                }
                is AudioTrackPlayerData -> {
                    var valueToDisplay = data.name
                    MainApplication.INSTANCE.appConfig.audioReplacementName.forEach { audioPatternData ->
                        if (data.name.contains(audioPatternData.pattern)) {
                            valueToDisplay = data.name.replace(audioPatternData.pattern, "")
                            binding.ivBadge.apply {
                                visible()
                                ImageProxy.load(
                                    context = binding.root.context,
                                    url = audioPatternData.thumb,
                                    width = resources.getDimensionPixelSize(R.dimen.player_control_bottom_button_badge_icon_size),
                                    height = resources.getDimensionPixelSize(R.dimen.player_control_bottom_button_badge_icon_size),
                                    target = binding.ivBadge,
                                )
                            }
                        } else {
                            binding.ivBadge.gone()
                        }
                    }

                    // Set title
                    binding.tvTitle.apply {
                        text = valueToDisplay
                        isSelected = data.isSelected
                    }
                    binding.ivSelected.isVisible = data.isSelected

                    // Set max vip image
                    if (data.isVipRequired) {
                        binding.ivMaxVipRequire.show()
                        if (data.vipImage.isNotBlank()) {
                            ImageProxy.load(
                                context = binding.root.context,
                                url = data.vipImage,
                                width = binding.ivMaxVipRequire.width,
                                height = binding.ivMaxVipRequire.height,
                                target = binding.ivMaxVipRequire
                            )
                        }
                    } else {
                        binding.ivMaxVipRequire.hide()
                    }
                }
                is ExpandPlayerData -> {
                    // Set title
                    binding.tvTitle.apply {
                        text = data.title
                        isSelected = data.isSelected
                    }
                    binding.ivSelected.isVisible = data.isSelected

                    // Set max vip image
                    if (data.isVipRequired) {
                        binding.ivMaxVipRequire.show()
                        if (data.vipImage.isNotBlank()) {
                            ImageProxy.load(
                                context = binding.root.context,
                                url = data.vipImage,
                                width = binding.ivMaxVipRequire.width,
                                height = binding.ivMaxVipRequire.height,
                                target = binding.ivMaxVipRequire
                            )
                        }
                    } else {
                        binding.ivMaxVipRequire.hide()
                    }
                }

                is PlayerSpeed -> {
                    binding.tvTitle.apply {
                        text = data.title
                        isSelected = data.isSelected
                    }
                    binding.ivSelected.isVisible = data.isSelected
                    binding.ivMaxVipRequire.hide()
                }
                else -> {

                }
            }
        }

    }

}