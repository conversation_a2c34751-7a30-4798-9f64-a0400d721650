package com.fptplay.mobile.features.mqtt

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkRequest
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.fpl.plugin.mqtt.Action
import com.fpl.plugin.mqtt.Connection
import com.fpl.plugin.mqtt.listener.MqttActionCallback
import com.fpl.plugin.mqtt.model.ReceivedMessage
import com.fpl.plugin.mqtt.model.Subscription
import com.fpl.plugin.mqtt.sevice.QoS
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.common.utils.NetworkUtils
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.features.mqtt.MqttUtil.EVENT_ERROR
import com.fptplay.mobile.features.mqtt.MqttUtil.LOG_ID_ERROR
import com.fptplay.mobile.features.mqtt.MqttUtil.LWT_MESSAGE
import com.fptplay.mobile.features.mqtt.MqttUtil.LWT_TOPIC
import com.fptplay.mobile.features.mqtt.MqttUtil.MQTT_ENABLE
import com.fptplay.mobile.features.mqtt.MqttUtil.PING_CCU_TOPIC
import com.fptplay.mobile.features.mqtt.MqttUtil.PUBLISHER
import com.fptplay.mobile.features.mqtt.MqttUtil.REMOTE_TOPIC
import com.fptplay.mobile.features.mqtt.MqttUtil.SEPARATOR
import com.fptplay.mobile.features.mqtt.MqttUtil.SUBSCRIBER
import com.fptplay.mobile.features.mqtt.MqttUtil.updateFromDetail
import com.fptplay.mobile.features.mqtt.MqttUtil.fromJsonString
import com.fptplay.mobile.features.mqtt.MqttUtil.getReconnectInterval
import com.fptplay.mobile.features.mqtt.MqttUtil.isAutoReconnect
import com.fptplay.mobile.features.mqtt.MqttUtil.toJsonString
import com.fptplay.mobile.features.mqtt.MqttUtil.toMillisecond
import com.fptplay.mobile.features.mqtt.database.IMqttNotificationListener
import com.fptplay.mobile.features.mqtt.model.Publisher
import com.fptplay.mobile.features.mqtt.database.MqttNotificationListener
import com.fptplay.mobile.features.mqtt.listener.LimitCcuActionListener
import com.fptplay.mobile.features.mqtt.model.MessageArrived
import com.fptplay.mobile.features.mqtt.model.MqttConfig
import com.fptplay.mobile.features.mqtt.model.MqttNotificationDetail
import com.fptplay.mobile.features.mqtt.model.MqttOptionData
import com.fptplay.mobile.features.mqtt.model.MqttRoom
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.common.Config
import com.xhbadxx.projects.module.domain.entity.fplay.common.MQTTConfig
import com.xhbadxx.projects.module.domain.repository.fplay.CommonRepository
import com.xhbadxx.projects.module.util.common.Util
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import org.eclipse.paho.client.mqttv3.android.IMqttToken
import org.eclipse.paho.client.mqttv3.android.MqttConnectOptions
import org.eclipse.paho.client.mqttv3.android.MqttException
import org.eclipse.paho.client.mqttv3.android.MqttMessage
import timber.log.Timber
import java.util.Timer
import java.util.TimerTask
import kotlin.coroutines.cancellation.CancellationException

class MqttConnectManager(
    private val context: Context,
    private val sharedPreferences: SharedPreferences,
    private val commonRepository: CommonRepository,
    private val trackingProxy: TrackingProxy,
    private val trackingInfo: Infor
) : DefaultLifecycleObserver, IMqttNotificationListener {
    companion object {
        val INSTANCE: MqttConnectManager by lazy {
            MqttConnectManager(
                context = MainApplication.INSTANCE.applicationContext,
                sharedPreferences = MainApplication.INSTANCE.sharedPreferences,
                commonRepository = MainApplication.INSTANCE.commonRepository,
                trackingProxy = MainApplication.INSTANCE.trackingProxy,
                trackingInfo = MainApplication.INSTANCE.trackingInfo
            )
        }
        const val LOG_MQTT = "MqttConnectManager"

    }

    private var mqttConnection : Connection? = null
    private var config : MqttConfig ? = null
    private var userTopic: String = ""
    private val connectivityManager by lazy {
        MainApplication.INSTANCE.applicationContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    private var accessTokenTimer : Timer? = null
    private var timeDelay: Long = 0
    private var isScheduled : Boolean = false
    private var isAppInForeground: Boolean = false
    private var isInitialized: Boolean = false
    private var mqttNotificationListener: MqttNotificationListener? = null
    private var limitCCUActionListener: LimitCcuActionListener? = null

    private var pingCcuBackupJob: Job? = null
    private var pingCcuBackupScope: CoroutineScope? = null
    inner class AccessTokenTask : TimerTask() {
        override fun run() {
            getMqttAccessToken()
            isScheduled = false
            accessTokenTimer?.cancel() // Hủy timer sau khi chạy một lần
            accessTokenTimer = null
        }
    }

    val exceptionHandler = CoroutineExceptionHandler { _, throwable ->
        throwable.printStackTrace()
        Timber.tag(LOG_MQTT).d("Error: ${throwable.message}")
    }
    // Lifecycle Observer Methods
    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        isAppInForeground = true
        if (MainApplication.INSTANCE.appConfig.mqttEnable) {
            Timber.tag(LOG_MQTT).d("onStart")
            startListeningToEmergencyList()
        }
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        Timber.tag(LOG_MQTT).d("onStop")
        isAppInForeground = false
        stopListeningToEmergencyList()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        cleanup()
        disconnectMQTT()
        super.onDestroy(owner)
    }

    private fun startListeningToEmergencyList(){
        if(MainApplication.INSTANCE.appConfig.mqttEnable){
            Timber.tag(LOG_MQTT).d("startListening")
            if (sharedPreferences.userLogin()){
                if (mqttNotificationListener == null) {
                    mqttNotificationListener = MqttNotificationListener(this)
                }
                config?.detail?.emergencyRooms?.let {
                    mqttNotificationListener?.startListeningToEmergencyList(
                        it.map { room ->
                            MqttRoom( roomId = room.roomId, roomType = room.roomType)
                        }
                    )
                }
            }
        }
    }

    fun stopListeningToEmergencyList(){
        mqttNotificationListener?.stopListeningToEmergencyList()
    }

    fun init(appConfig: Config) {
        if (isInitialized) {
            Timber.tag(LOG_MQTT).d("MQTT already initialized")
            return
        }
        config = MqttUtil.initConfig(appConfig = appConfig)
        Timber.tag(LOG_MQTT).d("room: ${appConfig.mqttEmmenencyRooms} mqttRandom:${appConfig.mqttRandom} option:${appConfig.mqttOptions}")
        Timber.tag(LOG_MQTT).d("Init MQTT isInitialized: $isInitialized \n config: $config  ")
        runAccessTokenTimer()
        isInitialized = true
    }

    fun disconnectMQTT() {
        try {
            Timber.tag(LOG_MQTT).d("Disconnecting MQTT")
            // Disconnect MQTT connection
            if (isConnected()){
                mqttConnection?.disconnect()
            }
            // Cancel access token timer
            accessTokenTimer?.cancel()
            accessTokenTimer = null
            isScheduled = false

            // Clear user topic
            unSubscriberToUserTopic()
            isInitialized = false
            Timber.tag(LOG_MQTT).d("MQTT disconnected successfully")
        } catch (e: Exception) {
            Timber.tag(LOG_MQTT).e(e, "Error disconnecting MQTT")
        }
    }
    fun shouldStartMqtt(isOpenSelectOnBoarding: Boolean, isLoginProfileChanged: Boolean){
        val shouldStartMqtt = !isOpenSelectOnBoarding || (sharedPreferences.userLogin() && !isLoginProfileChanged)
        Timber.tag("MqttConnectManager").d("shouldStartMqtt : $shouldStartMqtt")
        if (shouldStartMqtt) {
            MqttConnectManager.INSTANCE.startListeningToEmergencyList()
            MqttConnectManager.INSTANCE.runAccessTokenTimer()
        }
    }

    private fun runAccessTokenTimer(random: Long = 0) {
        if(!isScheduled) {
            if (accessTokenTimer == null) {
                accessTokenTimer = Timer("getAccessTokenTimer", false)
            }
            isScheduled = true
            accessTokenTimer?.schedule(AccessTokenTask(),timeDelay + random)
        } else {
            Timber.tag(LOG_MQTT).d("The timer for get access token is scheduled or running.")
        }
    }

    fun registerNetworkCallback(networkCallback: ConnectivityManager.NetworkCallback) {
        connectivityManager.registerNetworkCallback(NetworkRequest.Builder().build(), networkCallback)
    }

    fun unregisterNetworkCallback(networkCallback: ConnectivityManager.NetworkCallback) {
        connectivityManager.unregisterNetworkCallback(networkCallback)
    }

    private fun connectMQTT(successData: MQTTConfig.Data) {
        config?.connection = successData
        val clientId = MainApplication.INSTANCE.appConfig.xAgent.plus("__").plus(Util.deviceId(context))
        mqttConnection?.let {
            updateAndConnectMQTT(it, successData, clientId)
        } ?: kotlin.run {
            Timber.tag(LOG_MQTT).i("connectMQTT createConnection clientId: $clientId")
            mqttConnection = Connection.createConnection(
                clientHandle = "",
                clientId = clientId,
                uri = successData.url.tcp,
                context = context,
                tlsConnection = successData.option.enableTls,
                automaticReconnect = successData.option.automaticReconnect.isAutoReconnect(),
                reconnectInterval = successData.option.automaticReconnect.getReconnectInterval(),
                mqttActionCallback = mqttActionCallback,
                connectionOptions = optionsFromConfig(successData)
            ).connect()
        }
    }

    private fun updateAndConnectMQTT(mqttConnection : Connection, successData : MQTTConfig.Data, clientId : String) {
        Timber.tag(LOG_MQTT).i("connectMQTT updateAndConnectMQTT clientId: $clientId")
        mqttConnection.updateConnection(
            clientId = clientId,
            uri = successData.url.tcp,
            tlsConnection = successData.option.enableTls,
            automaticReconnect = successData.option.automaticReconnect.isAutoReconnect(),
            reconnectInterval = successData.option.automaticReconnect.getReconnectInterval(),
            mqttActionCallback = mqttActionCallback,
            connectionOptions = optionsFromConfig(successData)
        ).connect(true)
    }

    private fun optionsFromConfig(data: MQTTConfig.Data): MqttConnectOptions {
        val connOpts = MqttConnectOptions()
        connOpts.isCleanSession = false
        connOpts.connectionTimeout = data.option.connectionTimeout
        connOpts.keepAliveInterval = data.option.keepAlive
        connOpts.userName = if (data.jwtFrom == "username") {
            data.token
        } else ""
        connOpts.password = if (data.jwtFrom == "password") {
            data.token.toCharArray()
        } else charArrayOf()

        connOpts.setWill(LWT_TOPIC, LWT_MESSAGE.toByteArray(), data.option.qos, false)
        Timber.tag(LOG_MQTT).i("setWill topic: $LWT_TOPIC, message: $LWT_MESSAGE, qos: ${data.option.qos}")
        connOpts.isAutomaticReconnect = false
//           if (data.option.enableTls){
//               // TODO Add Keys to conOpts here
////               connOpts.setSocketFactory()
//           }
        return connOpts
    }

    fun getMessage(payload: String, qos: Int, retained: Boolean) : MqttMessage {
        val mes = MqttMessage(payload.toByteArray())
        mes.qos = qos
        mes.isRetained = retained
        return mes
    }

    private val mqttActionCallback = object : MqttActionCallback {
        override fun onSuccess(action: Action, topic: String, token: IMqttToken?) {
            when(action) {
                Action.CONNECT -> {
                    subscriptionToUserTopic()
                }
                else -> {}
            }
        }

        override fun requestToken() {
            Timber.tag(LOG_MQTT).i("requestToken")
            if (NetworkUtils.isNetworkAvailable()) {
                getMqttAccessToken()
            }
        }

        override fun onFailure(
            action: Action,
            topic: String,
            token: IMqttToken?,
            exception: Throwable?
        ) {
            when(action) {
                Action.CONNECT -> {
                    if (exception is MqttException) {
                        if (exception.reasonCode == MqttException.REASON_CODE_NOT_AUTHORIZED.toInt()) {
                            sendLogError(
                                code = exception.reasonCode.toString(),
                                msg = exception.cause?.message ?: exception.message ?: ""
                            )
                        }
                    }
                }

                Action.SUBSCRIBE -> {
                    sendLogError(
                        itemName = SUBSCRIBER,
                        url = topic,
                        code = ((exception as? MqttException)?.reasonCode ?: -1).toString(),
                        msg = exception?.cause?.message ?: exception?.message ?: ""
                    )
                }

                Action.PUBLISH -> {
                    sendLogError(
                        itemName = PUBLISHER,
                        url = topic,
                        code = ((exception as? MqttException)?.reasonCode ?: -1).toString(),
                        msg = exception?.cause?.message ?: exception?.message ?: ""
                    )
                }
                else -> {}
            }
        }


    }

    private fun republishMessagesFailed() {
        mqttConnection?.let { connection ->
            val messageFailures = connection.messageList.filter { !it.isSuccess }
            if (messageFailures.isNotEmpty()) {
                messageFailures.forEach {
                    val newMessage = it.message
                    val publisher = newMessage.payload.decodeToString().fromJsonString()
                    if (publisher != null) {
                        publisher.isRetry = 1
                        newMessage.payload = publisher.toJsonString().toByteArray()
                        Timber.tag(Connection.TAG).i("Auto-ReceivedMessage to: ${it.topic} message: $publisher")
                        connection.publishMessage(it.topic, newMessage)
                    } else {
                        Timber.tag(Connection.TAG).i("Auto-ReceivedMessage Failed message is not a valid topic: ${it.topic} message: ${newMessage.payload.decodeToString()}")
                    }
                }
            }
        }
    }

    fun subscriptionToUserTopic() {
        if (sharedPreferences.userLogin()) {
            userTopic = getUserTopic()
            mqttConnection?.addNewSubscription(
                Subscription(
                    topic = userTopic,
                    qos = QoS.valueOf(config?.connection?.option?.qos ?: 2),
                    clientHandle = mqttConnection!!.handle(),
                    isEnableNotifications = false
                )
            )
        }
    }

    fun unSubscriberToUserTopic() {
        mqttConnection?.unsubscribe(userTopic)
        userTopic = ""
    }

    fun subscriptionToTopic(type: String, typeId: String) {
        val topic = getContentTopic(type, typeId)
        mqttConnection?.addNewSubscription(
            Subscription(
                topic = topic,
                qos = QoS.valueOf(config?.connection?.option?.qos ?: 2),
                clientHandle = mqttConnection!!.handle(),
                isEnableNotifications = false
            )
        )
    }

    fun unSubscriberToTopic(type: String, typeId: String) {
        val topic = getContentTopic(type, typeId)
        mqttConnection?.unsubscribe(topic)
    }

    fun publishToTopic(publisher: Publisher, type: String, typeId: String) {
        Timber.tag(LOG_MQTT).d("publishToTopic publisher: $publisher ")
        val topic = getContentTopic(type, typeId)
        mqttConnection?.publishMessage(
            topic = topic,
            payload = publisher.toJsonString(),
            qos = QoS.valueOf(config?.connection?.option?.qos ?: 2),
            isRetained = true
        )
    }

    private fun getUserTopic(): String {
        return StringBuilder()
            .append(REMOTE_TOPIC)
            .append(SEPARATOR)
            .append(sharedPreferences.userId())
            .append(SEPARATOR)
            .append(Util.deviceId(context))
            .toString()
    }

    fun getContentTopic(type: String, typeId: String): String {
        return StringBuilder()
            .append(PING_CCU_TOPIC)
            .append(SEPARATOR)
            .append(type)
            .append(SEPARATOR)
            .append(typeId)
            .toString()
    }

    fun messageArrived(): MutableLiveData<MutableList<ReceivedMessage>>? {
        return mqttConnection?.messages
    }



    private fun reconnectMQTT() {
        if (isConnected()) return
        mqttConnection?.connect(isReconnect = true)
    }

    fun isConnected(): Boolean {
        return mqttConnection?.isConnected ?: false
    }

    fun isInitialized(): Boolean {
        return isInitialized
    }
    private var _updateStatusMqttFromFireStore = MutableLiveData<Boolean?>()

    val updateStatusMqttFromFireStore get() = _updateStatusMqttFromFireStore
    override fun onNotificationsReceived(room: MqttRoom, mqttNotificationDetail: MqttNotificationDetail?) {
        Timber.tag(LOG_MQTT).d("roomMqtt: $room, onNotificationsReceived $mqttNotificationDetail")
        config?.updateFromDetail(details = mqttNotificationDetail ){
            updateStatusMqttFromFireStore.postValue(mqttNotificationDetail?.mqtt?.enable == MQTT_ENABLE)
        }
        Timber.tag(LOG_MQTT).d("getCurrentMqttConfig ${MqttUtil.getCurrentMqttConfig}")
    }

    override fun onMqttConfigError(error: String) {

    }

    private fun getMqttAccessToken() {
        Timber.tag(LOG_MQTT).d("get MQTT access token")
        val job = Job()
        val scope = CoroutineScope(Dispatchers.IO + job)
        scope.launch(exceptionHandler) {
            try {
                commonRepository.getMQTTConfig().collect {
                    when (it) {
                        is Result.Success -> {
                            Timber.tag(LOG_MQTT).d("get MQTT Config Success ${it.successData.data}")
                            if (it.successData.status == "1") {
                                timeDelay = 0
                                connectMQTT(it.successData.data)
                            } else {
                                Timber.tag(LOG_MQTT).d("get MQTT Config Failed Code: ${it.successData.errorCode} ${it.successData.msg}")
                                retryGetAccessToken()
                            }

                        }
                        is Result.UserError.RequiredLogin -> {
                            Timber.tag(LOG_MQTT).d("get MQTT Config Failed ${it.message} requiredLogin: ${it.requiredLogin}")
                        }
                        is Result.Error -> {
                            Timber.tag(LOG_MQTT).d("get MQTT Config Failed ${it.message}")
                            retryGetAccessToken()
                        }
                        else -> {}
                    }
                }
            } catch (ex: Exception) {
                ex.printStackTrace()
                Timber.tag(LOG_MQTT).d("Error: ${ex.message}")
            }
        }
    }

    private fun retryGetAccessToken() {
        config?.detail?.automaticRetry?.let {
            val min = it.minRetryInterval.toMillisecond()
            val max = it.maxRetryInterval.toMillisecond()
            val random = it.random.toMillisecond()
            if (min > 0 && max > 0 && min < max) {
                if (timeDelay == 0L) {
                    timeDelay = min
                } else {
                    timeDelay *= 2
                    if (timeDelay > max) {
                        timeDelay = max
                    }
                }
                Timber.tag(LOG_MQTT).d("retry Get Access Token after $timeDelay + $random ms")
                runAccessTokenTimer(random)
            }
        }
    }

    fun sendLogLimitCCU(publisher: Publisher, topic: String) {
        Timber.tag(LOG_MQTT).d("Limit CCU from topic: $topic data: $publisher")
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = LOG_ID_ERROR,
                event = EVENT_ERROR,
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = MqttUtil.ACTION_LIMIT_CCU,
                itemName = "",
                url = topic,
                errorCode = publisher.data?.code ?: "",
                errorMessage = publisher.data?.message?.description ?: publisher.data?.message?.title ?: ""
            )
        )
    }
    fun sendLogLimitCCU(publisher: MessageArrived, topic: String) {
        Timber.tag(LOG_MQTT).d("Limit CCU from topic: $topic data: $publisher")
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = LOG_ID_ERROR,
                event = EVENT_ERROR,
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = MqttUtil.ACTION_LIMIT_CCU,
                itemName = "",
                url = topic,
                errorCode = "",
                errorMessage = publisher.data?.action?.desc ?: ""
            )
        )
    }
    
    fun sendLogError(itemName: String = "", url: String = "", screen: String = "", code: String = "", msg: String) {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = LOG_ID_ERROR,
                event = EVENT_ERROR,
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = screen,
                itemName = itemName,
                url = url,
                errorCode = code,
                errorMessage = msg
            )
        )
    }

    fun setLimitCcuActionListener(listener: LimitCcuActionListener?) {
        limitCCUActionListener = listener
    }

    fun checkLimitCcu(pingMqtt: Boolean, mqttMode: Int, codeLimitCcu: String, processPlay: () -> Unit = {}) {
        if (pingMqtt && mqttConnection != null) {
            val option = config?.detail?.options?.find { it.mqttMode == mqttMode }
            Timber.tag("LimitCCU").d("checkLimitCcu option: $option")
            if (codeLimitCcu == MqttUtil.SUCCESS_CODE) {
                processPlay()
                return
            }
            option?.let {
                when(mqttMode) {
                    1 -> {
                        processPlay()
                    }
                    2 -> {
                        if (it.waitingApproval > 0 && it.enableBackupApi) {
                            val delay = it.waitingApproval.toMillisecond()
                            scheduleLimitCcuBackup(delay, it)
                        } else processPlay()
                    }
                    3 -> {
                        processPlay()
                        if (option.previewWaitingApproval > 0 && it.enableBackupApi) {
                            val delay = option.previewWaitingApproval.toMillisecond()
                            scheduleLimitCcuBackup(delay, it)
                        } else processPlay()
                    }
                }
            } ?: processPlay()
        } else {
            processPlay()
        }
    }

    fun scheduleLimitCcuBackup(delayMs: Long, option: MqttOptionData) {
        cancelLimitCcuBackup()
        if (pingCcuBackupScope == null) {
            pingCcuBackupScope = CoroutineScope(Dispatchers.IO + Job())
        }
        pingCcuBackupJob = pingCcuBackupScope?.launch {
            try {
                Timber.tag(LOG_MQTT).d("Start ping CCU backup after $delayMs ms")
                delay(delayMs)

                if (isActive) {
                    limitCCUActionListener?.checkPingCcuBackup(option)
                } else {
                    Timber.tag(LOG_MQTT).d("scheduled job is not active!")
                }
            } catch (e : CancellationException ) {
                e.printStackTrace()
                Timber.tag(LOG_MQTT).d("Error: ${e.message}")
            } catch (e : Exception) {
                e.printStackTrace()
                Timber.tag(LOG_MQTT).d("Error: ${e.message}")
            }
        }
    }

    fun cancelLimitCcuBackup() {
        pingCcuBackupJob?.let {
            if (it.isActive) {
                it.cancel(CancellationException("scheduled job is canceled!"))
            }
        }
        pingCcuBackupJob = null
    }

    fun cleanup() {
        cancelLimitCcuBackup()
        pingCcuBackupScope?.cancel()
    }
}
