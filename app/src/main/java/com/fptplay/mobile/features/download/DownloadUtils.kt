package com.fptplay.mobile.features.download

import android.content.Context
import androidx.annotation.OptIn
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.media3.common.util.UnstableApi
import com.fplay.module.downloader.VideoDownloadManager
import com.fplay.module.downloader.listener.DownloadKeyResult
import com.fplay.module.downloader.model.CollectionVideoTaskItem
import com.fplay.module.downloader.model.TaskInitialStatus
import com.fplay.module.downloader.model.VideoPlayMode
import com.fplay.module.downloader.model.VideoTaskItem
import com.fplay.module.downloader.model.VideoTaskState
import com.fplay.module.downloader.model.WarningScenario
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.common.utils.DateTimeUtils
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.features.download.model.DownloadTaskState
import androidx.media3.exoplayer.offline.DownloadHelper
import androidx.media3.exoplayer.util.Utils.IS_SIGMA_DRM
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.xhbadxx.projects.module.domain.entity.fplay.vod.VodDownloadInfo
import com.xhbadxx.projects.module.util.common.Util.toBase64Default
import com.xhbadxx.projects.module.util.logger.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.IOException
import kotlin.math.max
import kotlin.math.roundToInt

object DownloadUtils {
    val StartDownload = "Download"
    val DownloadSuccess = "DownloadSuccess"
    val DownloadFail = "DownloadFailed"
    val CancelDownload = "CancelDownload"
    val ClearDownloaded = "ClearDownloaded"
    val Resume = "Resume"
    private const val GET_KEY_ERR_CODE = 401
    private const val LICENSE_URL = "https://license.sigmadrm.com/license/verify/widevine"
    //Staging:
    //private const val LICENSE_URL = "https://license-staging.sigmadrm.com/license/verify/widevine"
    private const val MERCHANT_FPTPLAY = "fptplay"
    private const val GET_KEY_SCREEN = "GetDRMKeyOffline"
    private const val EVENT_GET_KEY_SUCCESS = "GetDRMKeySuccessfully"
    private const val EVENT_GET_KEY_FAIL = "GetDRMKeyFailed"
    private const val CODE_GET_KEY_SUCCESS = 0
    private const val CODE_GET_KEY_FAIL = 1
    private const val MESSAGE_GET_KEY_FAILED = "get key failed: "
    private const val MESSAGE_CONFIG_NULL = "config is null!"
    private const val MESSAGE_KEY_NULL = "key is null!"
    private const val MESSAGE_MERCHANT_UNSUPPORTED = "merchant must be fptplay! Merchant received is %s"
    fun Int.readyDownload(): Boolean {
        return this == VideoTaskState.DEFAULT || this == VideoTaskState.PREPARE || this == VideoTaskState.PENDING
    }

    fun canDownload(episode: Details.Episode?, isAirlineLayout: Boolean = false, isPlaylist : Boolean = false, profileEnableDownload: Boolean = true): Boolean {
        if(!profileEnableDownload) return false // logic multi profile
        return if(isPlaylist) episode.enableDownload() else episode?.isTrailer != 1 && (isAirlineLayout  || episode.enableDownload())
    }

    private fun Details.Episode?.enableDownload() = (this != null && downloadLocal == 1)

    @OptIn(UnstableApi::class)
    private fun downloadKeyOffId(context: Context?, downloadTaskItem: VideoTaskItem, result: DownloadKeyResult) {
        if (downloadTaskItem.verifyDrm()) {
            if (!downloadTaskItem.merchant.isNullOrBlank() && downloadTaskItem.merchant == MERCHANT_FPTPLAY) {
                // SIGMA
                context?.let {
                    val mediaItem = DRMUtils.createMediaItem(
                        userId = MainApplication.INSTANCE.sharedPreferences.userId(),
                        merchant = downloadTaskItem.merchant ?: "",
                        session = downloadTaskItem.session ?: "",
                        url = downloadTaskItem.url,
                        licenseUrl = LICENSE_URL
                    )
                    val httpDataSourceFactory = DRMUtils.getHttpDataSourceFactory(context)
                    val renderersFactory = DRMUtils.buildRenderersFactory(context, preferExtensionRenderer = false)

                    val exoplayerDownloadHelper = DownloadHelper.forMediaItem(
                        context,
                        mediaItem,
                        renderersFactory,
                        httpDataSourceFactory
                    )
                    CoroutineScope(Dispatchers.IO).launch {
                        exoplayerDownloadHelper.prepare(object : DownloadHelper.Callback {
                            override fun onPrepared(helper: DownloadHelper) {
                                IS_SIGMA_DRM = true
                                val drmConfiguration = mediaItem.localConfiguration?.drmConfiguration
                                val format = DRMUtils.getFirstFormatWithDrmInitData(exoplayerDownloadHelper)
                                if (drmConfiguration == null || httpDataSourceFactory == null || format == null) {
                                    val errMessage = MESSAGE_GET_KEY_FAILED + MESSAGE_CONFIG_NULL
                                    Logger.d("DownloadUtils $errMessage")
                                    result.downloadKeyFailed(GET_KEY_ERR_CODE, errMessage)
                                    return
                                }
                                val keyOffId = DRMUtils.downloadKeyOffsetId(
                                    userId = MainApplication.INSTANCE.sharedPreferences.userId(),
                                    merchant = downloadTaskItem.merchant ?: "",
                                    session = downloadTaskItem.session ?: "",
                                    format = format,
                                    drmConfiguration = drmConfiguration,
                                    httpDataSourceFactory = httpDataSourceFactory
                                )
                                keyOffId?.let {
                                    val base64 = it.toBase64Default()
                                    Logger.d("DownloadUtils get key success: $base64")
                                    result.downloadKeySuccess(base64)
                                } ?: run {
                                    val errMessage = MESSAGE_GET_KEY_FAILED + MESSAGE_KEY_NULL
                                    Logger.d("DownloadUtils $errMessage")
                                    result.downloadKeyFailed(GET_KEY_ERR_CODE, errMessage)
                                }
                            }
                            override fun onPrepareError(helper: DownloadHelper, e: IOException) {
                                val errMessage = MESSAGE_GET_KEY_FAILED + e.message
                                Logger.d("DownloadUtils $errMessage")
                                result.downloadKeyFailed(GET_KEY_ERR_CODE, errMessage)
                            }
                        })
                    }
                }
            } else {
                val errMessage = MESSAGE_GET_KEY_FAILED + MESSAGE_MERCHANT_UNSUPPORTED + (downloadTaskItem.merchant ?: "")
                Logger.d("DownloadUtils $errMessage")
                result.downloadKeyFailed(GET_KEY_ERR_CODE, errMessage)
            }
        }
    }

    fun getKeyOffId(context: Context?, downloadTaskItem: VideoTaskItem, result: DownloadKeyResult, trackingProxy: TrackingProxy, trackingInfo: Infor) {
        val timeGetKey = System.currentTimeMillis()
        downloadKeyOffId(context, downloadTaskItem, object: DownloadKeyResult {
            override fun downloadKeySuccess(key: String) {
                val realTimePlaying = System.currentTimeMillis() - timeGetKey
                sendLogResultGetKey(downloadTaskItem, EVENT_GET_KEY_SUCCESS, CODE_GET_KEY_SUCCESS, trackingProxy, trackingInfo, realTimePlaying.toString())
                result.downloadKeySuccess(key)
            }

            override fun downloadKeyFailed(errCode: Int, errMessage: String) {
                downloadTaskItem.errorCode = errCode
                downloadTaskItem.errorMessage = errMessage
                sendLogResultGetKey(downloadTaskItem, EVENT_GET_KEY_FAIL, CODE_GET_KEY_FAIL, trackingProxy, trackingInfo)
                result.downloadKeyFailed(errCode, errMessage)
            }
        })
    }

    fun sendLogResultGetKey(item: VideoTaskItem, event: String, status: Int, trackingProxy: TrackingProxy, trackingInfo: Infor, realTimePlaying: String = "") {
        val logInfo = InforMobile(
            infor = trackingInfo,
            logId = "166",
            appSource = item.appId ?: "" ,
            appId = TrackingUtil.currentAppId,
            appName = TrackingUtil.currentAppName,
            screen = GET_KEY_SCREEN,
            subMenuId = TrackingUtil.blockId,
            event = event,
            status = status.toString(),
            itemId = item.movieId,
            itemName = item.title,
            chapterId = item.chapterIdx.toString(),
            EpisodeID = item.episodeId,
            refItemId = item.refItemId ?: "",
            refEpisodeID = item.refEpisodeId ?: "",
            url = item.url,
            isLinkDRM = "0",
            errorCode = item.errorCode.toString(),
            errorMessage = item.errorMessage,
            boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
            startTime = TrackingUtil.startTime,
            blocKPosition = TrackingUtil.blockIndex,
            realTimePlaying = realTimePlaying
        )
        Logger.d("LogResultGetKey info: $logInfo")
        trackingProxy.sendEvent(logInfo)
    }
    fun startNewDownload(vodDownloadInfo: VodDownloadInfo, vod: Details, episode: Details.Episode, episodeIndex: Int, hasCollectionInDb: Boolean, isAirline : Boolean) {
        val downloadTaskItem = createDownloadTaskItem(vodDownloadInfo, vod, episode, episodeIndex, hasCollectionInDb, isAirline)
        VideoDownloadManager.instance.initializationDownloadTask(
            item = downloadTaskItem,
            initialState = TaskInitialStatus.NEW
        )
    }

    fun startDownloadWithNewUrl(
        url: String,
        item: VideoTaskItem,
        d2gTime: Int,
        streamSession: String
    ) {
        item.resetToReDownload(getExpiredTimeForTaskItem(d2gTime))
        item.backupUrl = item.url
        item.url = url
        item.fetchLinkCount += 1
        item.lastUpdateTime = System.currentTimeMillis()
        if (streamSession.isNotBlank() && streamSession != item.streamSession) {
            item.streamSession = streamSession
        }
        VideoDownloadManager.instance.initializationDownloadTask(
            item = item,
            initialState = TaskInitialStatus.RETRY
        )
    }
    fun extendDownloadTaskItem(item: VideoTaskItem, info: VodDownloadInfo) {
        VideoDownloadManager.instance.extendDownloadItem(
            item = item,
            d2gTime = getExpiredTimeForTaskItem(info.d2gTime)
        )
    }
    private fun getExpiredTimeForTaskItem(d2gTime: Int): Int {
        return d2gTime.takeIf { it > 0 } ?: run {
            MainApplication.INSTANCE.appConfig.d2gTime
        }
    }
    private fun createDownloadTaskItem(vodDownloadInfo: VodDownloadInfo, vod: Details, episode: Details.Episode, episodeIndex: Int, hasCollectionInDb: Boolean = false, isAirline : Boolean): VideoTaskItem {
        val taskItem = VideoTaskItem(
            movieId = vod.blockContent.id,
            chapterId = "${vod.blockContent.id}-${episode.id}",
            url = vodDownloadInfo.downloadDashUrl.ifBlank { vodDownloadInfo.downloadUrl },
            isSeries = !((vod.blockContent.episodeType == 0 || vod.blockContent.episodeType == 2) && vod.blockContent.episodeTotal == 1),
            episodeId = episode.realEpisodeId,
            chapterIdx = episodeIndex,
            coverUrl = vod.blockContent.landscapeTitle,
            thumbUrl = episode.horizontalImage,
            title = vod.blockContent.titleVietnam,
            subTitle = vod.blockContent.titleEnglish,
            chapterName = episode.titleVietnam
        )
        taskItem.duration = episode.duration.toLongOrNull() ?: 0L
        taskItem.isInDatabase = hasCollectionInDb
        taskItem.isChapterInDatabase = false
        taskItem.isAirline = isAirline
        taskItem.profileId = ""
        taskItem.expiredTime = getExpiredTimeForTaskItem(vodDownloadInfo.d2gTime)
        taskItem.timeStartIntro = vodDownloadInfo.introFrom
        taskItem.timeStartContent = vodDownloadInfo.startContent
        taskItem.timeEndContent = vodDownloadInfo.endContent
        taskItem.overlayLogo = vod.blockContent.overlayLogo
        taskItem.maturityAdvisories = vod.maturityRating.advisories
        taskItem.maturityValue = vod.maturityRating.value
        taskItem.maturityPrefix = vod.maturityRating.prefix
        taskItem.maturityPosition = vod.maturityRating.position
        taskItem.warnings = vodDownloadInfo.warning.map {
            WarningScenario(
                it.content,
                it.from,
                it.to
            )
        }
        taskItem.playMode = if (vodDownloadInfo.isAudio) VideoPlayMode.AUDIO_ONLY else VideoPlayMode.DEFAULT
        taskItem.backgroundUrl = vodDownloadInfo.audioBackgroundImageUrl
        taskItem.streamSession = vodDownloadInfo.streamSession
        taskItem.appId = vod.blockContent.appId
        taskItem.refItemId = vod.blockContent.refId
        taskItem.refEpisodeId = episode.refEpisodeId
        taskItem.isDrm = vodDownloadInfo.isVerimatrix
        taskItem.session = vodDownloadInfo.session
        taskItem.merchant = vodDownloadInfo.merchant
        taskItem.estimateSizeToBytes = vodDownloadInfo.downloadSize
        taskItem.realEpisodeTotal = vod.blockContent.episodeTotal
        return taskItem
    }

    fun getDownloadStatusByState(stage: Int, percentString: String = "", outDateTime: Float = 48f): String {
        return when (stage) {
            VideoTaskState.START,
            VideoTaskState.PREPARE,
            VideoTaskState.PENDING -> "Đang trong hàng đợi"
            VideoTaskState.DOWNLOADING -> "Đang tải xuống... ${percentString}"
            VideoTaskState.PAUSE -> "Tạm dừng"
            VideoTaskState.ERROR -> "Tải thất bại"
            VideoTaskState.SUCCESS -> if (outDateTime > 0) "Hết hạn trong ${outDateTime.roundToInt()} giờ" else "Hết hạn"
            else -> ""
        }
    }

    fun calculateTimeLeft(lastUpdateTime: Long,expiredTime : Int): Float {
//        var d2gTime = expiredTime
//        if(d2gTime == 0) {
//            d2gTime = 48
//        }
        val timeElapsed = max(System.currentTimeMillis() - lastUpdateTime, 0) / 3600f / 1000f
        return max(expiredTime - timeElapsed, 0f)
    }

    fun bindEventFragmentResult(chapterId: String,fragment : Fragment,eventDelete : (() -> Unit)? = null,eventList : (()-> Unit)? = null, eventExtend : (()-> Unit)? = null, checkInternet : (()-> Boolean)? = null) {
        fragment.setFragmentResultListener(Utils.OPTION_DIALOG_DOWNLOAD_KEY) { _, bundle ->
                val statusId = bundle.getInt(Utils.OPTION_DIALOG_DOWNLOAD_ID_KEY, 0)
                when (statusId) {
                    DownloadTaskState.PAUSE -> {
                        VideoDownloadManager.instance.pauseDownloadTask(chapterId)
                    }
                    DownloadTaskState.REDOWNLOAD -> {
                        if (checkInternet?.invoke() == true) {
                            VideoDownloadManager.instance.reDownloadTask(chapterId, MainApplication.INSTANCE.appConfig.d2gTime, TaskInitialStatus.RESTART)
                        }
                    }
                    DownloadTaskState.RESUME -> {
                        if (checkInternet?.invoke() == true) {
                            VideoDownloadManager.instance.resumeDownload(chapterId)
                        }
                    }
                    DownloadTaskState.EXTEND -> {
                        if (checkInternet?.invoke() == true) {
                            eventExtend?.invoke()
                        }
                    }
                    DownloadTaskState.LIST -> {
                        eventList?.invoke()
                    }
                    DownloadTaskState.DELETE -> {
                        eventDelete?.invoke()
                    }
                }
        }
    }

    fun setOptionResult(state : Int,lastUpdateTime : Long,expiredTime : Int): String {
        return when (state) {
            VideoTaskState.START,
            VideoTaskState.PREPARE,
            VideoTaskState.PENDING,
            VideoTaskState.DOWNLOADING -> Utils.OPTION_DIALOG_DOWNLOADING
            VideoTaskState.PAUSE -> Utils.OPTION_DIALOG_DOWNLOAD_PAUSE
            VideoTaskState.ERROR ->  Utils.OPTION_DIALOG_ERROR
            VideoTaskState.SUCCESS -> if (calculateTimeLeft(lastUpdateTime,expiredTime) > 0) Utils.OPTION_DIALOG_DELETE_FROM_DOWNLOAD else Utils.OPTION_DIALOG_EXPIRED
            else -> ""
        }
    }
    fun listEqual(list1 : List<Any>, list2 : List<Any>) : Boolean {
        if(list1.size != list2.size) return false
        val pairList = list1.zip(list2)
        return pairList.all { (il1, il2) ->
            il1 == il2
        }
    }
    fun listCollectionEqual(list1 : List<CollectionVideoTaskItem>, list2 : List<CollectionVideoTaskItem>) : Boolean {
        if(list1.size != list2.size) return false
        val pairList = list1.zip(list2)
        return pairList.all { (il1, il2) ->
            listEqual(il1.listChapters.filter { it.taskState == VideoTaskState.SUCCESS },il2.listChapters.filter { it.taskState == VideoTaskState.SUCCESS })
        }
    }
}