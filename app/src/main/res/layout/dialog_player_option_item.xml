<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_container"
    android:layout_width="match_parent"
    android:layout_height="@dimen/player_menu_options_item_height"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/app_margin_small">

    <ImageView
        android:id="@+id/iv_badge"
        android:layout_width="@dimen/player_menu_options_item_image_size"
        android:layout_height="@dimen/player_menu_options_item_image_size"
        android:layout_gravity="center_vertical"
        android:scaleType="fitCenter"
        android:layout_marginEnd="@dimen/margin_common_small"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:gravity="start|center_vertical"
        android:maxLines="1"
        android:fontFamily="@font/sf_pro_display_regular"
        android:textColor="@color/white_87"
        android:textSize="@dimen/player_playlist_overlay_sub_title_text_size"
        tools:text="Thuyet minh" />

    <ImageView
        android:id="@+id/iv_max_vip_require"
        android:layout_width="@dimen/_42sdp"
        android:layout_height="@dimen/_14sdp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/_6sdp"
        android:visibility="gone"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iv_selected"
        android:layout_width="@dimen/player_menu_options_item_check_size"
        android:layout_height="@dimen/player_menu_options_item_check_size"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/margin_common_small"
        android:src="@drawable/ic_check"
        android:visibility="gone"
        tools:visibility="visible" />

</LinearLayout>