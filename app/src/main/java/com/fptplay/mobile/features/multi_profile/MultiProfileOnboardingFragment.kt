package com.fptplay.mobile.features.multi_profile

import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.NetworkUtils
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.common.utils.viewutils.GridItemDecoration
import com.fptplay.mobile.databinding.MultiProfileOnboardingFragmentBinding
import com.fptplay.mobile.features.multi_profile.adapter.MultiProfileOnboardingAdapter
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.features.multi_profile.views.MultiProfileOnboardingLoadingView
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.visible
import com.xhbadxx.projects.module.domain.entity.fplay.user.OnboardingData
import com.xhbadxx.projects.module.domain.entity.fplay.user.UpdateOnboarding
import com.xhbadxx.projects.module.util.common.IEventListener
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

@AndroidEntryPoint
class MultiProfileOnboardingFragment : BaseFragment<MultiProfileOnboardingViewModel.ProfileOnboardingState, MultiProfileOnboardingViewModel.ProfileOnboardingIntent>() {
    override val viewModel: MultiProfileOnboardingViewModel by viewModels()
    private var _binding: MultiProfileOnboardingFragmentBinding? = null
    private val binding get() = _binding!!
    private val onboardingAdapter by lazy { MultiProfileOnboardingAdapter() }
    override val handleBackPressed = true
    private val safeArgs: MultiProfileOnboardingFragmentArgs by navArgs()
    private var lastOrientation = Configuration.ORIENTATION_UNDEFINED

    override fun bindComponent() {
        setUpRecyclerView(MainApplication.INSTANCE.applicationContext.resources.configuration)
    }
    override val handleConfigurationChange = true

    private fun setUpRecyclerView(newConfig: Configuration?) {
        if (lastOrientation == newConfig?.orientation) return
        lastOrientation = newConfig?.orientation ?: Configuration.ORIENTATION_UNDEFINED
        val dimenTop :Int = when (newConfig?.orientation) {
            Configuration.ORIENTATION_PORTRAIT -> {
                if (context.isTablet()) resources.getDimensionPixelSize(R.dimen.block_interest_margin_top_portrait) else resources.getDimensionPixelSize(R.dimen.block_interest_margin_top)
            }
            else -> resources.getDimensionPixelSize(R.dimen.block_interest_margin_top)
        }

        binding.rvProfileInterest.apply {
            layoutManager = GridLayoutManager(context, 3, RecyclerView.VERTICAL, false)
            adapter = onboardingAdapter
            if(itemDecorationCount > 0) {
                for(index in 0 until itemDecorationCount) {
                    removeItemDecoration(getItemDecorationAt(index))
                }
            }
            addItemDecoration(
                    GridItemDecoration(
                    spanCount = 3,
                    spacing = resources.getDimensionPixelSize(R.dimen.interest_icon_size_item_padding),
                    includeEdge = false,
                    topSpacing = dimenTop,
                    bottomSpacing = resources.getDimensionPixelSize(R.dimen.block_margin_bottom),
                )
            )
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = MultiProfileOnboardingFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun bindOrientationStateChange(newConfig: Configuration) {
        super.bindOrientationStateChange(newConfig)
        if (_binding != null) {
            setUpRecyclerView(newConfig)
        }
    }

    override fun backHandler() {
        if (isPosFirstProfile) {
            showAlertDialog(
                title = getString(R.string.notification),
                message = getString(R.string.multi_profile_on_boarding_message_confirm_skip),
                textConfirm = getString(R.string.multi_profile_on_boarding_message_button_continue,),
                textClose = getString(R.string.multi_profile_on_boarding_message_button_skip,),
                onConfirm = {},
                onClose = {
                    if (safeArgs.isFromLoginScreen){
                        setFragmentResult(Utils.PROFILE_ONBOARD_DISMISS_CHANGED_EVENT, bundleOf())
                        findNavController().navigateUp()
                    }else{
                        viewModel.clearProfileOnboarding()
                        MultiProfileUtils.restartHome(activity,restartKeepIntent = true)
                    }
                },
                showTitle = true
            )

        } else {
            saveListProfileOnboarding(code = viewModel.getCurProfileCode(), itemHashtag = onBoardingSelected.map { it.hashtag })
            setUpUIProfileWithCurrentIndex(next = false)
        }
    }

    override fun initData() {
        viewModel.saveProfileId(safeArgs.profileId)
        showOnBoardingLoadingView()
    }

    override fun bindData() {
        hidePageError()
        if (viewModel.getListProfileOnboarding().isNullOrEmpty()) {
            viewModel.dispatchIntent(MultiProfileOnboardingViewModel.ProfileOnboardingIntent.GetProfileOnboarding)
        } else {
            setUpLayoutProfileOnboarding()
        }
    }

    override fun retryLoadPage() {
        // get list profile onboarding
        hidePageError()
        viewModel.clearProfileOnboarding()
        viewModel.dispatchIntent(MultiProfileOnboardingViewModel.ProfileOnboardingIntent.GetProfileOnboarding)
    }

    private fun setUpLayoutProfileOnboarding() {
        viewModel.getListProfileOnboarding()?.find { it.code == viewModel.getCurProfileCode() }
            ?.let {
                binding.toolbar.title = it.title
                onboardingAdapter.bindData(it.items){
                    if (_binding != null) {
                        binding.rvProfileInterest.post {
                            binding.rvProfileInterest.layoutManager?.scrollToPosition(0)
                            updateUIStateBtnNext((onBoardingSelected.size) >= MIN_ITEMS_PROFILE_ONBOARDING_LOADING)
                        }
                    }
                }
            }
    }

    private val isPosFirstProfile
        get() = viewModel.getListProfileOnboarding()
        ?.let { list -> list.indexOfFirst { it.code == viewModel.getCurProfileCode() } == 0 }
        ?: false

    private val isLastProfile
        get() = viewModel.getListProfileOnboarding()?.let { list ->
            val lastIndex = list.size - 1
            list.indexOfFirst { it.code == viewModel.getCurProfileCode() } == lastIndex
        } ?: false
    private val onBoardingSelected  get() = onboardingAdapter.data().filter { it.isSelected }?: emptyList()

    private fun setUpUIProfileWithCurrentIndex(next: Boolean = true) {
        val list = viewModel.getListProfileOnboarding() ?: return
        val currentIndex = list.indexOfFirst { it.code == viewModel.getCurProfileCode() }
        if (currentIndex != -1) {
            val targetIndex = if (next) currentIndex + 1 else currentIndex - 1
            list.getOrNull(targetIndex)?.let {
                viewModel.saveCurProfileCode(code = it.code)
                setUpLayoutProfileOnboarding()

            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.clearProfileOnboarding()
        _binding = null
    }

    override fun MultiProfileOnboardingViewModel.ProfileOnboardingState.toUI() {
        when (this) {
            is MultiProfileOnboardingViewModel.ProfileOnboardingState.Loading -> {

            }
            is MultiProfileOnboardingViewModel.ProfileOnboardingState.ResultGetProfileOnboarding -> {
                if (status == "1"){
                    viewModel.saveListProfileOnboarding(data.component)
                    viewModel.saveCurProfileCode(data.component.firstOrNull()?.code?:"")
                    setUpLayoutProfileOnboarding()
                }
                else{
                    viewModel.clearProfileOnboarding()
                    showPageError(
                        title = "",
                        navigationIcon = R.drawable.ic_arrow_left,
                        errorMessage = "Error: ${MultiProfileUtils.errorMessage(context = requireContext(), message = message)}"
                    )
                }
            }

            is MultiProfileOnboardingViewModel.ProfileOnboardingState.ResultSubmittedProfileOnboarding -> {
                // back to other screen
                if (safeArgs.isFromLoginScreen){
                    viewModel.clearProfileOnboarding()
                    setFragmentResult(Utils.PROFILE_ONBOARD_DISMISS_CHANGED_EVENT, bundleOf())
                    Toast.makeText(context,message,Toast.LENGTH_LONG).show()
                    findNavController().navigateUp()

                }else{
                    viewModel.clearProfileOnboarding()
                    MultiProfileUtils.restartHome(activity,restartKeepIntent = true)
                    Toast.makeText(context,message,Toast.LENGTH_LONG).show()
                }
            }
            is MultiProfileOnboardingViewModel.ProfileOnboardingState.ErrorRequiredLogin -> {
                viewModel.clearProfileOnboarding()
                navigateToLoginWithParams(isDirect = false,requestRestartApp = true)
            }

            is MultiProfileOnboardingViewModel.ProfileOnboardingState.Error -> {
                if (intent is MultiProfileOnboardingViewModel.ProfileOnboardingIntent.GetProfileOnboarding){
                    viewModel.clearProfileOnboarding()
                    showPageError(
                        title = "",
                        navigationIcon = R.drawable.ic_arrow_left,
                        errorMessage = "Error: ${MultiProfileUtils.errorMessage(context = requireContext(), message = message)}"
                    )
                }
                if (intent is MultiProfileOnboardingViewModel.ProfileOnboardingIntent.SubmittedProfileOnboarding){
                    if (safeArgs.isFromLoginScreen){
                        viewModel.clearProfileOnboarding()
                        setFragmentResult(Utils.PROFILE_ONBOARD_DISMISS_CHANGED_EVENT, bundleOf())
                        Toast.makeText(context,message,Toast.LENGTH_LONG).show()
                        findNavController().navigateUp()
                    }else{
                        viewModel.clearProfileOnboarding()
                        MultiProfileUtils.restartHome(activity,restartKeepIntent = true)
                        Toast.makeText(context,message,Toast.LENGTH_LONG).show()
                    }
                }
            }

            is MultiProfileOnboardingViewModel.ProfileOnboardingState.ErrorNoInternet -> {
                // show view internet
                if (intent is MultiProfileOnboardingViewModel.ProfileOnboardingIntent.GetProfileOnboarding){
                    showPageError(
                        title = "",
                        navigationIcon = R.drawable.ic_arrow_left,
                        errorMessage = "Error: ${MultiProfileUtils.errorMessage(context = requireContext(), message = message)}"
                    )
                }
                if (intent is MultiProfileOnboardingViewModel.ProfileOnboardingIntent.SubmittedProfileOnboarding){
                    showWarningDialog(message)
                }
            }
            is MultiProfileOnboardingViewModel.ProfileOnboardingState.Done -> {

            }
        }
    }

    override fun bindEvent() {
        binding.toolbar.setNavigationOnClickListener {
            backHandler()
        }

        onboardingAdapter.eventListener = object : IEventListener<OnboardingData.Data.Component.Item> {
            override fun onClickedItem(position: Int, data: OnboardingData.Data.Component.Item) {
                onboardingAdapter.changeDataAt(position,data.copy(isSelected = !data.isSelected)){
                    updateUIStateBtnNext((onBoardingSelected.size) >= MIN_ITEMS_PROFILE_ONBOARDING_LOADING)
                }
            }
        }

        binding.btnSkip.setOnClickListener {
            if (isLastProfile) {
                Timber.tag("tu-multiProfile").i("code : ${viewModel.getCurProfileCode()}" + "getListProfileOnboarding : ${viewModel.getListProfileOnboarding().transformToUpdateOnboarding()}")
                if (NetworkUtils.isNetworkAvailable()) {
                    viewModel.dispatchIntent(
                        MultiProfileOnboardingViewModel.ProfileOnboardingIntent.SubmittedProfileOnboarding(
                            profileId = viewModel.getProfileId(),
                            items = viewModel.getListProfileOnboarding().transformToUpdateOnboarding()))
                } else {
                    showWarningDialog(<EMAIL>(R.string.error_no_intent))
                }
            } else {
                saveListProfileOnboarding(code = viewModel.getCurProfileCode(), itemHashtag = listOf())
                Timber.tag("tu-multiProfile").i("code : ${viewModel.getCurProfileCode()}" + "getListProfileOnboarding : ${viewModel.getListProfileOnboarding().transformToUpdateOnboarding()}")
                setUpUIProfileWithCurrentIndex(next = true)
            }
        }

        binding.btnNext.setOnClickListener {
            if (isLastProfile) {
                Timber.tag("tu-multiProfile").i("code : ${viewModel.getCurProfileCode()}" + "getListProfileOnboarding : ${viewModel.getListProfileOnboarding().transformToUpdateOnboarding()}")
                if (NetworkUtils.isNetworkAvailable()) {
                    viewModel.dispatchIntent(
                        MultiProfileOnboardingViewModel.ProfileOnboardingIntent.SubmittedProfileOnboarding(
                            profileId = viewModel.getProfileId(),
                            items = viewModel.getListProfileOnboarding().transformToUpdateOnboarding()))
                } else {
                    showWarningDialog(<EMAIL>(R.string.error_no_intent))
                }
            }
            else {
                saveListProfileOnboarding(code = viewModel.getCurProfileCode(), itemHashtag = onBoardingSelected.map { it.hashtag })
                setUpUIProfileWithCurrentIndex(next = true)
                Timber.tag("tu-multiProfile").i("code : ${viewModel.getCurProfileCode()}"+ "getListProfileOnboarding : ${viewModel.getListProfileOnboarding().transformToUpdateOnboarding()}")
            }
        }
    }

    private fun List<OnboardingData.Data.Component>?.transformToUpdateOnboarding(): List<UpdateOnboarding> {
        return this?.map { component ->
            val selectedHashtags = component.items.filter { it.isSelected }.map { it.hashtag }
            if (selectedHashtags.isNotEmpty()) {
                UpdateOnboarding(
                    componentCode = component.code,
                    itemHashtag = selectedHashtags
                )
            } else {
                UpdateOnboarding(
                    componentCode = component.code,
                    itemHashtag = listOf()
                )
            }
        }?.ifEmpty {
            return listOf()
        } ?: listOf()
    }

    private fun saveListProfileOnboarding(code:String,itemHashtag: List<String>){
        val  dataUpdate = viewModel.getListProfileOnboarding()
        dataUpdate?.forEach{component ->
            if (component.code == code){
                component.items.forEachIndexed { _, item ->
                    item.isSelected = itemHashtag.contains(item.hashtag)
                }
            }
        }
        viewModel.saveListProfileOnboarding(data = dataUpdate)
    }

    private fun inflateOnBoardingLoadingView() {
        binding.profileOnBoardingView.apply {
            onBoardingListener = object :
                MultiProfileOnboardingLoadingView.ProfileOnBoardingLoadingViewListener {
                override fun hideOnBoardingLoadingView() {
                    // clear loading on boarding
                }

                override fun showOnBoardingLoadingView() {
                    // delay 3s before hide loading
                    lifecycleOwner?.lifecycleScope?.launch(Dispatchers.IO) {
                        delay(TIME_PROFILE_ONBOARDING_LOADING)
                        launch(Dispatchers.Main) {
                            hideLoadingView()
                        }
                    }
                }
            }
            lifecycleOwner = this@MultiProfileOnboardingFragment
        }
    }

    private fun updateUIStateBtnNext(isEnabled:Boolean = false){
        binding.apply {
            binding.btnNext.isEnabled =isEnabled
            if (isEnabled){
                binding.btnNext.background = AppCompatResources.getDrawable(requireContext(), R.drawable.account_rounded_btn_background_enable)
                binding.btnNext.setTextColor(ContextCompat.getColor(requireContext(), R.color.app_content_text_color))
            }
            else{
                binding.btnNext.background = AppCompatResources.getDrawable(requireContext(), R.drawable.account_rounded_btn_background_disable)
                binding.btnNext.setTextColor(ContextCompat.getColor(requireContext(), R.color.app_content_text_disable_color))
            }
        }
    }










































































































































































































































































































































































































































































    private fun showOnBoardingLoadingView() {
        inflateOnBoardingLoadingView()
        binding.profileOnBoardingView.apply {
            visible()
            showOnBoardingLoadingView()
        }
    }

    private fun hideLoadingView() {
        binding.profileOnBoardingView.apply {
            hideOnBoardingLoadingView()
            gone()
        }
    }
    companion object {
        const val TIME_PROFILE_ONBOARDING_LOADING = 3000L
        const val MIN_ITEMS_PROFILE_ONBOARDING_LOADING = 1

    }
}
