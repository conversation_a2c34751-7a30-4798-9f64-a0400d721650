package com.fptplay.mobile.player.model

import androidx.annotation.DrawableRes
import com.fptplay.mobile.player.utils.PlayerConstants
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject

data class PlayerBottomControlData(
    val _id: PlayerConstants.PlayerBottomControlType,
    @DrawableRes val iconRes: Int,
    var value: String,
    val title: String,
    var isEnable: Boolean,
): BaseObject()