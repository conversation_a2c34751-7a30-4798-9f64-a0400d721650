package com.fptplay.mobile.features.mega.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import coil.load
import com.fptplay.mobile.BuildConfig
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.extensions.onClick
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.AllItemUnknownBinding
import com.fptplay.mobile.databinding.MegaAccountLogoutBlockBinding
import com.fptplay.mobile.databinding.MegaAppVersionBlockBinding
import com.fptplay.mobile.databinding.MegaMenuV3BlockBinding
import com.fptplay.mobile.databinding.MegaMultiProfileItemV2Binding
import com.fptplay.mobile.databinding.MegaProfileItemBinding
import com.fptplay.mobile.databinding.MegaProfileItemV2Binding
import com.fptplay.mobile.features.mega.data.BlockAccountLogout
import com.fptplay.mobile.features.mega.data.BlockAppVersion
import com.fptplay.mobile.features.mega.util.MegaAppGridItemDecoration
import com.fptplay.mobile.features.mega.util.MegaMenuItemDecoration
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenu
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenuItem
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.Util.checkToShowContent
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import timber.log.Timber

class MegaMenuV3Adapter(private val context: Context, private val sharedPreferences: SharedPreferences) :
    BaseAdapter<MegaMenu.Block, RecyclerView.ViewHolder>() {

    var megaMenuItemClickListener: IEventListener<MegaMenuItem>? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        Timber.tag("tam-mega").d("onCreateViewHolder viewType $viewType")
        return when (viewType) {
            0 -> ProfileViewHolder(
                MegaProfileItemV2Binding.inflate(LayoutInflater.from(parent.context), parent, false)
            )

            1 -> MultiProfileViewHolder(
                MegaMultiProfileItemV2Binding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )

            2 -> BlockLogoutHolder(
                MegaAccountLogoutBlockBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )

            3 -> BlockGridSquareSmallHolder(
                MegaMenuV3BlockBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )

            4 -> BlockNavigationMenuHolder(
                MegaMenuV3BlockBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                ),
                isolated = false
            )

            5 -> BlockBannerHolder(
                MegaMenuV3BlockBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )

            6 -> BlockNavigationMenuHolder(
                MegaMenuV3BlockBinding.inflate(LayoutInflater.from(parent.context), parent, false),
                isolated = true
            )

            7 -> BlockAppVersionHolder(
                MegaAppVersionBlockBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )


            else -> MegaMenuUnknownBlockViewHolder(
                AllItemUnknownBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )
        }

    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val data = differ.currentList[position]
        when (holder) {
            is ProfileViewHolder -> holder.bind(data)
            is BlockNavigationMenuHolder -> holder.bind(data)
            is BlockGridSquareSmallHolder -> holder.bind(data)
            is MultiProfileViewHolder -> holder.bind(data)
            is BlockBannerHolder -> holder.bind(data)
            is BlockLogoutHolder -> holder.bind(data)
            is BlockAppVersionHolder -> holder.bind(data)
            is MegaMenuUnknownBlockViewHolder -> holder.bind(data)
        }
    }


    override fun getItemViewType(position: Int): Int {
        val item = differ.currentList[position]
        return when (item.blockStyle) {
            MegaMenu.BlockStyle.SquareGridSmall -> 3
            MegaMenu.BlockStyle.NavigationMenu -> 4
            MegaMenu.BlockStyle.Banner -> 5
            MegaMenu.BlockStyle.Unknown -> {
                when (item) {
                    is MegaMenu.BlockProfile,
                    is MegaMenu.BlockProfileLogin -> 0
                    is MegaMenu.BlockMultiProfile -> 1
                    is BlockAccountLogout -> 2
                    is BlockAppVersion -> 7
                    else -> -1
                }

            }

            MegaMenu.BlockStyle.IsolateNavigationMenu -> 6
        }
    }

    inner class ProfileViewHolder(private val binding: MegaProfileItemV2Binding) :
        RecyclerView.ViewHolder(binding.root) {

        init {

            binding.ivHamburger.onClick(500) {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickView(
                        absoluteAdapterPosition,
                        view = this,
                        data = it
                    )
                }
            }
            binding.ivQrCode.onClick(500) {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickView(
                        absoluteAdapterPosition,
                        view = this,
                        data = it
                    )
                }
            }
            binding.ctlProfile.onClick(500) {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        it
                    )
                }
            }
        }

        fun bind(megaMenuBlock: MegaMenu.Block) {

            when (megaMenuBlock) {
                is MegaMenu.BlockProfile -> showProfileLayout(megaMenuBlock)
                is MegaMenu.BlockProfileLogin -> showLoginLayout()
                else -> {}
            }
        }

        private fun showLoginLayout() {
            binding.apply {
                ivAvatar.setImageResource(R.drawable.ic_user_default_avatar)
                tvTitleLogin.show()
                tvSubtitleLogin.hide()

                tvUsername.hide()
                tvLabelSubscription.hide()
            }
        }

        private fun showProfileLayout(menu: MegaMenu.BlockProfile) {
            binding.apply {
//                ivAvatar.setImageResource(R.drawable.ic_user_default_avatar)
                ivAvatar.load(menu.userAvatar)
                tvUsername.checkToShowContent(menu.userName, goneViewWhenNoText = true)
                tvUsername.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0)
                tvLabelSubscription.hide()

                tvTitleLogin.hide()
                tvSubtitleLogin.hide()
            }
        }
    }

    inner class MultiProfileViewHolder(private val binding: MegaMultiProfileItemV2Binding) :
        RecyclerView.ViewHolder(binding.root) {
        private val avatarSize by lazy {
            Utils.getSizeInPixel(
                context = binding.root.context,
                resId = R.dimen.mega_multi_profile_item_ava_size
            )
        }

        init {
            binding.avatarView.showRibbonKid = false
            binding.avatarView.showRibbonPrivate = false
            binding.ctlProfile.onClick(500) {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        it
                    )
                }
            }

            binding.ivHamburger.onClick(500) {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickView(
                        absoluteAdapterPosition,
                        view = this,
                        data = it
                    )
                }
            }
            binding.ivQrCode.onClick(500) {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickView(
                        absoluteAdapterPosition,
                        view = this,
                        data = it
                    )
                }
            }
        }

        fun bind(megaMenuBlock: MegaMenu.Block) {
            if (megaMenuBlock is MegaMenu.BlockMultiProfile) {
                binding.apply {
//                ivAvatar.setImageResource(R.drawable.ic_user_default_avatar)
                    avatarView.bindProfile(
                        url = megaMenuBlock.profileAvatar,
                        width = avatarSize,
                        height = avatarSize
                    )
                    tvUsername.checkToShowContent(
                        megaMenuBlock.profileName,
                        goneViewWhenNoText = true
                    )
                }
            }
        }


    }

    inner class BlockNavigationMenuHolder(private val binding: MegaMenuV3BlockBinding, private val isolated: Boolean) :
        RecyclerView.ViewHolder(binding.root) {

        private val menuAdapter by lazy {
            NavigationMenuAdapter(binding.root.context, isolated = isolated, sharedPreferences = sharedPreferences).apply {
                eventListener = megaMenuItemClickListener
            }
        }

        private val menuMarginTop by lazy {
            context.resources.getDimensionPixelSize(R.dimen.mega_block_item_margin_top)
        }
        init {
            binding.rvMenu.apply {
                adapter = menuAdapter
                layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
                if(isolated) {
                    addItemDecoration(MegaMenuItemDecoration(menuMarginTop))

                } else {
                    background =
                        ContextCompat.getDrawable(
                            binding.root.context,
                            R.drawable.mega_menu_background
                        )
                }
            }
        }

        fun bind(megaMenuBlock: MegaMenu.Block) {
            binding.apply {
                tvTitle.checkToShowContent(megaMenuBlock.title, goneViewWhenNoText = true)
                tvSubTitle.checkToShowContent(megaMenuBlock.subTitle, goneViewWhenNoText = true)
                menuAdapter.bind(megaMenuBlock.megaMenus)
                tvViewAll.hide()
            }
        }

    }

    inner class BlockGridSquareSmallHolder(private val binding: MegaMenuV3BlockBinding) :
        RecyclerView.ViewHolder(binding.root) {

        private val marginBetweenBlock by lazy {
            context.resources.getDimensionPixelSize(R.dimen.mega_app_margin_between)
        }
        private val appsAdapter by lazy {
            MegaAppV2Adapter(context).apply {
                eventListener = megaMenuItemClickListener
            }
        }

        init {
            binding.apply {
                rvMenu.apply {
                    adapter = appsAdapter
                    layoutManager = GridLayoutManager(
                        context,
                        MegaAppAdapter.APP_ITEMS_PER_ROW,
                        RecyclerView.VERTICAL,
                        false
                    )
                    addItemDecoration(
                        MegaAppGridItemDecoration(
                            MegaAppAdapter.APP_ITEMS_PER_ROW, marginBetweenBlock, includeEdge = false, includeHeader = true
                        )
                    )
                }

                tvViewAll.onClickDelay {
                    item(absoluteAdapterPosition)?.let { data ->
                        eventListener?.onClickView(absoluteAdapterPosition, this, data)

                    }
                }
            }

        }

        fun bind(menu: MegaMenu.Block) {
            binding.apply {
                tvTitle.checkToShowContent(menu.title, goneViewWhenNoText = true)
                tvSubTitle.checkToShowContent(menu.subTitle, goneViewWhenNoText = true)
                appsAdapter.bind(menu.megaMenus)
                // Currently hide view all
                tvViewAll.hide()
            }
        }

    }

    inner class BlockBannerHolder(private val binding: MegaMenuV3BlockBinding) :
        RecyclerView.ViewHolder(binding.root) {

        private val bannerAdapter by lazy {
            MegaBannerAdapter(context).apply {
                eventListener = megaMenuItemClickListener
            }
        }

        init {
            binding.apply {
                rvMenu.apply {
                    adapter = bannerAdapter
                    layoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)

                }

                tvViewAll.onClickDelay {
                    item(absoluteAdapterPosition)?.let { data ->
                        eventListener?.onClickView(absoluteAdapterPosition, this, data)

                    }
                }
            }

        }

        fun bind(menu: MegaMenu.Block) {
            Timber.tag("tam-mega").e("${this.javaClass.simpleName} bind $menu")
            binding.apply {
                tvTitle.checkToShowContent(menu.title, goneViewWhenNoText = true)
                tvSubTitle.checkToShowContent(menu.subTitle, goneViewWhenNoText = true)
                bannerAdapter.bind(menu.megaMenus)
                // Currently hide view all
                tvViewAll.hide()
            }
        }

    }

    inner class BlockLogoutHolder(private val binding: MegaAccountLogoutBlockBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.onClick(500) {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        it
                    )
                }
            }
        }

        fun bind(menu: MegaMenu.Block) {
            Timber.tag("tam-mega").i("${this.javaClass.simpleName} bind $menu")

        }

    }

    inner class BlockAppVersionHolder(private val binding: MegaAppVersionBlockBinding)  :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(menu: MegaMenu.Block) {
            Timber.tag("tam-mega").i("${this.javaClass.simpleName} bind $menu")
            binding.tvAppVersion.text = String.format(
                binding.root.context.getString(R.string.version_param),
                "${MainApplication.INSTANCE.appConfig.nameOs} ${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})"
            )
        }
    }

    inner class MegaMenuUnknownBlockViewHolder(binding: AllItemUnknownBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(data: BaseObject) {}
    }

}