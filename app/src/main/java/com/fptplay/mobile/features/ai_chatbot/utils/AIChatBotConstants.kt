package com.fptplay.mobile.features.ai_chatbot.utils

object AI<PERSON>hatBotConstants {
    const val AI_CHAT_BOX_ID_APP = "chat_bot_ai"
    const val DEFAULT_ERROR_CODE = 0
    const val AI_CHAT_BOX_REQUIRED_LOGIN_ERROR_CODE = 401
    // LogId
    const val AI_CHAT_BOX = "CHATBOT"
    const val AI_CHAT_BOX_LOG_EVENT_ENTER_CHAT_BOT = "EnterChatBot"
    const val AI_CHAT_BOX_LOG_EVENT_REQUEST_CHAT_BOT = "Request"
    const val AI_CHAT_BOX_LOG_EVENT_FEEDBACK_CHAT_BOT = "Feedback"
    // Type
    const val AI_CHAT_BOX_METADATA_TYPE_ENTER_CHAT = "chat-box-enter_chat"
    const val AI_CHAT_BOX_METADATA_TYPE_REQUEST = "chatbot_send_request"
    const val AI_CHAT_BOX_METADATA_TYPE_FEEDBACK = "chatbot_user_reaction"
    const val AI_CHAT_BOX_METADATA_KEY_TYPE= "type"
    const val AI_CHAT_BOX_METADATA_KEY_VALUE= "value"
    const val AI_CHAT_BOX_METADATA_KEY_STATUS= "status"
    const val AI_CHAT_BOX_METADATA_KEY_ITEM_NAME= "itemname"

    // Screen
    const val AI_CHAT_BOX_SCREEN = "Chatbot"
    const val AI_CHAT_BOX_SCREEN_DEFAULT = "General"
    //SubMenuId
    const val AI_CHAT_BOX_SUB_MENU_ID = "Chatbot"

    // IS_Comment
    const val AI_CHAT_BOX_IS_COMMENT = "2"


    //ERROR
    const val ERROR_CODE_NETWORK_ERROR = "NETWORK_ERROR"
    const val ERROR_CODE_AUTH_FAILED = "AUTH_FAILED"
}