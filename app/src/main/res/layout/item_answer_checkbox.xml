<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_survey_answer"
    android:layout_marginVertical="@dimen/_4sdp"
    android:paddingStart="@dimen/_8sdp"
    android:paddingEnd="0dp">

    <CheckBox
        android:id="@+id/check_box"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_46sdp"
        android:textColor="@color/white_87"
        android:textSize="@dimen/_11ssp"
        android:ellipsize="end"
        android:maxLines="2"
        android:paddingStart="@dimen/_11sdp"
        android:paddingEnd="@dimen/_11sdp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:fontFamily="@font/sf_pro_display_semibold"
        android:buttonTint="@color/survey_selector_answer_tint"
        android:gravity="center_vertical|start"
        android:textAlignment="viewStart"/>

</androidx.constraintlayout.widget.ConstraintLayout>
