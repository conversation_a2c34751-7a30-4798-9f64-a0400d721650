{"formatVersion": "1.1", "component": {"group": "androidx.media3", "module": "media3-exoplayer-workmanager", "version": "1.6.1.rc01", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.media3", "module": "media3-exoplayer", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-exoplayer-workmanager-1.6.1.rc01.aar", "url": "media3-exoplayer-workmanager-1.6.1.rc01.aar", "size": 17074, "sha512": "59d3bf232bb13ec6cbea8c7fc46502bea4263cc8deccd1f4a10b769cc2f6644fb9b24be37eecb41be3e0c67a3ef4b4d8f0bd270ec79a4affae2a167839c93a96", "sha256": "0f89004212def121bdc28938f552475158960eec8a22aec8194eab0cf6f5c4a7", "sha1": "2ca285d5fe2cade57a66e54b8ce3eb11df80ccc5", "md5": "710775bbd7051435be94f07eb6eecf1f"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "androidx.work", "module": "work-runtime", "version": {"requires": "2.8.1"}}, {"group": "androidx.media3", "module": "media3-exoplayer", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-exoplayer-workmanager-1.6.1.rc01.aar", "url": "media3-exoplayer-workmanager-1.6.1.rc01.aar", "size": 17074, "sha512": "59d3bf232bb13ec6cbea8c7fc46502bea4263cc8deccd1f4a10b769cc2f6644fb9b24be37eecb41be3e0c67a3ef4b4d8f0bd270ec79a4affae2a167839c93a96", "sha256": "0f89004212def121bdc28938f552475158960eec8a22aec8194eab0cf6f5c4a7", "sha1": "2ca285d5fe2cade57a66e54b8ce3eb11df80ccc5", "md5": "710775bbd7051435be94f07eb6eecf1f"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "media3-exoplayer-workmanager-1.6.1.rc01-sources.jar", "url": "media3-exoplayer-workmanager-1.6.1.rc01-sources.jar", "size": 3582, "sha512": "deffa4ac557c757b6783c3058712a53e8b0e32f01c083aff80b610812e1687ae372fc160b77c926de622fc17c9d29c944239d32d1b6d711c593c670208f57716", "sha256": "d56a934a669195befb19d4f1af40c8d24bef165ff4ef83b00edb784244700291", "sha1": "01c6a68530ae9590a2ded0a5b7e53b7b3e977dce", "md5": "0df83e26b3bec75e0e37060f1677eb09"}]}]}