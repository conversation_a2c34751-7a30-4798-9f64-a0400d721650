package com.fptplay.mobile.features.introduce

import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.common.SendSurvey
import com.xhbadxx.projects.module.domain.entity.fplay.common.SurveyQuestion
import com.xhbadxx.projects.module.domain.repository.fplay.CommonRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class IntroduceViewModel @Inject constructor(
) : BaseViewModel<IntroduceViewModel.IntroduceIntent, IntroduceViewModel.IntroduceState>() {

    sealed class IntroduceState : ViewState {
        data class Loading(val intent: IntroduceIntent? = null) : IntroduceState()
        data class Error(val message: String, val intent: IntroduceIntent? = null) : IntroduceState()
        data class ErrorNoInternet(val message: String, val intent: IntroduceIntent? = null) : IntroduceState()
        data class ErrorRequiredLogin(val message: String, val intent: IntroduceIntent? = null) : IntroduceState()
        data class Done(val intent: IntroduceIntent? = null) : IntroduceState()
        object Init : IntroduceState()
        // Add more states as needed for your survey feature
    }

    sealed class IntroduceIntent : ViewIntent {
        // Add more intents as needed for your survey feature
    }

    override fun dispatchIntent(intent: IntroduceIntent) {

    }

    override fun <T> Result<T>.reduce(
        intent: IntroduceIntent?,
        successFun: (Boolean, T) -> IntroduceState
    ): IntroduceState {
        return when (this) {
            is Result.Init -> IntroduceState.Loading(intent = intent)
            is Result.Success -> {
                successFun(this.isCached, this.successData)
            }
            is Result.UserError.RequiredLogin -> IntroduceState.ErrorRequiredLogin(message = this.message, intent = intent)
            is Result.Error -> {
                if (this is Result.Error.Intenet) {
                    IntroduceState.ErrorNoInternet(message = this.message, intent = intent)
                } else {
                    IntroduceState.Error(message = this.message, intent = intent)
                }
            }
            Result.Done -> IntroduceState.Done(intent = intent)
        }
    }
}
