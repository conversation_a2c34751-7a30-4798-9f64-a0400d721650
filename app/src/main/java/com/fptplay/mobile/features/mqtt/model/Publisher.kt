package com.fptplay.mobile.features.mqtt.model

import com.google.gson.annotations.SerializedName
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType

data class Publisher(
    @SerializedName("action")
    val action: String = "",
    @SerializedName("created_time")
    val createdTime: Long = 0,
    @SerializedName("is_retry")
    var isRetry: Int = 0,
    val uid: String = "",
    val contract: String = "",
    val netMode: String = "",
    @SerializedName("profile_id")
    val profileId: String = "",
    @SerializedName("app_ver")
    val appVer: String = "",
    @SerializedName("content_type")
    val contentType: String = "",
    @SerializedName("playlist_id")
    val playlistId: String = "",
    @SerializedName("chapter_id")
    val chapterId: String = "",
    @SerializedName("episode_id")
    val episodeId: String = "",
    @SerializedName("item_id")
    val itemId: String = "",
    @SerializedName("ref_playlist_id")
    val refPlaylistId: String = "",
    @SerializedName("ref_episode_id")
    val refEpisodeId: String = "",
    @SerializedName("ref_item_id")
    val refItemId: String = "",
    @SerializedName("data")
    val data : Data? = null,
    @SerializedName("app_source")
    val appSource: String,
    @SerializedName("is_link_drm")
    val isLinkDrm: String,
    @SerializedName("mqtt_mode")
    val mode: String,
    @SerializedName("source_provider")
    val sourceProvider: String,
    @SerializedName("drm_partner")
    val drmPartner: String,
    @SerializedName("business_plan")
    val businessPlan: String,
    @SerializedName("is_free")
    val isFree: String
) {
    @Transient
    var itemType: ItemType = ItemType.Unknown

    class Data(
        @SerializedName("code")
        val code: String = "",
        @SerializedName("msg")
        val message: Message? = null
    )

    class Message(
        @SerializedName("title")
        val title: String = "",
        @SerializedName("desc")
        val description: String = ""
    )
}
