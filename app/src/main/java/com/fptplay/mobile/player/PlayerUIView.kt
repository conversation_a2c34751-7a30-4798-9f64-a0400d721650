package com.fptplay.mobile.player

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.media.AudioManager
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.ScaleGestureDetector.SimpleOnScaleGestureListener
import android.view.Surface
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.SeekBar
import androidx.annotation.OptIn
import androidx.annotation.UiThread
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.graphics.drawable.toBitmap
import androidx.core.view.GestureDetectorCompat
import androidx.core.view.doOnAttach
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import coil.target.Target
import com.fplay.ads.logo_instream.ui.LogoInStreamContainer
import com.fplay.module.downloader.model.VideoTaskItem
import com.fplay.module.downloader.model.VideoTaskState
import com.fptplay.dial.connection.models.transfer_model.ActionEventType
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.ActivityExtensions.isAirlineLayout
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.extensions.setGradientDrawableFromHexColors
import com.fptplay.mobile.common.ui.view.PlayerVodSeekbarView
import com.fptplay.mobile.common.ui.view.TooltipsView
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.StringUtils
import com.fptplay.mobile.common.utils.viewutils.HorizontalItemDecoration
import com.fptplay.mobile.databinding.PlayerUiViewBinding
import com.fptplay.mobile.features.download.DownloadUtils
import com.fptplay.mobile.features.pairing_control.PairingControlConnectionHelper
import com.fptplay.mobile.features.pairing_control.Utils
import com.fptplay.mobile.features.pairing_control.model.RemotePlayerState
import com.fptplay.mobile.features.pladio.util.context
import com.fptplay.mobile.features.sport_interactive.utils.SportInteractiveUtils.checkShowSportInteractive
import com.fptplay.mobile.player.PlayerUtils.getIndexOf
import com.fptplay.mobile.player.adapter.PlayerBottomControlAdapter
import com.fptplay.mobile.player.config.PlayerConfig
import com.fptplay.mobile.player.handler.PlayerHandler
import com.fptplay.mobile.player.interfaces.IPlayerControl
import com.fptplay.mobile.player.model.PlayerBottomControlData
import com.fptplay.mobile.player.model.PlayerDataControlAddition
import com.fptplay.mobile.player.thumb.ThumbProxy
import com.fptplay.mobile.player.thumb.ThumbView
import com.fptplay.mobile.player.utils.PlayerConstants
import com.fptplay.mobile.player.utils.fadeIn
import com.fptplay.mobile.player.utils.fadeOut
import com.fptplay.mobile.player.utils.forceInvisible
import com.fptplay.mobile.player.utils.forceVisible
import com.fptplay.mobile.player.utils.getPhysicalScreenWidthWithOrientation
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.invisible
import com.fptplay.mobile.player.utils.moveLeftAndFadeIn
import com.fptplay.mobile.player.utils.moveRightAndFadeIn
import com.fptplay.mobile.player.utils.resizeHeightAnimation
import com.fptplay.mobile.player.utils.visible
import com.fptplay.mobile.player.views.DismissCallback
import com.fptplay.mobile.player.views.DoubleSeekOverlay
import com.fptplay.mobile.player.views.PlayerPlaylistOverlayView
import com.fptplay.mobile.player.views.PlaylistOverlayEventListener
import com.fptplay.mobile.vod.data.BuyPackageGuide
import androidx.media3.common.C
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.ui.AspectRatioFrameLayout
import com.tear.modules.player.exo.ExoPlayerProxy
import com.tear.modules.player.util.IPlayer
import com.tear.modules.player.util.PlayerControlView
import com.tear.modules.player.util.TrackType
import com.xhbadxx.projects.module.domain.entity.fplay.live.TvChannelDetail
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.Util
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.invisible
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.util.Locale
import java.util.Timer
import java.util.TimerTask
import kotlin.math.abs
import kotlin.math.ceil


@OptIn(UnstableApi::class)
class PlayerUIView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val TAG = this::class.java.simpleName

    //region Variables
    private val binding = PlayerUiViewBinding.inflate(LayoutInflater.from(context), this, true)
    private var viewLifecycleOwner: LifecycleOwner? = null

    private var useController = true

    private val playerBottomControlAdapter = PlayerBottomControlAdapter()

    //
    private var playerView: PlayerView? = null
    private var fragmentActivity: FragmentActivity? = null
    private var player: IPlayer? = null
    private val playerListener: Player.Listener by lazy { PlayerListener() }
    private var playerUIEvents: IPlayerControl? = null
    private val errorTime: Long
        get() = if (player is ExoPlayerProxy) C.TIME_UNSET else -1

    // Player Data Control
    private var playerConfig: PlayerConfig = PlayerConfig()
    private var playerDataControl: PlayerControlView.Data? = null
    private var playerDataControlAddition: PlayerDataControlAddition = PlayerDataControlAddition()
    private var details: Details? = null
    private var listNextVideo: List<Details.RelatedVod>? = null
    private var tvChannelDetail: TvChannelDetail? = null
    private var screenType: PlayerHandler.ScreenType? = null

    private var curEpisodeData: Details.Episode? = null

    // Thumb
    private val thumbnailHeight by lazy { resources?.getDimensionPixelSize(R.dimen._82sdp) ?: 68 }
    private val thumbnailWidth by lazy { resources?.getDimensionPixelSize(R.dimen._120sdp) ?: 120 }
    private val thumbnailHeightFullscreen by lazy {
        resources?.getDimensionPixelSize(R.dimen._104sdp) ?: 90
    }
    private val thumbnailWidthFullscreen by lazy {
        resources?.getDimensionPixelSize(R.dimen._160sdp) ?: 160
    }
    private var isRegThumbnail = true
    private var thumbLoadSuccess = false
    private val thumbProxy: ThumbProxy by lazy { ThumbProxy(context = context) }
    private val thumbData = mutableListOf<PlayerControlView.Data.Thumbnail>()
    private var thumbView: ThumbView? = null

    // View handler
    private var isUIHidden = false
    private var isUIShown = false
    private var uiTask: TimerTask? = null
    private var showUITask: TimerTask? = null
    private var lastInteractTimeMs = 0L

    // Progress
    private val delayTimeToUpdateProgress: Long = 500
    private val updateProgressRunnable: Runnable by lazy { Runnable { updateProgress() } }
    //endregion

    // Var for layout UI
    private var isFullscreen = false
    private var isLandscapeMode = false
    private var isBuffering = false
    private var isSeekBarDragging = false
    var playerControllerLocked = false
    private var isPlayingTimeShift = false
    private var isVodOffline = false
    private var isPiPMode = false
    private var titleForOffline = ""
    private val episodesForOffline = mutableListOf<VideoTaskItem>()
    private var curEpisodeOffline: VideoTaskItem? = null
    private var isRecommendVideoShow = false
    //

    //region Skip intro credits
    private var currentIntroFromMillis = 0
    private var currentStartContentMillis = 0
    private var currentEndContentMillis = 0
    //endregion

    //region Gestures control volume and brightness
    private var layoutParams: WindowManager.LayoutParams? = null
    private var currentBrightness = 0f
    private var currentVolumeLevel = 0
    private var mAudioManager: AudioManager? = null
    private var intLeft = false
    private var intRight = false
    private var diffX = 0
    private var diffY = 0
    private var downX = 0f
    private var downY = 0f
    //endregion

    //region Premiere
    private var detailsPremiere: com.xhbadxx.projects.module.domain.entity.fplay.premier.Details? =
        null
    //endregion

    //region Vip Trailer
    private var isPlayingVipTrailer = false
    //endregion

    //region Vip Trailer
    private var isShowSportInteractive = false
    //endregion

    //region Feature -> Hide UI & Hide Progress + Lock Icon
    private var playerUICanVisible = true
    private var playerProgressAndLockVisible = true
    //endregion

    //region Thumb - Hover Image
    private var isThumbShown = false
    //endregion

    private val tooltipsView by lazy { TooltipsView(context) }
    private var isShowTooltipTV: Boolean = true

    //region Pairing Control
    private var isSupportCast = true
    private val pairingConnection by lazy { MainApplication.INSTANCE.pairingConnectionHelper }
    //endregion Pairing Control


    //region Age restrictions
    private var ageRestrictionShowed = false
    private var ageRestrictionAnimationCompleted = false
    private var ageRestrictionValue = ""
    private var ageRestrictionAdvisories = ""
    private var ageRestrictionPosition = AgeRestrictionPosition.TOP_LEFT
    private var ageRestrictionDuration = 10000L
    private var isWarningContentsOnly = false

    private var handlerCheckShowAgeRestrictions: Handler? = null
    private var runnableCheckShowAgeRestrictions = Runnable {
        showAgeRestrictions()
    }

    private var handlerCheckHideAgeRestrictions: Handler? = null
    private var runnableCheckHideAgeRestrictions = Runnable {
        hideAgeRestrictions(force = false)
    }

    private var handlerShowNowAgeRestrictions: Handler? = null
    private var runnableShowNowAgeRestrictions = Runnable {
        showNowAgeRestrictions()
    }
    //endregion

    //region seekbar
    private val seekbarChangeListener by lazy {
        object : SeekBar.OnSeekBarChangeListener {
            private var mCurrentProgress = 0

            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                mCurrentProgress = progress
                if (fromUser && seekBar != null) {
                    // Update thumb position
                    updateThumbPosition()

                    Logger.d("PlayerUIView > onProgressChanged > progress > $progress > isThumbLoadSuccess: $thumbLoadSuccess")
                    thumbView?.setTime(
                        time = Util.convertTime(progress.toLong()),
                        totalTime = Util.convertTime(player?.totalDuration() ?: 0),
                        isLoadThumbSuccess = thumbLoadSuccess
                    )

                    if (thumbLoadSuccess) {
                        thumbView?.let { thumbView ->
                            getThumbData(currentTime = progress)?.let { thumbnail ->
                                ImageProxy.load(
                                    context = context,
                                    data = File(thumbnail.sheetPath),
                                    target = thumbView.getImageThumb(),
                                    fallback = object : Target {
                                        override fun onSuccess(result: Drawable) {
                                            viewLifecycleOwner?.lifecycleScope?.launch {
                                                withContext(Dispatchers.IO) {
                                                    thumbnail.frame.let { frame ->
                                                        val bitmap = Bitmap.createBitmap(
                                                            result.toBitmap(),
                                                            frame.x,
                                                            frame.y,
                                                            frame.width,
                                                            frame.height
                                                        )
                                                        withContext(Dispatchers.Main) {
                                                            thumbView.setThumbnail(bitmap = bitmap)
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                )
                            } ?: kotlin.run {
                                thumbView.setThumbnail(bitmap = null)
                            }
                        }
                    } else {
                        thumbView?.setThumbnail(null)
                    }
                } else {
                    binding.tvCurrentTime.text = Util.convertTime(progress.toLong())
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                isSeekBarDragging = true
                player?.run {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            pause()
                        }

                        else -> {}
                    }
                    updatePlayOrPause()
                    if (isRegThumbnail) {
                        removeThumbnailView()
                        binding.playerSeekProgress.let {
                            thumbView = ThumbView(context)
                            updateThumbPosition()
                            <EMAIL>(thumbView)
                        }
                    }
                }
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                isSeekBarDragging = false
                player?.run {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            play()
                        }

                        else -> {}
                    }
                    updatePlayOrPause()
                    removeThumbnailView()
                    binding.tvCurrentTime.text = Util.convertTime(mCurrentProgress.toLong())
                    playerUIEvents?.onSeek(mCurrentProgress.toLong())
                }
                updateProgress()
            }
        }
    }
    //endregion

    var isEnableLiveChat: Boolean
        set(enable) {
            binding.ibPlayerLiveChat.isEnabled = enable
        }
        get() {
            return binding.ibPlayerLiveChat.isEnabled
        }

    init {
        doOnAttach {
            initTouchViews()
            bindComponent()
            bindEvent()
            updateAllViews()
        }
    }

    //region Init views

    private fun bindComponent() {
        isUIHidden = !binding.playerLayoutDim.isVisible
        binding.playerSeekProgress.apply {
            setProgress(0)
            setSecondaryProgress(0)
            setMax(100)
        }
        mAudioManager =
            context?.applicationContext?.getSystemService(Context.AUDIO_SERVICE) as? AudioManager
        layoutParams = fragmentActivity?.window?.attributes
        binding.ibPlayerLiveChat.isEnabled = false
        binding.rcvBottomControl.apply {
            playerBottomControlAdapter.eventListener =
                object : IEventListener<PlayerBottomControlData> {
                    override fun onClickedItem(position: Int, data: PlayerBottomControlData) {
                        onBottomControlItemClicked(data)
                    }
                }
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            addItemDecoration(
                HorizontalItemDecoration(
                    spacingBetween = resources.getDimensionPixelSize(R.dimen.player_control_bottom_button_margin),
                    spacingEdge = 0
                )
            )
            adapter = playerBottomControlAdapter
        }
        context?.let {
            playerBottomControlAdapter.bind(
                PlayerUtils.generateDefaultPlayerBottomControlSettingList(
                    it
                )
            )
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun bindEvent() {
        binding.playerLayoutTouch.setOnTouchListener(gestureDetectorListener)

        binding.ibBack.setOnTouchListener(gestureDetectorListener)
        binding.ibBack.onClickDelay { playerUIEvents?.onBack() }

        binding.ibPlay.setOnTouchListener(gestureDetectorListener)
        binding.ibPlay.onClickDelay {
            playerUIEvents?.onPlayToggle()
            updatePlayOrPause()
        }

        binding.ibSeekNext10s.setOnTouchListener(gestureDetectorListener)
        binding.ibSeekNext10s.onClickDelay { playerUIEvents?.onSeekNext() }

        binding.ibSeekPrevious10s.setOnTouchListener(gestureDetectorListener)
        binding.ibSeekPrevious10s.onClickDelay { playerUIEvents?.onSeekPrevious() }

        binding.ibSkipNext.setOnTouchListener(gestureDetectorListener)
        binding.ibSkipNext.onClickDelay { playerUIEvents?.onNext() }

        binding.ibSkipPrevious.setOnTouchListener(gestureDetectorListener)
        binding.ibSkipPrevious.onClickDelay { playerUIEvents?.onPrevious() }

        binding.ibPlayerLock.setOnTouchListener(gestureDetectorListener)
        binding.ibPlayerLock.onClickDelay {
            handleOnLockButtonClick()
            playerUIEvents?.onLockToggle()
        }

        binding.ibPlayerLiveChat?.setOnTouchListener(gestureDetectorListener)
        binding.ibPlayerLiveChat?.onClickDelay { playerUIEvents?.onLiveChatClick() }

        binding.ibSportInteractive.setOnTouchListener(gestureDetectorListener)
        binding.ibSportInteractive.onClickDelay { playerUIEvents?.onSportInteractiveClick() }

        binding.ibMulticam.setOnTouchListener(gestureDetectorListener)
        binding.ibMulticam.onClickDelay { playerUIEvents?.onMulticam() }

        binding.ibCast.setOnTouchListener(gestureDetectorListener)
        binding.ibCast.onClickDelay { playerUIEvents?.onCast() }

        binding.ibExpand.setOnTouchListener(gestureDetectorListener)
        binding.ibExpand.onClickDelay { playerUIEvents?.onExpand() }

        binding.ibMore.setOnTouchListener(gestureDetectorListener)
        binding.ibMore.onClickDelay { playerUIEvents?.onMore(
            playerBottomControlAdapter.data()
        ) }

        binding.ibFullscreen.setOnTouchListener(gestureDetectorListener)
        binding.ibFullscreen.onClickDelay { playerUIEvents?.onFullScreenToggle() }

//        binding.ibSubtitle.setOnTouchListener(gestureDetectorListener)
//        binding.ibSubtitle.onClickDelay { playerUIEvents?.onAudioAndSubtitle() }
//
//        binding.ibSpeed.setOnTouchListener(gestureDetectorListener)
//        binding.ibSpeed.onClickDelay { playerUIEvents?.onPlayerSpeed() }
//
//        binding.ibSetting.setOnTouchListener(gestureDetectorListener)
//        binding.ibSetting.onClickDelay {
//            playerUIEvents?.onSetting()
//            tooltipsView.hide()
//            MainApplication.INSTANCE.sharedPreferences.setShouldShowTooltip(Constants.TOOLTIP_SETTING_PLAYER, shouldShow = false)
//        }
//
//        binding.ibSetting.apply {
//            setOnTouchListener { view, event ->
//                if (view.isPressed && event.action == MotionEvent.ACTION_UP) {
//                    if (event.eventTime - event.downTime >= 3000) {
//                        playerUIEvents?.onDebugViewStateChange()
//                        true
//                    } else false
//                } else false
//            }
//        }
//        binding.ibSetting.setOnLongClickListener {
//            playerUIEvents?.onDebugViewStateChange()
//            true
//        }

        binding.tvSkipIntro.setOnTouchListener(gestureDetectorListener)
        binding.tvSkipIntro.onClickDelay {
            playerUIEvents?.onSkipIntro()
            binding.tvSkipIntro.hide()
        }

        binding.tvWatchCredit.setOnTouchListener(gestureDetectorListener)
        binding.tvWatchCredit.onClickDelay {
            playerUIEvents?.onWatchCredit()
            binding.clSkipCredit.hide()
        }

        binding.flSkipCredit.setOnTouchListener(gestureDetectorListener)
        binding.flSkipCredit.onClickDelay {
            playerUIEvents?.onSkipCredit()
            binding.clSkipCredit.hide()
        }
        // report Vod
        binding.ibReport.setOnTouchListener(gestureDetectorListener)
        binding.ibReport.onClickDelay { playerUIEvents?.onReport() }
        // Recommend next video
        binding.layoutRecommendPortrait.root.onClickDelay {
        }
        binding.layoutRecommendPortrait.ibClose.onClickDelay {
            playerUIEvents?.onRecommendClose()
            showNextVideoRecommendation(isShow = false)
        }
        binding.layoutRecommendPortrait.btnWatchNow.onClickDelay {
            playerUIEvents?.onRecommendWatchNow(related = getFirstRelatedVideo())
            showNextVideoRecommendation(isShow = false)
        }
        binding.layoutRecommendPortrait.btnPlayTrailer.onClickDelay {
            playerUIEvents?.onRecommendPlayTrailer(related = getFirstRelatedVideo())
            showNextVideoRecommendation(isShow = false)
        }

        binding.layoutRecommendLandscape.root.onClickDelay {

        }
        binding.layoutRecommendLandscape.ibClose.onClickDelay {
            playerUIEvents?.onRecommendClose()
            showNextVideoRecommendation(isShow = false)
        }
        binding.layoutRecommendLandscape.btnWatchNow.onClickDelay {
            playerUIEvents?.onRecommendWatchNow(related = getFirstRelatedVideo())
            showNextVideoRecommendation(isShow = false)
        }
        binding.layoutRecommendLandscape.btnPlayTrailer.onClickDelay {
            playerUIEvents?.onRecommendPlayTrailer(related = getFirstRelatedVideo())
            showNextVideoRecommendation(isShow = false)
        }
        binding.layoutRecommendLandscape.ibBack.onClickDelay { playerUIEvents?.onBack() }
        //

        binding.playerSeekProgress.setOnTouchListener(gestureDetectorListener)
        binding.playerSeekProgress.setOnSeekBarChangeListener(seekbarChangeListener)

        player?.internalPlayer()?.let {
            when (it) {
                is ExoPlayer -> {
                    it.addListener(playerListener)
                }
            }
        }

        binding.buyPackageGuide.tvBuyNow.onClickDelay {
            playerUIEvents?.onClickBuyPackage()
        }

        binding.buyPackageGuide.ivClose.onClickDelay {
            playerUIEvents?.hideBuyPackageGuide()
        }

    }

    private fun removeThumbnailView() {
        try {
            thumbView?.run {
                <EMAIL>(this)
            }
            thumbView = null
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    //endregion

    //region Overrides
    override fun onAttachedToWindow() {
        super.onAttachedToWindow()

        // Pairing Control
        MainApplication.INSTANCE.pairingConnectionHelper.addPlayerStateListener(listener = remotePlayerState)
        //
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        removeCallbacks(updateProgressRunnable)
        // Pairing Control
        MainApplication.INSTANCE.pairingConnectionHelper.removePlayerStateListener(listener = remotePlayerState)
        //
        // Age Restrictions
        removeShowAgeRestrictionsHandler()
        removeHideAgeRestrictionsHandler()
        removeShowNowAgeRestrictionsHandler()
        //
    }
    //endregion


    fun initPlayerUI(
        playerView: PlayerView,
        fragmentActivity: FragmentActivity?,
        player: IPlayer,
        playerUIEvents: IPlayerControl,
        lifecycleOwner: LifecycleOwner?,
        useController: Boolean,
        screenType: PlayerHandler.ScreenType
    ) {
        this.playerView = playerView
        this.fragmentActivity = fragmentActivity
        this.player = player
        this.playerUIEvents = playerUIEvents
        this.viewLifecycleOwner = lifecycleOwner
        this.useController = useController
        this.screenType = screenType
        if (!useController) binding.root.hide()
        binding.doubleSeekOverLay.apply {
            setPlayer(player = playerView)
            setPlayerTouch(playerLayoutTouch = binding.playerLayoutTouch)
            setDoubleTapGestureListener(doubleTapGestureListener = gestureListener)
            updateScreenType(screenType = screenType)
            performListener(object : DoubleSeekOverlay.PerformListener {
                override fun onAnimationStart() {
                    hideUI()
                    binding.doubleSeekOverLay.show()
                }

                override fun onAnimationEnd() {
                    binding.doubleSeekOverLay.hide()
                }
            })
        }
    }

    fun setPlayerUIViewVisible(isVisible: Boolean) {
        this.playerUICanVisible = isVisible
        binding.doubleSeekOverLay.setPlayerUIViewVisible(isVisible = isVisible)
        if (!playerUICanVisible) {
            hideUI()
        }
    }

    fun setPlayerProgressAndLockVisible(isVisible: Boolean) {
        this.playerProgressAndLockVisible = isVisible
    }

    fun updateListNextVideo(data: List<Details.RelatedVod>) {
        this.listNextVideo = data
    }

    fun updateScreenType(screenType: PlayerHandler.ScreenType) {
        this.screenType = screenType
    }

    /**
     * Set a custom seekbar view. When set, the default playerSeekProgress will be hidden
     * and all progress updates will be redirected to the custom seekbar.
     * @param customSeekBarView The custom PlayerVodSeekbarView to use, or null to use default seekbar
     */
    @SuppressLint("ClickableViewAccessibility")
    fun setCustomSeekBarView(customSeekBarView: PlayerVodSeekbarView?) {
        Logger.d("$TAG > setCustomSeekBarView: customSeekBarView=$customSeekBarView")
        // Remove listener from previous custom seekbar
        this.customSeekBarView?.setOnSeekBarChangeListener(null)

        this.customSeekBarView = customSeekBarView
        this.isUsingCustomSeekBar = customSeekBarView != null

        Logger.d("$TAG > setCustomSeekBarView: isUsingCustomSeekBar=$isUsingCustomSeekBar")

        if (isUsingCustomSeekBar) {
            // Hide default seekbar
            binding.playerSeekProgress.hide()
            Logger.d("$TAG > setCustomSeekBarView: hiding default seekbar, setting touch listener on custom seekbar")

            // Set up listener on custom seekbar
            this.customSeekBarView?.setOnTouchListener(gestureDetectorListener)
            this.customSeekBarView?.setOnSeekBarChangeListener(seekbarChangeListener)

            // Initialize custom seekbar with current values
            this.customSeekBarView?.setProgress(binding.playerSeekProgress.getProgress())
            this.customSeekBarView?.setSecondaryProgress(binding.playerSeekProgress.getSecondaryProgress())
            this.customSeekBarView?.setMax(binding.playerSeekProgress.getMax())
        } else {
            // Show default seekbar
            binding.playerSeekProgress.show()
            binding.playerSeekProgress.setOnTouchListener(gestureDetectorListener)
            Logger.d("$TAG > setCustomSeekBarView: showing default seekbar, setting touch listener on default seekbar")
            this.customSeekBarView = null
        }
    }

    /**
     * Get the currently active seekbar (custom or default)
     */
    private fun getActiveSeekBar(): PlayerVodSeekbarView {
        return if (isUsingCustomSeekBar) {
            customSeekBarView ?: binding.playerSeekProgress
        } else {
            binding.playerSeekProgress
        }
    }

    /**
     * Set progress on the active seekbar
     */
    private fun setActiveSeekBarProgress(progress: Int) {
        Logger.d("$TAG > setActiveSeekBarProgress: $progress")
        getActiveSeekBar().setProgress(progress)
    }

    /**
     * Set secondary progress on the active seekbar
     */
    private fun setActiveSeekBarSecondaryProgress(progress: Int) {
        getActiveSeekBar().setSecondaryProgress(progress)
    }

    /**
     * Set max value on the active seekbar
     */
    private fun setActiveSeekBarMax(max: Int) {
        getActiveSeekBar().setMax(max)
    }

    /**
     * Get progress from the active seekbar
     */
    private fun getActiveSeekBarProgress(): Int {
        return getActiveSeekBar().getProgress()
    }

    /**
     * Get max value from the active seekbar
     */
    private fun getActiveSeekBarMax(): Int {
        return getActiveSeekBar().getMax()
    }

    fun isShowingFrameOverlay(): Boolean {
        return binding.flOverlay.isVisible
    }

    fun closeFrameOverlayView(type: PlayerConstants.PlayerFrameOverlayType?) {
        if(binding.flOverlay.isVisible) {
            closePlaylistOverlayView()
            playerUIEvents?.onCloseFrameOverlay(type)
        }
    }

    fun updateIsPlayingTimeShift(isPlayingTimeshift: Boolean) {
        this.isPlayingTimeShift = isPlayingTimeshift
        binding.doubleSeekOverLay.updateIsPlayingTimeShift(isPlayingTimeShift = isPlayingTimeshift)
        updateAllViews()
    }

    fun updatePlayerSpeed(speed: Float) {
        playerDataControlAddition.playerSpeed = speed
    }

    fun updatePlayerVideoQualityName(qualityName: String) {
        playerDataControlAddition.playerBitrateName = qualityName
    }

    fun updateIsVodOffline(isVodOffline: Boolean) {
        this.isVodOffline = isVodOffline
    }

    fun updatePlayerTitleForOffline(title: String) {
        this.titleForOffline = title
    }

    fun updatePlayerEpisodeForOffline(episodes: List<VideoTaskItem>) {
        this.episodesForOffline.clear()
        this.episodesForOffline.addAll(episodes)
    }

    fun updateCurrentVODDataOffline(curEpisode: VideoTaskItem) {
        this.curEpisodeOffline = curEpisode
    }

    fun prepareThumb(episode: Details.Episode) {
        thumbData.clear()
        thumbProxy.bind(
            inforUrl = episode.thumbnailUrl,
            baseUrl = episode.thumbnailUrl.substring(0, episode.thumbnailUrl.lastIndexOf('/') + 1),
            coroutineScope = viewLifecycleOwner?.lifecycleScope,
            eventsListener = object : ThumbProxy.EventsListener {
                override fun onSuccess(data: List<PlayerControlView.Data.Thumbnail>) {
                    thumbLoadSuccess = true
                    thumbData.clear()
                    thumbData.addAll(data)
                }

                override fun onError(reason: String) {
                    thumbLoadSuccess = false
                }
            }
        )
    }

    fun prepareDataPlayerControl(
        playerConfig: PlayerConfig? = null,
        details: Details? = null,
        tvChannelDetail: TvChannelDetail? = null,
        detailsPremiere: com.xhbadxx.projects.module.domain.entity.fplay.premier.Details? = null,
        dataPlayerControl: PlayerControlView.Data
    ) {
        playerConfig?.let { this.playerConfig = it }
        this.details = details
        this.tvChannelDetail = tvChannelDetail
        this.detailsPremiere = detailsPremiere
        this.playerDataControl = dataPlayerControl
    }

    fun updateDataPlayerControl(playerDataControl: PlayerControlView.Data) {
        this.playerDataControl = playerDataControl
        updateButtonAudioAndSubtitle()
    }

    fun updateCurrentVODData(curEpisode: Details.Episode) {
        this.curEpisodeData = curEpisode
    }

    fun updateCurrentVodIndex(index: Int) {
        details?.blockEpisode?.episodes?.let {
            if (index >= 0 && index < it.size) {
                this.curEpisodeData = it[index]
                updateCenterControl()
            }
        }
    }

    fun updatePlayingVipTrailer(isPlayingVipTrailer: Boolean) {
        this.isPlayingVipTrailer = isPlayingVipTrailer
    }

    fun updateShowSportInteractiveButton(isShow: Boolean) {
        this.isShowSportInteractive = isShow
        updateSportInteractiveButton()
    }

    fun updatePiPMode(isPiPMode: Boolean) {
        this.isPiPMode = isPiPMode
        if (isPiPMode) {
            hideUI()
            //
            binding.tvPlayerVolumeBrightness.hide()
        }
    }

    fun updateLayouts(isFullscreen: Boolean, isLandscapeMode: Boolean) {
        this.isFullscreen = isFullscreen
        this.isLandscapeMode = isLandscapeMode
        updateAllViews()
        changeLayoutRecommendNextVideo()
        updateGuidelineSkipIntroCredit()
        updateAgeRestrictionGuideline()
    }

    fun resetBrightness() {
        // Reset brightness
        try {
            layoutParams?.screenBrightness = -1f
            fragmentActivity?.window?.attributes = layoutParams
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    fun updateOverlayLogo(url: String?) {
        Handler(Looper.getMainLooper()).postDelayed({
            if (url.isNullOrBlank()) {
                binding.ivOverlayLogo.hide()
            } else {
                if (!isThumbShown) {
                    binding.ivOverlayLogo.show()
                    if (isVodOffline) {
                        ImageProxy.loadLocal(
                            context = binding.root.context,
                            data = url,
                            width = 1920,
                            height = 1080,
                            target = binding.ivOverlayLogo
                        )
                    } else {
                        ImageProxy.load(
                            context = binding.root.context,
                            url = url,
                            width = 1920,
                            height = 1080,
                            target = binding.ivOverlayLogo
                        )
                    }
                } else {
                    binding.ivOverlayLogo.hide()
                }
            }
        }, 600)
    }

    fun updateSkipIntroData(
        currentIntroFromMillis: Int,
        currentStartContentMillis: Int,
        currentEndContentMillis: Int
    ) {
        this.currentIntroFromMillis = currentIntroFromMillis
        this.currentStartContentMillis = currentStartContentMillis
        this.currentEndContentMillis = currentEndContentMillis

        // Reset UI
        binding.tvSkipIntro.hide()
    }

    fun canAutoNextEpisode(): Boolean {
        if (screenType == PlayerHandler.ScreenType.Live) return false
        if (screenType == PlayerHandler.ScreenType.Premiere) return false
        if (isPlayingVipTrailer) return false
        if (playerConfig.isPlayPreview) return false
        if (isVodOffline) {
            return if (episodesForOffline.size < 2) {
                false
            } else {
                when (episodesForOffline.indexOf(curEpisodeOffline)) {
                    -1 -> false
                    0 -> true
                    episodesForOffline.size - 1 -> false
                    else -> true
                }
            }
        }
        val listEpisode = details?.blockEpisode?.episodes ?: listOf()
        return if (listEpisode.size < 2) {
            false
        } else {
            when (listEpisode.getIndexOf(curEpisodeData)) {
                -1 -> false
                0 -> true
                listEpisode.size - 1 -> false
                else -> true
            }
        }
    }

    fun isShowNextVideoRecommendation(): Details.RelatedVod? {
        if (screenType == PlayerHandler.ScreenType.Live) return null
        if (screenType == PlayerHandler.ScreenType.Premiere) return null
        if (isPlayingVipTrailer) return null
        if (playerConfig.isPlayPreview) return null
        val listEpisode = details?.blockEpisode?.episodes ?: listOf()
        return if (listEpisode.isNotEmpty() && listEpisode.getIndexOf(curEpisodeData) == listEpisode.size - 1) {
            val listRelated = listNextVideo ?: details?.blockRelated?.relateds ?: listOf()
            if (listRelated.isNotEmpty()) {
                val firstItem = listRelated.first()
                if (firstItem.relatedType != Details.RelatedVodType.UNKNOWN) {
                    firstItem
                } else null
            } else null
        } else {
            null
        }
    }

    fun getNextEpisodeOffline(): Pair<Int, VideoTaskItem>? {
        val index = episodesForOffline.indexOf(curEpisodeOffline)
        var increase = 1
        for (i in (index + increase)..episodesForOffline.size - 1) {
            val episode = episodesForOffline[i]
            if (episode.taskState == VideoTaskState.SUCCESS && DownloadUtils.calculateTimeLeft(
                    episode.lastUpdateTime,
                    episode.expiredTime
                ) > 0
            ) {
                break
            } else {
                increase += 1
            }
        }
        if ((index != -1) && (index + increase in episodesForOffline.indices)) {
            return Pair(index + increase, episodesForOffline[index + increase])
        }
        return null
    }

    fun getPreviousEpisodeOffline(): Pair<Int, VideoTaskItem>? {
        val index = episodesForOffline.indexOf(curEpisodeOffline)
        var decrease = 1
        for (i in (index - decrease) downTo 0) {
            val episode = episodesForOffline[i]
            if (episode.taskState == VideoTaskState.SUCCESS && DownloadUtils.calculateTimeLeft(
                    episode.lastUpdateTime,
                    episode.expiredTime
                ) > 0
            ) {
                break
            } else {
                decrease -= 1
            }
        }
        if ((index != -1) && (index - decrease in episodesForOffline.indices)) {
            return Pair(index - decrease, episodesForOffline[index - decrease])
        }
        return null
    }

    fun getExactEpisodeOffline(videoTaskItem: VideoTaskItem?): Pair<Int, VideoTaskItem>? {
        val index = episodesForOffline.indexOf(videoTaskItem)
        var decrease = 1
        for (i in (index - decrease) downTo 0) {
            val episode = episodesForOffline[i]
            if (episode.taskState == VideoTaskState.SUCCESS && DownloadUtils.calculateTimeLeft(
                    episode.lastUpdateTime,
                    episode.expiredTime
                ) > 0
            ) {
                break
            } else {
                decrease -= 1
            }
        }
        if ((index != -1) && (index - decrease in episodesForOffline.indices)) {
            return Pair(index - decrease, episodesForOffline[index - decrease])
        }
        return null
    }


    fun getFirstRelatedVideo(): Details.RelatedVod? {
        val listRelated = listNextVideo ?: details?.blockRelated?.relateds ?: listOf()
        return if (listRelated.isNotEmpty()) {
            listRelated.first()
        } else null
    }

    private fun onBottomControlItemClicked(data: PlayerBottomControlData) {
        when (data._id) {
            PlayerConstants.PlayerBottomControlType.ID_SUBTITLE_SETTING -> {
                playerUIEvents?.onOpenSubtitleSelection()
            }

            PlayerConstants.PlayerBottomControlType.ID_AUDIO_SETTING -> {
                playerUIEvents?.onOpenAudioSelection()
            }

            PlayerConstants.PlayerBottomControlType.ID_VIDEO_SETTING -> {
                playerUIEvents?.onSetting()
                tooltipsView.hide()
                MainApplication.INSTANCE.sharedPreferences.setShouldShowTooltip(
                    Constants.TOOLTIP_SETTING_PLAYER,
                    shouldShow = false
                )

            }

            PlayerConstants.PlayerBottomControlType.ID_PLAY_SPEED_SETTING -> {
                playerUIEvents?.onPlayerSpeed()
            }

            PlayerConstants.PlayerBottomControlType.ID_PLAYLIST_SETTING -> {
                openPlaylistOverlayView()
            }

            PlayerConstants.PlayerBottomControlType.ID_CHANNEL_LIST_SETTING -> {
                // TODO: Handle channel list setting click
            }

            PlayerConstants.PlayerBottomControlType.ID_EPG_SETTING -> {
                // TODO: Handle EPG setting click
            }
        }
    }

    fun showSkipCredit(isShow: Boolean) {
        if (isShow && isForceHideSkipIntroCredit()) return
        binding.clSkipCredit.isVisible = isShow
    }

    fun updateTextSkipCredit(progress: Int) {
        binding.pbSkipCredit.progress = progress
    }

    fun updateIsSupportCast(isSupport: Boolean) {
        isSupportCast = isSupport
        updateAllViews()
    }

    private fun updateCastButtonState() {
        binding.ibCast.isSelected = MainApplication.INSTANCE.pairingConnectionHelper.isConnected
    }

    private fun updateCastTitle() {
        binding.tvCastTitle.apply {
            val remoteData = MainApplication.INSTANCE.pairingConnectionHelper.getRemoteData()
            remoteData?.let {
                text = if (!Utils.isSessionRunning()) {
                    context.getString(
                        R.string.pairing_cast_title_player_stop_toast,
                        it.receiverName
                    )
                } else {
                    context.getString(R.string.pairing_cast_title_player, it.receiverName)
                }
            } ?: kotlin.run {
                text = context.getString(R.string.pairing_cast_title_player, "")
            }
        }
        if (!MainApplication.INSTANCE.pairingConnectionHelper.isConnected) {
            binding.tvCastTitle.isVisible = false
        }
    }

    private fun updateGuidelineSkipIntroCredit() {
        if (isFullscreen) {
            if (context.isTablet()) {
                binding.guidelineSkipIntro.setGuidelinePercent(0.7f)
                binding.guidelineSkipCredit.setGuidelinePercent(0.7f)
            } else {
                binding.guidelineSkipIntro.setGuidelinePercent(0.6f)
                binding.guidelineSkipCredit.setGuidelinePercent(0.6f)
            }
        } else {
            if (context.isTablet()) {
                binding.guidelineSkipIntro.setGuidelinePercent(0.8f)
                binding.guidelineSkipCredit.setGuidelinePercent(0.8f)
            } else {
                binding.guidelineSkipIntro.setGuidelinePercent(0.7f)
                binding.guidelineSkipCredit.setGuidelinePercent(0.7f)
            }
        }
    }

    private fun changeLayoutRecommendNextVideo() {
        if (isRecommendVideoShow) {
            showRecommendLayout()
        }
    }

    private fun showRecommendLayout() {
        if (isForceHideRecommend()) return
        if (isFullscreen) {
            binding.layoutRecommendLandscape.root.show()
            binding.layoutRecommendPortrait.root.hide()
        } else {
            binding.layoutRecommendLandscape.root.hide()
            binding.layoutRecommendPortrait.root.show()
        }
    }

    fun showNextVideoRecommendation(isShow: Boolean, related: Details.RelatedVod? = null) {
        if (isShow && isForceHideRecommend()) return
        isRecommendVideoShow = isShow
        if (isShow) {
            hideUI()
            showRecommendLayout()
            related?.run {
                binding.layoutRecommendLandscape.tvTitle.text = this.titleVietnam
                binding.layoutRecommendPortrait.tvTitle.text = this.titleVietnam
                binding.layoutRecommendLandscape.tvVideoInfo.let {
                    it.text = StringUtils.getMetaText(context = binding.root.context, data = this)
                }
                binding.layoutRecommendLandscape.tvDescription.let {
                    it.text = this.des
                }
                ImageProxy.load(
                    context = binding.layoutRecommendLandscape.root.context,
                    url = this.horizontalImage,
                    width = binding.layoutRecommendLandscape.root.context.resources.getDimensionPixelSize(
                        R.dimen.player_recommend_thumb_width_landscape
                    ),
                    height = binding.layoutRecommendLandscape.root.context.resources.getDimensionPixelSize(
                        R.dimen.player_recommend_thumb_height_landscape
                    ),
                    target = binding.layoutRecommendLandscape.ivThumb
                )
                ImageProxy.load(
                    context = binding.layoutRecommendPortrait.root.context,
                    url = this.horizontalImage,
                    width = binding.layoutRecommendPortrait.root.context.resources.getDimensionPixelSize(
                        R.dimen.player_recommend_thumb_width_portrait
                    ),
                    height = binding.layoutRecommendPortrait.root.context.resources.getDimensionPixelSize(
                        R.dimen.player_recommend_thumb_height_portrait
                    ),
                    target = binding.layoutRecommendPortrait.ivThumb
                )
                if (related.relatedType == Details.RelatedVodType.VOD_PLAYLIST) {
                    val totalPlaylist = related.playlistTotal.plus(" VIDEOS")
                    binding.layoutRecommendPortrait.itemTotalPlaylist.root.show()
                    binding.layoutRecommendPortrait.itemTotalPlaylist.tvPlaylist.text =
                        totalPlaylist
                    binding.layoutRecommendLandscape.itemTotalPlaylist.root.show()
                    binding.layoutRecommendLandscape.itemTotalPlaylist.tvPlaylist.text =
                        totalPlaylist
                    ImageProxy.load(
                        context = binding.layoutRecommendPortrait.itemTotalPlaylist.root.context,
                        url = MainApplication.INSTANCE.appConfig.type2,
                        width = binding.root.context.resources.getDimensionPixelSize(R.dimen.item_horizontal_slider_playlist_width),
                        height = binding.root.context.resources.getDimensionPixelSize(R.dimen._10sdp),
                        target = binding.layoutRecommendPortrait.itemTotalPlaylist.ivPlaylist,
                    )
                    ImageProxy.load(
                        context = binding.layoutRecommendLandscape.itemTotalPlaylist.root.context,
                        url = MainApplication.INSTANCE.appConfig.type2,
                        width = binding.root.context.resources.getDimensionPixelSize(R.dimen.item_horizontal_slider_playlist_width),
                        height = binding.root.context.resources.getDimensionPixelSize(R.dimen._10sdp),
                        target = binding.layoutRecommendLandscape.itemTotalPlaylist.ivPlaylist,
                    )
                } else {
                    binding.layoutRecommendPortrait.itemTotalPlaylist.root.hide()
                    binding.layoutRecommendLandscape.itemTotalPlaylist.root.hide()
                }
            }
        } else {
            binding.layoutRecommendLandscape.root.hide()
            binding.layoutRecommendPortrait.root.hide()
        }
    }

    fun isNextVideoRecommendationShow() = isRecommendVideoShow

    fun showRecommendButtonTrailer(isShow: Boolean) {
        binding.layoutRecommendPortrait.btnPlayTrailer.isVisible = isShow
        binding.layoutRecommendLandscape.btnPlayTrailer.isVisible = isShow
    }

    fun updateTextWatchNowRecommendation(text: String) {
        binding.layoutRecommendPortrait.tvWatchNow.text = text
        binding.layoutRecommendLandscape.tvWatchNow.text = text
    }

    fun updateTextPlayTrailerRecommendation(text: String) {
        binding.layoutRecommendPortrait.tvPlayTrailer.text = text
        binding.layoutRecommendLandscape.tvPlayTrailer.text = text
    }

    fun showPlayerUIView(delay: Long = 0L) {
        lastInteractTimeMs = System.currentTimeMillis()
        if (delay != 0L) {
            startShowUITask(delay = delay)
        } else {
            showUI()
            startUIHidingTask()
        }
        updateAllViews()
    }

    fun onShowThumb(isShow: Boolean) {
        this.isThumbShown = isShow
        binding.doubleSeekOverLay.updateThumbShown(isThumbShown = isShow)
        if (isShow) {
            updateOverlayLogo(url = null)
        }
    }

    //region Player Events
    fun onBandwidth(message: String) {
        Logger.d("$TAG onBandwidth")
        isBuffering = false
    }

    fun onPrepare() {
        Logger.d("$TAG onPrepare")
        isBuffering = true
    }

    fun onStart() {
        Logger.d("$TAG onStart")
        isBuffering = false
        if (!playerControllerLocked) {
            if (screenType == PlayerHandler.ScreenType.Vod) {
                binding.ibPlay.forceVisible()
            }
        }
        updateAllViews()
    }

    fun onBuffering() {
        Logger.d("$TAG onBuffering")
        isBuffering = true
        if (!playerControllerLocked) {
            binding.ibPlay.forceInvisible()
        }
        updatePreviousNextEpisode()
    }

    fun onReady() {
        Logger.d("$TAG onReady")
        isBuffering = false
        if (!playerControllerLocked) {
            if (screenType == PlayerHandler.ScreenType.Vod) {
                binding.ibPlay.forceVisible()
            }
            updateAllViews()
        }
    }

    fun onStop() {
        Logger.d("$TAG onStop")
        isBuffering = false
    }

    fun onEnd() {
        Logger.d("$TAG onEnd")
        isBuffering = false
        showUI()
        updateAllViews()
    }

    fun onError(code: Int, name: String, detail: String) {
        Logger.d("$TAG onError")
        isBuffering = false
//        showUI()
    }
    //endregion


    fun isVisible() = isUIShown

    private fun isShowButtonMore(): Boolean = !isFullscreen

    private fun updateAllViews() {
        Logger.d("$TAG > updateAllViews")
        updateLockButtonUI(playerControllerLocked)
        updateFrameLayoutOverlay()
        updateTopControl()
        updateCenterControl()
        updateBottomControl()
        updateButtonAudioAndSubtitle()
        updateCastState()
    }

    private fun updateTopControl() {
        if (playerControllerLocked) {
            binding.clTopControl.hide()
            return
        }
        binding.clTopControl.show()

        player?.run {
            if (isFullscreen) {
                binding.tvTitle.show()
                binding.ibMore.hide()
                if(isPlayingVipTrailer || isThumbShown) {
                    binding.ibBack.show()
                } else {
                    binding.ibBack.invisible()
                }
                binding.ibExpand.show()
            } else {
                binding.tvTitle.hide()
                binding.ibMore.show()
                binding.ibBack.show()
                binding.ibExpand.hide()
            }
        }

        if (isThumbShown) {
            binding.tvTitle.isVisible = isFullscreen
            binding.llTopControl.hide()
        } else {
            binding.llTopControl.show()
        }
        if (MainApplication.INSTANCE.pairingConnectionHelper.isConnected && !isSupportCast && !isPlayingVipTrailer) {
            binding.tvTitle.isVisible = isFullscreen
            binding.llTopControl.hide()
        }
        when (screenType) {
            is PlayerHandler.ScreenType.Live -> {
                binding.tvTitle.text = tvChannelDetail?.name ?: ""
                binding.ibMulticam.isVisible = false
                binding.ibCast.isVisible = isShowCastButton()
                binding.ibMore.isVisible = isShowButtonMore()
                binding.ibReport.isVisible = isShowReportButton()

            }

            is PlayerHandler.ScreenType.Premiere -> {
                binding.tvTitle.text = detailsPremiere?.title ?: ""
                binding.ibMulticam.isVisible = false // detailsPremiere?.hasMulticam == true
                binding.ibCast.isVisible = isShowCastButton()
                binding.ibMore.isVisible = isShowButtonMore()
                binding.ibReport.isVisible = isShowReportButton()

            }

            is PlayerHandler.ScreenType.Vod -> {
                val title = if (isVodOffline) {
                    this.titleForOffline
                } else {
                    if (details?.blockContent?.episodeType == 0) {
                        details?.blockContent?.titleVietnam ?: ""
                    } else {
                        if (curEpisodeData == null) details?.blockContent?.titleVietnam
                            ?: "" else fragmentActivity?.getString(
                            R.string.vod_title_pattern,
                            details?.blockContent?.titleVietnam ?: "",
                            curEpisodeData?.titleVietnam ?: ""
                        )
                    }
                }
                binding.tvTitle.text = title
                if (!isVodOffline) {
                    binding.ibMulticam.isVisible = false
                    binding.ibCast.isVisible = isShowCastButton()
                    binding.ibMore.isVisible = isShowButtonMore()
                    binding.ibReport.isVisible = isShowReportButton()

                } else {
                    binding.ibMulticam.isVisible = false
                    binding.ibCast.isVisible = false
                    binding.ibMore.isVisible = isShowButtonMore()
                    binding.ibReport.isVisible = false

                }
            }

            else -> {}
        }

        if (isFullscreen) {
            val clTopControlLP = binding.clTopControl.layoutParams as? MarginLayoutParams
            clTopControlLP?.marginEnd =
                resources.getDimensionPixelSize(R.dimen.player_control_margin_parent)
            binding.clTopControl.layoutParams = clTopControlLP
        } else {
            val clTopControlLP = binding.clTopControl.layoutParams as? MarginLayoutParams
            clTopControlLP?.marginEnd = resources.getDimensionPixelSize(R.dimen.margin_common)
            binding.clTopControl.layoutParams = clTopControlLP
        }
    }

    private fun updateFrameLayoutOverlay() {
        if ((!isFullscreen && binding.flOverlay.isVisible) || player?.isPlaying() == true) {
            closeFrameOverlayView(null)
        }
    }

    private fun updateCenterControl() {
        updatePlayOrPause()
        updateSkipNextPrevious()
        updateSeekNextPrevious()
        updatePreviousNextEpisode()
        updateLiveChatButton()
        updatePlayerLockButton()
        updateSportInteractiveButton()
    }

    private fun updateBottomControl() {
        Logger.d(
            "$TAG > updateBottomControl > " +
                    """
                    playerControllerLocked = $playerControllerLocked
                    isThumbShown = $isThumbShown
                    isPlayingVipTrailer = $isPlayingVipTrailer
                    isSupportCast = $isSupportCast
                    isFullscreen = $isFullscreen
                    videoSetting = ${isShowSettingButton()}
                    playSpeed = ${isShowPlayerSpeedButton()}
                """.trimIndent() +
                    ""
        )

        if (playerControllerLocked) {
            binding.clBottomControl.hide()
            binding.rcvBottomControl.hide()
            return
        }
        if (isThumbShown) {
            binding.clBottomControl.hide()
            binding.rcvBottomControl.hide()
            return
        }
        if (MainApplication.INSTANCE.pairingConnectionHelper.isConnected && !isSupportCast && !isPlayingVipTrailer) {
            binding.clBottomControl.hide()
            binding.rcvBottomControl.hide()
            return
        }

        if (isFullscreen) {
            binding.rcvBottomControl.show()
        } else {
            binding.rcvBottomControl.hide()
        }
        updateProgress()
        player?.run {
            if (screenType == PlayerHandler.ScreenType.Live) {
                if (!isPlayingTimeShift) {
                    binding.tvCurrentTime.hide()
                    binding.playerSeekProgress.hide()
                    binding.tvLive.show()
                    playerBottomControlAdapter.setControlEnabled(
                        type = PlayerConstants.PlayerBottomControlType.ID_VIDEO_SETTING,
                        enabled = isShowSettingButton()
                    )
                    playerBottomControlAdapter.setControlTitle(
                        type = PlayerConstants.PlayerBottomControlType.ID_VIDEO_SETTING,
                        title = playerDataControlAddition.playerBitrateName
                    )

                    playerBottomControlAdapter.setControlEnabled(
                        type = PlayerConstants.PlayerBottomControlType.ID_CHANNEL_LIST_SETTING,
                        enabled = true
                    )

                    playerBottomControlAdapter.setControlEnabled(
                        type = PlayerConstants.PlayerBottomControlType.ID_EPG_SETTING,
                        enabled = true
                    )

//                    binding.ibSetting.isVisible = isShowSettingButton()
                } else {
                    binding.tvCurrentTime.show()
                    binding.playerSeekProgress.isVisible = playerProgressAndLockVisible
                    binding.tvLive.hide()
                    playerBottomControlAdapter.setControlEnabled(
                        type = PlayerConstants.PlayerBottomControlType.ID_VIDEO_SETTING,
                        enabled = false
                    )
//                    binding.ibSetting.hide()
                }
            } else if (screenType == PlayerHandler.ScreenType.Premiere) {
                binding.tvCurrentTime.hide()
                binding.playerSeekProgress.hide()
                binding.tvLive.show()
                playerBottomControlAdapter.setControlEnabled(
                    type = PlayerConstants.PlayerBottomControlType.ID_VIDEO_SETTING,
                    enabled = isShowSettingButton()
                )
//                binding.ibSetting.isVisible = isShowSettingButton()
            } else { // VOD
                binding.tvCurrentTime.show()
                binding.playerSeekProgress.isVisible =
                    (playerProgressAndLockVisible && !isUsingCustomSeekBar)
                Logger.d("$TAG > updateBottomControl: VOD mode - playerSeekProgress.isVisible=${binding.playerSeekProgress.isVisible}, playerProgressAndLockVisible=$playerProgressAndLockVisible, isUsingCustomSeekBar=$isUsingCustomSeekBar")
                binding.tvLive.hide()
                playerBottomControlAdapter.setControlEnabled(
                    type = PlayerConstants.PlayerBottomControlType.ID_VIDEO_SETTING,
                    enabled = isShowSettingButton()
                )
//                binding.ibSetting.isVisible = isShowSettingButton()
            }

            playerBottomControlAdapter.setControlEnabled(
                type = PlayerConstants.PlayerBottomControlType.ID_PLAY_SPEED_SETTING,
                enabled = isShowPlayerSpeedButton()
            )
//            binding.ibSpeed.isVisible = isShowPlayerSpeedButton()


            if (screenType == PlayerHandler.ScreenType.Live) {
                if (!isPlayingTimeShift) {
                    binding.tvTotalTime.hide()
                } else {
                    binding.tvTotalTime.show()
                }
            } else if (screenType == PlayerHandler.ScreenType.Premiere) {
                binding.tvTotalTime.hide()
            } else { // VOD
                binding.tvTotalTime.show()
            }


            if (isFullscreen) {
                binding.ibFullscreen.setImageResource(R.drawable.ic_player_unfullscreen)

                val timeLayoutMargin = binding.tvCurrentTime.layoutParams as? ViewGroup.MarginLayoutParams
                timeLayoutMargin?.marginStart = resources.getDimensionPixelSize(R.dimen.player_control_margin_parent)
                binding.tvCurrentTime.layoutParams = timeLayoutMargin

                val icFullScreenLP = binding.ibFullscreen.layoutParams as? MarginLayoutParams
                icFullScreenLP?.marginEnd = resources.getDimensionPixelSize(R.dimen.player_control_margin_parent)
                binding.ibFullscreen.layoutParams = icFullScreenLP

                val icLockLP = binding.ibPlayerLock.layoutParams as? MarginLayoutParams
                icLockLP?.marginStart = resources.getDimensionPixelSize(R.dimen.player_control_margin_parent_minus_padding)
                binding.ibPlayerLock.layoutParams = icLockLP
            }
            else {
                binding.ibFullscreen.setImageResource(R.drawable.ic_player_fullscreen)

                val timeLayoutMargin = binding.tvCurrentTime.layoutParams as? ViewGroup.MarginLayoutParams
                timeLayoutMargin?.marginStart = resources.getDimensionPixelSize(R.dimen.margin_common)
                binding.tvCurrentTime.layoutParams = timeLayoutMargin

                val icFullScreenLP = binding.ibFullscreen.layoutParams as? MarginLayoutParams
                icFullScreenLP?.marginEnd = resources.getDimensionPixelSize(R.dimen.margin_common)
                binding.ibFullscreen.layoutParams = icFullScreenLP

                val icLockLP = binding.ibPlayerLock.layoutParams as? MarginLayoutParams
                icLockLP?.marginStart = resources.getDimensionPixelSize(R.dimen.player_control_margin_common_minus_padding)
                binding.ibPlayerLock.layoutParams = icLockLP
            }

            playerBottomControlAdapter.setControlEnabled(
                type = PlayerConstants.PlayerBottomControlType.ID_PLAYLIST_SETTING,
                enabled = isShowPlaylistControl()
            )

        }

        playerDataControl?.tracks?.let { tracks ->
            val listSubtitle =
                tracks.filter { item -> (item.type == TrackType.TEXT.ordinal || ((item.id == "Tắt phụ đề" || item.name == "Tắt phụ đề") && item.type == 10002)) }
            val listAudio = tracks.filter { item -> (item.type == TrackType.AUDIO.ordinal) }

            val selectedAudio = listAudio.firstOrNull { it.isSelected }
            val selectedSubtitle = listSubtitle.firstOrNull { it.isSelected }

            playerBottomControlAdapter.setControlTitle(
                type = PlayerConstants.PlayerBottomControlType.ID_SUBTITLE_SETTING,
                title = selectedSubtitle?.name ?: ""
            )

            playerBottomControlAdapter.setControlTitle(
                type = PlayerConstants.PlayerBottomControlType.ID_AUDIO_SETTING,
                title = selectedAudio?.name ?: ""
            )

            playerBottomControlAdapter.setControlTitle(
                type = PlayerConstants.PlayerBottomControlType.ID_VIDEO_SETTING,
                title = playerDataControlAddition.playerBitrateName
            )

            playerBottomControlAdapter.setControlTitle(
                type = PlayerConstants.PlayerBottomControlType.ID_PLAY_SPEED_SETTING,
                title = "${playerDataControlAddition.playerSpeed}x"
            )
        }


    }

    private fun updateCastState() {
        binding.tvCastTitle.isVisible = isSupportCast && !isPlayingVipTrailer
        updateCastButtonState()
        updateCastTitle()
    }

    fun showSkipIntro(isShow: Boolean) {
        if (isShow && isForceHideSkipIntroCredit()) return
        if (isShow) {
            binding.tvSkipIntro.show()
        } else {
            binding.tvSkipIntro.hide()
        }
    }

    fun checkShowSkipIntro() {
        if (isThumbShown) {
            binding.tvSkipIntro.hide()
        }
        if (MainApplication.INSTANCE.pairingConnectionHelper.isConnected && !isSupportCast && !isPlayingVipTrailer) {
            binding.tvSkipIntro.hide()
        }
        Handler(Looper.getMainLooper()).postDelayed({
            when (PlayerUtils.getPlayingType()) {
                PlayerView.PlayingType.Local -> {
                    val curDuration = player?.currentDuration() ?: 0
                    if (curDuration in currentIntroFromMillis until currentStartContentMillis) {
                        if (!isForceHideSkipIntroCredit()) {
                            binding.tvSkipIntro.show()
                        }
                    } else {
                        binding.tvSkipIntro.hide()
                    }
                }

                PlayerView.PlayingType.Cast -> {
                    val curDuration =
                        pairingConnection.getRemoteData()?.remotePlayer?.currentDuration ?: 0
                    if (curDuration in currentIntroFromMillis until currentStartContentMillis) {
                        binding.tvSkipIntro.isVisible = isSupportCast
                    } else {
                        binding.tvSkipIntro.hide()
                    }
                }
            }

        }, 500)
    }

    fun unlockControl() {
        playerControllerLocked = false
        binding.doubleSeekOverLay.updatePlayerControllerLocked(isLock = playerControllerLocked)
        updateAllViews()

        playerUIEvents?.onLockToggle()
    }

    fun openPlaylistOverlayView() {
        try {
            hideUI()
            binding.flOverlay.visible()
            binding.flOverlay.removeAllViews()
            val playlistView = createPlaylistOverlayViewIfNeeded().apply {
                setCurrentEpisode(episode = curEpisodeData)
                setPlaylistData(details?.blockEpisode?.episodes ?: emptyList())
            }
            binding.flOverlay.addView(playlistView)
            playerUIEvents?.onOpenFrameOverlay(PlayerConstants.PlayerFrameOverlayType.ID_PLAYLIST)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private var playlistOverlayView: PlayerPlaylistOverlayView? = null
    private fun createPlaylistOverlayViewIfNeeded(): PlayerPlaylistOverlayView {
        return playlistOverlayView ?: PlayerPlaylistOverlayView(context).apply {
            setListener(
                object : PlaylistOverlayEventListener {
                    override fun onClose() {
                        closeFrameOverlayView(PlayerConstants.PlayerFrameOverlayType.ID_PLAYLIST)
                    }

                    override fun onClickItem(episode: Details.Episode) {
                        onClickPlayItem(episode)
                    }
                }
            )
        }
    }

    private fun closePlaylistOverlayView() {
        binding.flOverlay.removeAllViews()
        binding.flOverlay.gone()
    }

    private fun onClickPlayItem(episode: Details.Episode) {
        playerUIEvents?.onPlayItem(episode)
    }

    private fun handleOnLockButtonClick() {
        playerControllerLocked = !playerControllerLocked
        binding.doubleSeekOverLay.updatePlayerControllerLocked(isLock = playerControllerLocked)
        updateAllViews()
    }

    private fun updateLockButtonUI(isLock: Boolean) {
        if (!isVisible()) return
        if (isThumbShown) {
            binding.ibPlay.invisible()
            return
        }
        if (MainApplication.INSTANCE.pairingConnectionHelper.isConnected && !isSupportCast && !isPlayingVipTrailer) {
            binding.ibPlay.invisible()
            return
        }
        player?.run {
            if (screenType == PlayerHandler.ScreenType.Live) {
                if (!isPlayingTimeShift) {
                    binding.clTopControl.isVisible = !isLock
                    binding.ibSeekPrevious10s.gone()
                    binding.ibSeekNext10s.gone()
                    binding.ibSkipNext.gone()
                    binding.ibSkipPrevious.gone()
                    if (isBuffering) {
                        binding.ibPlay.invisible()
                    } else {
                        binding.ibPlay.invisible()
                    }
                    binding.clBottomControl.isVisible = !isLock
                    if (isLock) {
                        binding.ibPlayerLock.setImageResource(R.drawable.ic_player_lock)
                        binding.playerLayoutDim.setBackgroundColor(Color.TRANSPARENT)
                        tooltipsView.hide()
                    } else {
                        binding.ibPlayerLock.setImageResource(R.drawable.ic_player_unlock)
                        binding.playerLayoutDim.let { it.setBackgroundColor(it.context.getColor(R.color.player_dim_bg_color)) }
                        showTooltips()

                    }
                } else {
                    binding.clTopControl.isVisible = !isLock
                    binding.ibSeekPrevious10s.isVisible = !isLock
                    binding.ibSeekNext10s.isVisible = !isLock
                    binding.ibSkipNext.gone()
                    binding.ibSkipPrevious.gone()
                    if (isBuffering) binding.ibPlay.invisible() else {
                        binding.ibPlay.isVisible = !isLock
                    }
                    binding.clBottomControl.isVisible = !isLock
                    if (isLock) {
                        binding.ibPlayerLock.setImageResource(R.drawable.ic_player_lock)
                        binding.playerLayoutDim.setBackgroundColor(Color.TRANSPARENT)
                        tooltipsView.hide()
                    } else {
                        binding.ibPlayerLock.setImageResource(R.drawable.ic_player_unlock)
                        binding.playerLayoutDim.let { it.setBackgroundColor(it.context.getColor(R.color.player_dim_bg_color)) }
                        showTooltips()
                    }
                }
            } else if (screenType == PlayerHandler.ScreenType.Premiere) {
                binding.clTopControl.isVisible = !isLock
                binding.ibSeekPrevious10s.gone()
                binding.ibSeekNext10s.gone()
                binding.ibSkipNext.gone()
                binding.ibSkipPrevious.gone()
                if (isBuffering) binding.ibPlay.invisible() else {
                    if (screenType == PlayerHandler.ScreenType.Vod) {
                        binding.ibPlay.isVisible = !isLock
                    }
                }
                binding.clBottomControl.isVisible = !isLock
                if (isLock) {
                    binding.ibPlayerLock.setImageResource(R.drawable.ic_player_lock)
                    binding.playerLayoutDim.setBackgroundColor(Color.TRANSPARENT)
                    tooltipsView.hide()
                } else {
                    binding.ibPlayerLock.setImageResource(R.drawable.ic_player_unlock)
                    binding.playerLayoutDim.let { it.setBackgroundColor(it.context.getColor(R.color.player_dim_bg_color)) }
                    showTooltips()
                }
            } else { // VOD
                binding.clTopControl.isVisible = !isLock
                binding.ibSeekPrevious10s.isVisible = !isLock
                binding.ibSeekNext10s.isVisible = !isLock
                val listEpisode = details?.blockEpisode?.episodes ?: listOf()
                if (listEpisode.size < 2) {
                    binding.ibSkipPrevious.gone()
                    binding.ibSkipNext.gone()
                } else {
                    binding.ibSkipNext.isVisible = (!isLock && binding.ibSkipNext.isVisible)
                    binding.ibSkipPrevious.isVisible = (!isLock && binding.ibSkipPrevious.isVisible)
                }
                if (isBuffering) binding.ibPlay.invisible() else {
                    if (screenType == PlayerHandler.ScreenType.Vod) {
                        binding.ibPlay.isVisible = !isLock
                    }
                }
                binding.clBottomControl.isVisible = !isLock
                if (isLock) {
                    binding.ibPlayerLock.setImageResource(R.drawable.ic_player_lock)
                    binding.playerLayoutDim.setBackgroundColor(Color.TRANSPARENT)
                    tooltipsView.hide()
                } else {
                    binding.ibPlayerLock.setImageResource(R.drawable.ic_player_unlock)
                    binding.playerLayoutDim.let { it.setBackgroundColor(it.context.getColor(R.color.player_dim_bg_color)) }
                    showTooltips()
                }
            }
        }

    }

    private fun showTooltips() {
//        if (MainApplication.INSTANCE.sharedPreferences.shouldShowTooltip(Constants.TOOLTIP_SETTING_PLAYER) && binding.ibSetting.isVisible) {
//            tooltipsView.showToolTips(binding.ibSetting,
//                binding.clControlContainer,
//                binding.root.context.getString(R.string.tool_tip_player),
//                TooltipsView.PositionTooltips.TopRight,
//                tooltipsCallback = object : TooltipsCallback {
//                    override fun onClickTooltips() {
//                        MainApplication.INSTANCE.sharedPreferences.setShouldShowTooltip(Constants.TOOLTIP_SETTING_PLAYER, false)
//                    }
//                })
//            isShowTooltipTV = true
//            playerUIEvents?.hideBuyPackageGuide()
//        } else { tooltipsView.hide() }
    }

    //region Update -> Play/Pause Button
    private fun updatePlayOrPause() {
        fun checkNecessaryChangeButtonSize(isFullscreen: Boolean): Boolean {
            Logger.d("$TAG > updatePlayOrPause > checkNecessaryChangeButtonSize - isFullscreen=$isFullscreen")
            Logger.d(
                "$TAG > updatePlayOrPause > checkNecessaryChangeButtonSize - ${
                    binding.ibPlay.width == context.resources.getDimensionPixelSize(
                        R.dimen.player_control_play_or_pause_size_fullscreen
                    )
                }"
            )
            return if (isFullscreen) binding.ibPlay.width != context.resources.getDimensionPixelSize(
                R.dimen.player_control_play_or_pause_size_fullscreen
            )
            else binding.ibPlay.width != context.resources.getDimensionPixelSize(R.dimen.player_control_play_or_pause_size)
        }

        if (!isVisible()) return
        try {
            if (checkNecessaryChangeButtonSize(isFullscreen)) {
                Logger.d("$TAG > updatePlayOrPause: checkNecessaryChangeButtonSize - isFullscreen=$isFullscreen")
                val playButtonSize = if (isFullscreen) {
                    context.resources.getDimensionPixelSize(R.dimen.player_control_play_or_pause_size_fullscreen)
                } else context.resources.getDimensionPixelSize(R.dimen.player_control_play_or_pause_size)
                binding.ibPlay.apply {
                    val lps = layoutParams
                    lps.width = playButtonSize
                    lps.height = playButtonSize
                    layoutParams = lps
                }
            }


        } catch (ex: Exception) {
            ex.printStackTrace()
        }


        when (PlayerUtils.getPlayingType()) {
            PlayerView.PlayingType.Local -> {
                if (player?.isPlaying() == true) {
                    binding.ibPlay.setImageResource(R.drawable.ic_player_pause)
                } else {
                    binding.ibPlay.setImageResource(R.drawable.ic_player_play)
                }
            }

            PlayerView.PlayingType.Cast -> {
                if (pairingConnection.getRemoteData()?.remotePlayer?.state == RemotePlayerState.PLAY) {
                    binding.ibPlay.setImageResource(R.drawable.ic_player_pause)
                } else {
                    binding.ibPlay.setImageResource(R.drawable.ic_player_play)
                }
            }

            else -> {}
        }
    }
    //endregion


    private fun isShowPlaylistControl(): Boolean {
        return details?.let {
            if (it.blockEpisode.episodes.firstOrNull()?.isItemOfPlaylist == true) {
                true
            } else {
                !((it.blockContent.episodeType == 0 || it.blockContent.episodeType == 2) && it.blockContent.episodeTotal == 1)
            }
        } ?: false
    }

    //region Update -> Skip Next/Previous Button
    private fun updateSkipNextPrevious() {
        Logger.d("$TAG > updateSkipNextPrevious")
        if (!isVisible()) return
        if (playerControllerLocked) {
            binding.ibSkipNext.gone()
            binding.ibSkipPrevious.gone()
            return
        }
        if (isThumbShown) {
            binding.ibSkipNext.gone()
            binding.ibSkipPrevious.gone()
            return
        }
        if (MainApplication.INSTANCE.pairingConnectionHelper.isConnected && !isSupportCast && !isPlayingVipTrailer) {
            binding.ibSkipNext.gone()
            binding.ibSkipPrevious.gone()
            return
        }
        player?.run {
            if (screenType == PlayerHandler.ScreenType.Live) {
                binding.ibSkipNext.gone()
                binding.ibSkipPrevious.gone()
            } else if (screenType == PlayerHandler.ScreenType.Premiere) {
                binding.ibSkipNext.gone()
                binding.ibSkipPrevious.gone()
            } else {
                val listEpisode = details?.blockEpisode?.episodes ?: listOf()
                if (listEpisode.size < 2 || !isFullscreen) {
                    binding.ibSkipPrevious.gone()
                    binding.ibSkipNext.gone()
                } else {
                    binding.ibSkipNext.visible()
                    binding.ibSkipPrevious.visible()
                    updateSkipButtonLayouts()
                }
            }
        }
    }

    /**
     * Helper function to update skip button layouts based on screen mode and device type
     */
    private fun updateSkipButtonLayouts() {
        val skipButtonConfig = getSkipButtonConfiguration()

        // Update skip next button
        updateSkipButtonLayout(
            button = binding.ibSkipNext,
            size = skipButtonConfig.size,
            margin = skipButtonConfig.margin,
            isStartMargin = true
        )

        // Update skip previous button  
        updateSkipButtonLayout(
            button = binding.ibSkipPrevious,
            size = skipButtonConfig.size,
            margin = skipButtonConfig.margin,
            isStartMargin = false
        )
    }

    /**
     * Data class to hold skip button configuration
     */
    private data class SkipButtonConfig(
        val size: Int,
        val margin: Int
    )

    /**
     * Get the appropriate configuration for skip buttons based on current state
     */
    private fun getSkipButtonConfiguration(): SkipButtonConfig {
        return if (isFullscreen) {
            if (!context.isTablet()) {
                SkipButtonConfig(
                    size = context.resources.getDimensionPixelSize(R.dimen.player_control_center_button_size_fullscreen),
                    margin = context.resources.getDimensionPixelSize(R.dimen.player_control_center_button_margin_fullscreen_landscape)
                )
            } else {
                val margin = if (isLandscapeMode) {
                    context.resources.getDimensionPixelSize(R.dimen.player_control_center_button_margin_fullscreen_landscape)
                } else {
                    context.resources.getDimensionPixelSize(R.dimen.player_control_center_button_margin_fullscreen_portrait)
                }
                SkipButtonConfig(
                    size = context.resources.getDimensionPixelSize(R.dimen.player_control_center_button_size_fullscreen),
                    margin = margin
                )
            }
        } else {
            SkipButtonConfig(
                size = context.resources.getDimensionPixelSize(R.dimen.player_control_center_button_size),
                margin = context.resources.getDimensionPixelSize(R.dimen.player_control_center_button_margin)
            )
        }
    }

    /**
     * Update layout parameters for a skip button
     */
    private fun updateSkipButtonLayout(
        button: View,
        size: Int,
        margin: Int,
        isStartMargin: Boolean
    ) {
        val currentLayoutParams = button.layoutParams as? ViewGroup.MarginLayoutParams ?: return

        // Check if layout update is needed to avoid unnecessary operations
        val needsUpdate = currentLayoutParams.width != size ||
                currentLayoutParams.height != size ||
                (isStartMargin && currentLayoutParams.marginStart != margin) ||
                (!isStartMargin && currentLayoutParams.marginEnd != margin)

        if (needsUpdate) {
            currentLayoutParams.apply {
                width = size
                height = size
                if (isStartMargin) {
                    marginStart = margin
                    marginEnd = 0
                } else {
                    marginEnd = margin
                    marginStart = 0
                }
            }
            button.layoutParams = currentLayoutParams
        }
    }
    //endregion

    //region Update -> Seek Next/Previous Button
    private fun updateSeekNextPrevious() {
        if (!isVisible()) return
        if (playerControllerLocked) {
            binding.ibSeekPrevious10s.gone()
            binding.ibSeekNext10s.gone()
            return
        }
        if (isThumbShown) {
            binding.ibSeekPrevious10s.gone()
            binding.ibSeekNext10s.gone()
            return
        }
        if (MainApplication.INSTANCE.pairingConnectionHelper.isConnected && !isSupportCast && !isPlayingVipTrailer) {
            binding.ibSeekPrevious10s.gone()
            binding.ibSeekNext10s.gone()
            return
        }
        player?.run {
            when (screenType) {
                PlayerHandler.ScreenType.Live -> {
                    if (!isPlayingTimeShift) {
                        binding.ibSeekPrevious10s.gone()
                        binding.ibSeekNext10s.gone()
                    } else {
                        binding.ibSeekPrevious10s.visible()
                        binding.ibSeekNext10s.visible()
                        updateSeekButtonLayouts()
                    }
                }

                PlayerHandler.ScreenType.Premiere -> {
                    binding.ibSeekPrevious10s.gone()
                    binding.ibSeekNext10s.gone()
                }

                else -> {
                    binding.ibSeekPrevious10s.visible()
                    binding.ibSeekNext10s.visible()
                    updateSeekButtonLayouts()
                }
            }
        }
    }

    /**
     * Helper function to update seek button layouts based on screen mode and device type
     */
    private fun updateSeekButtonLayouts() {
        val seekButtonConfig = getSeekButtonConfiguration()

        // Update seek next button
        updateSeekButtonLayout(
            button = binding.ibSeekNext10s,
            size = seekButtonConfig.size,
            margin = seekButtonConfig.margin,
            isStartMargin = true
        )

        // Update seek previous button
        updateSeekButtonLayout(
            button = binding.ibSeekPrevious10s,
            size = seekButtonConfig.size,
            margin = seekButtonConfig.margin,
            isStartMargin = false
        )
    }

    /**
     * Data class to hold seek button configuration
     */
    private data class SeekButtonConfig(
        val size: Int,
        val margin: Int
    )

    /**
     * Get the appropriate configuration for seek buttons based on current state
     */
    private fun getSeekButtonConfiguration(): SeekButtonConfig {
        return if (isFullscreen) {
            if (!context.isTablet()) {
                SeekButtonConfig(
                    size = context.resources.getDimensionPixelSize(R.dimen.player_control_center_button_size_fullscreen),
                    margin = context.resources.getDimensionPixelSize(R.dimen.player_control_center_button_margin_fullscreen_landscape)
                )
            } else {
                val margin = if (isLandscapeMode) {
                    context.resources.getDimensionPixelSize(R.dimen.player_control_center_button_margin_fullscreen_landscape)
                } else {
                    context.resources.getDimensionPixelSize(R.dimen.player_control_center_button_margin_fullscreen_portrait)
                }
                SeekButtonConfig(
                    size = context.resources.getDimensionPixelSize(R.dimen.player_control_center_button_size_fullscreen),
                    margin = margin
                )
            }
        } else {
            SeekButtonConfig(
                size = context.resources.getDimensionPixelSize(R.dimen.player_control_center_button_size),
                margin = context.resources.getDimensionPixelSize(R.dimen.player_control_center_button_margin)
            )
        }
    }

    /**
     * Update layout parameters for a seek button
     */
    private fun updateSeekButtonLayout(
        button: View,
        size: Int,
        margin: Int,
        isStartMargin: Boolean
    ) {
        val currentLayoutParams = button.layoutParams as? ViewGroup.MarginLayoutParams ?: return

        // Check if layout update is needed to avoid unnecessary operations
        val needsUpdate = currentLayoutParams.width != size ||
                currentLayoutParams.height != size ||
                (isStartMargin && currentLayoutParams.marginStart != margin) ||
                (!isStartMargin && currentLayoutParams.marginEnd != margin)

        if (needsUpdate) {
            currentLayoutParams.apply {
                width = size
                height = size
                if (isStartMargin) {
                    marginStart = margin
                    marginEnd = 0
                } else {
                    marginEnd = margin
                    marginStart = 0
                }
            }
            button.layoutParams = currentLayoutParams
        }
    }
    //endregion

    //region Update -> Progress
    private fun updateProgress() {
        if (!isVisible() && isFullscreen) return
        if (player == null || screenType == PlayerHandler.ScreenType.Live) {
            if (!isPlayingTimeShift) {
                binding.tvCurrentTime.hide()
                // Hide the appropriate seekbar
                getActiveSeekBar().hide()
                binding.tvTotalTime.hide()
            } else {
                if (!isSeekBarDragging) {
                    var currentDuration = errorTime
                    var bufferDuration = errorTime
                    var totalDuration = errorTime
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            currentDuration = player?.currentDuration() ?: errorTime
                            bufferDuration = player?.bufferDuration() ?: errorTime
                            totalDuration = player?.totalDuration() ?: errorTime
                        }

                        PlayerView.PlayingType.Cast -> {
                            val remoteData = pairingConnection.getRemoteData()?.remotePlayer
                            currentDuration = remoteData?.currentDuration ?: errorTime
                            bufferDuration = remoteData?.bufferDuration ?: errorTime
                            totalDuration = remoteData?.totalDuration ?: errorTime
                        }

                        else -> {}
                    }
                    thumbView?.setTime(
                        time = Util.convertTime(currentDuration),
                        totalTime = Util.convertTime(totalDuration),
                        isLoadThumbSuccess = thumbLoadSuccess
                    )
                    binding.tvTotalTime.text = String.format(
                        Locale.getDefault(),
                        context.getString(R.string.player_total_time_with_param),
                        Util.convertTime(totalDuration)
                    )
                    if (currentDuration != errorTime) setActiveSeekBarProgress(currentDuration.toInt())
                    if (bufferDuration != errorTime) setActiveSeekBarSecondaryProgress(
                        bufferDuration.toInt()
                    )
                    if (totalDuration != errorTime) setActiveSeekBarMax(totalDuration.toInt())

                    binding.tvCurrentTime.show()
                    if (isFullscreen) binding.tvTotalTime.show()
                    // Show/hide the appropriate seekbar based on custom seekbar usage
                    if (isUsingCustomSeekBar) {
                        customSeekBarView?.isVisible = playerProgressAndLockVisible
                        binding.playerSeekProgress.hide()
                    } else {
                        binding.playerSeekProgress.isVisible = playerProgressAndLockVisible
                    }
                    removeCallbacks(updateProgressRunnable)
                    postDelayed(updateProgressRunnable, delayTimeToUpdateProgress)
                }
            }
        } else if (screenType == PlayerHandler.ScreenType.Premiere) {
            binding.tvCurrentTime.hide()
            // Hide the appropriate seekbar
            getActiveSeekBar().hide()
            binding.tvTotalTime.hide()
        } else {
            if (!isSeekBarDragging) {
                var currentDuration = errorTime
                var bufferDuration = errorTime
                var totalDuration = errorTime
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
                        currentDuration = player?.currentDuration() ?: errorTime
                        bufferDuration = player?.bufferDuration() ?: errorTime
                        totalDuration = player?.totalDuration() ?: errorTime
                    }

                    PlayerView.PlayingType.Cast -> {
                        val remoteData = pairingConnection.getRemoteData()?.remotePlayer
                        currentDuration = remoteData?.currentDuration ?: errorTime
                        bufferDuration = remoteData?.bufferDuration ?: errorTime
                        totalDuration = remoteData?.totalDuration ?: errorTime
                    }

                    else -> {}
                }
                thumbView?.setTime(
                    time = Util.convertTime(currentDuration),
                    totalTime = Util.convertTime(totalDuration),
                    isLoadThumbSuccess = thumbLoadSuccess
                )
                binding.tvTotalTime.text = String.format(
                    Locale.getDefault(),
                    context.getString(R.string.player_total_time_with_param),
                    Util.convertTime(totalDuration)
                )
                if (currentDuration != errorTime) setActiveSeekBarProgress(currentDuration.toInt())
                if (bufferDuration != errorTime) setActiveSeekBarSecondaryProgress(bufferDuration.toInt())
                if (totalDuration != errorTime) setActiveSeekBarMax(totalDuration.toInt())

                binding.tvCurrentTime.show()
                if (isFullscreen) binding.tvTotalTime.show()
                // Show/hide the appropriate seekbar based on custom seekbar usage
                getActiveSeekBar().apply {
                    isVisible = playerProgressAndLockVisible && (player?.request != null)
                    Logger.d("$TAG > updateProgress: setting seekbar visible=$playerProgressAndLockVisible for ${if (isUsingCustomSeekBar) "custom" else "default"} seekbar")
                    if (isUIShown) {
                        showSeekbarControl()
                    }
                }
                removeCallbacks(updateProgressRunnable)
                postDelayed(updateProgressRunnable, delayTimeToUpdateProgress)
            }
        }
    }
    //endregion

    //region Thumbnail
    private fun updateThumbPosition() {
        val location = IntArray(2)
        val lp = if (isFullscreen) LayoutParams(
                thumbnailWidthFullscreen,
                thumbnailHeightFullscreen
            ) else LayoutParams(thumbnailWidth, thumbnailHeight)

        val activeSeekBar = getActiveSeekBar()
        activeSeekBar.getLocationInWindow(location)

        // Center thumbView both horizontally and vertically within the player UI view
        val playerUIWidth = binding.playerLayoutDim.width
        val playerUIHeight = binding.playerLayoutDim.height
        
        lp.leftMargin = (playerUIWidth - lp.width) / 2
        lp.topMargin = (playerUIHeight - lp.height) / 2

        thumbView?.layoutParams = lp
    }

    private fun getThumbData(currentTime: Int): PlayerControlView.Data.Thumbnail? {
        player?.run {
            try {
                for (item in thumbData) {
                    if (currentTime >= item.time.start && currentTime <= item.time.end) {
                        return item
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return null
    }

    //endregion

    //region Update Previous Next Episode
    private fun updatePreviousNextEpisode() {
        if (!isVisible()) return
        if (playerControllerLocked) {
            binding.ibSkipPrevious.gone()
            binding.ibSkipNext.gone()
            return
        }
        if (isThumbShown) {
            binding.ibSkipPrevious.gone()
            binding.ibSkipNext.gone()
            return
        }
        if (MainApplication.INSTANCE.pairingConnectionHelper.isConnected && !isSupportCast && !isPlayingVipTrailer) {
            binding.ibSkipPrevious.gone()
            binding.ibSkipNext.gone()
            return
        }
        if (player == null || screenType == PlayerHandler.ScreenType.Live) {
            // Do nothing
        } else if (screenType == PlayerHandler.ScreenType.Premiere) {
            // Do nothing
        } else {
            val listEpisode = details?.blockEpisode?.episodes ?: listOf()
            if (listEpisode.size < 2) {
                binding.ibSkipPrevious.gone()
                binding.ibSkipNext.gone()
            } else {
                when (listEpisode.getIndexOf(curEpisodeData)) {
                    -1 -> {
                        binding.ibSkipPrevious.isEnabled = false
                        binding.ibSkipNext.isEnabled = false
                    }

                    0 -> {
                        binding.ibSkipPrevious.isEnabled = false
                        binding.ibSkipNext.isEnabled = true
                    }

                    listEpisode.size - 1 -> {
                        binding.ibSkipPrevious.isEnabled = true
                        binding.ibSkipNext.isEnabled = false
                    }

                    else -> {
                        binding.ibSkipPrevious.isEnabled = true
                        binding.ibSkipNext.isEnabled = true
                    }
                }
            }
        }

    }
    //endregion

    //region Update Live Chat Button
    private fun updateLiveChatButton() {
        if (!isVisible()) return
        if (playerControllerLocked) {
            binding.ibPlayerLiveChat.gone()
            return
        }
        when (screenType) {
            PlayerHandler.ScreenType.Live -> {
                binding.ibPlayerLiveChat.gone()
            }

            PlayerHandler.ScreenType.Premiere -> {
                binding.ibPlayerLiveChat.isVisible =
                    isFullscreen && isLandscapeMode && detailsPremiere?.commentType == Constants.CHAT_EVENT_REAL_TIME_TYPE
//                binding.ibPlayerLiveChat?.isVisible = true
            }

            else -> {
                binding.ibPlayerLiveChat.gone()
            }
        }
    }
    //endregion

    //region Update Live Chat Button
    private fun updateSportInteractiveButton() {
        if (!isVisible()) return
        if (playerControllerLocked) {
            binding.ibSportInteractive.gone()
            return
        }
        if (!isShowSportInteractive) {
            binding.ibSportInteractive.gone()
            return
        }
        if (!isFullscreen) {
            binding.ibSportInteractive.gone()
            return
        }
        when (screenType) {
            PlayerHandler.ScreenType.Live -> { // TV
                binding.ibSportInteractive.gone()
            }

            PlayerHandler.ScreenType.Premiere -> { // Event
                binding.ibSportInteractive.checkShowSportInteractive(true)
            }

            else -> { // VOD
                binding.ibSportInteractive.gone()
            }
        }
    }
    //endregion

    //region Update Lock Button
    private fun updatePlayerLockButton() {
        if (!isVisible()) return
        if (isThumbShown) {
            binding.ibPlayerLock.gone()
            return
        }
        if (MainApplication.INSTANCE.pairingConnectionHelper.isConnected && !isSupportCast && !isPlayingVipTrailer) {
            binding.ibPlayerLock.gone()
            return
        }
        binding.ibPlayerLock.isVisible = playerProgressAndLockVisible
    }
    //endregion

    private fun updateButtonAudioAndSubtitle() {
        if (isThumbShown) {
            playerBottomControlAdapter.setControlEnabled(
                type = PlayerConstants.PlayerBottomControlType.ID_SUBTITLE_SETTING,
                enabled = false
            )
//            binding.ibSubtitle.hide()
            return
        }
        if (MainApplication.INSTANCE.pairingConnectionHelper.isConnected && !isSupportCast && !isPlayingVipTrailer) {
            playerBottomControlAdapter.setControlEnabled(
                type = PlayerConstants.PlayerBottomControlType.ID_SUBTITLE_SETTING,
                enabled = false
            )
//            binding.ibSubtitle.hide()
            return
        }
        val tracks = when (PlayerUtils.getPlayingType()) {
            PlayerView.PlayingType.Local -> {
                playerDataControl?.tracks ?: listOf()
            }

            PlayerView.PlayingType.Cast -> {
                pairingConnection.getRemoteData()?.remotePlayer?.tracks ?: listOf()
            }
        }
        val listSubtitle =
            tracks.filter { item -> (item.type == TrackType.TEXT.ordinal || ((item.id == "Tắt phụ đề" || item.name == "Tắt phụ đề") && item.type == 10002)) }
        val listAudio = tracks.filter { item -> item.type == TrackType.AUDIO.ordinal }
        if (listSubtitle.isNotEmpty()) {
            if (playerControllerLocked || isPlayingTimeShift || isPlayingVipTrailer) {
                playerBottomControlAdapter.setControlEnabled(
                    type = PlayerConstants.PlayerBottomControlType.ID_SUBTITLE_SETTING,
                    enabled = false
                )
//                binding.ibSubtitle.hide()
            } else {
                player?.run {
                    playerBottomControlAdapter.setControlEnabled(
                        type = PlayerConstants.PlayerBottomControlType.ID_SUBTITLE_SETTING,
                        enabled = true
                    )
//                    binding.ibSubtitle.show()
                }
            }
        } else {
            playerBottomControlAdapter.setControlEnabled(
                type = PlayerConstants.PlayerBottomControlType.ID_SUBTITLE_SETTING,
                enabled = false
            )
//            binding.ibSubtitle.hide()
        }

        if (listAudio.isNotEmpty()) {
            if (playerControllerLocked || isPlayingTimeShift || isPlayingVipTrailer) {
                playerBottomControlAdapter.setControlEnabled(
                    type = PlayerConstants.PlayerBottomControlType.ID_AUDIO_SETTING,
                    enabled = false
                )
            } else {
                player?.run {
                    playerBottomControlAdapter.setControlEnabled(
                        type = PlayerConstants.PlayerBottomControlType.ID_AUDIO_SETTING,
                        enabled = true
                    )
                }
            }
        } else {
            playerBottomControlAdapter.setControlEnabled(
                type = PlayerConstants.PlayerBottomControlType.ID_AUDIO_SETTING,
                enabled = false
            )
        }
    }

    private fun showUI() {
        if (isUIHidden && playerUICanVisible && !isForeHidePlayerUI()) {
            isUIHidden = false
            isUIShown = true
            binding.playerLayoutDim.fadeIn()
            updateAllViews()
            playerUIEvents?.hideBuyPackageGuide()
            if (MainApplication.INSTANCE.sharedPreferences.shouldShowTooltip(Constants.TOOLTIP_SETTING_PLAYER)) {
                if (!playerControllerLocked) {
//                    when (binding.ibSetting.isVisible) {
//                        true -> showTooltips()
//                        false -> tooltipsView.hide()
//                    }
                } else {
                    tooltipsView.hide()
                }

                playerView?.dismissListener(object : DismissCallback {
                    override fun onDismissClickListener(isToolTip: Boolean) {
                        if (isToolTip) {
                            tooltipsView.hide()
                        }
                    }
                })
            }
        }
    }

    private fun hideUI() {
        if (MainApplication.INSTANCE.sharedPreferences.shouldShowTooltip(Constants.TOOLTIP_SETTING_PLAYER)) {
            tooltipsView.hide()
        }
        isUIHidden = true
        isUIShown = false
        getActiveSeekBar().hideSeekbarControl()
        binding.playerLayoutDim.fadeOut()
    }

    private fun startShowUITask(delay: Long) {
        stopShowUITask()
        showUITask = object : TimerTask() {
            override fun run() {
                if (System.currentTimeMillis() - lastInteractTimeMs > delay) {
                    viewLifecycleOwner?.lifecycleScope?.launch {
                        showUI()
                        startUIHidingTask()
                    }
                    stopShowUITask()
                }
            }
        }
        Timer().scheduleAtFixedRate(showUITask, 0, 200)
    }

    private fun stopShowUITask() {
        showUITask?.cancel()
        showUITask = null
    }

    private fun startUIHidingTask() {
        stopUIHidingTask()
        uiTask = object : TimerTask() {
            override fun run() {
                if (System.currentTimeMillis() - lastInteractTimeMs > 3_000) {
                    hideUI()
                    stopUIHidingTask()
                }
            }
        }
        Timer().scheduleAtFixedRate(uiTask, 0, 200)
    }

    private fun stopUIHidingTask() {
        uiTask?.cancel()
        uiTask = null
    }

    private fun isShowSettingButton(): Boolean {
        if (player?.request == null) return false
        if (isPlayingVipTrailer) return false
        if (isVodOffline) return false
        if (playerConfig.isPlayPreview && (screenType is PlayerHandler.ScreenType.Live || screenType is PlayerHandler.ScreenType.Premiere)) return false
        if (pairingConnection.isConnected) return true
        return playerDataControl?.bitrates?.isNotEmpty() == true
    }

    private fun isShowSubtitleButton() {

    }

    private fun isShowPlayerSpeedButton(): Boolean {
        if (player?.request == null) return false
        if (isPlayingVipTrailer) return false
        if (isVodOffline) return true
        if (screenType is PlayerHandler.ScreenType.Vod) return true
        if (playerConfig.isPlayPreview && (screenType is PlayerHandler.ScreenType.Live || screenType is PlayerHandler.ScreenType.Premiere)) return false
        if (pairingConnection.isConnected) return false
        return false
    }


    //region Multitouch handler
    private var isScaling = false

    //region Handle double click for seek & change volume and brightness
    private val gestureListener: DoubleTapGestureListener =
        DoubleTapGestureListener(binding.playerLayoutTouch)
    private val gestureDetector: GestureDetectorCompat by lazy {
        GestureDetectorCompat(
            context,
            gestureListener
        )
    }

    private fun initTouchViews() {
        gestureListener.controls = binding.doubleSeekOverLay
    }

    private fun changeVolume(valueChangeY: Int, isIncrease: Boolean) {
        mAudioManager?.let {
            val maxVolumeLevel = it.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
            val deltaValueChange =
                ((valueChangeY * 1.1f / binding.playerLayoutTouch.height * 1.1f) * maxVolumeLevel).toInt()
            var newCurrentVolumeLevel = if (isIncrease) {
                currentVolumeLevel + deltaValueChange
            } else {
                currentVolumeLevel - deltaValueChange
            }
            if (newCurrentVolumeLevel > maxVolumeLevel) {
                newCurrentVolumeLevel = maxVolumeLevel
            }
            if (newCurrentVolumeLevel < 0) {
                newCurrentVolumeLevel = 0
            }
            it.setStreamVolume(AudioManager.STREAM_MUSIC, newCurrentVolumeLevel, 0)

            // ShowUI
            binding.tvPlayerVolumeBrightness.text = context?.getString(
                R.string.play_volume_with_value,
                "${newCurrentVolumeLevel}/${maxVolumeLevel}"
            )
            binding.tvPlayerVolumeBrightness.show()
        }
    }

    private fun changeBrightness(valueChangeY: Int, isIncrease: Boolean) {
        layoutParams?.let {
            if (currentBrightness == -1f) {
                currentBrightness = 0.5f
            }
            var newBrightness = if (isIncrease) {
                (currentBrightness + (valueChangeY * 1.1 / binding.playerLayoutTouch.height * 1.1)).toFloat()
            } else {
                (currentBrightness - (valueChangeY * 1.1 / binding.playerLayoutTouch.height * 1.1)).toFloat()
            }

            if (newBrightness <= 0f) newBrightness = 0.01f
            if (newBrightness > 1) newBrightness = 1f

            it.screenBrightness = newBrightness
            fragmentActivity?.window?.attributes = it

            // ShowUI
            binding.tvPlayerVolumeBrightness.text = context?.getString(
                R.string.play_brightness_with_value,
                "${(newBrightness * 100f).toInt()}%"
            )
            binding.tvPlayerVolumeBrightness.show()
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private val gestureDetectorListener = OnTouchListener { view, event ->
        lastInteractTimeMs = System.currentTimeMillis()
        Logger.d("$TAG > onTouch view=$view, isVisible=${view.isVisible}, isClickable=${view.isClickable}, isEnabled=${view.isEnabled}")
        Logger.d("$TAG > seekbar state: isUsingCustomSeekBar=$isUsingCustomSeekBar, defaultSeekbarVisible=${binding.playerSeekProgress.isVisible}")
        if (event.action == MotionEvent.ACTION_UP) {
            currentBrightness = -1f
            binding.tvPlayerVolumeBrightness.hide()
        }
        // Update View
        gestureListener.view = view
        // Scale gesture detector
        scaleGestureDetector.onTouchEvent(event)
        return@OnTouchListener gestureDetector.onTouchEvent(event)
    }

    /**
     * Gesture Listener for double tapping
     *
     * For more information which methods are called in certain situations look for
     * [GestureDetector.onTouchEvent][android.view.GestureDetector.onTouchEvent],
     * especially for ACTION_DOWN and ACTION_UP
     */
    inner class DoubleTapGestureListener(private val rootView: View) :
        GestureDetector.SimpleOnGestureListener() {

        private val mHandler = Handler(Looper.getMainLooper())
        private val mRunnable = Runnable {
            isDoubleTapping = false
            controls?.onDoubleTapFinished()
        }
        var view: View? = null
        var controls: PlayerDoubleTapListener? = null
        var isDoubleTapping = false
        var doubleTapDelay: Long = 650

        /**
         * Resets the timeout to keep in double tap mode.
         *
         * Called once in [PlayerDoubleTapListener.onDoubleTapStarted]. Needs to be called
         * from outside if the double tap is customized / overridden to detect ongoing taps
         */
        fun keepInDoubleTapMode() {
            isDoubleTapping = true
            mHandler.removeCallbacks(mRunnable)
            mHandler.postDelayed(mRunnable, doubleTapDelay)
        }

        /**
         * Cancels double tap mode instantly by calling [PlayerDoubleTapListener.onDoubleTapFinished]
         */
        fun cancelInDoubleTapMode() {
            mHandler.removeCallbacks(mRunnable)
            isDoubleTapping = false
            controls?.onDoubleTapFinished()
        }

        override fun onDown(e: MotionEvent): Boolean {
            // Reset isScaling
            isScaling = false

            // Used to override the other methods
            if (isDoubleTapping) {
                if (view?.id == rootView.id) controls?.onDoubleTapProgressDown(e.x, e.y)
                return true
            }

            // Gestures control volume and
            if (isFullscreen && view?.id == rootView.id) {
                layoutParams?.let { currentBrightness = it.screenBrightness }
                mAudioManager?.let {
                    currentVolumeLevel = it.getStreamVolume(AudioManager.STREAM_MUSIC)
                }
                downX = e.x
                downY = e.y
                intLeft = e.x < (binding.playerLayoutTouch.width / 2)
                intRight = e.x >= (binding.playerLayoutTouch.width / 2)
            }
            //

            return view?.id == rootView.id
        }

        override fun onScroll(
            e1: MotionEvent?,
            e2: MotionEvent,
            distanceX: Float,
            distanceY: Float
        ): Boolean {
            // Gestures control volume and brightness
            // Only handle (change volume/ change brightness) logic when only one fighter touch on screen
            if (e2.pointerCount > 1 || isScaling) {
                return super.onScroll(e1, e2, distanceX, distanceY)
            }
            //finger move to screen
            if (isFullscreen && view?.id == rootView.id) {
                val x2 = e2.x
                val y2 = e2.y

                diffX = ceil((e2.x - downX).toDouble()).toInt()
                diffY = ceil((e2.y - downY).toDouble()).toInt()

                if (abs(diffY) > abs(diffX)) {
                    if (intLeft) {
                        if (downY < y2) {
                            changeBrightness(abs(y2 - downY).toInt(), isIncrease = false)
                        } else if (downY > y2) {
                            changeBrightness(abs(y2 - downY).toInt(), isIncrease = true)
                        }
                    } else if (intRight) {
                        if (downY < y2) {
                            changeVolume(abs(y2 - downY).toInt(), isIncrease = false)
                        } else if (downY > y2) {
                            changeVolume(abs(y2 - downY).toInt(), isIncrease = true)
                        }
                    }
                }
            }
            //
            return super.onScroll(e1, e2, distanceX, distanceY)
        }

        override fun onSingleTapUp(e: MotionEvent): Boolean {
            if (isDoubleTapping) {
                if (view?.id == rootView.id) controls?.onDoubleTapProgressUp(e.x, e.y)
                return true
            }
            return super.onSingleTapUp(e)
        }

        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            // Ignore this event if double tapping is still active
            // Return true needed because this method is also called if you tap e.g. three times
            // in a row, therefore the controller would appear since the original behavior is
            // to hide and show on single tap
            if (isDoubleTapping) return true

            if (!binding.playerLayoutDim.isVisible) {
                showUI()
                startUIHidingTask()
            } else {
                if (view?.id == rootView.id) hideUI()
            }
            return rootView.performClick()
        }

        override fun onDoubleTap(e: MotionEvent): Boolean {
            // First tap (ACTION_DOWN) of both taps
            if (!isDoubleTapping) {
                isDoubleTapping = true
                keepInDoubleTapMode()
                if (view?.id == rootView.id) controls?.onDoubleTapStarted(e.x, e.y)
            }
            return true
        }

        override fun onDoubleTapEvent(e: MotionEvent): Boolean {
            // Second tap (ACTION_UP) of both taps
            if (e.actionMasked == MotionEvent.ACTION_UP && isDoubleTapping) {
                if (view?.id == rootView.id) controls?.onDoubleTapProgressUp(e.x, e.y)
                return true
            }
            return super.onDoubleTapEvent(e)
        }
    }

    interface PlayerDoubleTapListener {
        /**
         * Called when double tapping starts, after double tap gesture
         *
         * @param posX x tap position on the root view
         * @param posY y tap position on the root view
         */
        fun onDoubleTapStarted(posX: Float, posY: Float) {}

        /**
         * Called for each ongoing tap (also single tap) (MotionEvent#ACTION_DOWN)
         * when double tap started and still in double tap mode defined
         * by [DoubleTapPlayerView.getDoubleTapDelay]
         *
         * @param posX x tap position on the root view
         * @param posY y tap position on the root view
         */
        fun onDoubleTapProgressDown(posX: Float, posY: Float) {}

        /**
         * Called for each ongoing tap (also single tap) (MotionEvent#ACTION_UP}
         * when double tap started and still in double tap mode defined
         * by [DoubleTapPlayerView.getDoubleTapDelay]
         *
         * @param posX x tap position on the root view
         * @param posY y tap position on the root view
         */
        fun onDoubleTapProgressUp(posX: Float, posY: Float) {}

        /**
         * Called when [DoubleTapPlayerView.getDoubleTapDelay] is over
         */
        fun onDoubleTapFinished() {}
    }

    //endregion

    //region Handle pinch player
    private val scaleGestureListener: CustomOnScaleGestureListener by lazy { CustomOnScaleGestureListener() }
    private val scaleGestureDetector: ScaleGestureDetector by lazy {
        ScaleGestureDetector(
            context,
            scaleGestureListener
        )
    }

    inner class CustomOnScaleGestureListener : SimpleOnScaleGestureListener() {
        private var scaleFactor = 0f

        override fun onScale(detector: ScaleGestureDetector): Boolean {
            isScaling = true
            scaleFactor = detector.scaleFactor
            return true
        }

        override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
            return true
        }

        override fun onScaleEnd(detector: ScaleGestureDetector) {
            if (!<EMAIL>) return
            if (<EMAIL>) return
            if (scaleFactor > 1) {
                playerView?.setResizeMode(AspectRatioFrameLayout.RESIZE_MODE_ZOOM, 1)
            } else {
                playerView?.setResizeMode(AspectRatioFrameLayout.RESIZE_MODE_FIT, 2)
            }
        }
    }
    //endregion
    //endregion

    //region Player Listener
    inner class PlayerListener : Player.Listener {
        override fun onPlaybackStateChanged(state: Int) {
            when (state) {
                Player.STATE_READY -> {
                    updatePlayOrPause()
                }
            }
        }

    }
    //endregion

    // region Ads

    private var logoContainer: LogoInStreamContainer? = null

    @UiThread
    fun logoContainer(): LogoInStreamContainer? {
        if (binding.vsLogoContainer.parent == null) {
            // already inflated
            return logoContainer
        }
        return try {
            logoContainer = binding.vsLogoContainer.inflate() as LogoInStreamContainer
            logoContainer
        } catch (e: Exception) {
            Timber.tag("tamlog").e(e, "logoContainer inflate e")
            null
        }
    }
    // endregion Ads


    //region Pairing Control
    private fun isShowCastButton(): Boolean {
        if (MainApplication.INSTANCE.appConfig.castType != 1) return false
        if (isPlayingVipTrailer) return false
        if (fragmentActivity?.isAirlineLayout() == true) return false
        if (isVodOffline) return false
        if (isPlayingTimeShift) return false
        if (screenType is PlayerHandler.ScreenType.Vod) {
            if (curEpisodeData?.isTrailer == 1) {
                return false
            }
        }
        if (playerConfig.isPlayPreview) return false
        return true
    }

    private fun isShowReportButton(): Boolean {
        return playerConfig.enableReportPlayer
    }

    private fun isShowShareButton(): Boolean {
        return playerConfig.enableSharePlayer
    }

    private val remotePlayerState =
        object : PairingControlConnectionHelper.RemotePlayerStateListener {
            override fun onStateChanged(state: RemotePlayerState) {
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Cast -> {
                        fragmentActivity?.runOnUiThread {
                            updatePlayOrPause()
                        }
                    }

                    else -> {}
                }
            }

            override fun onActionEvent(type: String) {
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Cast -> {
                        when (type) {
                            ActionEventType.HIDE_CREDIT -> {
                                fragmentActivity?.runOnUiThread {
                                    playerUIEvents?.onWatchCredit(isSendEventToRemote = false)
                                    binding.clSkipCredit.hide()
                                }
                            }
                        }
                    }

                    else -> {}
                }
            }

            override fun onCastSessionChanged() {
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Cast -> {
                        fragmentActivity?.runOnUiThread {
                            updateCastState()
                        }
                    }

                    else -> {}
                }
            }

        }
    //endregion

    //region Age restrictions
    private fun resetAgeRestrictionState() {
        ageRestrictionAnimationCompleted = false
    }

    fun clearAgeRestrictionData() {
        isWarningContentsOnly = false
        ageRestrictionValue = ""
        ageRestrictionAdvisories = ""
        ageRestrictionPosition = AgeRestrictionPosition.TOP_LEFT
        ageRestrictionDuration = 10000
    }

    fun doShowAgeRestriction(
        checkPoint: Long,
        value: String,
        position: String,
        advisories: String,
        duration: Long
    ) {
        Logger.d("$TAG doShowAgeRestriction")
        //
        isWarningContentsOnly = value.isEmpty()
        ageRestrictionValue = value
        ageRestrictionAdvisories = advisories
        ageRestrictionPosition =
            (AgeRestrictionPosition from position.uppercase()) ?: AgeRestrictionPosition.TOP_LEFT
        ageRestrictionDuration = duration
        updateAgeRestrictionPosition()

        // Age restriction
        updateAgeRestrictionGuideline()
        resetAgeRestrictionState()
        fragmentActivity?.runOnUiThread {
            startAgeRestrictions(showAgeAnimationCompleted = ageRestrictionAnimationCompleted)
        }
        //
    }

    fun checkResumeAgeRestriction() {
        Logger.d("$TAG checkResumeAgeRestriction")
        // Age restriction
        if (!ageRestrictionShowed) {
            fragmentActivity?.runOnUiThread {
                startAgeRestrictions(showAgeAnimationCompleted = ageRestrictionAnimationCompleted)
            }
        }
        //
    }

    fun doHideAgeRestrictions() {
        // Age restriction
        fragmentActivity?.runOnUiThread {
            hideAgeRestrictions(force = true)
        }
        //
    }

    private fun showAgeRestrictions() {
        if (isForceHideAgeRestrictions()) return
        resetAgeRestrictionState()
        val margin = if (context.isTablet()) resources?.getDimensionPixelSize(R.dimen._3sdp)
            ?: 12 else resources?.getDimensionPixelSize(R.dimen._6sdp) ?: 20

        if (ageIsLeftPosition()) {
            binding.layoutAgeRestrictionsLeft.tvAgeRestriction.text = ageRestrictionValue
            binding.layoutAgeRestrictionsLeft.tvWarningContent.text = ageRestrictionAdvisories
        } else {
            binding.layoutAgeRestrictionsRight.tvAgeRestriction.text = ageRestrictionValue
            binding.layoutAgeRestrictionsRight.tvWarningContent.text = ageRestrictionAdvisories
        }

        if (isWarningContentsOnly) {
            if (ageIsLeftPosition()) {
                binding.layoutAgeRestrictionsLeft.run {
                    root.show()
                    tvAgeRestriction.gone()
                    tvWarningContent.visible(alpha = 0f)
                    ivLine.fadeIn(duration = 500) {
                        tvWarningContent.moveRightAndFadeIn(distance = margin.toFloat(), 1500)
                    }
                }
            } else {
                binding.layoutAgeRestrictionsRight.run {
                    root.show()
                    tvAgeRestriction.gone()
                    tvWarningContent.visible(alpha = 0f)
                    ivLine.fadeIn(duration = 500) {
                        tvWarningContent.moveLeftAndFadeIn(distance = margin.toFloat(), 1500)
                    }
                }
            }
        } else {
            if (ageIsLeftPosition()) {
                binding.layoutAgeRestrictionsLeft.run {
                    root.show()
                    tvAgeRestriction.visible(alpha = 0f)
                    if (ageRestrictionAdvisories.isEmpty()) tvWarningContent.gone() else tvWarningContent.visible(
                        alpha = 0f
                    )

                    ivLine.fadeIn(duration = 500) {
                        tvAgeRestriction.moveRightAndFadeIn(distance = margin.toFloat(), 1500)
                        if (ageRestrictionAdvisories.isEmpty()) tvWarningContent.gone() else tvWarningContent.moveRightAndFadeIn(
                            distance = margin.toFloat(),
                            1500
                        )
                    }
                }
            } else {
                binding.layoutAgeRestrictionsRight.run {
                    root.show()
                    tvAgeRestriction.visible(alpha = 0f)
                    if (ageRestrictionAdvisories.isEmpty()) tvWarningContent.gone() else tvWarningContent.visible(
                        alpha = 0f
                    )

                    ivLine.fadeIn(duration = 500) {
                        tvAgeRestriction.moveLeftAndFadeIn(distance = margin.toFloat(), 1500)
                        if (ageRestrictionAdvisories.isEmpty()) tvWarningContent.gone() else tvWarningContent.moveLeftAndFadeIn(
                            distance = margin.toFloat(),
                            1500
                        )
                    }
                }
            }
        }

        Looper.getMainLooper()?.let { handlerCheckHideAgeRestrictions = Handler(it) }
        handlerCheckHideAgeRestrictions?.postDelayed(
            runnableCheckHideAgeRestrictions,
            ageRestrictionDuration
        )

    }

    private fun startAgeRestrictions(showAgeAnimationCompleted: Boolean) {
        Logger.d("$TAG startAgeRestrictions")

        removeHideAgeRestrictionsHandler()
        removeShowNowAgeRestrictionsHandler()

        if (ageRestrictionValue.isNotEmpty() || ageRestrictionAdvisories.isNotEmpty()) {
            ageRestrictionShowed = true
            resetLayoutAgeRestrictions()
            if (ageIsBottomPosition()) {
                playerUIEvents?.hideBuyPackageGuide()
            }
            if (showAgeAnimationCompleted) {
                if (isWarningContentsOnly) {
                    if (ageIsLeftPosition()) {
                        binding.layoutAgeRestrictionsLeft.root.gone()
                    } else {
                        binding.layoutAgeRestrictionsRight.root.gone()
                    }
                } else {
                    Looper.getMainLooper()?.let { handlerShowNowAgeRestrictions = Handler(it) }
                    handlerShowNowAgeRestrictions?.postDelayed(runnableShowNowAgeRestrictions, 250)
                }
            } else {
                Looper.getMainLooper()?.let { handlerCheckShowAgeRestrictions = Handler(it) }
                handlerCheckShowAgeRestrictions?.postDelayed(runnableCheckShowAgeRestrictions, 250)
            }
        } else {
            ageRestrictionShowed = false
            binding.layoutAgeRestrictionsLeft.root.gone()
            binding.layoutAgeRestrictionsRight.root.gone()
        }
    }

    private fun showNowAgeRestrictions() {
        if (isForceHideAgeRestrictions()) return
        val margin = if (context.isTablet()) resources?.getDimensionPixelSize(R.dimen._3sdp)
            ?: 12 else resources?.getDimensionPixelSize(R.dimen._6sdp) ?: 20
        if (ageIsLeftPosition()) {
            binding.layoutAgeRestrictionsLeft.run {
                root.show()
                ivLine.show()
                tvAgeRestriction.text = ageRestrictionValue
                tvAgeRestriction.translationX = margin.toFloat()
                tvAgeRestriction.show()
            }
        } else {
            binding.layoutAgeRestrictionsRight.run {
                root.show()
                ivLine.show()
                tvAgeRestriction.text = ageRestrictionValue
                tvAgeRestriction.translationX = -margin.toFloat()
                tvAgeRestriction.show()
            }
        }
    }

    private fun hideAgeRestrictions(force: Boolean) {
        removeShowAgeRestrictionsHandler()
        removeHideAgeRestrictionsHandler()
        removeShowNowAgeRestrictionsHandler()

        if (force) {
            binding.layoutAgeRestrictionsLeft.root.gone()
            binding.layoutAgeRestrictionsRight.root.gone()
            ageRestrictionShowed = false
        } else {
            if (isWarningContentsOnly) {
                if (ageIsLeftPosition()) {
                    binding.layoutAgeRestrictionsLeft.run {
                        tvWarningContent.resizeHeightAnimation(percent = 0f, duration = 500) {
                            ageRestrictionAnimationCompleted = true
                            ageRestrictionShowed = false
                        }
                    }
                } else {
                    binding.layoutAgeRestrictionsRight.run {
                        tvWarningContent.resizeHeightAnimation(percent = 0f, duration = 500) {
                            ageRestrictionAnimationCompleted = true
                            ageRestrictionShowed = false
                        }
                    }
                }
            } else {
                if (ageIsLeftPosition()) {
                    binding.layoutAgeRestrictionsLeft.run {
                        tvWarningContent.resizeHeightAnimation(percent = 0f, duration = 500) {
                            ageRestrictionAnimationCompleted = true
                            ageRestrictionShowed = false
                        }
                    }
                } else {
                    binding.layoutAgeRestrictionsRight.run {
                        tvWarningContent.resizeHeightAnimation(percent = 0f, duration = 500) {
                            ageRestrictionAnimationCompleted = true
                            ageRestrictionShowed = false
                        }
                    }
                }
            }
        }
    }

    private fun removeShowAgeRestrictionsHandler() {
        handlerCheckShowAgeRestrictions?.removeCallbacks(runnableCheckShowAgeRestrictions)
        handlerCheckShowAgeRestrictions = null
    }

    private fun removeHideAgeRestrictionsHandler() {
        handlerCheckHideAgeRestrictions?.removeCallbacks(runnableCheckHideAgeRestrictions)
        handlerCheckHideAgeRestrictions = null
    }

    private fun removeShowNowAgeRestrictionsHandler() {
        handlerCheckHideAgeRestrictions?.removeCallbacks(runnableCheckHideAgeRestrictions)
        handlerCheckHideAgeRestrictions = null
    }

    private fun ageIsLeftPosition() =
        ageRestrictionPosition == AgeRestrictionPosition.TOP_LEFT || ageRestrictionPosition == AgeRestrictionPosition.BOTTOM_LEFT

    private fun ageIsBottomPosition() =
        ageRestrictionPosition == AgeRestrictionPosition.BOTTOM_LEFT || ageRestrictionPosition == AgeRestrictionPosition.BOTTOM_RIGHT

    private fun resetLayoutAgeRestrictions() {
        if (ageIsLeftPosition()) {
            binding.layoutAgeRestrictionsLeft.run {
                root.gone()
                root.post {
                    ivLine.gone()
                    tvAgeRestriction.gone()
                    tvWarningContent.gone()
                    tvAgeRestriction.translationX = 0f
                    tvWarningContent.translationX = 0f
                    //
                    val params = tvWarningContent.layoutParams
                    params.width = ViewGroup.LayoutParams.MATCH_PARENT
                    params.height = ViewGroup.LayoutParams.WRAP_CONTENT
                    tvWarningContent.layoutParams = params
                }
            }
        } else {
            binding.layoutAgeRestrictionsRight.run {
                root.gone()
                root.post {
                    ivLine.gone()
                    tvAgeRestriction.gone()
                    tvWarningContent.gone()
                    tvAgeRestriction.translationX = 0f
                    tvWarningContent.translationX = 0f
                    //
                    val params = tvWarningContent.layoutParams
                    params.width = ViewGroup.LayoutParams.MATCH_PARENT
                    params.height = ViewGroup.LayoutParams.WRAP_CONTENT
                    tvWarningContent.layoutParams = params
                }
            }
        }
    }

    private fun updateAgeRestrictionPosition() {
        try {
            val ageRestrictionMargin =
                fragmentActivity?.resources?.getDimensionPixelSize(R.dimen.age_restriction_margin)
                    ?: 14
            when (ageRestrictionPosition) {
                AgeRestrictionPosition.TOP_LEFT -> {
                    // Constraint layout
                    ConstraintSet().apply {
                        clone(binding.playerLayoutTouch)
                        connect(
                            R.id.layout_age_restrictions_left,
                            ConstraintSet.TOP,
                            ConstraintSet.PARENT_ID,
                            ConstraintSet.TOP
                        )
                        connect(
                            R.id.layout_age_restrictions_left,
                            ConstraintSet.START,
                            R.id.guideline_safe_start,
                            ConstraintSet.START
                        )
                        connect(
                            R.id.layout_age_restrictions_left,
                            ConstraintSet.END,
                            R.id.guideline_safe_end,
                            ConstraintSet.END
                        )
                        clear(R.id.layout_age_restrictions_left, ConstraintSet.BOTTOM)
                    }.applyTo(binding.playerLayoutTouch)
                    // Margin layout
                    val layoutParams =
                        binding.layoutAgeRestrictionsLeft.root.layoutParams as? ConstraintLayout.LayoutParams
                    if (layoutParams != null) {
                        layoutParams.leftMargin = ageRestrictionMargin
                        layoutParams.topMargin = ageRestrictionMargin
                        layoutParams.rightMargin = 0
                        layoutParams.bottomMargin = 0

                        binding.layoutAgeRestrictionsLeft.root.layoutParams = layoutParams
                    }
                    //
                }

                AgeRestrictionPosition.TOP_RIGHT -> {
                    // Constraint layout
                    ConstraintSet().apply {
                        clone(binding.playerLayoutTouch)
                        connect(
                            R.id.layout_age_restrictions_right,
                            ConstraintSet.TOP,
                            ConstraintSet.PARENT_ID,
                            ConstraintSet.TOP
                        )
                        connect(
                            R.id.layout_age_restrictions_right,
                            ConstraintSet.START,
                            R.id.guideline_safe_start,
                            ConstraintSet.START
                        )
                        connect(
                            R.id.layout_age_restrictions_right,
                            ConstraintSet.END,
                            R.id.guideline_safe_end,
                            ConstraintSet.END
                        )
                        clear(R.id.layout_age_restrictions_right, ConstraintSet.BOTTOM)
                    }.applyTo(binding.playerLayoutTouch)
                    // Margin layout
                    val layoutParams =
                        binding.layoutAgeRestrictionsRight.root.layoutParams as? ConstraintLayout.LayoutParams
                    if (layoutParams != null) {
                        layoutParams.leftMargin = 0
                        layoutParams.topMargin = ageRestrictionMargin
                        layoutParams.rightMargin = ageRestrictionMargin
                        layoutParams.bottomMargin = 0

                        binding.layoutAgeRestrictionsRight.root.layoutParams = layoutParams
                    }
                    //
                }

                AgeRestrictionPosition.BOTTOM_LEFT -> {
                    // Constraint layout
                    ConstraintSet().apply {
                        clone(binding.playerLayoutTouch)
                        connect(
                            R.id.layout_age_restrictions_left,
                            ConstraintSet.BOTTOM,
                            ConstraintSet.PARENT_ID,
                            ConstraintSet.BOTTOM
                        )
                        connect(
                            R.id.layout_age_restrictions_left,
                            ConstraintSet.START,
                            R.id.guideline_safe_start,
                            ConstraintSet.START
                        )
                        connect(
                            R.id.layout_age_restrictions_left,
                            ConstraintSet.END,
                            R.id.guideline_safe_end,
                            ConstraintSet.END
                        )
                        clear(R.id.layout_age_restrictions_left, ConstraintSet.TOP)
                    }.applyTo(binding.playerLayoutTouch)
                    // Margin layout
                    val layoutParams =
                        binding.layoutAgeRestrictionsLeft.root.layoutParams as? ConstraintLayout.LayoutParams
                    if (layoutParams != null) {
                        layoutParams.leftMargin = ageRestrictionMargin
                        layoutParams.topMargin = 0
                        layoutParams.rightMargin = 0
                        layoutParams.bottomMargin = ageRestrictionMargin

                        binding.layoutAgeRestrictionsLeft.root.layoutParams = layoutParams
                    }
                    //
                }

                AgeRestrictionPosition.BOTTOM_RIGHT -> {
                    // Constraint layout
                    ConstraintSet().apply {
                        clone(binding.playerLayoutTouch)
                        connect(
                            R.id.layout_age_restrictions_right,
                            ConstraintSet.BOTTOM,
                            ConstraintSet.PARENT_ID,
                            ConstraintSet.BOTTOM
                        )
                        connect(
                            R.id.layout_age_restrictions_right,
                            ConstraintSet.START,
                            R.id.guideline_safe_start,
                            ConstraintSet.START
                        )
                        connect(
                            R.id.layout_age_restrictions_right,
                            ConstraintSet.END,
                            R.id.guideline_safe_end,
                            ConstraintSet.END
                        )
                        clear(R.id.layout_age_restrictions_right, ConstraintSet.TOP)
                    }.applyTo(binding.playerLayoutTouch)
                    // Margin layout
                    val layoutParams =
                        binding.layoutAgeRestrictionsRight.root.layoutParams as? ConstraintLayout.LayoutParams
                    if (layoutParams != null) {
                        layoutParams.leftMargin = 0
                        layoutParams.topMargin = 0
                        layoutParams.rightMargin = ageRestrictionMargin
                        layoutParams.bottomMargin = ageRestrictionMargin

                        binding.layoutAgeRestrictionsRight.root.layoutParams = layoutParams
                    }
                    //
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun updateAgeRestrictionGuideline() {
        if (isFullscreen) {
            binding.layoutAgeRestrictionsLeft.guideline.setGuidelinePercent(0.5f)
            binding.layoutAgeRestrictionsRight.guideline.setGuidelinePercent(0.5f)
        } else {
            val percent = if (ageRestrictionPosition == AgeRestrictionPosition.TOP_LEFT
                || ageRestrictionPosition == AgeRestrictionPosition.BOTTOM_LEFT
            ) {
                0.6f
            } else 0.4f
            binding.layoutAgeRestrictionsLeft.guideline.setGuidelinePercent(percent)
            binding.layoutAgeRestrictionsRight.guideline.setGuidelinePercent(percent)
        }
    }

    enum class AgeRestrictionPosition(val value: String) {
        TOP_LEFT(value = "TL"),
        TOP_RIGHT(value = "TR"),
        BOTTOM_LEFT(value = "BL"),
        BOTTOM_RIGHT(value = "BR");

        companion object {
            infix fun from(value: String): AgeRestrictionPosition? =
                AgeRestrictionPosition.values().firstOrNull { it.value == value }
        }
    }
    //endregion

    // region Handle Display Cutouts
    fun updatePaddingDisplayCutouts(padding: List<Int>, rotation: Int) {
        when (rotation) {
            Surface.ROTATION_0, // Bottom - reset the padding in portrait
            Surface.ROTATION_180,
                -> { // Top - reset the padding if upside down
                binding.apply {
                    guidelineSafeStart.setGuidelineBegin(0)
                    guidelineSafeEnd.setGuidelineEnd(0)
                    guidelineControlSafeStart.setGuidelineBegin(0)
                    guidelineControlSafeEnd.setGuidelineEnd(0)
                    guidelineCenterSafeStart.setGuidelineBegin(0)
                    guidelineCenterSafeEnd.setGuidelineEnd(0)

                    layoutRecommendPortrait.guidelineSafeRecommendStart.setGuidelineBegin(0)
                    layoutRecommendPortrait.guidelineSafeRecommendEnd.setGuidelineEnd(0)
                    layoutRecommendLandscape.guidelineSafeRecommendStart.setGuidelineBegin(0)
                    layoutRecommendLandscape.guidelineSafeRecommendEnd.setGuidelineEnd(0)
                }
            }

            Surface.ROTATION_90, // Left
            Surface.ROTATION_270,
                -> { // Right
                binding.apply {
                    guidelineSafeStart.setGuidelineBegin(padding[0])
                    guidelineSafeEnd.setGuidelineEnd(padding[1])
                    guidelineControlSafeStart.setGuidelineBegin(padding[0])
                    guidelineControlSafeEnd.setGuidelineEnd(padding[1])
                    guidelineCenterSafeStart.setGuidelineBegin(padding[0])
                    guidelineCenterSafeEnd.setGuidelineEnd(padding[1])

                    layoutRecommendPortrait.guidelineSafeRecommendStart.setGuidelineBegin(padding[0])
                    layoutRecommendPortrait.guidelineSafeRecommendEnd.setGuidelineEnd(padding[1])
                    layoutRecommendLandscape.guidelineSafeRecommendStart.setGuidelineBegin(padding[0])
                    layoutRecommendLandscape.guidelineSafeRecommendEnd.setGuidelineEnd(padding[1])
                }
            }

            else -> {}
        }
    }
    // endregion

    //region Pip Handler
    private fun isForeHidePlayerUI(): Boolean {
        return isPiPMode
    }

    private fun isForceHideSkipIntroCredit(): Boolean {
        return isPiPMode
    }

    private fun isForceHideRecommend(): Boolean {
        return isPiPMode
    }

    private fun isForceHideAgeRestrictions(): Boolean {
        return isPiPMode
    }

    private fun isForceHideSituationWarning(): Boolean {
        return false
    }

    //endregion

    fun showBuyPackageGuide(buyPackageGuide: BuyPackageGuide) {
        binding.buyPackageGuide.apply {

            tvDescription.text = buyPackageGuide.description
            tvBuyNow.text = buyPackageGuide.button

            val placeholderId = R.drawable.image_placeholder_guide
            if (buyPackageGuide.imageUrl.isNotBlank()) {
                ImageProxy.load(
                    context = binding.root.context,
                    url = buyPackageGuide.imageUrl,
                    width = binding.context.resources.getDimensionPixelSize(R.dimen.buy_package_guide_logo_width),
                    height = binding.context.resources.getDimensionPixelSize(R.dimen.buy_package_guide_logo_height),
                    target = ivLogo,
                    placeHolderId = placeholderId,
                    errorDrawableId = placeholderId
                )
            } else {
                ImageProxy.loadLocal(
                    context = binding.root.context,
                    data = placeholderId,
                    width = binding.context.resources.getDimensionPixelSize(R.dimen.buy_package_guide_logo_width),
                    height = binding.context.resources.getDimensionPixelSize(R.dimen.buy_package_guide_logo_height),
                    target = ivLogo,
                    placeHolderId = placeholderId,
                    errorDrawableId = placeholderId,
                )
            }

            clContent.setGradientDrawableFromHexColors(
                hexColors = buyPackageGuide.gradientHexColors,
                defaultHexColor = BuyPackageGuide.DEFAULT_COLOR,
                cornerRadius = binding.context.resources.getDimension(R.dimen.buy_package_guide_bg_corners)
            )

            root.show()
        }
    }

    fun hideBuyPackageGuide() {
        try {
            binding.buyPackageGuide.root.hide()
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    fun isTooltipShowing(): Boolean {
        return tooltipsView.isShow()
    }

    fun isAgeRestrictionShowed(): Boolean {
        return ageRestrictionShowed
    }

    fun isAgeRestrictionShowedInBottom(): Boolean {
        return isAgeRestrictionShowed() && ageIsBottomPosition()
    }

    //endregion

    //region Custom SeekBar
    private var customSeekBarView: PlayerVodSeekbarView? = null
    private var isUsingCustomSeekBar = false

}