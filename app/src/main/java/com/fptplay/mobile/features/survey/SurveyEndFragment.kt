package com.fptplay.mobile.features.survey

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.fptplay.mobile.R
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.databinding.SurveyStartEndFragmentBinding
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger

@dagger.hilt.android.AndroidEntryPoint
class SurveyEndFragment : BaseFragment<SurveyViewModel.SurveyState, SurveyViewModel.SurveyIntent>() {
    override val viewModel: SurveyViewModel by activityViewModels()
    private var _binding: SurveyStartEndFragmentBinding? = null
    private val binding get() = _binding!!
    override val hasEdgeToEdge: Boolean = true
    override val handleBackPressed: Boolean = true

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = SurveyStartEndFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun backHandler() {
        // Do nothing, prevent back press
    }

    override fun initData() {
        binding.apply {
            tvSurveyTitle.text = viewModel.dataQuestion?.message?.title
            tvSurveyDescription.text = viewModel.dataQuestion?.message?.description
            ImageProxy.load(
                context = binding.root.context,
                url = viewModel.dataQuestion?.media?.iconSurvey,
                width = binding.ivSurveyImage.width,
                height = binding.ivSurveyImage.height,
                target = binding.ivSurveyImage
            )
            btnStartSurvey.text = viewModel.dataQuestion?.message?.textBtn
        }
    }

    override fun bindEvent() {
        super.bindEvent()
        binding.btnStartSurvey.setOnClickListener {
            viewModel.dispatchIntent(
                SurveyViewModel.SurveyIntent.SendSurvey(
                    type = viewModel.dataStart.typeEvent,
                    surveyId = viewModel.dataStart.surveyId,
                    eventId = viewModel.dataStart.eventId,
                    question = viewModel.dataQuestion?.questions ?: emptyList()
                ))
        }
    }

    override fun SurveyViewModel.SurveyState.toUI() {
        when (this) {
            is SurveyViewModel.SurveyState.Error -> {
                (parentFragment?.parentFragment as? SurveyFragment)?.startHome()
            }

            is SurveyViewModel.SurveyState.ErrorNoInternet -> {
                showWarningDialog(
                    getString(R.string.error_no_internet),
                    textConfirm = getString(R.string.all_retry),
                    onConfirm = {
                        viewModel.dispatchIntent(
                            SurveyViewModel.SurveyIntent.SendSurvey(
                                type = viewModel.dataStart.typeEvent,
                                surveyId = viewModel.dataStart.surveyId,
                                eventId = viewModel.dataStart.eventId,
                                question = viewModel.dataQuestion?.questions ?: emptyList()
                            ))
                    })
            }

            is SurveyViewModel.SurveyState.ErrorRequiredLogin -> {
                (parentFragment?.parentFragment as? SurveyFragment)?.startHome()
            }
            is SurveyViewModel.SurveyState.SendSurveyResult -> {
                (parentFragment?.parentFragment as? SurveyFragment)?.startHome()
            }
            else -> {}
        }
    }
}
