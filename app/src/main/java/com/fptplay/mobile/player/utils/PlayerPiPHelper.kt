package com.fptplay.mobile.player.utils

import android.app.AppOpsManager
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Rect
import android.os.Build
import android.os.Process
import android.util.Rational
import android.view.View
import com.fptplay.mobile.player.handler.PlayerHandler
import com.fptplay.mobile.player.media_session.MediaSessionHandler.DataSource
import androidx.media3.common.VideoSize
import com.xhbadxx.projects.module.domain.entity.fplay.payment.PackageUser
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

object PlayerPiPHelper {
    private const val PLAYER_PIP_WIDTH = 1920
    private const val PLAYER_PIP_HEIGHT = 1080

    const val DEFAULT_PIP_MIN_WIDTH = 432
    const val DEFAULT_PIP_MIN_HEIGHT = 243

    private const val PACKAGE_PLAN_DIS_ADS_VALUE = "dis-ads"

    private var isPlayerFullscreen = false

    //region Picture in Picture
    fun userPackagePlanSupportPictureInPicture(sharedPreferences: SharedPreferences): Boolean {
        return sharedPreferences.isEnablePIP()
    }

    fun checkOsVersionSupportPictureInPicture(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.O
    }

    fun isOsSupportAutoEnablePictureInPicture(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.S
    }

    /**
     * Permission is available from Android 26+ (Oreo).
     * On the Android 24+ it's allowed for all apps by default.
     * It's not available on Android 23 and lower.
     */
    fun checkPictureInPicturePermission(context: Context): Boolean {
        try {
            val appOpsManager = context.getSystemService(Context.APP_OPS_SERVICE) as? android.app.AppOpsManager
            appOpsManager?.let {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    val mode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        it.unsafeCheckOpNoThrow(AppOpsManager.OPSTR_PICTURE_IN_PICTURE, Process.myUid(), context.packageName)
                    } else {
                        @Suppress("DEPRECATION")
                        it.checkOpNoThrow(AppOpsManager.OPSTR_PICTURE_IN_PICTURE, Process.myUid(), context.packageName)
                    }
                    return mode == AppOpsManager.MODE_ALLOWED
                            && context.packageManager?.hasSystemFeature(PackageManager.FEATURE_PICTURE_IN_PICTURE) == true
                } else {
                    return false
                }
            } ?: kotlin.run {
                return false
            }
        } catch (e: Exception) {
            return false
        }
    }

    fun checkRulePlayBackgroundAudioPiP(sharedPreferences: SharedPreferences): Boolean {
        return userPackagePlanSupportPictureInPicture(sharedPreferences)
                && !checkOsVersionSupportPictureInPicture()
    }

    fun isPlayingLive(dataSource: DataSource): Boolean {
        val isPlayingLive = if (dataSource.isPlayVipTrailer) {
            true
        } else when (dataSource.screenType) {
            is PlayerHandler.ScreenType.Vod -> false
            is PlayerHandler.ScreenType.Live -> !dataSource.isPlayTimeShift
            is PlayerHandler.ScreenType.Premiere -> true
            else -> true
        }
        return isPlayingLive
    }
    //endregion


    // region UserType
    fun saveSupportPictureInPicture(sharedPreferences: SharedPreferences, isSupport: Boolean) {
        sharedPreferences.saveEnablePIP(isSupport)
    }
    fun userHaveSupportPip(data: List<PackageUser>?): Boolean {
        // TODO: Update rule support picture in picture
        if (data.isNullOrEmpty()) {
            return false
        }
        var haveEnablePiP = false
        for (packageUser in data) {
            if (packageUser.planType == PACKAGE_PLAN_DIS_ADS_VALUE && packageUser.isValid()) {
                haveEnablePiP = true
                break
            }
        }

        return haveEnablePiP
    }

    private fun PackageUser.isValid(): Boolean {
        val dateLeft = leftDate
        return dateLeft != null && dateLeft > 0
    }
    // endregion UserType

    //region Picture in Picture Logics
    fun storagePlayerMode(isFullscreen: Boolean) {
        isPlayerFullscreen = isFullscreen
    }

    fun restorePlayerMode(enterFullscreen: () -> Unit, exitFullscreen: () -> Unit) {
        if (isPlayerFullscreen) enterFullscreen.invoke() else exitFullscreen.invoke()
    }

    fun getAspectRatioForPiP(playerVideoSize: VideoSize?): Rational {
        playerVideoSize?.let {
            if (it.width > 0 && it.height > 0) {
                return Rational(it.width, it.height)
            } else {
                return Rational(PLAYER_PIP_WIDTH, PLAYER_PIP_HEIGHT)
            }
        } ?: kotlin.run {
            return Rational(PLAYER_PIP_WIDTH, PLAYER_PIP_HEIGHT)
        }
    }

    fun getVisibleRectForPiP(epvPlayer: View, playerVideoSize: VideoSize?): Rect {
        val visibleRect = Rect()
        epvPlayer.getGlobalVisibleRect(visibleRect)

        playerVideoSize?.let {
            val playerLayoutWidth =  max(abs(epvPlayer.right - epvPlayer.left), DEFAULT_PIP_MIN_WIDTH)
            val playerLayoutHeight = max(abs(epvPlayer.bottom - epvPlayer.top), DEFAULT_PIP_MIN_HEIGHT)
            val playerContentWidth = it.width
            val playerContentHeight = it.height
            if (playerLayoutWidth > 0 && playerLayoutHeight > 0) {
                // Way 1:
//                val videoAspectRatio = playerContentWidth.toFloat() / playerContentHeight.toFloat()
//                val layoutAspectRatio = playerLayoutWidth.toFloat() / playerLayoutHeight.toFloat()
//
//                val scaledVideoWidth: Int
//                val scaledVideoHeight: Int
//
//                if (layoutAspectRatio > videoAspectRatio) {
//                    // Layout is wider than the video aspect ratio, video will be full height
//                    scaledVideoHeight = playerLayoutHeight
//                    scaledVideoWidth = (scaledVideoHeight * videoAspectRatio).toInt()
//                } else {
//                    // Layout is taller than the video aspect ratio, video will be full width
//                    scaledVideoWidth = playerLayoutWidth
//                    scaledVideoHeight = (scaledVideoWidth / videoAspectRatio).toInt()
//                }
//
//                val horizontalOffset = (playerLayoutWidth - scaledVideoWidth) / 2
//                val verticalOffset = (playerLayoutHeight - scaledVideoHeight) / 2
//
//                visibleRect.left += horizontalOffset
//                visibleRect.right = visibleRect.left + scaledVideoWidth
//                visibleRect.top += verticalOffset
//                visibleRect.bottom = visibleRect.top + scaledVideoHeight

                // Way 2:
                // Calculate the scale factor
                val widthScale = playerLayoutWidth.toFloat() / playerContentWidth
                val heightScale = playerLayoutHeight.toFloat() / playerContentHeight
                val scale = min(widthScale, heightScale)

                // Calculate the offsets (if video is centered in the player layout)
                val offsetX = (playerLayoutWidth - playerContentWidth * scale) / 2
                val offsetY = (playerLayoutHeight - playerContentHeight * scale) / 2

                // Adjust the visibleRect to contain only the video content
                visibleRect.left = (visibleRect.left + offsetX).toInt()
                visibleRect.top = (visibleRect.top + offsetY).toInt()
                visibleRect.right = (visibleRect.left + playerContentWidth * scale).toInt()
                visibleRect.bottom = (visibleRect.top + playerContentHeight * scale).toInt()
            }
        }
        return visibleRect
    }
    //endregion
}