<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorBlack80">

    <ImageButton
        android:id="@+id/ib_close"
        android:layout_width="@dimen/_30sdp"
        android:layout_height="@dimen/_30sdp"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_marginEnd="@dimen/_20sdp"
        android:background="@color/transparent"
        android:src="@drawable/ic_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/margin_common"
        android:fontFamily="@font/sf_pro_display_bold"
        android:text="@string/player_playlist"
        android:textColor="@color/white"
        android:layout_marginHorizontal="@dimen/margin_common_large"
        android:textSize="@dimen/moment_playlist_title_text_size"
        app:layout_constraintBottom_toTopOf="@id/rcv_main" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcv_main"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/player_playlist_overlay_list_item_margin_bottom"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>