package com.fptplay.mobile.features.comment_v2

import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.os.bundleOf
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.contains
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.navArgs
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppConfig
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppNativeDataUpdateEventData
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppNativeEventListener
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppNativeLoadSuccessEventData
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppNativeMethodProvider
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppRenderEffectData
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppUIInterface
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppUsage
import com.fpl.plugin.mini_app_sdk.android_view.MiniAppView
import com.fpl.plugin.mini_app_sdk.android_view.ResponseMetadata
import com.fpl.plugin.mini_app_sdk.android_view.Token
import com.fpl.plugin.mini_app_sdk.entity.MiniAppManifest
import com.fpl.plugin.mini_app_sdk.model.DisplayModeOptions
import com.fpl.plugin.mini_app_sdk.model.MiniAppDeviceInfo
import com.fpl.plugin.mini_app_sdk.model.MiniAppDisplayInfo
import com.fpl.plugin.mini_app_sdk.model.MiniAppPlatformInfo
import com.fpl.plugin.mini_app_sdk.model.MiniAppUserInfo
import com.fpl.plugin.mini_app_sdk.model.OpenPopupData
import com.fpl.plugin.mini_app_sdk.model.OpenPopupType
import com.fpl.plugin.mini_app_sdk.model.RenderEffectOptions
import com.fptplay.mobile.BuildConfig
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.hideKeyboard
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.showKeyboard
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.DateTimeUtils
import com.fptplay.mobile.common.utils.DeeplinkUtils
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils.isEmulator
import com.fptplay.mobile.databinding.CommentV2FragmentBinding
import com.fptplay.mobile.databinding.ErrorLayoutBinding
import com.fptplay.mobile.features.comment_v2.data.CommentInfo
import com.fptplay.mobile.features.comment_v2.data.CommentTrackingData
import com.fptplay.mobile.features.comment_v2.utils.CommentV2Util
import com.fptplay.mobile.features.comment_v2.utils.CommentV2Util.mapToMiniAppManifest
import com.fptplay.mobile.features.pladio.util.context
import com.fptplay.mobile.player.utils.visible
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.image.ImageProxy
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import org.json.JSONObject
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class CommentV2Fragment : BaseFragment<CommentV2ViewModel.CommentState, CommentV2ViewModel.CommentIntent>() {

    @Inject
    lateinit var trackingInfo: Infor

    @Inject
    lateinit var trackingProxy: TrackingProxy

    private var _binding: CommentV2FragmentBinding? = null
    private val binding get() = _binding!!

    private var _errorBinding: ErrorLayoutBinding? = null
    private val errorBinding get() = _errorBinding!!

    override val viewModel: CommentV2ViewModel by activityViewModels()

    private val safeArgs: CommentV2FragmentArgs by navArgs()

    private var chatDisplayMode = MiniAppDisplayInfo.Mode.Default

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    private var errorType: String = ""

    private var isShowErrorLayout : Boolean = true

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = CommentV2FragmentBinding.inflate(inflater,container,false)
        _errorBinding = ErrorLayoutBinding.bind(binding.root)
        return binding.root
    }

    override fun setUpEdgeToEdge() {}
    
    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.getMiniApp()?.let {
            removeViewIfHaveParent(it.second)
        }
        viewModel.clearMiniApp()
        viewModel.trackingData = null
        _errorBinding = null
        _binding = null
    }

    override fun bindData() {
        super.bindData()
        viewModel.getMiniApp()?.let {
            viewModel.saveMiniApp(loadSuccess = false, it.second)
        }
        isShowErrorLayout = true
        viewModel.saveContentId(safeArgs.contentId)
        viewModel.dispatchIntent(
            CommentV2ViewModel.CommentIntent.GetMiniAppManifest(
                context = requireContext(),
                miniAppId = CommentV2Util.COMMENT_MINI_APP,
                force = !(viewModel.getMiniApp()?.first ?: false)
            )
        )
    }

    override fun bindComponent() {
        super.bindComponent()
        bindMiniAppView()
        viewModel.getDataCom()?.let { page ->
            binding.tvTitle.text = CommentV2Util.getTitleWithCommentTotal(resources.getString(R.string.title_comment), page.total)
        }
    }

    override fun bindEvent() {
        super.bindEvent()
        binding.apply {
            ivClose.setOnClickListener {
                backHandler()
            }
            errorBinding.btnRetry.setOnClickListener {
                bindData()
            }
        }

    }

    override fun CommentV2ViewModel.CommentState.toUI() {
        when(this) {
            is CommentV2ViewModel.CommentState.Loading -> {

            }
            is CommentV2ViewModel.CommentState.ErrorRequiredLogin -> {
                when(intent) {
                    is CommentV2ViewModel.CommentIntent.GetMiniAppManifest -> {
                        showErrorByInternetLayout(CommentV2Util.MANIFEST_ERROR)
                    }
                    else -> {}
                }
            }
            is CommentV2ViewModel.CommentState.Error -> {
                when(intent) {
                    is CommentV2ViewModel.CommentIntent.GetMiniAppManifest -> {
                        showOtherErrorLayout(CommentV2Util.MANIFEST_ERROR)
                    }
                    else -> {}
                }
            }
            is CommentV2ViewModel.CommentState.ResultMiniAppManifest -> {
                if (data.code == CommentV2Util.API_SUCCESS_CODE.toString()) {
                    hideErrorLayout()
                    val manifest = data.data.mapToMiniAppManifest(safeArgs.contentId,safeArgs.episodeId)
                    loadMiniAppManifest(manifest, force)
                }
            }
            is CommentV2ViewModel.CommentState.SendTrackingLog -> {
                sendTrackingLog(meta = data)
                viewModel.dispatchIntent(CommentV2ViewModel.CommentIntent.UpdateAndSendTrackingComplete)
            }
            else -> {}
        }
    }

    private fun bindMiniAppView() {
        if (viewModel.getContentId() != safeArgs.contentId) {
            viewModel.clearMiniApp()
        }
        val miniApp = viewModel.getMiniApp() ?: kotlin.run {
            Pair(false, MiniAppView(binding.context))
        }

        removeViewIfHaveParent(miniApp.second)

        miniApp.apply {
            second.apply {
                prepareMiniAppUsage(
                    MiniAppUsage(
                        scope = viewLifecycleOwner.lifecycleScope,
                        activityForPermissionContract = requireActivity(),
                        miniAppNativeMethodProvider = miniAppNativeMethodProvider,
                        miniAppNativeEventListener = miniAppNativeEventListener,
                        miniAppUIController = miniAppUIController
                    )
                )
                prepareMiniAppconfig(MiniAppConfig(
                    manifestLoadTimeoutMs = CommentV2Util.MANIFEST_LOAD_TIMEOUT_MS
                ))
                layoutParams = FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.MATCH_PARENT
                )
            }
            binding.flMiniAppView.addView(miniApp.second)
            viewModel.saveMiniApp(first, second)

            binding.flMiniAppView.apply {
                postDelayed(
                    {
                        try {
                            _binding?.flMiniAppView?.visible()
                        } catch (ex: Exception) {
                            ex.printStackTrace()
                        }
                    }, 300L
                )
            }
        }
    }

    private fun removeViewIfHaveParent(view: MiniAppView) {
        val parentView = view.parent
        if (parentView != null) {
            if (parentView is ViewGroup && parentView.contains(view)) {
                parentView.removeView(view)
            }
        }
    }

    private fun loadMiniAppManifest(data: MiniAppManifest, force: Boolean = false) {
        viewModel.getMiniApp()?.second?.loadManifest(
            miniAppManifest = data,
            force = force
        )
    }

    private fun getChatDisplayInfo(): MiniAppDisplayInfo {
        var width = -1
        var height = -1
        if (_binding != null) {
            width = binding.flMiniAppView.measuredWidth
            height = binding.flMiniAppView.measuredHeight
        }
        return MiniAppDisplayInfo(
            success = true,
            mode = chatDisplayMode.value,
            width = width,
            height = height
        )
    }

    private fun requireLogin() {
        val extras = Bundle()
        extras.putString(Constants.ACTION_AFTER_LOGIN_KEY, Constants.ACTION_OPEN_COMMENT)
        // add extendsArgs If you want to reopen comments after navigate to login
        if (safeArgs.openInDialog) {
            parentFragment?.setFragmentResult(CommentV2Util.COMMENT_V2_RESULT, bundleOf(CommentV2Util.COMMENT_V2_KEY to CommentV2Util.COMMENT_REQUIRED_LOGIN))
        } else {
            parentFragment?.parentFragment?.navigateToLoginWithParams(extendsArgs = extras)
        }
    }

    private fun showKeyboard() {
        _binding?.let {
            binding.root.showKeyboard()
        }
    }

    private fun hideKeyboard() {
        _binding?.let {
            binding.root.hideKeyboard()
        }
    }

    private val miniAppNativeMethodProvider = object : MiniAppNativeMethodProvider {
        override fun closeFullScreen(requestId: String) {}

        override fun genApiToken(
            requestId: String,
            responseCallback: (Token, ResponseMetadata) -> Unit
        ) {
            viewModel.getPartnerToken(responseCallback)
        }

        override fun getDisplayInfo(requestId: String): MiniAppDisplayInfo {
            return getChatDisplayInfo()
        }

        override fun getPlatformInfo(requestId: String): MiniAppPlatformInfo {
            return MiniAppPlatformInfo(
                platform = "android",
                version = Build.VERSION.SDK_INT.toString(),
                deviceModel = Build.MODEL,
                isEmulator = isEmulator(),
                deviceType = if(context?.isTablet() == true) MiniAppPlatformInfo.DEVICE_TYPE_TABLET else MiniAppPlatformInfo.DEVICE_TYPE_PHONE
            )
        }

        override fun getDeviceInfo(requestId: String): MiniAppDeviceInfo {
            return MiniAppDeviceInfo(
                deviceId = sharedPreferences.androidId(),
                version = BuildConfig.VERSION_NAME
            )
        }

        override fun getUserId(): String {
            return sharedPreferences.userId()
        }

        override fun getUserInfo(
            requestId: String,
            responseCallback: (MiniAppUserInfo?, ResponseMetadata) -> Unit
        ) {
            viewModel.getUserInfo(
                requestId = requestId,
                responseCallback = { userInfo, responseMetadata ->
                    responseCallback(userInfo, responseMetadata)
                }
            )
        }

        override fun interactContent(requestId: String, action: String) {}

        @Deprecated("Use [getUserInfo] instead", ReplaceWith(
            "MiniAppUserInfo()",
            "com.fpl.plugin.mini_app_sdk.model.MiniAppUserInfo"
        ))
        override suspend fun onProvideUserInfo(): MiniAppUserInfo {
            return MiniAppUserInfo()
        }

        override fun openDeeplink(url: String) {
            // process deeplink or open browser
            DeeplinkUtils.parseDeepLinkAndExecute(
                deeplink = url,
                useWebViewInApp = false,
                trackingInfo = trackingInfo,
                isDeeplinkCalledInApp = true
            )
        }

        override fun openFullScreen(requestId: String) {}

        override fun openPopUp(requestId: String, type: OpenPopupType, options: OpenPopupData?) {}

        override fun renderEffect(
            requestId: String,
            effectData: MiniAppRenderEffectData,
            customAnimation: RenderEffectOptions?
        ) {}

        override fun requestLogin(requestId: String) {
            lifecycleScope.launch {
                requireLogin()
            }
        }

        override fun setDisplayMode(
            requestId: String,
            mode: MiniAppDisplayInfo.Mode,
            options: DisplayModeOptions?
        ): MiniAppDisplayInfo {
            return getChatDisplayInfo()
        }

        override fun hideKeyboard(requestId: String) {
            hideKeyboard()
        }

        override fun showKeyboard(requestId: String) {
            showKeyboard()
        }

        override fun sendLog(
            requestId: String,
            type: String,
            message: String,
            timestamp: Long,
            metadata: JSONObject?
        ) {
            metadata?.let {
                CommentV2Util.parseLogInfoFromJson(it)?.let { info ->
                    viewModel.dispatchIntent(CommentV2ViewModel.CommentIntent.TriggerUpdateTrackingData(info, sendTrackingAfterUpdate = true))
                }
            }
        }
    }

    private val miniAppNativeEventListener = object : MiniAppNativeEventListener {
        override fun loadManifestTimeout(manifest: MiniAppManifest?) {
            showOtherErrorLayout(CommentV2Util.WEBVIEW_ERROR)
        }

        override fun loadSuccess(requestId: String, data: MiniAppNativeLoadSuccessEventData) {
            super.loadSuccess(requestId, data)
            isShowErrorLayout = !data.isSuccess
            hideErrorLayout()
            viewModel.getMiniApp()?.let {
                viewModel.saveMiniApp(loadSuccess = data.isSuccess, it.second)
            }
        }

        override fun dataUpdate(requestId: String, data: MiniAppNativeDataUpdateEventData) {
            data.data?.let {
                CommentV2Util.parseInfoFromJson(it)?.let { info ->
                    updateCommentData(info)
                }
            }
        }
    }

    private fun updateCommentData(info: CommentInfo) {
        if (_binding != null) {
            binding.tvTitle.text = CommentV2Util.getTitleWithCommentTotal(resources.getString(R.string.title_comment), info.totalCmt)
        }
        viewModel.dispatchIntent(CommentV2ViewModel.CommentIntent.UpdateCommentInfo(info))
    }

    private val miniAppUIController = object : MiniAppUIInterface {
        override fun onCloseMiniApp() {
            backHandler()
        }
    }

    private fun showErrorByInternetLayout(errorType: String) {
        showErrorLayout(errorType)
        if (_errorBinding != null) {
            ImageProxy.loadLocal(
                context = errorBinding.root.context,
                data = R.drawable.ic_no_internet,
                width = 0,
                height = 0,
                target = errorBinding.ivError
            )
            errorBinding.tvError.text = errorBinding.root.resources.getString(R.string.error_layout_title_no_internet)
            errorBinding.tvTroubleshoot.text = errorBinding.root.resources.getString(R.string.error_layout_troubleshoot)
        }
    }

    private fun showOtherErrorLayout(errorType: String) {
        showErrorLayout(errorType)
        if (_errorBinding != null) {
            ImageProxy.loadLocal(
                context = errorBinding.root.context,
                data = R.drawable.iv_mega_error,
                width = 0,
                height = 0,
                target = errorBinding.ivError
            )
            errorBinding.tvError.text = errorBinding.root.resources.getString(R.string.error_layout_title)
            errorBinding.tvTroubleshoot.text = errorBinding.root.resources.getString(R.string.error_layout_troubleshoot)
        }
    }

    private fun showErrorLayout(errorType: String) {
        if (_binding != null && isShowErrorLayout) {
            this.errorType = errorType
            binding.errorLayout.visibility = View.VISIBLE
        }
    }

    private fun hideErrorLayout() {
        if (_binding != null) {
            this.errorType = ""
            binding.errorLayout.visibility = View.GONE
        }
    }

    private fun sendTrackingLog(meta: CommentTrackingData) {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = meta.logId,
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                appSource = meta.appSource,
                screen = meta.screen,
                event =meta.event,
                isLive = TrackingUtil.isLive,
                itemId = safeArgs.contentId,
                itemName = meta.itemName,
                chapterId = meta.chapterId,
                EpisodeID = meta.episodeId,
                url = meta.url,
                isLinkDRM = meta.isLinkDrm,
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                blocKPosition = TrackingUtil.blockIndex,
                subMenuId = TrackingUtil.blockId,
                status = meta.status,
                startTime = TrackingUtil.startTime,
                keyword = TrackingUtil.keyword,
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                businessPlan = meta.businessPlan,
                playerName = meta.playerName,
            )
        )
    }

    override fun backHandler() {
        super.backHandler()
    }

}