package com.fptplay.mobile.services.player

import androidx.media3.common.ForwardingSimpleBasePlayer
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import com.google.common.util.concurrent.Futures
import com.google.common.util.concurrent.ListenableFuture
import com.xhbadxx.projects.module.util.logger.Logger

private const val TAG = "CustomForwardingSimpleBasePlayer"

@UnstableApi
class CustomForwardingSimpleBasePlayer(
    private val player: Player,
    private val listener: OnForwardingSimpleBasePlayerListener
) : ForwardingSimpleBasePlayer(player) {

    override fun getState(): State {
        Logger.d("$TAG -> getState")
        val state = super.getState()
        return listener.getState(state)
    }

    override fun handleSetPlayWhenReady(playWhenReady: Boolean): ListenableFuture<*> {
        if (playWhenReady) {
            if (listener.onPlay(player = player)) {
                return Futures.immediateVoidFuture()
            }
        } else {
            if (listener.onPause(player = player)) {
                return Futures.immediateVoidFuture()
            }
        }
        return super.handleSetPlayWhenReady(playWhenReady)
    }

    override fun handleSeek(mediaItemIndex: Int, positionMs: Long, seekCommand: Int): ListenableFuture<*> {
        Logger.d("$TAG -> handleSeek: $seekCommand")
        return when (seekCommand) {
            COMMAND_SEEK_TO_NEXT, COMMAND_SEEK_TO_NEXT_MEDIA_ITEM -> {
                listener.onSkipToNext(player = player)
                Futures.immediateVoidFuture()
            }
            COMMAND_SEEK_TO_PREVIOUS, COMMAND_SEEK_TO_PREVIOUS_MEDIA_ITEM -> {
                listener.onSkipToPrevious(player = player)
                Futures.immediateVoidFuture()
            }
            else -> super.handleSeek(mediaItemIndex, positionMs, seekCommand)
        }
    }

    interface OnForwardingSimpleBasePlayerListener {
        fun getState(state: State): State
        fun onSkipToNext(player: Player)
        fun onSkipToPrevious(player: Player)
        fun onPlay(player: Player): Boolean = false
        fun onPause(player: Player): Boolean = false
    }
}