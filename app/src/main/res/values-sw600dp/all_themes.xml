<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.FPTPlay" parent="Theme.MaterialComponents.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/accent</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:navigationBarColor">@android:color/transparent</item>

        <!-- Optional, if your app is drawing behind the status bar also. -->
        <item name="android:statusBarColor">@color/status_bar_color</item>
        <!-- Customize your theme here. -->
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar.FullWidth</item>
    </style>

    <style name="Theme.FPTPlay.SplashScreen" parent="Theme.FPTPlay">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@drawable/splash_bg</item>
        <item name="android:statusBarColor">@color/black</item>
    </style>

    <style name="BottomNavigation" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="android:background">@color/black</item>
        <item name="itemIconTint">@color/bottom_navigation_item_color_state</item>
        <item name="itemTextColor">@color/bottom_navigation_item_color_state</item>
        <item name="itemHorizontalTranslationEnabled">false</item>
        <item name="labelVisibilityMode">labeled</item>
        <item name="android:textSize">@dimen/_7ssp</item>

        <item name="itemIconPadding">@dimen/_4sdp</item>
    </style>

    <style name="FullScreenDialog" parent="Theme.FPTPlay">
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/dialog_dim_color</item>
        <item name="android:windowAnimationStyle">@style/FullScreenDialog.Animation</item>

    </style>

    <style name="FullScreenDialogTablet" parent="Theme.FPTPlay">
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/background_tablet_70</item>
        <item name="android:windowAnimationStyle">@style/FullScreenDialog.Animation</item>
    </style>

    <style name="FullScreenDialogDark" parent="Theme.FPTPlay">
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/dialog_dim_dark_color</item>
        <item name="android:windowAnimationStyle">@style/FullScreenDialog.Animation</item>
    </style>

    <style name="FullScreenCameraDialogDark" parent="Theme.FPTPlay">
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/dialog_dim_dark_color</item>
        <item name="android:windowAnimationStyle">@style/FullScreenCameraDialogDark.Animation</item>
    </style>

    <style name="FullScreenCameraDialogDark.Animation">
        <item name="android:windowEnterAnimation">@anim/slide_bottom_to_top_in</item>
        <item name="android:windowExitAnimation">@anim/slide_top_to_bottom_out</item>
    </style>

    <style name="FullScreenDialog.Animation">
        <item name="android:windowEnterAnimation">@android:anim/fade_in</item>
        <item name="android:windowExitAnimation">@android:anim/fade_out</item>
    </style>

    <style name="FullScreenTransparentDialog" parent="Theme.FPTPlay">
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/dialog_dim_dark_color</item>
        <item name="android:windowAnimationStyle">@style/FullScreenDialog.Animation</item>
    </style>

    <style name="FullScreenPairingControlTransparentDialog" parent="Theme.FPTPlay">
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/dialog_dim_dark_color</item>
    </style>

    <style name="MultiProfilePinProfileBottomSheetDialogTheme" parent="FullScreenDialog">
        <!--        <item name="bottomSheetStyle">@style/CommentBottomSheetStyle</item>-->
        <item name="android:windowSoftInputMode">stateAlwaysHidden</item>
    </style>
    <!--Button-->
    <style name="AllButtonStyle">
        <item name="android:textColor">@color/all_button_text_color_state</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:background">@drawable/all_button_state</item>
        <item name="android:textSize">@dimen/all_button_text_size</item>
    </style>

    <style name="RoundButtonStyle">
        <item name="android:background">@drawable/round_button_state</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/app_content_textview_size</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="AllButtonGley">
        <item name="android:textColor">@color/all_button_text_color_state</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:background">@drawable/all_button_disable</item>
        <item name="android:textSize">@dimen/all_button_text_size</item>
    </style>

    <!--Text-->
    <style name="BaseTextView">
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/_7ssp</item>
        <item name="singleLine">true</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="BlockHeaderText">
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/block_header_text_size</item>
        <item name="android:fontFamily">@font/sf_pro_display_bold</item>
    </style>

    <style name="DialogHeaderText">
        <item name="android:maxLines">2</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/header_text_size</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="DialogSubHeaderText">
        <item name="android:textColor">@color/app_sub_content_text_color</item>
        <item name="android:textSize">@dimen/sub_header_text_size</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="BlockSubHeaderText">
        <item name="android:textColor">@color/app_sub_content_text_color</item>
        <item name="android:textSize">@dimen/app_content_textview_size</item>
        <item name="android:fontFamily">@font/sf_pro_display_medium</item>
    </style>

    <style name="AllContentTextStyle">
        <item name="android:textSize">@dimen/app_content_textview_size</item>
        <item name="android:textColor">@color/app_content_text_color</item>
    </style>

    <style name="TextLive">
        <item name="android:layout_width">@dimen/_25sdp</item>
        <item name="android:layout_height">@dimen/_14sdp</item>
        <item name="android:text">@string/live</item>
        <item name="android:background">@drawable/live_ic</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textAllCaps">true</item>
    </style>

    <!--Snackbar-->
    <style name="SnackbarTextViewTitle">
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textColor">#DEFFFFFF</item>
        <item name="android:textSize">@dimen/_7ssp</item>
    </style>

    <!--QR Code-->
    <style name="RoundedBackgroundBottomSheet" parent="@style/Widget.Design.BottomSheet.Modal">
        <item name="android:background">@color/transparent</item>
    </style>

    <style name="QRCodeGuideBottomSheetDialogTheme" parent="@style/Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
        <item name="bottomSheetStyle">@style/RoundedBackgroundBottomSheet</item>
    </style>

    <!--TabLayout-->
    <style name="AllTabTextAppearance">
        <item name="android:textSize">@dimen/_7ssp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="AllTabLayoutStyle" parent="Widget.Design.TabLayout">
        <item name="tabTextAppearance">@style/AllTabTextAppearance</item>
        <item name="tabSelectedTextColor">@android:color/white</item>
        <item name="tabMode">scrollable</item>
        <item name="tabTextColor">#99FFFFFF</item>
        <item name="tabIndicatorFullWidth">false</item>
        <item name="tabIndicatorColor">#FE592A</item>
    </style>

    <style name="GiftStoreTabletMenu" parent="Widget.Design.TabLayout">
        <item name="tabTextAppearance">@style/AllTabListChannelTextAppearance</item>
        <item name="tabSelectedTextColor">@android:color/white</item>
        <item name="tabMode">scrollable</item>
        <item name="tabTextColor">#99FFFFFF</item>
        <item name="tabIndicatorFullWidth">false</item>
        <item name="tabIndicatorColor">#FE592A</item>
    </style>

    <style name="AllTabListChannelTextAppearance">
        <item name="android:textSize">@dimen/_7ssp</item>
    </style>

    <style name="TabLayoutListChannelStyle" parent="AllTabLayoutStyle">
        <item name="tabTextAppearance">@style/AllTabListChannelTextAppearance</item>
    </style>

    <style name="RoundedDarkBackgroundBottomSheet" parent="@style/Widget.Design.BottomSheet.Modal">
        <item name="android:background">@color/transparent</item>
        <item name="android:gravity">center_horizontal</item>
    </style>

    <style name="QRCodeLinkBottomSheetDialogTheme" parent="@style/Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
        <item name="bottomSheetStyle">@style/RoundedDarkBackgroundBottomSheet</item>
    </style>

    <!--Centered Title Toolbar-->
    <style name="ToolbarTextViewTitle">
        <item name="android:fontFamily">@font/sf_pro_display_medium</item>
        <item name="android:textColor">@color/color_white</item>
        <item name="android:textSize">@dimen/_8ssp</item>
        <item name="singleLine">true</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>
    <!--Block-->
    <style name="BlockPoster" parent="">
        <item name="cornerSize">@dimen/block_item_corner_radius</item>
    </style>

    <!--Payment package-->
    <style name="PaymentPackage" parent="">
        <item name="cornerSize">@dimen/package_radius</item>
    </style>

    <!-- Dialog -->
    <style name="TransparentDialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="android:windowAnimationStyle">@style/TransparentDialog.Animation</item>
    </style>

    <style name="TransparentDialog.Animation">
        <item name="android:windowEnterAnimation">@android:anim/fade_in</item>
        <item name="android:windowExitAnimation">@android:anim/fade_out</item>
    </style>

    <!--Payment-->
    <style name="PaymentTextAttrs">
        <item name="android:textSize">@dimen/_10ssp</item>
        <item name="android:textColor">@color/white_87</item>
        <item name="android:maxLines">3</item>
        <item name="android:ellipsize">end</item>
        <item name="android:gravity">center_vertical</item>
        <item name="fontFamily">@font/sf_pro_display_medium</item>
    </style>

    <style name="PaymentRegisterButtonWhite">
        <item name="android:background">@drawable/round_button_state</item>
        <item name="android:textColor">@color/white_87</item>
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="android:textAllCaps">false</item>
        <item name="fontFamily">@font/sf_pro_display_medium</item>
    </style>

    <style name="PaymentRegisterButtonAccent">
        <item name="android:background">@drawable/round_button_pressed</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="PaymentTitleText">
        <item name="android:textColor">@color/white_87</item>
        <item name="android:textSize">@dimen/_13ssp</item>
        <item name="android:textStyle">bold</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
    </style>

    <style name="PaymentSuccessDialog" parent="BaseTextView">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
    </style>

    <style name="PaymentSuccessDialog.TextViewMainTitle">
        <item name="android:textSize">20sp</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingTop">24dp</item>
        <item name="android:paddingBottom">32dp</item>
        <item name="android:paddingLeft">32dp</item>
        <item name="android:paddingRight">32dp</item>
    </style>

    <style name="PaymentSuccessDialog.TextViewTitle">
        <item name="android:layout_marginTop">16dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:layout_marginStart">32dp</item>
    </style>

    <style name="PaymentSuccessDialog.TextViewValue">
        <item name="android:gravity">right|end</item>
        <item name="android:textSize">14sp</item>
        <item name="android:layout_marginEnd">32dp</item>
        <item name="android:layout_marginLeft">4dp</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
    </style>

    <style name="PaymentSuccessDialog.TextButton">
        <item name="android:background">?android:attr/selectableItemBackground</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textSize">17sp</item>
        <item name="android:textColor">@color/color_accent</item>
        <item name="android:textStyle">bold</item>
        <item name="android:paddingTop">20dp</item>
        <item name="android:paddingBottom">20dp</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
        <item name="android:gravity">center</item>
    </style>

    <!--Vod Actor-->
    <style name="CircleImageActor" parent="">
        <item name="cornerSize">@dimen/_34sdp</item>
    </style>

    <!--Vod Actor-->
    <style name="CircleImageComment" parent="">
        <item name="cornerSize">@dimen/_14sdp</item>
    </style>

    <!--region Airline-->
    <style name="AirlineSwitchLanguage">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_10ssp</item>
    </style>

    <style name="AirlineBottomNavigation" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="android:background">#D9131313</item>
        <item name="itemIconTint">@color/airline_bottom_navigation_item_color_state</item>
        <item name="itemTextColor">@color/airline_bottom_navigation_item_color_state</item>
        <item name="itemHorizontalTranslationEnabled">false</item>
        <item name="labelVisibilityMode">labeled</item>
        <item name="elevation">0dp</item>
        <item name="itemIconPadding">@dimen/_4sdp</item>
    </style>

    <style name="AirlineVodTabLayout">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_8ssp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="AirlineVodStructureItemTitle">
        <item name="fontFamily">@font/sf_pro_display_light</item>
        <item name="android:textSize">@dimen/_6ssp</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="singleLine">true</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="AirlineVodStructureGroupTitle">
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/_8ssp</item>
    </style>

    <style name="AirlineVodStructureGroupViewMore" parent="AirlineVodStructureItemTitle">
        <item name="android:textColor">@color/app_sub_content_text_color</item>
    </style>

    <style name="ProgressBarHorizontal" parent="Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:indeterminateDrawable">@drawable/all_view_loading_horizontal_drawable
        </item>
        <item name="android:indeterminate">true</item>
    </style>
    <!--endregion-->

    <!--region Account-->
    <style name="Account.Toolbar.MenuTheme" parent="Theme.FPTPlay">
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_12ssp</item>
        <item name="actionMenuTextColor">#FE5A2A</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="CircleImageViewStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <style name="Account.UpdateInfo.Edittext" parent="Widget.MaterialComponents.TextInputLayout.FilledBox">
        <item name="hintEnabled">false</item>
        <item name="hintTextColor">#61FFFFFF</item>
        <item name="boxBackgroundColor">@android:color/transparent</item>
        <item name="endIconMode">clear_text</item>
        <item name="endIconDrawable">@drawable/ic_clear</item>
        <item name="boxStrokeColor">#1AFFFFFF</item>
        <item name="boxStrokeWidth">1dp</item>
        <item name="boxStrokeWidthFocused">1dp</item>
    </style>

    <style name="Account.PromotionCode.Edittext" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="hintEnabled">false</item>
        <item name="hintTextColor">#61FFFFFF</item>
        <item name="boxBackgroundColor">@color/all_button_disable</item>
        <item name="boxStrokeColor">@android:color/transparent</item>
        <item name="boxCornerRadiusTopStart">@dimen/_3sdp</item>
        <item name="boxCornerRadiusTopEnd">@dimen/_3sdp</item>
        <item name="boxCornerRadiusBottomStart">@dimen/_3sdp</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/_3sdp</item>
        <item name="endIconMode">clear_text</item>
        <item name="endIconDrawable">@drawable/ic_clear_account_promotion_code</item>
        <item name="errorEnabled">true</item>
        <item name="errorTextColor">@color/color_accent</item>
        <item name="errorIconDrawable">@null</item>
        <item name="errorIconTint">@color/color_accent</item>
        <item name="errorTextAppearance">@style/Account.PromotionCode.Edittext.Error</item>
        <item name="boxStrokeErrorColor">@color/color_accent</item>
    </style>

    <style name="Account.PromotionCode.Edittext.Error" parent="BaseTextView">
        <item name="android:textSize">@dimen/_5sdp</item>
    </style>

    <style name="Account.BottomSheetDialogTheme" parent="@style/Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
    </style>

    <style name="AccountDatePickerTheme">
        <item name="android:textColorPrimary">@color/app_content_text_color</item>
        <item name="colorControlNormal">@color/app_content_text_color</item>
    </style>

    <style name="TransparentBackgroundBottomSheet" parent="@style/Widget.Design.BottomSheet.Modal">
        <item name="android:background">@null</item>
    </style>

    <!--endregion-->

    <!--region Pairing-->
    <style name="PairingRoundedBackgroundBottomSheet" parent="@style/Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/pairing_bottom_sheet_rounded_background</item>
    </style>

    <style name="PairingBottomSheetDialogTheme" parent="@style/Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
        <item name="bottomSheetStyle">@style/PairingRoundedBackgroundBottomSheet</item>
    </style>
    <!-- endregion -->

    <!--region Multi Profile-->
    <style name="MultiProfile.Toolbar.MenuTheme" parent="Theme.FPTPlay">
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_8ssp</item>
        <item name="actionMenuTextColor">#FE5A2A</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <!--endregion Multi Profile-->

    <!--  region viewing history  -->

    <style name="MultiProfile.Toolbar.ViewingHistory" parent="Theme.FPTPlay">
        <item name="android:fontFamily">@font/sf_pro_display_semibold</item>
        <item name="android:textSize">@dimen/_6ssp</item>
        <item name="actionMenuTextColor">@color/white_87</item>
        <item name="android:textAllCaps">false</item>
    </style>


    <style name="RoundedCornersImageViewStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">@dimen/_3sdp</item>
    </style>

    <style name="SmallTitle" parent="BaseTextView">
        <item name="android:fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textColor">@color/white_87</item>
        <item name="android:textSize">@dimen/_6ssp</item>
        <item name="android:letterSpacing">0.02</item>
    </style>

    <style name="MediumBody" parent="BaseTextView">
        <item name="android:fontFamily">@font/sf_pro_display_medium</item>
        <item name="android:textColor">@color/white_87</item>
        <item name="android:textSize">@dimen/_6ssp</item>
        <item name="android:letterSpacing">0.02</item>
    </style>

</resources>