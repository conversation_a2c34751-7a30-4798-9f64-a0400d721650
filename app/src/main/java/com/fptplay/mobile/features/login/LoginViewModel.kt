package com.fptplay.mobile.features.login

import android.os.Bundle
import androidx.lifecycle.SavedStateHandle
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.features.ads.utils.AdsUtils
import com.fptplay.mobile.features.mega.MegaViewModel.MegaState
import com.fptplay.mobile.player.utils.PlayerPiPHelper
import com.tear.modules.tracking.model.Infor
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.common.IntroductionPage
import com.xhbadxx.projects.module.domain.entity.fplay.login.*
import com.xhbadxx.projects.module.domain.entity.fplay.loginv2.AccountSetPinEntity
import com.xhbadxx.projects.module.domain.entity.fplay.otp.ResetPinEntity
import com.xhbadxx.projects.module.domain.entity.fplay.otp.SendOtpV2Entity
import com.xhbadxx.projects.module.domain.entity.fplay.otp.UserOtpType
import com.xhbadxx.projects.module.domain.entity.fplay.otp.VerifyOTPV2Entity
import com.xhbadxx.projects.module.domain.entity.fplay.payment.PackageUser
import com.xhbadxx.projects.module.domain.entity.fplay.user.UserInfo
import com.xhbadxx.projects.module.domain.repository.fplay.CommonRepository
import com.xhbadxx.projects.module.domain.repository.fplay.LoginRepository
import com.xhbadxx.projects.module.domain.repository.fplay.LoginRepositoryV2
import com.xhbadxx.projects.module.domain.repository.fplay.OtpRepository
import com.xhbadxx.projects.module.domain.repository.fplay.PaymentRepository
import com.xhbadxx.projects.module.domain.repository.fplay.UserRepository
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.lifecycle.HiltViewModel
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class LoginViewModel @Inject constructor(
    private val loginRepository: LoginRepository,
    private val loginRepositoryV2: LoginRepositoryV2,
    private val commonRepository: CommonRepository,
    private val userRepository: UserRepository,
    private val paymentRepository: PaymentRepository,
    private val otpRepository: OtpRepository,
    private val sharedPreferences: SharedPreferences,
    private val savedState: SavedStateHandle,
    private val trackingInfo: Infor
) : BaseViewModel<LoginViewModel.LoginIntent, LoginViewModel.LoginState>() {

    companion object {
        const val CALL_FROM_LOGIN = "0"
        const val CALL_FROM_REGIS = "1"
        const val CALL_FROM_RESET_PASS = "2"
        const val CALL_FROM_UPDATE_PHONE = "3"
        const val CALL_FROM_LOGIN_NEW_DEVICE = "4"
        val country = Countries.Country(
            code = "VN",
            name =  "Việt Nam",
            prefixCode = "84",
            defaultCode = "VN"
        )
    }

    var policy: PolicyEntity? = null
        private set

    sealed class LoginState : ViewState {
        data class Loading(val data: LoginIntent? = null) : LoginState()
        data class ResultInforPage(val isCached: Boolean, val data: List<IntroductionPage>) : LoginState()
        data class ResultPolicy(val isCached: Boolean, val data: PolicyEntity, val intent:LoginIntent) : LoginState()
        data class ResultLogin(val isCached: Boolean, val data: Login) : LoginState()
        data class ResultResetToken(val isCached: Boolean, val data: Login) : LoginState()
        data class ResultVerifyOTP(val isCached: Boolean, val data: Login) : LoginState()
        data class ResultResendOTP(val isCached: Boolean, val data: ActionOtpStatus) : LoginState()
        data class ResultRegister(val isCached: Boolean, val data: RegisterOtp) : LoginState()
        data class ResultCreatePass(val isCached: Boolean, val data: Login) : LoginState()
        data class ResultResetPass(val isCached: Boolean, val data: Login) : LoginState()
        data class ResultResetPassOtp(val isCached: Boolean, val data: Login) : LoginState()
        data class ResultUserInfo(val isCached: Boolean, val data: UserInfo) : LoginState()
        data class ResultPackageUser(val isCached: Boolean, val data: List<PackageUser>, val userInfo: UserInfo) : LoginState()
        data class ResultUpdatePhone(val isCached: Boolean, val data: ActionOtpStatus) : LoginState()
        data class ResultEnterRegister(val data: LoginIntent? = null) : LoginState()
        data class ResultSendOtpV1(val isCached: Boolean, val data: RequestOtp, val intent: LoginIntent) : LoginState()

        data class ResultChangePasswordOtpV1(val isCached: Boolean, val data: com.xhbadxx.projects.module.domain.entity.fplay.common.Status) : LoginState()
        data class ResultCreatePinSetting(val isCached: Boolean, val data: AccountSetPinEntity) : LoginState()

        //new flow otp
        data class ResultCreateUserPinSettingNewFlowOtp(val isCached: Boolean, val data: ResetPinEntity) : LoginState()
        data class ResultRequestOtpV2(val data: SendOtpV2Entity, val intent: LoginIntent) : LoginState()
        data class ResultVerifyOtpV2(val data: VerifyOTPV2Entity, val intent: LoginIntent) : LoginState()

        data class ErrorNoInternet(val intent: LoginIntent? = null, val message: String) : LoginState()
        data class ErrorManyRequest(val message: String, val seconds:Long, val data: LoginIntent? = null) : LoginState()
        data class ErrorRequiredLogin(val message: String, val intent: LoginIntent? = null) : LoginState()
        data class Error(val message: String, val data: LoginIntent? = null) : LoginState()
        data class Done(val data: LoginIntent? = null) : LoginState()
        object Init : LoginState()
    }

    sealed class LoginIntent : ViewIntent {
        object GetInforPage : LoginIntent()
        data class GetPolicy(val type: String, val email: String) : LoginIntent()
        data class CallLogin(val phone: String, val countryCode: String, val pass: String, val pushRegId: String) : LoginIntent()
        object AddDeviceRegistrationToken : LoginIntent()
        data class ResetToken(val phone: String, val countryCode: String) : LoginIntent()
        data class VerifyOTP(val phone: String, val countryCode: String, val otp: String, val pushRegId: String) : LoginIntent()
        data class VerifyOTPV1(val typeOtp: String, val otpCode: String, val phone: String, val pushRegId: String) : LoginIntent()
        data class ResendOTPV1(val phone: String, val typeOtp: String) : LoginIntent()
        data class SendOTPV1(val typeOtp: String, val countryCode: String, val phone: String) : LoginIntent()
        data class ResendOTP(val phone: String, val countryCode: String, val typeOtp: String) : LoginIntent()
        data class Register(val phone: String, val countryCode: String) : LoginIntent()
        data class CreatePassword(val phone: String, val countryCode: String, val pass: String, val verifyToken: String, val pushRegId: String) : LoginIntent()
        data class ResetPassword(val phone: String, val countryCode: String, val pass: String, val pushRegId: String, val verifyToken: String) : LoginIntent()
        data class ResetPasswordOTP(val phone: String, val countryCode: String) : LoginIntent()
        data class LoginWithProvider(val providerId: String, val providerToken: String, val googleV3: Boolean, val pushRegId: String,val providerVersion : String="") : LoginIntent()
        data class ChangePasswordOtpV1(val verifyToken: String, val otpType: String, val pass: String, val passConfirm: String) : LoginIntent()
        data class CreateUserPinSetting(val pin: String, val pinConfirm: String) : LoginIntent()

        data class UpdatePhoneNumber(val phone: String, val email: String, val countryCode: String) : LoginIntent()
        object GetInfoUser : LoginIntent()
        data class GetPackageUser(val userId: String, val userInfo: UserInfo) : LoginIntent()
        data class EnterRegister(val data: LoginIntent? = null) : LoginIntent()
        
        //new flow otp
        data class UpdateUserPinSettingNewFlowOtp(val pin: String, val pinConfirm: String, val otpType: UserOtpType, val verifyToken: String) : LoginIntent()
        data class RequestOtpV2(val phone: String, val verifyToken: String, val otpType: UserOtpType) : LoginIntent()
        data class RequestResendOtpV2(val phone: String, val verifyToken: String, val otpType: UserOtpType) : LoginIntent()
        data class VerifyOtpV2(val phone: String, val otpCode: String, val otpType: UserOtpType) : LoginIntent()

    }

    override fun dispatchIntent(intent: LoginIntent) {
        safeLaunch {
            when (intent) {
//                is LoginIntent.GetCountries -> {
//                    countries?.apply {
//                        _state.value = LoginState.ResultCountries(isCached = false, data = this)
//                    } ?: kotlin.run {
//                        loginRepository.getCountries().collect {
//                            _state.value = it.reduce(intent = intent) { isCached, data ->
//                                LoginState.ResultCountries(isCached = isCached, data = data)
//                            }
//                            countries = it.data
//                        }
//                    }
//                }
                is LoginIntent.GetInforPage -> {
                    commonRepository.getInformationPage(pageName = policy?.data?.value?: "").collect {
                        _state.value = it.reduce(intent = intent) { isCached, data  ->
                            LoginState.ResultInforPage(isCached = isCached, data = data)
                        }
                    }
                }

                is LoginIntent.GetPolicy -> {
                    loginRepository.getPolicy().collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            policy = it.data
                            Logger.d("trangtest policy1 = $policy")
                            LoginState.ResultPolicy(isCached = isCached, data = data, intent = intent)
                        }
                    }

                }
                is LoginIntent.CallLogin -> {
                    loginRepository.loginPhone(intent.phone, intent.countryCode, intent.pass, intent.pushRegId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LoginState.ResultLogin(isCached = isCached, data = data)
                        }
                    }
                }
                is LoginIntent.ResetToken -> {
                    loginRepository.resetToken(intent.phone, intent.countryCode).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LoginState.ResultResetToken(isCached = isCached, data = data)
                        }
                    }
                }
                is LoginIntent.VerifyOTP -> {
                    loginRepository.verifyOtp(intent.otp, intent.phone, intent.countryCode, intent.pushRegId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LoginState.ResultVerifyOTP(isCached = isCached, data = data)
                        }
                    }
                }
                is LoginIntent.ResendOTP -> {
                    loginRepository.resendOtp(intent.phone, intent.countryCode, intent.typeOtp)
                        .collect {
                            _state.value = it.reduce(intent = intent) { isCached, data ->
                                LoginState.ResultResendOTP(isCached = isCached, data = data)
                            }
                        }
                }
                is LoginIntent.Register -> {
                    loginRepository.register(intent.phone, intent.countryCode).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LoginState.ResultRegister(isCached = isCached, data = data)
                        }
                    }
                }
                is LoginIntent.CreatePassword -> {
                    loginRepository.createPassword(intent.phone, intent.countryCode, intent.pass, intent.pass, intent.verifyToken, intent.pushRegId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LoginState.ResultCreatePass(isCached = isCached, data = data)
                        }
                    }
                }
                is LoginIntent.ResetPassword -> {
                    loginRepository.resetPassword(intent.phone, intent.countryCode, intent.pass, intent.pass, intent.verifyToken, intent.pushRegId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LoginState.ResultResetPass(isCached = isCached, data = data)
                        }
                    }
                }
                is LoginIntent.ResetPasswordOTP -> {
                    loginRepository.resetPasswordOtp(intent.phone, intent.countryCode).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LoginState.ResultResetPassOtp(isCached = isCached, data = data)
                        }
                    }
                }
                is LoginIntent.GetInfoUser -> {
                    userRepository.getUserInfo().collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            TrackingUtil.saveUserDataForLog(data)
                            Utils.saveUserInfo(sharedPreferences, data)
                            LoginState.ResultUserInfo(isCached = isCached, data = data)
                        }
                    }
                }
                is LoginIntent.LoginWithProvider -> {
                    loginRepository.loginWithProvider(intent.providerId, intent.providerToken, intent.googleV3, intent.pushRegId,intent.providerVersion).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LoginState.ResultLogin(isCached = isCached, data = data)
                        }
                    }
                }
                is LoginIntent.UpdatePhoneNumber -> {
                    loginRepository.updatePhoneNumber(intent.phone, intent.email, intent.countryCode).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LoginState.ResultUpdatePhone(isCached = isCached, data = data)
                        }
                    }
                }
                is LoginIntent.GetPackageUser -> {
                    paymentRepository.getPackageUser(intent.userId).collect {
                        _state.value = it.reduceForAds(intent = intent) { isCached, data ->
                            Timber.d("*****Get user package")
                            LoginState.ResultPackageUser(isCached = isCached, data = data, userInfo = intent.userInfo)
                        }
                    }
                }
                is LoginIntent.EnterRegister -> {
                    savedState.set(Constants.ENTER_REGISTER, false)
                    _state.value = LoginState.ResultEnterRegister()
                }
                is LoginIntent.AddDeviceRegistrationToken -> {
                    commonRepository.addDeviceRegistrationToken(sharedPreferences.fcmToken(), sharedPreferences.userId().ifBlank { null }).collect {
                        Timber.d("Run register fcm token $it")
                    }
                }
                is LoginIntent.SendOTPV1 -> {
                    userRepository.requestOtp(intent.typeOtp, intent.countryCode, intent.phone).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LoginState.ResultSendOtpV1(isCached = isCached, data = data, intent = intent)
                        }
                    }
                }
                is LoginIntent.ResendOTPV1 -> {
                    userRepository.resendRequestOtp(intent.typeOtp, intent.phone).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LoginState.ResultSendOtpV1(isCached = isCached, data = data, intent = intent)
                        }
                    }
                }
                is LoginIntent.VerifyOTPV1 -> {
                    userRepository.verifyOtpV1(intent.typeOtp, intent.otpCode, intent.phone, intent.pushRegId).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LoginState.ResultVerifyOTP(isCached = isCached, data = data)
                        }
                    }
                }

                is LoginIntent.ChangePasswordOtpV1 -> {
                    userRepository.changePasswordOtpV1(verifyToken = intent.verifyToken, otpType = intent.otpType, newPassword = intent.pass, newPasswordAgain = intent.passConfirm).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LoginState.ResultChangePasswordOtpV1(isCached = isCached, data = data)
                        }
                    }
                }

                is LoginIntent.CreateUserPinSetting -> {
                    loginRepositoryV2.accountSetPinV2(pin = intent.pin, confirmPin = intent.pinConfirm).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LoginState.ResultCreatePinSetting(isCached = isCached, data = data)
                        }
                    }
                }

                is LoginIntent.UpdateUserPinSettingNewFlowOtp -> {
                    otpRepository.accountResetPin(
                        pinCode = intent.pin,
                        confirmPinCode = intent.pinConfirm,
                        typeValidate = intent.otpType,
                        verifyToken = intent.verifyToken
                    ).collect {
                        _state.value = it.reduce(intent = intent) { isCached, data ->
                            LoginState.ResultCreateUserPinSettingNewFlowOtp(isCached = isCached, data = data)
                        }
                    }
                }

                is LoginIntent.RequestOtpV2 -> {
                    otpRepository.sendOtpV2(
                        phone = intent.phone,
                        verifyToken = intent.verifyToken,
                        typeOtp = intent.otpType
                    ).collect { result ->
                        _state.value = result.reduceForOtpNewFlow(intent = intent) { _, sendOtpV2Entity ->
                            LoginState.ResultRequestOtpV2(data = sendOtpV2Entity, intent = intent)
                        }
                    }

                }
                is LoginIntent.RequestResendOtpV2 -> {
                    otpRepository.resendOtpV2(
                        phone = intent.phone,
                        typeOtp = intent.otpType
                    ).collect { result ->
                        _state.value = result.reduceForOtpNewFlow(intent = intent) { _, sendOtpV2Entity ->
                            LoginState.ResultRequestOtpV2(data = sendOtpV2Entity, intent = intent)
                        }
                    }
                }
                is LoginIntent.VerifyOtpV2 -> {
                    otpRepository.verifyOtpV2(
                        phone = intent.phone,
                        typeOtp = intent.otpType,
                        otp =  intent.otpCode,
                    ).collect { result ->
                        _state.value = result.reduceForOtpNewFlow(intent = intent) { _, verifyOtpV2 ->
                            LoginState.ResultVerifyOtpV2(data = verifyOtpV2, intent = intent)
                        }
                    }
                }
            }
        }
    }

    override fun <T> Result<T>.reduce(
        intent: LoginIntent?,
        successFun: (Boolean, T) -> LoginState
    ): LoginState {
        return when (this) {
            is Result.Init -> LoginState.Loading(data = intent)
            is Result.Success -> successFun(this.isCached, this.successData)
            is Result.Error -> LoginState.Error(message = this.message, data = intent)
            Result.Done -> LoginState.Done(data = intent)
        }
    }

    fun <T> Result<T>.reduceForOtpNewFlow(
        intent: LoginIntent? = null,
        successFun: (Boolean, T) -> LoginState
    ): LoginState {
        return when (this) {
            is Result.Init -> LoginState.Loading(data = intent)
            is Result.Success -> successFun(this.isCached, this.successData)
            is Result.ServerError.ManyRequest -> LoginState.ErrorManyRequest(message = this.message, seconds = this.seconds, data = intent)
            is Result.UserError.RequiredLogin -> LoginState.ErrorRequiredLogin(this.message, intent = intent)
            is Result.Error.Intenet -> LoginState.ErrorNoInternet(message = this.message, intent = intent)
            is Result.Error -> LoginState.Error(message = this.message, data = intent)
            Result.Done -> LoginState.Done(data = intent)
        }
    }

    fun Result<List<PackageUser>>.reduceForAds(
        intent: LoginIntent.GetPackageUser?,
        successFun: (Boolean, List<PackageUser>) -> LoginState
    ): LoginState {
        return when (this) {
            is Result.Init -> LoginState.Loading(data = intent)
            is Result.Success -> {
                AdsUtils.saveUserType(sharedPreferences = sharedPreferences, haveDisAdsPackage = AdsUtils.userHaveDisAdsPackage(this.successData))
                PlayerPiPHelper.saveSupportPictureInPicture(sharedPreferences = sharedPreferences, isSupport = PlayerPiPHelper.userHaveSupportPip(this.successData))
                successFun(this.isCached, this.successData)
            }
            is Result.Error -> {
//                AdsUtils.saveUserType(sharedPreferences = sharedPreferences, haveDisAdsPackage = false)
                LoginState.Error(message = this.message, data = intent)
            }
            Result.Done -> LoginState.Done(data = intent)
        }
    }

    fun saveStartScreenData(args: LoginFragmentArgs, arguments: Bundle?) {
        savedState.set(Constants.POP_UP_TO_ID, args.popupToId)
        savedState.set(Constants.POP_UP_TO_INCLUSIVE, args.popUpToInclusive)
        savedState.set(Constants.NAVIGATION_ID, args.navigationId)
        savedState.set(Constants.ID_TO_PLAY, args.idToPlay)
        savedState.set(Constants.TIME_SHIFT_LIMIT, args.timeShiftLimit)
        savedState.set(Constants.TIME_SHIFT, args.timeShift)
        savedState.set(Constants.ENTER_REGISTER, args.enterRegister)
        savedState.set(Constants.CHECK_REQUIRE_VIP, args.checkRequireVip)
        savedState.set(Constants.IS_PLAYLIST, args.isPlaylist)
        savedState.set(Constants.LOGIN_NAVIGATE_EXTRA_DATA, arguments?.getBundle(Constants.LOGIN_NAVIGATE_EXTRA_DATA))

    }

    fun popUpToId() = savedState.get<Int>(Constants.POP_UP_TO_ID) ?: -1
    fun popUpToInclusive() = savedState.get<Boolean>(Constants.POP_UP_TO_INCLUSIVE) ?: false
    fun navigationId() = savedState.get<Int>(Constants.NAVIGATION_ID) ?: -1
    fun idToPlay() = savedState.get<String>(Constants.ID_TO_PLAY) ?: ""
    fun timeShiftLimit() = savedState.get<Int>(Constants.TIME_SHIFT_LIMIT) ?: 0
    fun timeShift() = savedState.get<Int>(Constants.TIME_SHIFT) ?: 0
    fun enterRegister() = savedState.get<Boolean>(Constants.ENTER_REGISTER) ?: false
    fun checkRequireVip() = savedState.get<Boolean>(Constants.CHECK_REQUIRE_VIP) ?: false
    fun isPlaylist() = savedState.get<Boolean>(Constants.IS_PLAYLIST) ?: false
    fun extraData() = savedState.get<Bundle>(Constants.LOGIN_NAVIGATE_EXTRA_DATA)

    class Status {
        companion object {
            const val IS_SUCCESS = 1
            const val ERROR_LOGIN_AUTHENTICATION_FAILED = 9
            const val ERROR_LOGIN_NEED_UPDATE_PHONE = 16
            const val ERROR_LOGIN_MORE_THAN_THREE_DEVICES = 33
            const val ERROR_LOGIN_NEW_DEVICES = 44
            const val ERROR_MANY_REQUEST_21 = 21
            const val ERROR_MANY_REQUEST_22 = 22
            const val ERROR_MANY_REQUEST_429 = 429

            //new flow otp
            const val ERROR_LIMIT_CALL_API = "2"
            const val ERROR_TOKEN_EXPIRE = "9"



        }
    }
}