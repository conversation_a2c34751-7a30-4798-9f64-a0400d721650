<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:id="@+id/fl_media_container"
        android:layout_width="wrap_content"
        android:layout_height="match_parent">
    </FrameLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_thumbnail"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="0dp"
        app:layout_constraintDimensionRatio="w,9:16"
        android:layout_height="match_parent"
        android:scaleType="fitCenter"
        android:adjustViewBounds="true"/>

    <com.fptplay.mobile.features.moments.view.MomentPlayerUiView
        android:id="@+id/moment_player_ui_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />
    <ImageView
        android:visibility="invisible"
        android:id="@+id/iv_overlay_logo"
        android:layout_width="@dimen/_64sdp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_88sdp"
        android:layout_marginEnd="@dimen/_11sdp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="@id/fl_media_container" />

</androidx.constraintlayout.widget.ConstraintLayout>