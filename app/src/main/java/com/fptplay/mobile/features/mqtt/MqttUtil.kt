package com.fptplay.mobile.features.mqtt

import com.fpl.plugin.mqtt.model.ReceivedMessage
import com.fptplay.mobile.features.mqtt.MqttUtil.fromJsonString
import com.fptplay.mobile.features.mqtt.MqttUtil.mapToPublisher
import com.fptplay.mobile.features.mqtt.model.ActionDetails
import com.fptplay.mobile.features.mqtt.model.AutomaticRetryData
import com.fptplay.mobile.features.mqtt.model.EmergencyRoomData
import com.fptplay.mobile.features.mqtt.model.LimitCcuData
import com.fptplay.mobile.features.mqtt.model.MessageArrived
import com.xhbadxx.projects.module.domain.entity.fplay.common.Config
import com.fptplay.mobile.features.mqtt.model.MqttAutomaticRetry
import com.fptplay.mobile.features.mqtt.model.MqttConfig
import com.fptplay.mobile.features.mqtt.model.MqttConfigDetail
import com.fptplay.mobile.features.mqtt.model.MqttConnectionConfig
import com.fptplay.mobile.features.mqtt.model.MqttModeOption
import com.fptplay.mobile.features.mqtt.model.MqttNotificationConfig
import com.fptplay.mobile.features.mqtt.model.MqttNotificationDetail
import com.fptplay.mobile.features.mqtt.model.MqttNotificationType
import com.fptplay.mobile.features.mqtt.model.MqttOptionData
import com.fptplay.mobile.features.mqtt.model.MqttRoom
import com.fptplay.mobile.features.mqtt.model.Publisher
import com.google.gson.Gson
import com.xhbadxx.projects.module.domain.entity.fplay.common.MQTTConfig
import com.xhbadxx.projects.module.domain.entity.fplay.common.MQTTPing

object MqttUtil {
    const val SUCCESS_CODE = "200"
    const val LIMIT_CODE = "406"
    const val REMOTE_TOPIC = "remote"
    const val PING_CCU_TOPIC = "pingccu"
    const val SEPARATOR = "/"
    const val LWT_TOPIC = "will/disconnect"
    const val LWT_MESSAGE = "{}"
    const val ACTION_START = "start"
    const val ACTION_END = "stop"
    const val LOG_ID_ERROR = "114"
    const val EVENT_ERROR = "Error"
    const val ACTION_LIMIT_CCU = "limit_ccu"
    const val PUBLISHER = "_Publisher"
    const val SUBSCRIBER = "_Subcriber"
    const val DEFAULT_MODE = 1
    const val MQTT_ENABLE = "1"
    private const val RETRY_ENABLE = "1"
    private const val BACKUP_ENABLE = "1"
    private var mqttNotificationConfig: MqttNotificationConfig? = null

    fun MQTTConfig.AutomaticReconnect.isAutoReconnect(): Boolean {
        return this.enable && this.minRetryInterval > 0 && this.maxRetryInterval > 0 && this.minRetryInterval < this.maxRetryInterval
    }


    fun initConfig(appConfig: Config? = null) : MqttConfig? {
        return appConfig?.let {
            MqttConfig(
                xAgent = it.xAgent,
                platformGroup = it.platformGroup,
                detail = MqttConfigDetail(
                    emergencyRooms = it.mqttEmmenencyRooms.map { room ->
                        EmergencyRoomData(roomId = room.roomId, roomType = room.roomType)
                    },
                    enable = it.mqttEnable,
                    emergencyEnableRandomDelay = it.emergencyRandomDelay.toIntOrNull() ?: -1,
                    automaticRetry = AutomaticRetryData(
                        enable = it.automaticRetryEnable,
                        maxRetryInterval = it.maxRetryInterval,
                        minRetryInterval = it.minRetryInterval,
                        random = it.mqttRandom.toIntOrNull() ?: -1
                    ),
                    options = it.mqttOptions.map { option ->
                        MqttOptionData(
                            mqttMode = option.mqttMode.toIntOrNull() ?: 1,
                            waitingApproval = option.waitingApproval.toIntOrNull() ?: -1,
                            previewWaitingApproval = option.previewWaitingApproveal.toIntOrNull() ?: -1,
                            enableBackupApi = option.enableBackupApi == BACKUP_ENABLE,
                            maxRetryBackupApi = option.maxRetryBackupApi.toIntOrNull() ?: -1
                        )
                    }
                )
            )
        }
    }

    private fun mapToRooms(rooms: List<MqttRoom>?): List<EmergencyRoomData> {
        return rooms?.let {
            it.map { room ->
                mapToRoomData(room)
            }
        } ?: emptyList()
    }

    private fun mapToRoomData(room: MqttRoom) : EmergencyRoomData {
        return EmergencyRoomData(
            roomId = room.roomId,
            roomType = room.roomType
        )
    }

    private fun mapToOptions(options: List<MqttModeOption>?): List<MqttOptionData> {
        return options?.let {
            it.map { option ->
                mapToOptionData(option)
            }
        } ?: emptyList()
    }

    private fun mapToOptionData(option: MqttModeOption) : MqttOptionData {
        return MqttOptionData(
            mqttMode =option.mqttMode?.toIntOrNull() ?: 1,
            waitingApproval = option.waitingApproval?.toIntOrNull() ?: -1,
            previewWaitingApproval = option.previewWaitingApproval?.toIntOrNull() ?: -1,
            enableBackupApi = option.enableBackupApi == BACKUP_ENABLE,
            maxRetryBackupApi = option.maxRetryBackupApi?.toIntOrNull() ?: -1
        )
    }

    private fun mapAutomaticRetry(data: MqttAutomaticRetry?): AutomaticRetryData {
        return AutomaticRetryData(
            enable = data?.enable == RETRY_ENABLE,
            maxRetryInterval = data?.maxRetryInterval?.toIntOrNull() ?: -1,
            minRetryInterval = data?.minRetryInterval?.toIntOrNull() ?: -1,
            random = data?.random?.toIntOrNull() ?: -1
        )
    }

    private fun MqttConfig.updateMqttConfig(data: MqttNotificationDetail) {
        detail = MqttConfigDetail(
            emergencyRooms = mapToRooms(data.mqtt?.rooms),
            enable =  data.mqtt?.enable == MQTT_ENABLE,
            emergencyEnableRandomDelay = data.mqtt?.emergencyEnableRandomDelay?.toIntOrNull() ?: -1,
            automaticRetry = mapAutomaticRetry(data.mqtt?.automaticRetry),
            options = mapToOptions(data.mqtt?.options)
        )
    }

    var getCurrentMqttConfig: MqttNotificationConfig?
        get() = if (mqttNotificationConfig != null) {
            mqttNotificationConfig
        } else MqttNotificationConfig.emptyMqttNotificationConfig
        set(value) {
            mqttNotificationConfig = value
        }

    fun MqttConfig.updateFromDetail(details: MqttNotificationDetail?,callback: (Boolean) -> Unit = {}){
        if(details == null) return
        when(details.notificationType){
            MqttNotificationType.ALL.value -> {
                this.updateMqttConfig(details)
                callback.invoke(details.mqtt?.enable == MQTT_ENABLE)
            }
            MqttNotificationType.ENABLE.value -> {
                this.detail.enable = details.mqtt?.enable == MQTT_ENABLE
                callback.invoke(true)
            }
            MqttNotificationType.AUTOMATIC_RETRY.value -> {
                this.detail.automaticRetry = mapAutomaticRetry(details.mqtt?.automaticRetry)
            }
            MqttNotificationType.OPTIONS.value -> {
                this.detail.options = mapToOptions(details.mqtt?.options)
            }
            MqttNotificationType.ROOMS.value -> {
                this.detail.emergencyRooms = mapToRooms(details.mqtt?.rooms)
            }
            else -> {}
        }
    }

    fun MQTTConfig.AutomaticReconnect.getReconnectInterval(): Pair<Long, Long> {
        return Pair(this.minRetryInterval.toLong(), this.maxRetryInterval.toLong())
    }

    fun Publisher.toJsonString(): String {
        return Gson().toJson(this)
    }

    fun String.fromJsonString(): Publisher? {
        return try {
            Gson().fromJson(this, Publisher::class.java)
        } catch (ex: Exception) {
            null
        }
    }

    fun Int.toMillisecond(): Long {
        return (this * 1000).toLong()
    }

    fun ReceivedMessage.mapToPublisher(): Publisher? {
        val payload = this.message.payload.decodeToString()
        return payload.fromJsonString()
    }

    fun ReceivedMessage.mapToMessageArrived(): MessageArrived? {
        val payload = this.message.payload.decodeToString()
        return try {
            Gson().fromJson(payload, MessageArrived::class.java)
        } catch (ex: Exception) {
            null
        }
    }

    fun getCurrentTimeInSeconds(): Long {
        val currentTimeMillis = System.currentTimeMillis()
        return currentTimeMillis / 1000L // Chia cho 1000 để chuyển từ mili giây sang giây
    }

    fun MQTTPing.mapPingToMessageArrived(): MessageArrived {
        return MessageArrived(
            action = this.action,
            code = this.code,
            data = LimitCcuData(
                action = ActionDetails(
                    title = this.data?.title ?: "",
                    desc = this.data?.desc ?: "",
                    titleEn = this.data?.titleEn ?: "",
                    descEn = this.data?.descEn ?: ""
                )
            ),
            itemId = this.itemId,
            episodeId = this.episodeId,
            chapterId = this.chapterId,
            playlistId = this.playlistId,
            createdTime = this.createdTime?.toLongOrNull() ?: 0
        )
    }
}