package com.fptplay.mobile.features.game_hip_fest.utis

import android.content.Context
import android.graphics.*
import com.fptplay.mobile.R
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.WriterException
import com.google.zxing.qrcode.QRCodeWriter
import java.util.*
import androidx.core.graphics.createBitmap
import androidx.core.graphics.scale
import androidx.core.graphics.set

object QRCodeUtils {
     fun createQRCodeTicket(widthQR:Int,sizeLogo:Int,context: Context?, content:String): Bitmap?{
        val hints =  Hashtable<EncodeHintType,String>()
        hints[EncodeHintType.CHARACTER_SET] = "UTF-8"
        /**
         * convert string with UTF-8 encoding
         */
        var widthQR =widthQR
        val writer = QRCodeWriter()
        try {
            val bitMatrix = writer.encode(content, BarcodeFormat.QR_CODE, widthQR, widthQR,hints)
            val width = bitMatrix.width
            val height = bitMatrix.height
            val bmp = createBitmap(width, height, Bitmap.Config.RGB_565)
            for (x in 0 until width) {
                for (y in 0 until height) {
                    bmp[x, y] = if (bitMatrix[x, y]) Color.BLACK else Color.WHITE
                }
            }
            val options = BitmapFactory.Options()
            options.outWidth = 250
            options.inJustDecodeBounds = false
            options.outHeight = 250
            val logo =
                BitmapFactory.decodeResource(context?.resources, R.drawable.ic_qr,options)
            val overlay = logo.scale(sizeLogo, sizeLogo)
            val bitMerged = mergeBitmaps(bmp, overlay);
            return bitMerged
        } catch (e: WriterException) {
            e.printStackTrace()
        }
        return null
    }
    private fun mergeBitmaps(bmp1: Bitmap, bmp2: Bitmap): Bitmap? {
        val bmOverlay = createBitmap(bmp1.width, bmp1.height, bmp1.config ?: Bitmap.Config.RGB_565)
        val xLogo = (bmp1.width - bmp2.width ) / 2f
        val yLogo = (bmp1.height - bmp2.height) / 2f
        val canvas = Canvas(bmOverlay)
        canvas.drawBitmap(bmp1, Matrix(), null)
        canvas.drawBitmap(bmp2, xLogo, yLogo, null)
        return bmOverlay
    }
}