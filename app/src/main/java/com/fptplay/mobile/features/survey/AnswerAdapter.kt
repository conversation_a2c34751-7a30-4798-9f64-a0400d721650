package com.fptplay.mobile.features.survey

import android.annotation.SuppressLint
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.EditText
import android.widget.RadioButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.utils.Utils
import com.xhbadxx.projects.module.domain.entity.fplay.common.SurveyQuestion
import com.xhbadxx.projects.module.domain.entity.fplay.common.SurveyQuestion.TypeOption
import com.xhbadxx.projects.module.util.logger.Logger

class AnswerAdapter(
    private val limitText: Int = 1000,
    private val onItemCheck: ((SurveyQuestion.Answer) -> Unit)? = null,
    private val hideKeyboard: ((Boolean) -> Unit)? = null
) : RecyclerView.Adapter<AnswerAdapter.AnswerViewHolder>() {

    private var answers: List<SurveyQuestion.Answer> = arrayListOf()
    private var oldPos = -1
    private var isMultiple: Boolean = false

    sealed class AnswerViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)
    class RadioButtonViewHolder(itemView: View) : AnswerViewHolder(itemView) {
        val radioButton: RadioButton = itemView.findViewById(R.id.radio_button)
    }

    class CheckBoxViewHolder(itemView: View) : AnswerViewHolder(itemView) {
        val checkBox: CheckBox = itemView.findViewById(R.id.check_box)
    }

    class TextViewHolder(itemView: View) : AnswerViewHolder(itemView) {
        val tvAnswerText: TextView = itemView.findViewById(R.id.tv_answer_text)
        val etAnswer: EditText = itemView.findViewById(R.id.et_answer_textarea)
        val tvCharCount: TextView = itemView.findViewById(R.id.tv_char_count)
    }

    override fun getItemViewType(position: Int): Int {
        val answer = answers[position]
        return when (answer.type) {
            TypeOption.RADIO -> 0
            TypeOption.CHECKBOX -> 1
            else -> 2
        }
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AnswerViewHolder {
        return when (viewType) {
            0 -> RadioButtonViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.item_answer_radio, parent, false))
            1 -> CheckBoxViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.item_answer_checkbox, parent, false))
            else -> TextViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.item_answer, parent, false))
        }
    }
    override fun onBindViewHolder(holder: AnswerViewHolder, position: Int, payloads: MutableList<Any>) {
        Logger.d("onBindViewHolder - payloads  holder: $holder, position: $position, payloads: $payloads")
        if (payloads.isNotEmpty() && payloads.contains("SELECTION_CHANGED")) {
            val answer = answers[position]
            when(holder) {
                is RadioButtonViewHolder -> {
                    holder.radioButton.isChecked = answer.isSelected
                }
                is CheckBoxViewHolder -> {
                    holder.checkBox.isChecked = answer.isSelected
                }
                is TextViewHolder -> {
                    holder.etAnswer.setText(answer.textAnswer)
                    holder.etAnswer.scrollTo(0,0) // Reset scroll position to top
                    updateTvCharCount(answer.textAnswer.length, holder.tvCharCount)
                }
            }
        } else {
            super.onBindViewHolder(holder, position, payloads)
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onBindViewHolder(holder: AnswerViewHolder, position: Int) {
        Logger.d("onBindViewHolder holder: $holder")
        val answer = answers[position]
        when (holder) {
            is RadioButtonViewHolder -> {
                if(answer.isSelected) oldPos = holder.bindingAdapterPosition
                holder.radioButton.text = answer.name
                holder.radioButton.isChecked = answer.isSelected
                holder.radioButton.setOnClickListener {
                    hideKeyboard?.invoke(true)
                    answer.isSelected = holder.radioButton.isChecked
                    onItemCheck?.invoke(answer)
                    updateOldPos(holder.bindingAdapterPosition)
                }
            }

            is CheckBoxViewHolder -> {
                holder.checkBox.text = answer.name
                holder.checkBox.isChecked = answer.isSelected
                holder.checkBox.setOnCheckedChangeListener { _, isChecked ->
                    answer.isSelected = isChecked
                    onItemCheck?.invoke(answer)
                }
            }

            is TextViewHolder -> {
                if(answer.isSelected) oldPos = holder.bindingAdapterPosition
                updateTvCharCount(answer.textAnswer.length, holder.tvCharCount)
                holder.etAnswer.setText(answer.textAnswer)
                holder.tvAnswerText.text = answer.name
                holder.etAnswer.movementMethod = android.text.method.ScrollingMovementMethod.getInstance()
                holder.etAnswer.isVerticalScrollBarEnabled = true
                holder.etAnswer.setOnTouchListener { v, event ->
                    v.parent.requestDisallowInterceptTouchEvent(true)
                    if (event.action == MotionEvent.ACTION_UP) {
                        v.parent.requestDisallowInterceptTouchEvent(false)
                        v.requestFocus()
                        if(answer.textAnswer.isBlank())
                            holder.etAnswer.setSelection(0) // Set cursor to the start if text is empty - for some devices
                        v.performClick()
                    }
                    false
                }

                holder.etAnswer.addTextChangedListener(object : TextWatcher {
                    override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
                    override fun onTextChanged(text: CharSequence?, p1: Int, p2: Int, p3: Int) {
                        val charCount = text?.length ?: 0
                        updateTvCharCount(charCount, holder.tvCharCount)
                        answer.textAnswer = holder.etAnswer.text.toString()
                        val selected = answer.textAnswer.length in 1..limitText
                        if (answer.isSelected != selected) {
                            if(selected) {
                                updateOldPos(holder.bindingAdapterPosition)
                            }
                            answer.isSelected = selected
                            onItemCheck?.invoke(answer)
                        }
                    }

                    override fun afterTextChanged(p0: Editable?) {
                    }
                })
            }
        }
    }

    override fun getItemCount(): Int {
        val count = answers.size
        Logger.d("AnswerAdapter getItemCount() called, returning: $count")
        return count
    }

    private fun updateTvCharCount(charCount: Int, tvCharCount: TextView) {
        Utils.fromHtml(
            tvCharCount,
            if (charCount > limitText)
                tvCharCount.context.getString(R.string.survey_limit_option, "$charCount", "$limitText")
            else tvCharCount.context.getString(R.string.survey_no_limit_option, "$charCount", "$limitText")
        )
    }
    private fun updateOldPos(curPos:Int) { //if isMultiple is true, we don't update oldPos
        if(!isMultiple) {
            if (oldPos != -1 && oldPos != curPos) {
                answers.getOrNull(oldPos)?.let {
                    it.isSelected = false
                    it.textAnswer = ""
                    notifyItemChanged(oldPos, "SELECTION_CHANGED")
                }
            }
            oldPos = curPos
        }
    }
    @SuppressLint("NotifyDataSetChanged")
    fun updateAnswers(newAnswers: List<SurveyQuestion.Answer>, isMultiple: Boolean = false) {
        this.isMultiple = isMultiple
        answers = newAnswers
        notifyDataSetChanged()
    }
}
