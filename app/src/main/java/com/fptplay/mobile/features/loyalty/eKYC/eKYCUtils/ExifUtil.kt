package com.fptplay.mobile.features.loyalty.eKYC.eKYCUtils

import android.graphics.Bitmap
import android.graphics.Matrix
import android.media.ExifInterface
import timber.log.Timber
import java.io.IOException
import java.lang.reflect.InvocationTargetException

class ExifUtil {
    companion object {

        /**
         * Rotate an image if required.
         *
         * @param img           The image bitmap
         * @param selectedImage Image URI
         * @return The resulted Bitmap after manipulation
         */
        @Throws(IOException::class)
        fun rotateImageIfRequired(img: Bitmap?, selectedImage: String): Bitmap? {

            val ei = ExifInterface(selectedImage)
            val orientation =
                ei.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL)

            return when (orientation) {
                ExifInterface.ORIENTATION_ROTATE_90 -> rotateImage(img, 90)
                ExifInterface.ORIENTATION_ROTATE_180 -> rotateImage(img, 180)
                ExifInterface.ORIENTATION_ROTATE_270 -> rotateImage(img, 270)
                else -> img
            }
        }

        private fun rotateImage(img: Bitmap?, degree: Int): Bitmap? {
            if (img == null) return null
            val matrix = Matrix()
            matrix.postRotate(degree.toFloat())
            val rotatedImg = Bitmap.createBitmap(
                img.copy(img.config ?: Bitmap.Config.ARGB_8888, true),
                0,
                0,
                img.width,
                img.height,
                matrix,
                true
            )
            img.recycle()
            return rotatedImg
        }


        fun rotateBitmap(src: String, bitmap: Bitmap): Bitmap {
            try {
                val orientation: Int = getExifOrientation(src)
                if (orientation == 1) {
                    Timber.e("Return orientation = 1")
                    return bitmap
                }
                val matrix = Matrix()
                when (orientation) {
                    2 -> matrix.setScale(-1f, 1f)
                    3 -> matrix.setRotate(180f)
                    4 -> {
                        matrix.setRotate(180f)
                        matrix.postScale(-1f, 1f)
                    }

                    5 -> {
                        matrix.setRotate(90f)
                        matrix.postScale(-1f, 1f)
                    }

                    6 -> matrix.setRotate(90f)
                    7 -> {
                        matrix.setRotate(-90f)
                        matrix.postScale(-1f, 1f)
                    }

                    8 -> matrix.setRotate(-90f)
                    else -> {
                        Timber.e("Return else")
                        return bitmap.config?.let { bitmap.copy(it, true) } ?: bitmap
                    }
                }
                return try {
                    val oriented =
                        Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
//                    bitmap.recycle()

                    Timber.e("Return oriented $orientation")
                    oriented
                } catch (e: OutOfMemoryError) {
                    e.printStackTrace()
                    Timber.e("Return oriented else")
                    bitmap.config?.let { bitmap.copy(it, true) } ?: bitmap
                }
            } catch (e: IOException) {
                e.printStackTrace()
            }
            Timber.e("Return final")
            return bitmap.config?.let { bitmap.copy(it, true) } ?: bitmap
        }

        @Throws(IOException::class)
        private fun getExifOrientation(src: String): Int {
            var orientation = 1
            try {
                val exif = ExifInterface(src);
                orientation = exif.getAttributeInt(
                    ExifInterface.TAG_ORIENTATION,
                    1
                );
            } catch (e: ClassNotFoundException) {
                e.printStackTrace()
            } catch (e: SecurityException) {
                e.printStackTrace()
            } catch (e: NoSuchMethodException) {
                e.printStackTrace()
            } catch (e: IllegalArgumentException) {
                e.printStackTrace()
            } catch (e: InstantiationException) {
                e.printStackTrace()
            } catch (e: IllegalAccessException) {
                e.printStackTrace()
            } catch (e: InvocationTargetException) {
                e.printStackTrace()
            } catch (e: NoSuchFieldException) {
                e.printStackTrace()
            }
            return orientation
        }
    }
}