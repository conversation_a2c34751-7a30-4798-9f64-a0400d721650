package com.fptplay.mobile.features.download.fragment

import android.content.res.Configuration
import android.graphics.Rect
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fplay.module.downloader.VideoDownloadManager
import com.fplay.module.downloader.listener.DownloadListener
import com.fplay.module.downloader.model.CollectionVideoTaskItem
import com.fplay.module.downloader.model.VideoTaskItem
import com.fplay.module.downloader.model.VideoTaskState
import com.fptplay.mobile.*
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.runOnUiThread
import com.fptplay.mobile.common.extensions.showSnackBar
import com.fptplay.mobile.common.ui.bases.BaseActivity
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.view.GlobalSnackbarManager
import com.fptplay.mobile.common.ui.view.SnackbarManager
import com.fptplay.mobile.common.utils.NetworkUtils
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.VodDetailOfflineFragmentBinding
import com.fptplay.mobile.features.download.DownloadUtils
import com.fptplay.mobile.features.download.DownloadViewModel
import com.fptplay.mobile.features.download.adapter.VideoDownloadListAdapter
import com.fptplay.mobile.features.download.model.UpdateDownloadProgress
import com.fptplay.mobile.features.download.model.UpdateDownloadStatus
import com.fptplay.mobile.vod.VodDetailViewModel
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

@AndroidEntryPoint
class VodDetailOfflineFragment : BaseFragment<VodDetailViewModel.VodDetailState, VodDetailViewModel.VodDetailIntent>() {

    private val safeArgs: VodDetailOfflineFragmentArgs by navArgs()

    private var _binding: VodDetailOfflineFragmentBinding? = null
    private val binding get() = _binding!!

    private val mAdapter: VideoDownloadListAdapter by lazy { VideoDownloadListAdapter(true) }

    override val viewModel: VodDetailViewModel by activityViewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.saveDataDetail(null)
    }
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        VideoDownloadManager.instance.setGlobalDownloadListener(mListener)
        _binding = VodDetailOfflineFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initData() {
        viewModel.saveId(id = safeArgs.chapterId)
        viewModel.saveFileHash(hash = safeArgs.fileHash)
        viewModel.triggerInitPlayer(true)
    }

    override fun bindData() {
        viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.GetAllDownloadVideos(isAirline = true,safeArgs.movieId))
    }

    override fun bindComponent() {
        binding.rvDownloadList.apply {
            adapter = mAdapter
            layoutManager = LinearLayoutManager(binding.root.context, LinearLayoutManager.VERTICAL, false)
            addItemDecoration(
                object : RecyclerView.ItemDecoration() {
                    override fun getItemOffsets(
                        outRect: Rect,
                        view: View,
                        parent: RecyclerView,
                        state: RecyclerView.State
                    ) {
                        outRect.bottom = resources.getDimensionPixelSize(R.dimen.payment_package_margin)
                    }
                }
            )
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun bindEvent() {
        mAdapter.eventListener = object : IEventListener<CollectionVideoTaskItem> {
            override fun onClickedItem(position: Int, data: CollectionVideoTaskItem) {
                if (data.listChapters[0].taskState == VideoTaskState.SUCCESS) {
                    if (DownloadUtils.calculateTimeLeft(data.listChapters[0].lastUpdateTime,data.listChapters[0].expiredTime) > 0) {
                        //viewModel.triggerPlayEpisode(data = data)
                        viewModel.saveId(id = data.listChapters[0].chapterId)
                        viewModel.saveFileHash(hash = data.listChapters[0].fileHash ?: "")
                        viewModel.triggerInitPlayer(true)

                        mAdapter.selectEpisode(position, data)
                    } else
                        showConfirmMovieDialog(data.listChapters[0])
                }
            }

            override fun onClickView(position: Int, view: View?, data: CollectionVideoTaskItem) {
                if (view?.id == R.id.ivBtnMore) {
                    showConfirmMovieDialog(data.listChapters[0])
                }
            }
        }
        observeData()
        bindEventFragmentResult()
    }
    //endregion

    private fun showConfirmMovieDialog(
        item: VideoTaskItem
    ) {
        DownloadUtils.bindEventFragmentResult(item.chapterId,this@VodDetailOfflineFragment, eventDelete =  {
            binding.pbLoading.root.show()
            lifecycleScope.launch(Dispatchers.IO) {
                VideoDownloadManager.instance.deleteVideoTask(item.chapterId, true)
            }
        }, eventExtend = {
            viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.CheckExtendForDownload(item))
        }, checkInternet = {
            if(NetworkUtils.isNetworkAvailable()) {
                true
            } else {
                showWarningDialog(<EMAIL>(R.string.error_no_intent))
                false
            }
        })
        val optionDown = DownloadUtils.setOptionResult(item.taskState,item.lastUpdateTime,item.expiredTime)
        findNavController().navigate(
            NavAirlineDirections.actionGlobalToVodOptionDialogFragment(
                optionDown,hasEdgeToEdge,isDownloadType = true
            )
        )
    }

    private fun showConfirmDeleteMovieDialog(dialogMessage: String, chapterId: String) {
        parentFragment?.findNavController()?.navigate(
            VodDetailOfflineFragmentDirections.actionVodDetailOfflineFragmentToDownloadMoreOptionBottomSheetDialogFragment(
                type = Utils.DOWNLOAD_OPTION_DIALOG_DELETE_TYPE,
                title = dialogMessage,
                optionOneText = getString(R.string.delete),
                optionOneValue = chapterId,
                optionTwoText = getString(R.string.warning_dialog_button_negative_text),
                optionTwoValue = "",
            )
        )
    }

    private fun bindEventFragmentResult() {
        findNavController().run {
            setFragmentResultListener(Utils.DOWNLOAD_OPTION_DIALOG_DELETE_TYPE) { _, bundle ->
                val chapterId = bundle.getString(Utils.DOWNLOAD_OPTION_DIALOG_CHOSEN_OPTION_ID_KEY, "")
                if (chapterId.isNotEmpty()) {
                    binding.pbLoading.root.show()
                    VideoDownloadManager.instance.deleteVideoTask(chapterId, true)
                }
            }
        }
    }
    private val mListener: DownloadListener = object : DownloadListener() {
        private var mLastProgressTimeStamp: Long = 0

        override fun onDownloadDefault(oldState: Int, item: VideoTaskItem) {
            Timber.d("onDownloadDefault: $item")
            val pos = mAdapter.data().indexOfFirst { collectionVideoTaskItem ->
                collectionVideoTaskItem.listChapters[0].chapterId == item.chapterId
            }

            if (pos != -1) {
                runOnUiThread {
                    mAdapter.removeIndex(pos) {
                        mAdapter.notifyItemRemoved(pos)
                        binding.pbLoading.root.hide()
                        if (viewModel.getId() == item.chapterId) {
                            backHandler()
                        }
                    }
                }
            }
        }

        override fun onDownloadPrepare(item: VideoTaskItem) {
            Timber.d("onDownloadPrepare: %s", item.url)
            val prePos = mAdapter.itemCount
            val pos = mAdapter.data()
                .indexOfFirst { collectionVideoTaskItem -> collectionVideoTaskItem.listChapters[0].chapterId == item.chapterId }
            if (pos != -1) {
                runOnUiThread { mAdapter.notifyItemRangeChanged(pos, mAdapter.itemCount - pos) }
            } else {
                mAdapter.add(item.toCollectionVideoTaskItem())
                runOnUiThread {
                    mAdapter.notifyItemRangeRemoved(prePos, mAdapter.itemCount - prePos)
                    mAdapter.notifyItemRangeInserted(prePos, mAdapter.itemCount - prePos)
                }
            }
        }

        override fun onDownloadProgress(item: VideoTaskItem) {
            val currentTimeStamp = System.currentTimeMillis()
            if (currentTimeStamp - mLastProgressTimeStamp > 1000) {
                Timber.d("onDownloadProgress: ${item.percentString}, curTs=${item.curTs}, totalTs=${item.totalTs}")
                notifyChanged(
                    item,
                    UpdateDownloadProgress(item.percentString, item.downloadSizeString)
                )
                mLastProgressTimeStamp = currentTimeStamp
            }
        }

        override fun onDownloadPause(item: VideoTaskItem) {
            Timber.d("onDownloadPause: %s", item.url)
            notifyChanged(item, UpdateDownloadStatus(VideoTaskState.PAUSE))
        }

        override fun onDownloadError(item: VideoTaskItem) {
            Timber.d("onDownloadError: %s", item.url)
            if (item.hasLinkRefetch) {
                viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.FetchDownloadLink(item))
            } else {
                notifyChanged(item, UpdateDownloadStatus(VideoTaskState.ERROR))
            }
        }

        override fun onDownloadSuccess(item: VideoTaskItem) {
            Timber.d("onDownloadSuccess: $item")
            notifyChanged(item, UpdateDownloadStatus(VideoTaskState.SUCCESS))
        }
    }

    private fun notifyChanged(item: VideoTaskItem, payloads: Any? = null) {
        val pos = mAdapter.data().indexOfFirst { collectionVideoTaskItem ->
            collectionVideoTaskItem.listChapters[0].chapterId == item.chapterId
        }
        if (pos != -1) {
            runOnUiThread {
                if (payloads != null) {
                    mAdapter.notifyItemChanged(pos, payloads)

                    val data = mAdapter.data()[pos]
                    if (payloads is UpdateDownloadStatus) {
                        data.listChapters[0].sourceState = payloads.status
                        mAdapter.changeDataAt(pos, data)
                    }
                } else
                    mAdapter.notifyItemChanged(pos)
            }
        }
    }

    override fun VodDetailViewModel.VodDetailState.toUI() {
        when (this) {
            is VodDetailViewModel.VodDetailState.Loading -> {
                when (data) {
                    is VodDetailViewModel.VodDetailIntent.CheckExtendForDownload,
                    is VodDetailViewModel.VodDetailIntent.GetAllDownloadVideos,
                    is VodDetailViewModel.VodDetailIntent.FetchDownloadLink-> {
                        binding.pbLoading.root.show()
                    }
                    else -> {}
                }
            }
            is VodDetailViewModel.VodDetailState.ResultDetailOffline -> {
                binding.pbLoading.root.hide()
                viewModel.saveVipRequired(isRequired = false, requiredVip = null)
                viewModel.triggerPrepareOfflinePlayer(this.data, this.currentId)
                val currentItem = data.listChapters.first { it.chapterId == this.currentId }
                binding.tvTitle.text = data.title +
                        if (data.isSeries)
                            " - " + currentItem.chapterName
                        else
                            ""
                binding.tvSubTitle.text = data.subTitle
                if (currentItem.maturityValue.isNullOrBlank()) {
                    binding.tvVideoInfo.isVisible = false
                } else {
                    binding.tvVideoInfo.isVisible = true
                    binding.tvVideoInfo.text = currentItem.maturityValue
                }
                if (currentItem.maturityAdvisories.isNullOrBlank()) {
                    binding.tvAgeRestriction.isVisible = false
                } else {
                    binding.tvAgeRestriction.isVisible = true
                    binding.tvAgeRestriction.text = currentItem.maturityAdvisories
                }
            }
            /*is VodDetailViewModel.VodDetailState.ResultDetail -> {
                binding.pbLoading.root.hide()
                viewModel.saveDataDetail(this.data)
                updateDetailUI(isPlayerCalled = this.intent is VodDetailViewModel.VodDetailIntent.GetDetail && this.intent.isPlayerCalled)
            }
            is VodDetailViewModel.VodDetailState.ResultOnPlayerChanged -> {
                curEpisode = this.episode
                binding.tvTitle.text = <EMAIL>(R.string.vod_title_pattern, viewModel.getDataDetail()?.blockContent?.titleVietnam ?:"", curEpisode?.titleVietnam ?:"")
                curEpisode?.let {
                    viewModel.getDataDetail()?.let { detail ->
                        viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.GetDownloadStage(detail.blockContent.id, it))
                    }
                }
            }*/
            is VodDetailViewModel.VodDetailState.GetAllDownloadVideosResult -> {
                if (this.data != null) {
                    mAdapter.clear()
                    mAdapter.add(data = this.data, true) {
                        data.forEachIndexed { index, collectionVideoTaskItem ->
                            if (collectionVideoTaskItem.listChapters[0].chapterId == viewModel.getId()) {
                                mAdapter.selectEpisode(index, collectionVideoTaskItem)
                                return@forEachIndexed
                            }
                        }
                        binding.pbLoading.root.hide()
                    }
                }
            }
            is VodDetailViewModel.VodDetailState.ResultCheckExtendForDownload -> {
                binding.pbLoading.root.hide()
                if (this.vodDownloadInfo.downloadUrl.isNotBlank()) {
                    DownloadUtils.extendDownloadTaskItem(item = this.item, info = this.vodDownloadInfo)
                } else {
                    showWarningDialog(resources.getString(R.string.noti_pakage_unavailable))
                }
            }
            is VodDetailViewModel.VodDetailState.ResultFetchDownloadLink -> {
                binding.pbLoading.root.hide()
                if (this.vodDownloadInfo.downloadUrl.isNotBlank()) {
                    DownloadUtils.startDownloadWithNewUrl(this.vodDownloadInfo.downloadUrl,this.item,this.vodDownloadInfo.d2gTime, this.vodDownloadInfo.streamSession)
                } else {
                    showWarningDialog(resources.getString(R.string.noti_pakage_unavailable))
                }
            }
            is VodDetailViewModel.VodDetailState.Done -> {
                when (intent) {
                    is VodDetailViewModel.VodDetailIntent.CheckExtendForDownload,
                    is VodDetailViewModel.VodDetailIntent.GetAllDownloadVideos,
                    is VodDetailViewModel.VodDetailIntent.FetchDownloadLink-> {
                        binding.pbLoading.root.hide()
                    }
                    else -> {}
                }
            }
            is VodDetailViewModel.VodDetailState.Error -> {
                when (data) {
                    is VodDetailViewModel.VodDetailIntent.GetAllDownloadVideos -> {
                        binding.pbLoading.root.hide()
                    }
                    is VodDetailViewModel.VodDetailIntent.CheckExtendForDownload,
                    is VodDetailViewModel.VodDetailIntent.FetchDownloadLink-> {
                        binding.pbLoading.root.hide()
                        showWarningDialog(resources.getString(R.string.noti_pakage_unavailable))
                    }
                    else -> {}
                }
            }
            else -> {}
        }
    }

    private fun observeData() {
        viewModel.isFullScreen.observe(viewLifecycleOwner) {
            it?.run {
                if (context.isTablet()) {
                    handleTabletLayout(isFullscreen = it.first, isLandscapeMode = it.second)
                    if (it.second) { // isLandscapeMode
                        binding.cdlVod.isVisible = !it.first
                    }else {
                        binding.cdlVod.isVisible = !it.first
                    }

                } else {
                    binding.cdlVod.isVisible = !it.first
                }
                // handle position when switching to fullscreen or back to normal mode
                handlePositionSnackBar(it.first, it.second)
                //
            }
        }
    }
    // region Snackbar
    private fun handlePositionSnackBar(isFullscreen: Boolean,isLandscape: Boolean){
        GlobalSnackbarManager.withCurrent()?.position =  if (isFullscreen) SnackbarManager.Position.TOP_CENTER else SnackbarManager.Position.BOTTOM_CENTER
    }

    private fun handleTabletLayout(isFullscreen: Boolean, isLandscapeMode: Boolean) {
        if (_binding == null) return
        try {
            if (isLandscapeMode) {
                if (isFullscreen) {
                    ConstraintSet().apply {
                        connect(R.id.cdlVod, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
                        connect(R.id.cdlVod, ConstraintSet.START, R.id.f_player, ConstraintSet.END)
                        connect(R.id.cdlVod, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.cdlVod, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clRoot)
                } else {
                    ConstraintSet().apply {
                        connect(R.id.cdlVod, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
                        connect(R.id.cdlVod, ConstraintSet.START, R.id.f_player, ConstraintSet.END)
                        connect(R.id.cdlVod, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.cdlVod, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clRoot)
                }
            } else {
                if (isFullscreen) {
                    ConstraintSet().apply {
                        connect(R.id.cdlVod, ConstraintSet.TOP, R.id.tl_title, ConstraintSet.BOTTOM)
                        connect(R.id.cdlVod, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
                        connect(R.id.cdlVod, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.cdlVod, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clRoot)
                } else {
                    ConstraintSet().apply {
                        connect(R.id.cdlVod, ConstraintSet.TOP, R.id.tl_title, ConstraintSet.BOTTOM)
                        connect(R.id.cdlVod, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
                        connect(R.id.cdlVod, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.cdlVod, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clRoot)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        mAdapter.notifyDataSetChanged()
    }
}