package com.fptplay.mobile.features.mqtt.model

import com.xhbadxx.projects.module.domain.entity.fplay.common.MQTTConfig

data class MqttConfig(
    val xAgent : String,
    val platformGroup : String,
    var detail: MqttConfigDetail,
    var connection: MQTTConfig.Data? = null
)

data class MqttConfigDetail(
    var emergencyRooms: List<EmergencyRoomData>,
    var enable: Boolean,
    val emergencyEnableRandomDelay: Int,
    var automaticRetry: AutomaticRetryData,
    var options: List<MqttOptionData>
)

data class AutomaticRetryData(
    val enable: Boolean,
    val maxRetryInterval: Int,
    val minRetryInterval: Int,
    val random: Int
)

data class EmergencyRoomData(
    val roomId: String,
    val roomType: String
)

data class MqttOptionData(
    val mqttMode: Int,
    val waitingApproval: Int,
    val previewWaitingApproval: Int,
    val enableBackupApi: Bo<PERSON>an,
    val maxRetryBackupApi: Int
)