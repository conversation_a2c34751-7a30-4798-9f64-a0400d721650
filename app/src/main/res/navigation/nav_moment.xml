<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_moment"
    app:startDestination="@id/momentsFragment">
    <fragment
        android:id="@+id/momentsFragment"
        android:name="com.fptplay.mobile.features.moments.MomentsFragment"
        android:label="Moments List"
        tools:layout="@layout/moments_fragment">
        <argument android:name="momentId"
            app:argType="string"
            android:defaultValue=""/>
        <argument android:name="clickItem"
            app:argType="boolean"
            android:defaultValue="false"/>
        <argument
            android:writePermission="true"
            android:name="chapterId"
            android:defaultValue=""
            app:argType="string"/>
        <argument
            android:name="isPlaylistLayout"
            android:defaultValue="false"
            app:argType="boolean"/>
        <argument
            android:name="indexForPlaylist"
            android:defaultValue="1"
            app:argType="integer"/>
        <argument
            android:name="startPosition"
            android:defaultValue="0L"
            app:argType="long"/>

        <argument
            android:name="isPlaying"
            android:defaultValue="true"
            app:argType="boolean"/>
        <argument
            android:name="startNextEpisode"
            android:defaultValue="false"
            app:argType="boolean"/>

        <argument android:name="showMetadata"
            app:argType="boolean"
            android:defaultValue="true"/>
        <argument android:name="sourceScreen"
            app:argType="string"
            android:defaultValue=""/>
        <argument android:name="relatedId"
            app:argType="string"
            android:defaultValue=""
            app:nullable="true"/>
        <argument android:name="isFromDeeplink"
            app:argType="boolean"
            android:defaultValue="false"/>
        <argument android:name="openListEpisodeDialog"
            app:argType="boolean"
            android:defaultValue="false"/>
        <action
            android:id="@+id/action_global_moment_comment_bottom_sheet"
            app:destination="@id/commentBottomSheetDialogFragment">
            <argument android:name="momentId"
                app:argType="string"
                android:defaultValue=""/>
            <argument android:name="enable"
                app:argType="boolean"
                android:defaultValue="true"/>
            <argument android:name="userCom"
                app:argType="boolean"
                android:defaultValue="true"/>
            <argument android:name="showKeyboard"
                app:argType="boolean"
                android:defaultValue="true"/>
            <argument android:name="requiredMessage"
                app:argType="string"
                android:defaultValue=""/>
            <argument android:name="isLoginRequired"
                app:argType="boolean"
                android:defaultValue="false"/>
            <argument android:name="autoComment"
                app:argType="string"
                android:defaultValue="" />
            <argument android:name="parentIdForAutoComment"
                app:argType="string"
                android:defaultValue="" />
        </action>
        <action
            android:id="@+id/action_momentsFragment_to_momentPlaylistBottomSheetFragment"
            app:destination="@id/momentPlaylistBottomSheetFragment" />
        <action
            android:id="@+id/action_momentsFragment_self"
            app:destination="@id/momentsFragment" >
            <argument android:name="momentId"
                app:argType="string"
                android:defaultValue=""/>
            <argument
                android:name="chapterId"
                android:defaultValue=""
                app:argType="string"/>
        </action>

    </fragment>
    <dialog
        android:id="@+id/commentBottomSheetDialogFragment"
        android:name="com.fptplay.mobile.features.moments.chat.MomentCommentBottomSheetFragment"
        android:label="commentBottomSheetDialogFragment"
        tools:layout="@layout/fragment_moment_chat_bottom_sheet">
        <argument android:name="momentId"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="chapterId"
            android:defaultValue=""
            app:argType="string"/>
        <argument android:name="enable"
            app:argType="boolean"
            android:defaultValue="false"/>
        <argument android:name="userCom"
            app:argType="boolean"
            android:defaultValue="false"/>
        <argument android:name="showKeyboard"
            app:argType="boolean"
            android:defaultValue="true"/>
        <argument android:name="requiredMessage"
            app:argType="string"
            android:defaultValue=""/>
        <argument android:name="isLoginRequired"
            app:argType="boolean"
            android:defaultValue="false"/>
        <argument android:name="autoComment"
            app:argType="string"
            android:defaultValue="" />
        <argument android:name="parentIdForAutoComment"
            app:argType="string"
            android:defaultValue="" />
        <action
            android:id="@+id/action_global_moment_comment_bottom_sheet_to_arrange_option"
            app:destination="@+id/momentArrangeFragment">
        </action>
    </dialog>

    <action
        android:id="@+id/action_global_moment_to_comment_v2_fragment"
        app:destination="@+id/comment_dialog_v2_fragment"
        app:enterAnim="@anim/slide_up"
        app:popExitAnim="@anim/slide_down"/>
    <dialog
        android:id="@+id/comment_dialog_v2_fragment"
        android:name="com.fptplay.mobile.features.comment_v2.CommentV2DialogFragment"
        android:label="Comment V2 Dialog Fragment"
        tools:layout="@layout/comment_v2_dialog_fragment">
        <argument
            android:name="contentId"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="episodeId"
            app:argType="string"
            android:defaultValue=""/>
    </dialog>

    <dialog
        android:id="@+id/momentArrangeFragment"
        android:name="com.fptplay.mobile.features.moments.chat.MomentArrangeFragment"
        android:label="moment Arrange Fragment"
        tools:layout="@layout/livetv_choose_day_fragment">
    </dialog>
    <dialog
        android:id="@+id/momentPlaylistBottomSheetFragment"
        android:name="com.fptplay.mobile.features.moments.MomentPlaylistBottomSheetFragment"
        android:label="MomentPlaylistBottomSheetFragment"
        tools:layout="@layout/moment_playlist_bottom_sheet_fragment">
        <argument
            android:name="playlistId"
            app:argType="string" />
        <argument
            android:name="currPlaylistEpisodeId"
            app:argType="string" />
    </dialog>
</navigation>