{"formatVersion": "1.1", "component": {"group": "androidx.media3", "module": "media3-exoplayer-smoothstreaming", "version": "1.6.1.rc01", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.media3", "module": "media3-exoplayer", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-exoplayer-smoothstreaming-1.6.1.rc01.aar", "url": "media3-exoplayer-smoothstreaming-1.6.1.rc01.aar", "size": 61701, "sha512": "2efbed28176afc1b68ce1a01aba14e56ab0a04e6a4655a51be3f0fde72e0f024606288936bac44c9d7d7d9b19606dd30c13b5e1fa393ba5a555ae141d41dd110", "sha256": "bd59581737ae4bac3913c534a6ca0e2ff4f75c5a879cdd83cfaef3f9b22ef5f6", "sha1": "14b49b88a8fa8c15a7799729e9d9fa834cff0623", "md5": "25b95e35a6c4889a943586b323f5c515"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.6.0"}}, {"group": "androidx.media3", "module": "media3-exoplayer", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-exoplayer-smoothstreaming-1.6.1.rc01.aar", "url": "media3-exoplayer-smoothstreaming-1.6.1.rc01.aar", "size": 61701, "sha512": "2efbed28176afc1b68ce1a01aba14e56ab0a04e6a4655a51be3f0fde72e0f024606288936bac44c9d7d7d9b19606dd30c13b5e1fa393ba5a555ae141d41dd110", "sha256": "bd59581737ae4bac3913c534a6ca0e2ff4f75c5a879cdd83cfaef3f9b22ef5f6", "sha1": "14b49b88a8fa8c15a7799729e9d9fa834cff0623", "md5": "25b95e35a6c4889a943586b323f5c515"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "media3-exoplayer-smoothstreaming-1.6.1.rc01-sources.jar", "url": "media3-exoplayer-smoothstreaming-1.6.1.rc01-sources.jar", "size": 30614, "sha512": "e405b7869aa5e8599ff669811f82bb576a2a36dc74447a0437efa0dcd04c13474f42485a2f3b2480335e619433fd052d34ae1630b7754d95b56a13c161f3f4fe", "sha256": "674cdb3125197c28a03fa22baf04cf88ed853d942cb66a924127917c92d6df56", "sha1": "c0e8bfee615fb928304c4a69eaf49c0bf5a27c37", "md5": "17006274d84f255d1fbfc66bd986c557"}]}]}