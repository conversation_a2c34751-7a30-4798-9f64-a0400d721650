package com.fptplay.mobile.features.download.callback;

import static androidx.media3.exoplayer.util.Utils.IS_SIGMA_DRM;

import android.annotation.TargetApi;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import androidx.annotation.Nullable;

import androidx.media3.common.C;
import androidx.media3.common.util.UnstableApi;
import androidx.media3.datasource.DataSourceInputStream;
import androidx.media3.datasource.DataSpec;
import androidx.media3.datasource.HttpDataSource;
import androidx.media3.datasource.StatsDataSource;
import androidx.media3.exoplayer.drm.ExoMediaDrm.KeyRequest;
import androidx.media3.exoplayer.drm.ExoMediaDrm.ProvisionRequest;
import androidx.media3.exoplayer.drm.MediaDrmCallback;
import androidx.media3.exoplayer.drm.MediaDrmCallbackException;
import androidx.media3.common.util.Assertions;
import androidx.media3.common.util.Util;
import com.google.common.collect.ImmutableMap;
import com.sigma.packer.RequestInfo;
import com.sigma.packer.SigmaDrmPacker;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * A {@link MediaDrmCallback} that makes requests using {@link HttpDataSource} instances.
 */
@UnstableApi
@TargetApi(18)
/** A {@link MediaDrmCallback} that makes requests using {@link HttpDataSource} instances. */
public final class WidevineMediaDrmCallback implements MediaDrmCallback {

    private static final int MAX_MANUAL_REDIRECTS = 5;

    private final HttpDataSource.Factory dataSourceFactory;
    @Nullable
    private final String defaultLicenseUrl;
    private final boolean forceDefaultLicenseUrl;
    private final Map<String, String> keyRequestProperties;

    /**
     * @param defaultLicenseUrl The default license URL. Used for key requests that do not specify
     *     their own license URL. May be {@code null} if it's known that all key requests will specify
     *     their own URLs.
     * @param dataSourceFactory A factory from which to obtain {@link HttpDataSource} instances.
     */
    public WidevineMediaDrmCallback(
            @Nullable String defaultLicenseUrl, HttpDataSource.Factory dataSourceFactory) {
        this(defaultLicenseUrl, /* forceDefaultLicenseUrl= */ false, dataSourceFactory);
    }

    /**
     * @param defaultLicenseUrl The default license URL. Used for key requests that do not specify
     *     their own license URL, or for all key requests if {@code forceDefaultLicenseUrl} is set to
     *     true. May be {@code null} if {@code forceDefaultLicenseUrl} is {@code false} and if it's
     *     known that all key requests will specify their own URLs.
     * @param forceDefaultLicenseUrl Whether to force use of {@code defaultLicenseUrl} for key
     *     requests that include their own license URL.
     * @param dataSourceFactory A factory from which to obtain {@link HttpDataSource} instances.
     */
    public WidevineMediaDrmCallback(
            @Nullable String defaultLicenseUrl,
            boolean forceDefaultLicenseUrl,
            HttpDataSource.Factory dataSourceFactory) {
        Assertions.checkArgument(!(forceDefaultLicenseUrl && TextUtils.isEmpty(defaultLicenseUrl)));
        this.dataSourceFactory = dataSourceFactory;
        this.defaultLicenseUrl = defaultLicenseUrl;
        this.forceDefaultLicenseUrl = forceDefaultLicenseUrl;
        this.keyRequestProperties = new HashMap<>();
    }

    /**
     * Sets a header for key requests made by the callback.
     *
     * @param name The name of the header field.
     * @param value The value of the field.
     */
    public void setKeyRequestProperty(String name, String value) {
        Assertions.checkNotNull(name);
        Assertions.checkNotNull(value);
        synchronized (keyRequestProperties) {
            keyRequestProperties.put(name, value);
        }
    }

    /**
     * Clears a header for key requests made by the callback.
     *
     * @param name The name of the header field.
     */
    public void clearKeyRequestProperty(String name) {
        Assertions.checkNotNull(name);
        synchronized (keyRequestProperties) {
            keyRequestProperties.remove(name);
        }
    }

    /** Clears all headers for key requests made by the callback. */
    public void clearAllKeyRequestProperties() {
        synchronized (keyRequestProperties) {
            keyRequestProperties.clear();
        }
    }

    @Override
    public byte[] executeProvisionRequest(UUID uuid, ProvisionRequest request)
            throws MediaDrmCallbackException {
        String url =
                request.getDefaultUrl() + "&signedRequest=" + Util.fromUtf8Bytes(request.getData());
        return executePost(
                dataSourceFactory,
                url,
                /* httpBody= */ null,
                /* requestProperties= */ Collections.emptyMap());
    }

    @Override
    public byte[] executeKeyRequest(UUID uuid, KeyRequest request) throws MediaDrmCallbackException {
        String url = request.getLicenseServerUrl();
        if (forceDefaultLicenseUrl || TextUtils.isEmpty(url)) {
            url = defaultLicenseUrl;
        }
        if (TextUtils.isEmpty(url)) {
            throw new MediaDrmCallbackException(
                    new DataSpec.Builder().setUri(Uri.EMPTY).build(),
                    Uri.EMPTY,
                    /* responseHeaders= */ ImmutableMap.of(),
                    /* bytesLoaded= */ 0,
                    /* cause= */ new IllegalStateException("No license URL"));
        }
        Map<String, String> requestProperties = new HashMap<>();
        // Add standard request properties for supported schemes.
        String contentType =
                C.PLAYREADY_UUID.equals(uuid)
                        ? "text/xml"
                        : (C.CLEARKEY_UUID.equals(uuid) ? "application/json" : "application/octet-stream");
        requestProperties.put("Content-Type", contentType);
        if (C.PLAYREADY_UUID.equals(uuid)) {
            requestProperties.put(
                    "SOAPAction", "http://schemas.microsoft.com/DRM/2007/03/protocols/AcquireLicense");
        }
        // Add additional request properties.
        synchronized (keyRequestProperties) {
            // Check and edit RequestProperties
            if (keyRequestProperties.containsKey("sigma-custom-data")){
                String jsonObjectStr = keyRequestProperties.get("sigma-custom-data");
                if (jsonObjectStr != null) {

                    JSONObject originalData;
                    try {
                        originalData = new JSONObject(jsonObjectStr);
                    }catch (JSONException ex) {
                        throw new RuntimeException("Error while creating object", ex);
                    }
                    try {
                        RequestInfo requestInfo = SigmaDrmPacker.requestInfo(request.getData());
                        originalData.put("reqId", requestInfo.requestId);
                        originalData.put("deviceInfo", requestInfo.deviceInfo);
                        keyRequestProperties.put("custom-data", Base64.encodeToString(originalData.toString().getBytes(), Base64.NO_WRAP));
                    } catch (JSONException e) {
                        throw new RuntimeException("Error while adding key properties", e);
                    }

                }
            }
            // Add to Request Properties
            requestProperties.putAll(keyRequestProperties);
        }
        byte[] bytes = executePost(dataSourceFactory, url, request.getData(), requestProperties);
        // xBADx
        if (IS_SIGMA_DRM) {
            try {
                JSONObject jsonObject = new JSONObject(new String(bytes));
                // If you don't use feature license encrypt, please comment 3 lines below
                String licenseEncrypted = jsonObject.getString("license");
                return Base64.decode(licenseEncrypted, Base64.DEFAULT);
                // If you don't use feature license encrypt, please uncomment line below
                // return Base64.decode(jsonObject.getString("license"), Base64.DEFAULT);
            } catch (JSONException e) {
                throw new RuntimeException("Error while parsing response", e);
            }
        }else{
            return bytes;
        }
    }

//    @Override
//    public byte[] executeKeyRequest(UUID uuid, KeyRequest request) throws MediaDrmCallbackException {
//        try {
//            String url = request.getLicenseServerUrl();
//            if (forceDefaultLicenseUrl || TextUtils.isEmpty(url)) {
//                url = defaultLicenseUrl;
//            }
//            Map<String, String> requestProperties = new HashMap<>();
//            // Add standard request properties for supported schemes.
//            String contentType = "application/octet-stream";
//            requestProperties.put("Content-Type", contentType);
//            requestProperties.put("custom-data", getCustomData(request));
//
//            // Add additional request properties.
//            synchronized (keyRequestProperties) {
//                requestProperties.putAll(keyRequestProperties);
//            }
//            String base64Encoded = Base64.encodeToString(request.getData(), Base64.NO_WRAP);
//            byte[] bytes = executePost(dataSourceFactory, url, request.getData(), requestProperties);
//            JSONObject jsonObject = new JSONObject(new String(bytes));
//            String licenseEncrypted = jsonObject.getString("license");
//            return Base64.decode(licenseEncrypted, Base64.DEFAULT);
//        } catch (JSONException e) {
//            throw new RuntimeException("Error while parsing response", e);
//        } catch (Exception e) {
//            throw new RuntimeException("Error while parsing response", e);
//        }
//    }

    private static byte[] executePost(
            HttpDataSource.Factory dataSourceFactory,
            String url,
            @Nullable byte[] httpBody,
            Map<String, String> requestProperties)
            throws MediaDrmCallbackException {
        StatsDataSource dataSource = new StatsDataSource(dataSourceFactory.createDataSource());
        int manualRedirectCount = 0;
        DataSpec dataSpec =
                new DataSpec.Builder()
                        .setUri(url)
                        .setHttpRequestHeaders(requestProperties)
                        .setHttpMethod(DataSpec.HTTP_METHOD_POST)
                        .setHttpBody(httpBody)
                        .setFlags(DataSpec.FLAG_ALLOW_GZIP)
                        .build();
        DataSpec originalDataSpec = dataSpec;
        try {
            while (true) {
                DataSourceInputStream inputStream = new DataSourceInputStream(dataSource, dataSpec);
                try {
                    return Util.toByteArray(inputStream);
                } catch (HttpDataSource.InvalidResponseCodeException e) {
                    @Nullable String redirectUrl = getRedirectUrl(e, manualRedirectCount);
                    if (redirectUrl == null) {
                        throw e;
                    }
                    manualRedirectCount++;
                    dataSpec = dataSpec.buildUpon().setUri(redirectUrl).build();
                } finally {
                    Util.closeQuietly(inputStream);
                }
            }
        } catch (Exception e) {
            throw new MediaDrmCallbackException(
                    originalDataSpec,
                    Assertions.checkNotNull(dataSource.getLastOpenedUri()),
                    dataSource.getResponseHeaders(),
                    dataSource.getBytesRead(),
                    /* cause= */ e);
        }
    }

    @Nullable
    private static String getRedirectUrl(
            HttpDataSource.InvalidResponseCodeException exception, int manualRedirectCount) {
        // For POST requests, the underlying network stack will not normally follow 307 or 308
        // redirects automatically. Do so manually here.
        boolean manuallyRedirect =
                (exception.responseCode == 307 || exception.responseCode == 308)
                        && manualRedirectCount < MAX_MANUAL_REDIRECTS;
        if (!manuallyRedirect) {
            return null;
        }
        Map<String, List<String>> headerFields = exception.headerFields;
        if (headerFields != null) {
            @Nullable List<String> locationHeaders = headerFields.get("Location");
            if (locationHeaders != null && !locationHeaders.isEmpty()) {
                return locationHeaders.get(0);
            }
        }
        return null;
    }

    private String getCustomData(KeyRequest keyRequest) throws Exception {
        JSONObject customData = new JSONObject();
        customData.put("merchantId", "sctv");
        customData.put("appId", "RedTV");
        customData.put("userId", "exoplayer_userId_12345");
        customData.put("sessionId", "exoplayer_sessionId_12345");

        // If you user license encrypt feature then please uncomment 3 line below
        RequestInfo requestInfo = SigmaDrmPacker.requestInfo(keyRequest.getData());
        customData.put("reqId", requestInfo.requestId);
        customData.put("deviceInfo", requestInfo.deviceInfo);

        String customHeader = Base64.encodeToString(customData.toString().getBytes(), Base64.NO_WRAP);
        Log.e("Custom Data: ", customHeader);
        return customHeader;
    }
}
