<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:fillViewport="true"
    android:background="@android:color/black">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.fptplay.mobile.common.ui.view.CenteredTitleToolbar
            android:id="@+id/toolbar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingStart="0dp"
            android:paddingEnd="@dimen/_6ssp"
            android:theme="@style/Account.Toolbar.MenuTheme"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:navigationIcon="@drawable/ic_arrow_left"
            app:title="@string/account_info" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_user_info"
            android:layout_width="@dimen/delete_account_otp_layout_width_"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/account_info_padding_horizontal"
            android:layout_marginHorizontal="@dimen/account_info_padding_horizontal"
            android:background="@drawable/account_user_info_block_bg"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:visibility="visible"
            >
            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/iv_avatar"
                android:layout_width="@dimen/account_info_avatar_size"
                android:layout_height="@dimen/account_info_avatar_size"
                android:layout_marginTop="@dimen/account_info_padding_top"
                android:scaleType="centerCrop"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginStart="@dimen/account_info_padding_horizontal"
                app:shapeAppearanceOverlay="@style/CircleImageViewStyle" />

            <TextView
                android:id="@+id/tv_user_name"
                style="@style/BaseTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/account_info_margin_start"
                android:textSize="@dimen/account_info_user_name_text_size"
                android:fontFamily="@font/sf_pro_display_bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_avatar"
                app:layout_constraintTop_toTopOf="@id/iv_avatar"
                app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
                android:layout_marginEnd="@dimen/account_info_padding_horizontal"
                tools:text="FPT Play Shop" />
            <View
                android:id="@+id/divider"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_7sdp"
                android:layout_marginHorizontal="@dimen/account_info_padding_horizontal"
                android:background="#1AFFFFFF"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_avatar"
                />


            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/barrierLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:barrierDirection="right"
                app:constraint_referenced_ids="tvUid,tvContract,tvPhoneNumber" />
            <TextView
                android:id="@+id/tvUid"
                style="@style/BaseTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/account_info_padding_horizontal"
                android:layout_marginHorizontal="@dimen/account_info_padding_horizontal"
                android:text="@string/account_uuid_label"
                android:textColor="@color/white_60"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider" />

            <TextView
                android:id="@+id/tvUidValue"
                style="@style/BaseTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/account_info_padding_horizontal"
                app:layout_constraintBottom_toBottomOf="@id/tvUid"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/barrierLabel"
                android:gravity="end"
                app:layout_constraintTop_toTopOf="@id/tvUid"
                tools:text="0118502"/>
            <TextView
                android:id="@+id/tvContract"
                style="@style/BaseTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/account_info_padding_horizontal"
                android:layout_marginHorizontal="@dimen/account_info_padding_horizontal"
                android:text="@string/account_contract_number_label"
                android:textColor="@color/white_60"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvUid" />

            <TextView
                android:id="@+id/tvContractValue"
                style="@style/BaseTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/account_info_padding_horizontal"
                app:layout_constraintBottom_toBottomOf="@id/tvContract"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/barrierLabel"
                android:gravity="end"
                app:layout_constraintTop_toTopOf="@id/tvContract"
                tools:text="SGH056523"/>
            <TextView
                android:id="@+id/tvPhoneNumber"
                style="@style/BaseTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/account_info_padding_horizontal"
                android:layout_marginHorizontal="@dimen/account_info_padding_horizontal"
                android:layout_marginBottom="@dimen/account_info_padding_horizontal"
                android:text="@string/account_phone_number_label"
                android:textColor="@color/white_60"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvContract"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <TextView
                android:id="@+id/tvPhoneNumberValue"
                style="@style/BaseTextView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/account_info_padding_horizontal"
                app:layout_constraintBottom_toBottomOf="@id/tvPhoneNumber"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/barrierLabel"
                android:gravity="end"
                app:layout_constraintTop_toTopOf="@id/tvPhoneNumber"
                tools:text="**********"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="@dimen/delete_account_otp_layout_width_"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/account_info_padding_horizontal_group"
            android:layout_marginHorizontal="@dimen/account_info_padding_horizontal"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_user_info">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_user_email"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/account_user_info_block_bg"
                app:layout_constraintTop_toBottomOf="@id/layout_user_info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <ImageView
                    android:id="@+id/iv_email"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/account_change_password_ic_padding"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_account_mail"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_email"
                    style="@style/BaseTextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingRight="@dimen/_11sdp"
                    android:text="@string/email"
                    android:fontFamily="@font/sf_pro_display_regular"
                    app:layout_constraintStart_toEndOf="@id/iv_email"
                    app:layout_constraintTop_toTopOf="@id/iv_email"
                    app:layout_constraintBottom_toBottomOf="@id/iv_email"
                    android:includeFontPadding="false"/>

                <TextView
                    android:id="@+id/tv_email_edit"
                    style="@style/BaseTextView"
                    android:layout_width="wrap_content"
                    android:maxWidth="@dimen/account_info_email_content_max_width"
                    android:layout_height="wrap_content"
                    android:scaleType="fitStart"
                    android:textColor="@color/white_60"
                    app:layout_constraintBottom_toBottomOf="@id/tv_email"
                    app:layout_constraintEnd_toStartOf="@id/iv_edit_email"
                    app:layout_constraintStart_toEndOf="@id/tv_email"
                    app:layout_constraintTop_toTopOf="@id/tv_email"
                    app:layout_constraintHorizontal_bias="1"
                    android:includeFontPadding="false"
                    />

                <ImageView
                    android:id="@+id/iv_edit_email"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="@dimen/account_change_password_navigation_padding"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_arrow_right"
                    app:layout_constraintBottom_toBottomOf="@id/tv_email"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tv_email" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_user_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/account_info_padding_horizontal"
                android:background="@drawable/account_user_info_block_bg"
                app:layout_constraintTop_toBottomOf="@id/layout_user_info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <ImageView
                    android:id="@+id/ivPassword"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/account_change_password_ic_padding"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_account_password"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvPassword"
                    style="@style/BaseTextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingRight="@dimen/_11sdp"
                    android:text="@string/account_password_label"
                    android:fontFamily="@font/sf_pro_display_regular"
                    app:layout_constraintStart_toEndOf="@id/ivPassword"
                    app:layout_constraintTop_toTopOf="@id/ivPassword"
                    app:layout_constraintBottom_toBottomOf="@id/ivPassword"
                    android:includeFontPadding="false"/>

                <TextView
                    android:id="@+id/tvPasswordEdit"
                    style="@style/BaseTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:scaleType="fitStart"
                    app:layout_constraintBottom_toBottomOf="@id/tvPassword"
                    app:layout_constraintEnd_toStartOf="@id/iv_edit_password"
                    app:layout_constraintStart_toEndOf="@id/tvPassword"
                    app:layout_constraintTop_toTopOf="@id/tvPassword"
                    app:layout_constraintHorizontal_bias="1"
                    android:includeFontPadding="false"
                    android:text="@string/account_password_edit_label"
                    />

                <ImageView
                    android:id="@+id/iv_edit_password"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="@dimen/account_change_password_navigation_padding"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_arrow_right"
                    app:layout_constraintBottom_toBottomOf="@id/tvPassword"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvPassword" />

            </androidx.constraintlayout.widget.ConstraintLayout>


        </LinearLayout>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_delete"
            android:layout_width="@dimen/delete_account_otp_layout_width_"
            android:layout_height="@dimen/account_change_pass_button_height"
            style="@style/BaseTextView"
            android:fontFamily="@font/sf_pro_display_bold"
            android:text="@string/delete_account"
            android:textAllCaps="false"
            android:textSize="@dimen/account_change_pass_button_text_size"
            android:textColor="@color/white_87"
            android:layout_marginHorizontal="@dimen/_14sdp"
            android:layout_marginTop="@dimen/_30sdp"
            android:layout_marginBottom="@dimen/_30sdp"
            android:background="@drawable/account_rounded_btn_background_disable"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            />
        <include
            android:id="@+id/pb_loading"
            layout="@layout/all_view_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8sdp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>