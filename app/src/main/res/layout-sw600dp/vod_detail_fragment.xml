<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <fragment
        android:id="@+id/f_player"
        android:name="com.fptplay.mobile.vod.VodPlayerFragment"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="16:9"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/scrollViewVodInfo"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#000"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/f_player"
        app:layout_constraintStart_toStartOf="@id/f_player"
        app:layout_constraintTop_toBottomOf="@id/f_player">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/flVodInfoContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <include
                android:id="@+id/layout_buy_package_detail"
                layout="@layout/layout_buy_package_detail"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/clVodInfo"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clVodInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/_10sdp"
                app:layout_constraintTop_toBottomOf="@id/layout_buy_package_detail">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_title_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#12FFFFFF"
                    android:paddingVertical="@dimen/_7sdp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tvTitleTablet"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_8sdp"
                        android:layout_marginEnd="@dimen/_21sdp"
                        android:ellipsize="end"
                        android:maxLines="2"
                        android:textColor="#DEFFFFFF"
                        android:textSize="@dimen/_8ssp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.google.android.flexbox.FlexboxLayout
                        android:id="@+id/llRatingGroupTablet"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_8sdp"
                        android:layout_marginTop="@dimen/_4sdp"
                        android:layout_marginBottom="@dimen/_7sdp"
                        android:orientation="horizontal"
                        app:flexWrap="wrap"
                        app:layout_constraintStart_toStartOf="@id/tvTitleTablet"
                        app:layout_constraintTop_toBottomOf="@id/tvTitleTablet" />

                    <TextView
                        android:id="@+id/tvVideoInfoTablet"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_8sdp"
                        android:layout_marginTop="@dimen/_3sdp"
                        android:layout_marginEnd="@dimen/_8sdp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textAlignment="textStart"
                        android:textSize="@dimen/_6ssp"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/llRatingGroupTablet" />


                    <TextView
                        android:id="@+id/tv_age_restriction_tablet"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/_8sdp"
                        android:layout_marginTop="@dimen/_3sdp"
                        android:ellipsize="end"
                        android:maxLines="3"
                        android:textAlignment="textStart"
                        android:textSize="@dimen/_6ssp"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvVideoInfoTablet" />

                    <TextView
                        android:id="@+id/tv_short_description"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_8sdp"
                        android:fontFamily="@font/sf_pro_display_medium"
                        android:maxLines="2"
                        android:textSize="@dimen/_7ssp"
                        app:layout_constraintEnd_toStartOf="@id/vDownTitleTablet"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_age_restriction_tablet" />


                    <androidx.appcompat.widget.AppCompatImageButton
                        android:id="@+id/vDownTitleTablet"
                        android:layout_width="@dimen/_18sdp"
                        android:layout_height="@dimen/_18sdp"
                        android:layout_marginEnd="@dimen/_2sdp"
                        android:background="@drawable/all_background_circle_ripple"
                        android:contentDescription="@string/talkback_icon_expand"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tvTitleTablet"
                        app:srcCompat="@drawable/ic_arrow_down" />


                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:id="@+id/ll_button_func_tablet"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_38sdp"
                    android:layout_marginTop="@dimen/_7sdp"
                    android:gravity="start"
                    android:orientation="horizontal"
                    android:paddingStart="@dimen/_8sdp"
                    app:layout_constraintTop_toBottomOf="@id/cl_title_info">

                    <LinearLayout
                        android:id="@+id/llFollowTablet"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingHorizontal="@dimen/_3ssp">

                        <ImageView
                            android:id="@+id/ivFollowTablet"
                            android:layout_width="@dimen/_8sdp"
                            android:layout_height="@dimen/_8sdp"
                            android:layout_marginHorizontal="@dimen/_10sdp"
                            android:src="@drawable/vod_detail_follow_selector" />

                        <TextView
                            android:id="@+id/tvFollowTablet"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_5sdp"
                            android:gravity="center_horizontal"
                            android:maxLines="1"
                            android:text="@string/vod_unfollow"
                            android:textSize="@dimen/_5ssp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llShareTablet"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingHorizontal="@dimen/_3ssp">

                        <ImageView
                            android:id="@+id/ivShareTablet"
                            android:layout_width="@dimen/_8sdp"
                            android:layout_height="@dimen/_8sdp"
                            android:layout_marginHorizontal="@dimen/_10sdp"
                            android:src="@drawable/ic_share" />

                        <TextView
                            android:id="@+id/tvShareTablet"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_5sdp"
                            android:text="@string/share"
                            android:textSize="@dimen/_5ssp" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llDownloadTablet"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingHorizontal="@dimen/_3ssp"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <ImageView
                            android:id="@+id/ivDownloadTablet"
                            android:layout_width="@dimen/_8sdp"
                            android:layout_height="@dimen/_8sdp"
                            android:layout_marginHorizontal="@dimen/_10sdp"
                            android:src="@drawable/ic_download" />

                        <com.airbnb.lottie.LottieAnimationView
                            android:id="@+id/animTabletDownloading"
                            android:layout_width="@dimen/_8sdp"
                            android:layout_height="@dimen/_8sdp"
                            android:layout_marginHorizontal="@dimen/_10sdp"
                            android:scaleType="fitXY"
                            android:visibility="gone"
                            app:lottie_autoPlay="true"
                            app:lottie_loop="true"
                            app:lottie_rawRes="@raw/anmation_icon_download" />

                        <TextView
                            android:id="@+id/tvDownloadTablet"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_5sdp"
                            android:text="@string/vod_download"
                            android:textSize="@dimen/_5ssp" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llRankGameTablet"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingHorizontal="@dimen/_3ssp"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <ImageView
                            android:id="@+id/ivRankGame"
                            android:layout_width="@dimen/_8sdp"
                            android:layout_height="@dimen/_8sdp"
                            android:layout_marginHorizontal="@dimen/_10sdp"
                            android:src="@drawable/ic_rank_game" />

                        <TextView
                            android:id="@+id/tvRankGame"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_5sdp"
                            android:text="@string/vod_rank_game"
                            android:textSize="@dimen/_5ssp" />

                    </LinearLayout>

                </LinearLayout>

                <!--                <View-->
                <!--                    android:id="@+id/view4Tablet"-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="@dimen/_1sdp"-->
                <!--                    android:background="#12FFFFFF"-->
                <!--                    app:layout_constraintStart_toStartOf="parent"-->
                <!--                    app:layout_constraintTop_toBottomOf="@id/ll_button_func_tablet" />-->

                <com.fptplay.mobile.vod.views.VodCommentTypeView
                    android:id="@+id/v_comment_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="@dimen/_12sdp"
                    android:paddingBottom="@dimen/_17sdp"
                    app:layout_constraintTop_toBottomOf="@id/ll_button_func_tablet" />

                <!--                <androidx.constraintlayout.widget.ConstraintLayout-->
                <!--                    android:id="@+id/tl_comment"-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:paddingTop="@dimen/_7sdp"-->
                <!--                    android:paddingBottom="@dimen/_9sdp"-->
                <!--                    app:layout_constraintTop_toBottomOf="@id/view4Tablet">-->

                <!--                    <TextView-->
                <!--                        android:id="@+id/tvTitleComTablet"-->
                <!--                        android:layout_width="wrap_content"-->
                <!--                        android:layout_height="wrap_content"-->
                <!--                        android:layout_marginStart="@dimen/_8sdp"-->
                <!--                        android:ellipsize="end"-->
                <!--                        android:maxLines="1"-->
                <!--                        android:textColor="#DEFFFFFF"-->
                <!--                        android:textSize="@dimen/_7ssp"-->
                <!--                        android:textStyle="bold"-->
                <!--                        app:layout_constraintStart_toStartOf="parent"-->
                <!--                        app:layout_constraintTop_toTopOf="parent" />-->

                <!--                    <TextView-->
                <!--                        android:id="@+id/tvEnableTablet"-->
                <!--                        android:layout_width="match_parent"-->
                <!--                        android:layout_height="@dimen/_12sdp"-->
                <!--                        android:layout_marginHorizontal="@dimen/_8sdp"-->
                <!--                        android:layout_marginTop="@dimen/_4sdp"-->
                <!--                        android:background="@drawable/vod_detail_comment_enable_bg"-->
                <!--                        android:gravity="center"-->
                <!--                        android:maxLines="1"-->
                <!--                        android:text="@string/vod_enable_comment"-->
                <!--                        android:textColor="#61FFFFFF"-->
                <!--                        android:textSize="@dimen/_6ssp"-->
                <!--                        android:visibility="gone"-->
                <!--                        app:layout_constraintEnd_toEndOf="parent"-->
                <!--                        app:layout_constraintStart_toStartOf="parent"-->
                <!--                        app:layout_constraintTop_toBottomOf="@id/tvTitleComTablet" />-->

                <!--                    <androidx.appcompat.widget.AppCompatImageButton-->
                <!--                        android:id="@+id/vDownCommentTablet"-->
                <!--                        android:layout_width="@dimen/_18sdp"-->
                <!--                        android:layout_height="@dimen/_18sdp"-->
                <!--                        android:layout_marginEnd="@dimen/_2sdp"-->
                <!--                        android:background="@drawable/all_background_circle_ripple"-->
                <!--                        app:layout_constraintBottom_toBottomOf="@id/tvTitleComTablet"-->
                <!--                        app:layout_constraintEnd_toEndOf="parent"-->
                <!--                        app:layout_constraintTop_toTopOf="@id/tvTitleComTablet"-->
                <!--                        app:srcCompat="@drawable/ic_arrow_down"-->
                <!--                        android:contentDescription="@string/talkback_icon_expand" />-->

                <!--                    <FrameLayout-->
                <!--                        android:id="@+id/chat_box_container_tablet"-->
                <!--                        android:layout_width="match_parent"-->
                <!--                        android:layout_height="wrap_content"-->
                <!--                        android:layout_marginTop="@dimen/_5sdp"-->
                <!--                        android:visibility="visible"-->
                <!--                        app:layout_constraintStart_toStartOf="parent"-->
                <!--                        app:layout_constraintTop_toBottomOf="@id/tvEnableTablet">-->

                <!--                        <include-->
                <!--                            android:id="@+id/chat_box_tablet"-->
                <!--                            layout="@layout/all_view_chat_box" />-->
                <!--                    </FrameLayout>-->

                <!--                    <com.google.android.material.imageview.ShapeableImageView-->
                <!--                        android:id="@+id/ivAvataTablet"-->
                <!--                        android:layout_width="@dimen/_16sdp"-->
                <!--                        android:layout_height="@dimen/_16sdp"-->
                <!--                        android:layout_marginStart="@dimen/_8sdp"-->
                <!--                        android:layout_marginTop="@dimen/_5sdp"-->
                <!--                        android:visibility="gone"-->
                <!--                        app:layout_constraintStart_toStartOf="parent"-->
                <!--                        app:layout_constraintTop_toBottomOf="@id/chat_box_container_tablet"-->
                <!--                        app:shapeAppearanceOverlay="@style/CircleImageViewStyle" />-->

                <!--                    <androidx.constraintlayout.widget.ConstraintLayout-->
                <!--                        android:id="@+id/clCommentTablet"-->
                <!--                        android:layout_width="0dp"-->
                <!--                        android:layout_height="wrap_content"-->
                <!--                        android:layout_marginStart="@dimen/_3sdp"-->
                <!--                        android:layout_marginTop="@dimen/_5sdp"-->
                <!--                        android:layout_marginEnd="@dimen/_8sdp"-->
                <!--                        android:background="@drawable/vod_detail_first_comment_bg"-->
                <!--                        android:paddingVertical="@dimen/_3sdp"-->
                <!--                        android:visibility="gone"-->
                <!--                        app:layout_constraintEnd_toEndOf="parent"-->
                <!--                        app:layout_constraintStart_toEndOf="@id/ivAvataTablet"-->
                <!--                        app:layout_constraintTop_toBottomOf="@id/chat_box_container_tablet">-->

                <!--                        <ImageView-->
                <!--                            android:id="@+id/iv_pin_tablet"-->
                <!--                            android:layout_width="@dimen/_6sdp"-->
                <!--                            android:layout_height="@dimen/_8sdp"-->
                <!--                            android:layout_marginStart="@dimen/_3sdp"-->
                <!--                            android:layout_marginEnd="@dimen/_6sdp"-->
                <!--                            android:src="@drawable/ic_vector__pin"-->
                <!--                            android:visibility="gone"-->
                <!--                            app:layout_constraintEnd_toEndOf="parent"-->
                <!--                            app:layout_constraintTop_toTopOf="parent" />-->

                <!--                        <TextView-->
                <!--                            android:id="@+id/tvNameComTablet"-->
                <!--                            android:layout_width="wrap_content"-->
                <!--                            android:layout_height="wrap_content"-->
                <!--                            android:layout_marginStart="@dimen/_9sdp"-->
                <!--                            android:maxLines="1"-->
                <!--                            android:textSize="@dimen/_6ssp"-->
                <!--                            app:layout_constraintStart_toStartOf="parent"-->
                <!--                            app:layout_constraintTop_toTopOf="parent" />-->

                <!--                        <TextView-->
                <!--                            android:id="@+id/tvCircleTablet"-->
                <!--                            android:layout_width="wrap_content"-->
                <!--                            android:layout_height="wrap_content"-->
                <!--                            android:layout_marginStart="@dimen/_3sdp"-->
                <!--                            android:maxLines="1"-->
                <!--                            android:text="•"-->
                <!--                            android:textColor="#61FFFFFF"-->
                <!--                            android:textSize="@dimen/_4ssp"-->
                <!--                            app:layout_constraintBottom_toBottomOf="@id/tvNameComTablet"-->
                <!--                            app:layout_constraintStart_toEndOf="@+id/tvNameComTablet"-->
                <!--                            app:layout_constraintTop_toTopOf="@id/tvNameComTablet" />-->

                <!--                        <TextView-->
                <!--                            android:id="@+id/tvDayComTablet"-->
                <!--                            android:layout_width="wrap_content"-->
                <!--                            android:layout_height="wrap_content"-->
                <!--                            android:layout_marginStart="@dimen/_3sdp"-->
                <!--                            android:maxLines="1"-->
                <!--                            android:textColor="#61FFFFFF"-->
                <!--                            android:textSize="@dimen/_6ssp"-->
                <!--                            app:layout_constraintBottom_toBottomOf="@id/tvNameComTablet"-->
                <!--                            app:layout_constraintStart_toEndOf="@id/tvCircleTablet"-->
                <!--                            app:layout_constraintTop_toTopOf="@id/tvNameComTablet" />-->

                <!--                        <TextView-->
                <!--                            android:id="@+id/tvCommentTablet"-->
                <!--                            android:layout_width="0dp"-->
                <!--                            android:layout_height="wrap_content"-->
                <!--                            android:layout_marginHorizontal="@dimen/_9sdp"-->
                <!--                            android:layout_marginTop="@dimen/_1sdp"-->
                <!--                            android:ellipsize="end"-->
                <!--                            android:maxLines="2"-->
                <!--                            android:textColor="#99FFFFFF"-->
                <!--                            android:textSize="@dimen/_7ssp"-->
                <!--                            app:layout_constraintBottom_toBottomOf="parent"-->
                <!--                            app:layout_constraintEnd_toEndOf="parent"-->
                <!--                            app:layout_constraintStart_toStartOf="parent"-->
                <!--                            app:layout_constraintTop_toBottomOf="@id/tvNameComTablet" />-->

                <!--                    </androidx.constraintlayout.widget.ConstraintLayout>-->
                <!--                </androidx.constraintlayout.widget.ConstraintLayout>-->

                <TextView
                    android:id="@+id/tv_moment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_9sdp"
                    android:fontFamily="@font/sf_pro_display_bold"
                    android:textColor="#DEFFFFFF"
                    android:textSize="@dimen/_7ssp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/v_comment_type" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rcv_moment"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_9sdp"
                    android:layout_marginTop="@dimen/_6sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_moment" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/gr_moment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="tv_moment, rcv_moment" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <FrameLayout
                android:id="@+id/flVodInfoOverlay"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@color/transparent"
                android:clickable="true"
                android:focusable="true"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>


    </ScrollView>

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/nav_host_fragment"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/f_player"
        app:navGraph="@navigation/nav_vod_child" />

    <ViewStub
        android:id="@+id/vs_omni_pop_up_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:inflatedId="@+id/vs_omni_pop_up_view"
        android:layout="@layout/ads_interactive_omni_product_view_layout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/f_player" />

    <ViewStub
        android:id="@+id/vs_game_emoji_popup_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:inflatedId="@+id/vs_game_emoji_popup_view"
        android:layout="@layout/game_emoji_layout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/f_player" />

    <include
        android:id="@+id/pb_loading"
        layout="@layout/download_view_loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <!--    <com.fptplay.mobile.features.game_emoji.view.GameEmojiView-->
    <!--        android:id="@+id/game_emoji_popup_view"-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="0dp"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintTop_toBottomOf="@+id/f_player"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        />-->

    <com.fptplay.mobile.common.ui.view.PlayerVodSeekbarView
        android:id="@+id/player_custom_seekbar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/player_timeline_progress_bar_height"
        app:layout_constraintBottom_toBottomOf="@id/f_player"
        app:layout_constraintEnd_toEndOf="@id/f_player"
        app:layout_constraintStart_toStartOf="@id/f_player"
        app:layout_constraintTop_toBottomOf="@id/f_player" />

</androidx.constraintlayout.widget.ConstraintLayout>