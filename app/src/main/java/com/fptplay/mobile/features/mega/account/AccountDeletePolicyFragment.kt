package com.fptplay.mobile.features.mega.account

import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.CheckBox
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import com.fptplay.mobile.NavAccountInfoDirections
import com.fptplay.mobile.NavAccountOtpActionDirections
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.databinding.AccountDeletePolicyFragmentBinding
import com.fptplay.mobile.features.about.AboutViewModel
import com.fptplay.mobile.features.mega.MegaViewModel
import com.fptplay.mobile.features.mega.account.model.AccountOtpType
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class AccountDeletePolicyFragment : BaseFragment<MegaViewModel.MegaState, MegaViewModel.MegaIntent>() {
    companion object {
        const val TIME_COUNT_DOWN  =30
    }
    override val handleBackPressed = true
    override val viewModel by activityViewModels<MegaViewModel>()
    private var _binding: AccountDeletePolicyFragmentBinding? = null
    private val binding get() = _binding!!
    private var countDownTimer: CountDownTimer? = null
    private var isDelete: Boolean = false
    override val hasEdgeToEdge = true
    @Inject
    lateinit var sharedPreferences: SharedPreferences
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = AccountDeletePolicyFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }
    override fun onDestroyView() {
        isDelete = false
        countDownTimer?.cancel()
        super.onDestroyView()
        _binding = null
    }
    override fun bindComponent() {
        binding.toolbar.setTitleTextColor(
            ContextCompat.getColor(
                requireActivity(),
                R.color.account_title
            )
        )
        updateDeleteButtonState(binding.cbConfirm.isChecked && isDelete)
        binding.webView.apply {
            settings.javaScriptEnabled = true
            webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView, url: String) {
                    if(_binding != null) {
                        binding.webView.loadUrl(
                            "javascript:document.body.style.setProperty(\"color\", \"#ABABAB\")"
                        )
                    }
                }
            }
            setBackgroundColor(Color.parseColor("#12FFFFFF"))
            val webSettings: WebSettings = settings
            webSettings.textZoom =  (webSettings.textZoom *0.80).toInt()
        }
    }
    override fun initData() {
        viewModel.dispatchIntent(MegaViewModel.MegaIntent.GetDeletePolicyAccount(pageName = AboutViewModel.DELETE_TYPE))
    }

    override fun backHandler() {
        Timber.tag("tam-multiprofile").d("${this.javaClass.simpleName} backHandler ${viewModel.getUserDeleteDataV2()?.userHasPackage ==true}")
        if (viewModel.getUserDeleteDataV2()?.userHasPackage == true){
            findNavController().navigateSafe(NavAccountOtpActionDirections.actionGlobalToDeleteAccountPackageUserFragment())
        }
        else{
            navigateToAccountInfo()
        }
    }
    override fun bindEvent() {
        binding.toolbar.setNavigationOnClickListener {
            backHandler()
        }
        binding.cbConfirm.setOnClickListener {
            onCheckboxClicked(it)
        }
        binding.btnCancel.setOnClickListener {
            navigateToAccountInfo()
        }
        binding.btnDeleteAccount.setOnClickListener {
            findNavController().navigateSafe(NavAccountOtpActionDirections.actionGlobalAccountToVerityAccountOtpV2Fragment(
                otpType = AccountOtpType.VerifyOtpDeleteAccount,
                verifyToken = viewModel.getUserDeleteDataV2()?.verifyToken ?: ""

            ))
        }
    }
    override fun bindData() {
        startCountdown(TIME_COUNT_DOWN)
    }

    private fun onCheckboxClicked(view : View) {
        if (view is CheckBox) {
            when (view.id) {
                R.id.cb_confirm -> {
                    val colorEnable = if (view.isChecked) "#DDFFFFFF" else "#61FFFFFF"
                    binding.cbConfirm.setTextColor(Color.parseColor(colorEnable))
                    updateDeleteButtonState(isChecked = view.isChecked &&  isDelete)
                }
            }
        }
    }
    private fun updateDeleteButtonState(isChecked: Boolean) {
        binding.apply {
            binding.btnDeleteAccount.isEnabled = isChecked
            binding.btnDeleteAccount.setTextColor(if (isChecked) resources.getColor(R.color.accent) else resources.getColor(
                R.color.white_38))
            binding.btnDeleteAccount.alpha = if (isChecked) 1f else 0.4f
        }
    }
    @SuppressLint("SetTextI18n")
    private fun startCountdown(seconds: Int) {
        var timeCountdown = seconds
        countDownTimer?.cancel()
        countDownTimer = object : CountDownTimer(timeCountdown * 1000L, 1000) {
            @SuppressLint("SetTextI18n")
            override fun onTick(millisUntilFinished: Long) {
                timeCountdown--
                isDelete = false
                binding.btnDeleteAccount.text =
                    getString(R.string.delete_account) + "(${timeCountdown}s)"
            }
            override fun onFinish() {
                isDelete = true
                binding.btnDeleteAccount.text = getString(R.string.delete_account)
                updateDeleteButtonState(isChecked = binding.cbConfirm.isChecked && isDelete)
            }
        }.start()
    }
    private fun navigateToAccountInfo(){
        Timber.tag("tam-multiprofile").d("${this.javaClass.simpleName} navigateToAccountInfo")
        activity?.findNavHostFragment()?.findNavController()?.navigateSafe(
            NavAccountInfoDirections.actionGlobalToAccountInfo())

    }
    private fun navigateToLoginAccount(){
        activity?.findNavHostFragment()?.findNavController()?.navigateSafe(
            NavHomeMainDirections.actionGlobalToHome())
        setFragmentResult(Constants.REFRESH_DATA, bundleOf())
    }
    override fun MegaViewModel.MegaState.toUI() {
        when (this) {
            is MegaViewModel.MegaState.Loading ->{
                showLoading()
            }
            is MegaViewModel.MegaState.ResultGetDeletePolicyAccount->{
                hideLoading()
                binding.webView.loadData(data.first().blockHtml.htmlMobile, "text/html", "")
            }
            is MegaViewModel.MegaState.Error->{
                hideLoading()
            }
            is MegaViewModel.MegaState.Done->{
                hideLoading()
            }
            is MegaViewModel.MegaState.ErrorRequiredLogin->{
                hideLoading()
                if (findNavController().currentDestination?.id == R.id.delete_account_policy_fragment)
                    navigateToLoginAccount()
            }
            else -> {
            }
        }
    }
}