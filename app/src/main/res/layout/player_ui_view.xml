<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/playerLayoutTouch"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_overlay_logo"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_safe_start"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="0dp" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_safe_end"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_end="0dp" />

    <include
        android:id="@+id/layout_age_restrictions_left"
        layout="@layout/player_age_restrictions_left_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/age_restriction_margin"
        android:layout_marginTop="@dimen/age_restriction_margin"
        app:layout_constraintEnd_toEndOf="@id/guideline_safe_end"
        app:layout_constraintStart_toStartOf="@id/guideline_safe_start"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/layout_age_restrictions_right"
        layout="@layout/player_age_restrictions_right_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/age_restriction_margin"
        android:layout_marginTop="@dimen/age_restriction_margin"
        app:layout_constraintEnd_toEndOf="@id/guideline_safe_end"
        app:layout_constraintStart_toStartOf="@id/guideline_safe_start"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/vs_logo_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout="@layout/ads_logo_instream_layout"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/playerLayoutDim"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#A6000000"
        android:visibility="gone"
        tools:visibility="visible">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_control_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline_control_safe_start"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:orientation="vertical"
                app:layout_constraintGuide_begin="0dp" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline_control_safe_end"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:orientation="vertical"
                app:layout_constraintGuide_end="0dp" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_marginTop="@dimen/margin_medium"
                android:id="@+id/cl_top_control"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/player_control_margin_parent"
                app:layout_constraintEnd_toEndOf="@id/guideline_control_safe_end"
                app:layout_constraintStart_toStartOf="@id/guideline_control_safe_start"
                app:layout_constraintTop_toTopOf="parent">

                <ImageButton
                    android:id="@+id/ib_back"
                    android:layout_width="@dimen/player_control_back_size"
                    android:layout_height="@dimen/player_control_back_size"
                    android:background="@drawable/all_background_circle_ripple"
                    android:src="@drawable/ic_arrow_back"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:visibility="invisible"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/white"
                    android:textSize="@dimen/player_title_content_text_size"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/ll_top_control"
                    app:layout_constraintStart_toEndOf="@id/ib_back"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:id="@+id/ll_top_control"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageButton
                        android:layout_marginStart="@dimen/margin_common"
                        android:id="@+id/ib_report"
                        android:layout_width="@dimen/player_control_top_button_wrapper_size"
                        android:layout_height="@dimen/player_control_top_button_wrapper_size"
                        android:background="@drawable/all_background_circle_ripple"
                        android:src="@drawable/ic_player_report" />

                    <ImageButton
                        android:layout_marginStart="@dimen/margin_common"
                        android:id="@+id/ib_multicam"
                        android:layout_width="@dimen/player_control_top_button_wrapper_size"
                        android:layout_height="@dimen/player_control_top_button_wrapper_size"
                        android:layout_marginEnd="@dimen/margin_common"
                        android:background="@drawable/all_background_circle_ripple"
                        android:src="@drawable/ic_player_multicam"
                        android:visibility="gone" />

                    <ImageButton
                        android:layout_marginStart="@dimen/margin_common"
                        android:id="@+id/ib_cast"
                        android:layout_width="@dimen/player_control_top_button_wrapper_size"
                        android:layout_height="@dimen/player_control_top_button_wrapper_size"
                        android:background="@drawable/all_background_circle_ripple"
                        android:src="@drawable/player_cast_button_seletor"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <ImageButton
                        android:layout_marginStart="@dimen/margin_common"
                        android:id="@+id/ib_expand"
                        android:layout_width="@dimen/player_control_top_button_wrapper_size"
                        android:layout_height="@dimen/player_control_top_button_wrapper_size"
                        android:background="@drawable/all_background_circle_ripple"
                        android:src="@drawable/ic_player_expand" />

                    <ImageButton
                        android:layout_marginStart="@dimen/margin_common"
                        android:id="@+id/ib_more"
                        android:layout_width="@dimen/player_control_top_button_wrapper_size"
                        android:layout_height="@dimen/player_control_top_button_wrapper_size"
                        android:background="@drawable/all_background_circle_ripple"
                        android:src="@drawable/ic_player_more" />
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_cast_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/_40sdp"
                android:textColor="@color/white"
                android:textSize="@dimen/player_title_content_text_size"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/cl_center_control"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_top_control" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_center_control"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline_center_safe_start"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:orientation="vertical"
                    app:layout_constraintGuide_begin="0dp" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline_center_safe_end"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:orientation="vertical"
                    app:layout_constraintGuide_end="0dp" />

                <ImageButton
                    android:id="@+id/ib_player_lock"
                    android:layout_width="@dimen/player_control_center_button_size"
                    android:layout_height="@dimen/player_control_center_button_size"
                    android:layout_marginStart="@dimen/player_control_margin_parent_minus_padding"
                    android:background="@drawable/all_background_circle_ripple"
                    android:src="@drawable/ic_player_unlock"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="@id/guideline_center_safe_start"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageButton
                    android:id="@+id/ib_seek_previous_10s"
                    android:layout_width="@dimen/player_control_center_button_size"
                    android:layout_height="@dimen/player_control_center_button_size"
                    android:layout_marginEnd="@dimen/player_control_center_button_margin"
                    android:background="@drawable/all_background_circle_ripple"
                    android:scaleType="fitXY"
                    android:src="@drawable/ic_seek_previous_10s"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/ib_skip_previous"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageButton
                    android:id="@+id/ib_skip_previous"
                    android:layout_width="@dimen/player_control_center_button_size"
                    android:layout_height="@dimen/player_control_center_button_size"
                    android:layout_marginEnd="@dimen/player_control_center_button_margin"
                    android:background="@drawable/all_background_circle_ripple"
                    android:scaleType="fitXY"
                    android:src="@drawable/player_skip_previous_selector"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/ib_play"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageButton
                    android:id="@+id/ib_play"
                    android:layout_width="@dimen/player_control_play_or_pause_size"
                    android:layout_height="@dimen/player_control_play_or_pause_size"
                    android:background="@drawable/all_background_circle_ripple"
                    android:scaleType="fitXY"
                    android:src="@drawable/ic_player_play"
                    android:visibility="invisible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageButton
                    android:id="@+id/ib_skip_next"
                    android:layout_width="@dimen/player_control_center_button_size"
                    android:layout_height="@dimen/player_control_center_button_size"
                    android:layout_marginStart="@dimen/player_control_center_button_margin"
                    android:background="@drawable/all_background_circle_ripple"
                    android:scaleType="fitXY"
                    android:src="@drawable/player_skip_next_selector"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/ib_play"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageButton
                    android:id="@+id/ib_seek_next_10s"
                    android:layout_width="@dimen/player_control_center_button_size"
                    android:layout_height="@dimen/player_control_center_button_size"
                    android:layout_marginStart="@dimen/player_control_center_button_margin"
                    android:background="@drawable/all_background_circle_ripple"
                    android:scaleType="fitXY"
                    android:src="@drawable/ic_seek_next_10s"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/ib_skip_next"
                    app:layout_constraintTop_toTopOf="parent" />

                <RelativeLayout
                    android:id="@+id/ib_sport_interactive"
                    android:layout_width="@dimen/_40sdp"
                    android:layout_height="@dimen/_40sdp"
                    android:layout_marginEnd="@dimen/app_margin"
                    app:layout_constraintEnd_toEndOf="@id/guideline_center_safe_end"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/ivMatchData"
                        android:layout_width="@dimen/player_control_center_button_size"
                        android:layout_height="@dimen/player_control_center_button_size"
                        android:layout_centerInParent="true"
                        android:background="@color/transparent"
                        android:src="@drawable/ic_button_match_info" />

                    <ImageView
                        android:id="@+id/ivNew"
                        android:layout_width="@dimen/_27sdp"
                        android:layout_height="@dimen/_17sdp"
                        android:layout_marginStart="@dimen/_23sdp"
                        android:scaleType="fitXY"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </RelativeLayout>

                <ImageButton
                    android:id="@+id/ib_player_live_chat"
                    android:layout_width="@dimen/_40sdp"
                    android:layout_height="@dimen/_40sdp"
                    android:layout_marginEnd="@dimen/app_margin"
                    android:background="@drawable/all_background_circle_ripple"
                    android:src="@drawable/ic_player_live_chat"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="@id/guideline_center_safe_end"
                    app:layout_constraintTop_toBottomOf="@id/ib_sport_interactive"
                    tools:visibility="visible" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_bottom_control"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/margin_medium"
                app:layout_constraintBottom_toTopOf="@id/rcv_bottom_control"
                app:layout_constraintEnd_toEndOf="@id/guideline_control_safe_end"
                app:layout_constraintStart_toStartOf="@id/guideline_control_safe_start">


                <TextView
                    android:id="@+id/tv_live"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/margin_common"
                    android:drawableStart="@drawable/ic_player_dot_live"
                    android:drawablePadding="@dimen/base_edit_text_padding"
                    android:gravity="center"
                    android:text="LIVE"
                    android:textAllCaps="true"
                    android:textColor="@color/white"
                    android:textSize="@dimen/player_skip_button_text_size"
                    android:textStyle="bold"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:ignore="UseCompatTextViewDrawableXml"
                    tools:visibility="gone" />

                <TextView
                    android:id="@+id/tv_current_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/player_control_margin_parent"
                    android:gravity="end"
                    android:textColor="@color/white_87"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="01:03:04" />

                <TextView
                    android:id="@+id/tv_total_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_10sdp"
                    android:gravity="center"
                    android:textColor="@color/white_60"
                    android:visibility="gone"
                    app:layout_constraintStart_toEndOf="@id/tv_current_time"
                    app:layout_constraintTop_toTopOf="@id/tv_current_time"
                    tools:text=" / 1:50:56"
                    tools:visibility="visible" />

                <ImageButton
                    android:id="@+id/ib_fullscreen"
                    android:padding="@dimen/player_control_bottom_timeline_button_padding"
                    android:layout_width="@dimen/player_control_bottom_timeline_button_wrapper_size"
                    android:layout_height="@dimen/player_control_bottom_timeline_button_wrapper_size"
                    android:layout_marginEnd="@dimen/player_control_margin_parent"
                    android:background="@drawable/all_background_circle_ripple"
                    android:src="@drawable/player_fullscreen_selector"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.fptplay.mobile.common.ui.view.PlayerVodSeekbarView
                    android:id="@+id/playerSeekProgress"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:maxHeight="@dimen/_5sdp"
                    android:minHeight="@dimen/_5sdp"
                    android:paddingVertical="@dimen/_6sdp"
                    android:progress="0"
                    android:layout_marginHorizontal="@dimen/player_control_margin_parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ib_fullscreen"
                    tools:visibility="visible" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcv_bottom_control"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/player_control_bottom_button_height"
                android:layout_marginBottom="@dimen/player_control_bottom_button_margin"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:itemCount="4"
                tools:listitem="@layout/player_bottom_item_view"
                tools:visibility="gone" />
        </androidx.constraintlayout.widget.ConstraintLayout>


    </FrameLayout>

    <com.fptplay.mobile.player.views.DoubleSeekOverlay
        android:id="@+id/doubleSeekOverLay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_skip_intro"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.7" />

    <TextView
        android:id="@+id/tv_skip_intro"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_6sdp"
        android:background="@drawable/player_skip_intro_bg"
        android:paddingHorizontal="@dimen/_9sdp"
        android:paddingVertical="@dimen/_6sdp"
        android:text="@string/player_skip_intro"
        android:textColor="#FFFFFF"
        android:textSize="@dimen/player_skip_button_text_size"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/guideline_skip_intro"
        app:layout_constraintEnd_toEndOf="@id/guideline_safe_end"
        app:layout_constraintTop_toTopOf="@id/guideline_skip_intro" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_skip_credit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.7" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_skip_credit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/guideline_skip_credit"
        app:layout_constraintEnd_toEndOf="@id/guideline_safe_end"
        app:layout_constraintTop_toTopOf="@id/guideline_skip_credit">

        <TextView
            android:id="@+id/tv_watch_credit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/margin_medium"
            android:background="@drawable/player_skip_intro_bg"
            android:paddingHorizontal="@dimen/_9sdp"
            android:paddingVertical="@dimen/_6sdp"
            android:text="@string/player_watch_credit"
            android:textAllCaps="true"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/player_skip_button_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/fl_skip_credit"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:id="@+id/fl_skip_credit"
            android:layout_width="@dimen/_90sdp"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/_6sdp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ProgressBar
                android:id="@+id/pb_skip_credit"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:max="100"
                android:progressDrawable="@drawable/player_skip_credit_progress_bg" />

            <TextView
                android:id="@+id/tv_skip_credit"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="@string/player_next_episode"
                android:textAlignment="center"
                android:textAllCaps="true"
                android:textColor="#FFFFFF"
                android:textSize="@dimen/player_skip_button_text_size"
                tools:ignore="UseCompatTextViewDrawableXml" />
        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/layout_recommend_portrait"
        layout="@layout/player_recommendation_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <include
        android:id="@+id/layout_recommend_landscape"
        layout="@layout/player_recommendation_view_land"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/tv_player_volume_brightness"
        style="@style/PlayerTextViewStatusBrightnessVolumeTimeSeek"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_60sdp"
        android:paddingHorizontal="@dimen/_10sdp"
        android:paddingVertical="@dimen/_5sdp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <include
        android:id="@+id/buy_package_guide"
        layout="@layout/popup_buy_package_guide"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/buy_package_guide_margin_bottom"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/fl_overlay"
        android:visibility="gone"/>

</androidx.constraintlayout.widget.ConstraintLayout>
