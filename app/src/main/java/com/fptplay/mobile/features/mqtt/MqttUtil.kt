package com.fptplay.mobile.features.mqtt

import com.fpl.plugin.mqtt.model.ReceivedMessage
import com.fptplay.mobile.features.mqtt.model.Publisher
import com.google.gson.Gson
import com.xhbadxx.projects.module.domain.entity.fplay.common.MQTTConfig

object MqttUtil {
    const val REMOTE_TOPIC = "remote"
    const val PING_CCU_TOPIC = "pingccu"
    const val SEPARATOR = "/"
    const val LWT_TOPIC = "will/disconnect"
    const val LWT_MESSAGE = "{}"
    const val ACTION_START = "start"
    const val ACTION_END = "stop"
    const val LOG_ID_ERROR = "114"
    const val EVENT_ERROR = "Error"
    const val ACTION_LIMIT_CCU = "limit_ccu"
    const val PUBLISHER = "_Publisher"
    const val SUBSCRIBER = "_Subcriber"
    const val DEFAULT_MODE = 1

    fun MQTTConfig.AutomaticReconnect.isAutoReconnect(): Boolean {
        return this.enable && this.minRetryInterval > 0 && this.maxRetryInterval > 0 && this.minRetryInterval < this.maxRetryInterval
    }

    fun MQTTConfig.AutomaticReconnect.getReconnectInterval(): Pair<Long, Long> {
        return Pair(this.minRetryInterval.toLong(), this.maxRetryInterval.toLong())
    }

    fun Publisher.toJsonString(): String {
        return Gson().toJson(this)
    }

    fun String.fromJsonString(): Publisher? {
        return try {
            Gson().fromJson(this, Publisher::class.java)
        } catch (ex: Exception) {
            null
        }
    }

    fun Int.toMillisecond(): Long {
        return (this * 1000).toLong()
    }

    fun ReceivedMessage.mapToPublisher(): Publisher? {
        val payload = this.message.payload.decodeToString()
        return payload.fromJsonString()
    }
}