<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_survey"
    app:startDestination="@id/survey_fragment">
    <fragment
        android:id="@+id/survey_fragment"
        android:name="com.fptplay.mobile.features.survey.SurveyFragment"
        android:label="SurveyFragment"
        tools:layout="@layout/survey_fragment">
        <argument
            android:name="originalLink"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="event"
            app:argType="string"
            app:nullable="false" />
    </fragment>

</navigation>