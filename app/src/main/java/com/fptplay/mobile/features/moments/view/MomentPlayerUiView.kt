package com.fptplay.mobile.features.moments.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.media.AudioManager
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.*
import android.view.View.OnTouchListener
import android.widget.FrameLayout
import android.widget.SeekBar
import androidx.annotation.OptIn
import androidx.appcompat.widget.AppCompatSeekBar
import androidx.core.content.ContextCompat
import androidx.core.view.GestureDetectorCompat
import androidx.core.view.doOnAttach
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import com.airbnb.paris.utils.setPaddingBottom
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.utils.StringUtils
import com.fptplay.mobile.databinding.MomentPlayerUiViewBinding
import com.fptplay.mobile.player.PlayerView
import com.fptplay.mobile.player.interfaces.IPlayerControl
import com.fptplay.mobile.player.utils.*
import androidx.media3.common.C
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import com.tear.modules.player.exo.ExoPlayerProxy
import com.tear.modules.player.util.IPlayer
import com.xhbadxx.projects.module.domain.entity.fplay.moment.MomentDetail
import com.xhbadxx.projects.module.util.common.Util
import com.xhbadxx.projects.module.util.common.Util.checkToShowContent
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.isShow
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.image.ImageProxy
import timber.log.Timber
import java.util.*


class MomentPlayerUiView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val TAG = this::class.java.simpleName

    //region Variables
    private val binding =
        MomentPlayerUiViewBinding.inflate(LayoutInflater.from(context), this, true)
    private var viewLifecycleOwner: LifecycleOwner? = null

    var onChildViewClickListener: OnClickListener? = null
    var onChildDoubleTapListener: MomentDoubleTapListener? = null

    //
    private var playerView: PlayerView? = null
    private var seekbar: AppCompatSeekBar? = null
    private var fragmentActivity: FragmentActivity? = null
    private var player: IPlayer? = null
    private val playerListener: Player.Listener by lazy { PlayerListener() }
    private var playerUIEvents: IPlayerControl? = null
    private val errorTime: Long
        @OptIn(UnstableApi::class)
        get() = if (player is ExoPlayerProxy) C.TIME_UNSET else -1

    // Player Data Control
    private var moment: MomentDetail? = null
    private var showMetadata: Boolean = true
    private var isPlaylistLayout: Boolean = false
    private var inAutoScrollCountdownMode = false

    // View handler
    private var isPlaying = false
    private var isPaused = false
    private var isUIHidden = false
    private var isUIShown = false
    private var uiTask: TimerTask? = null
    private var showUITask: TimerTask? = null
    private var lastInteractTimeMs = 0L

    // Progress
    private val delayTimeToUpdateProgress: Long = 1000
//    private val updateProgressRunnable: Runnable by lazy { Runnable { updateProgress() } }
    //endregion

    // Var for layout UI
    private var isFullscreen = false
    private var isBuffering = false
    private var isSeekBarDragging = false
    var playerControllerLocked = false
    //

    //region Gestures control volume and brightness
    private var layoutParams: WindowManager.LayoutParams? = null
    private var currentBrightness = 0f
    private var currentVolumeLevel = 0
    private var mAudioManager: AudioManager? = null
    private var intLeft = false
    private var intRight = false
    private var diffX = 0
    private var diffY = 0
    private var downX = 0f
    private var downY = 0f
    //endregion


    init {
        doOnAttach {
            initTouchView()
            bindComponent()
            bindEvent()
            updateAllViews()
        }
    }

    //region Init views

    private fun bindComponent() {
        mAudioManager =
            context?.applicationContext?.getSystemService(Context.AUDIO_SERVICE) as? AudioManager
        layoutParams = fragmentActivity?.window?.attributes

        // hide comment and like button in phase 1
        binding.apply {
            tvDescription.apply {
                setShowingLine = 3
                setShowLessTextColor = Color.WHITE
                setShowMoreTextColor = Color.WHITE
                addShowLessText= "Ẩn Bớt"
                addShowMoreText ="Thêm"
                setTextDesWhenLess = resources.getColor(R.color.white_60, null)
                setTextDesWhenShowMore = resources.getColor(R.color.white_87, null)
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun bindEvent() {
        binding.playerLayoutTouch.setOnTouchListener(gestureDetectorListener)

        binding.playerLayoutTouch.onClickDelay {
            onChildViewClickListener?.onClick(this)
        }

        binding.ctlError.onClickDelay {
            onChildViewClickListener?.onClick(this)
        }

        binding.ibPlay.setOnTouchListener(gestureDetectorListener)
        binding.ibPlay.onClickDelay {
            onChildViewClickListener?.onClick(this)
        }

        binding.ibBack.setOnTouchListener(gestureDetectorListener)
        binding.ibBack.onClickDelay {
            onChildViewClickListener?.onClick(this)
        }

//        binding.llShare2.setOnTouchListener(gestureDetectorListener)
//        binding.llShare2.onClickDelay {
//            onChildViewClickListener?.onClick(this)
//        }

        binding.llShare.setOnTouchListener(gestureDetectorListener)
        binding.llShare.onClickDelay {
            onChildViewClickListener?.onClick(this)
        }
        binding.llComment.setOnTouchListener(gestureDetectorListener)
        binding.llComment.onClickDelay {
            onChildViewClickListener?.onClick(this)
        }
        binding.llLike.setOnTouchListener(gestureDetectorListener)
        binding.llLike.onClickDelay {
            onChildViewClickListener?.onClick(this)
        }

        binding.ctlMetadata.setOnTouchListener(gestureDetectorListener)
        binding.ctlMetadata.onClickDelay {
            onChildViewClickListener?.onClick(this)
        }

        binding.llLike.setOnTouchListener(gestureDetectorListener)
        binding.llLike.onClickDelay {
            onChildViewClickListener?.onClick(this)
        }

        binding.llComment.setOnTouchListener(gestureDetectorListener)
        binding.llComment.onClickDelay {
            onChildViewClickListener?.onClick(this)
        }

//        binding.ctlPlaylist.setOnTouchListener(gestureDetectorListener)
//        binding.ctlPlaylist.onClickDelay {
//            onChildViewClickListener?.onClick(this)
//        }
    }

    private fun initTouchView() {
        gestureListener.controls = object : PlayerDoubleTapListener {
            override fun onDoubleTapStarted(posX: Float, posY: Float) {
            }

            override fun onDoubleTapProgressDown(posX: Float, posY: Float) {
                onChildDoubleTapListener?.onDoubleTap()
            }

            override fun onDoubleTapProgressUp(posX: Float, posY: Float) {
            }

            override fun onDoubleTapFinished() {

            }
        }
    }
    //endregion

    //endregion

    fun bindPlayer(player: IPlayer) {
        this.player = player
    }

    fun bindSeekbar(seekbar: AppCompatSeekBar) {
        binding.flPlayerSeekProgressContainer.let {
            val index = it.indexOfChild(seekbar)
            if (index < 0) {
                it.addView(seekbar)
            }
        }
    }

    fun unbindSeekbar() {
    }

    fun unbindPlayer() {
//        player?.removePlayerCallback(this)
        player = null
    }
    fun showHideBackButton(isShow:Boolean){
        binding.ibBack.isVisible = isShow
    }
    fun setMarginBottom(isSet:Boolean){
        if(isSet){

            binding.bgBottom.setPaddingBottom(0)
            binding.clBottomControl.setPaddingBottom(resources.getDimensionPixelSize(R.dimen.moment_player_ui_bottom_padding))
            binding.flPlayerSeekProgressContainer.setPaddingBottom(resources.getDimensionPixelSize(R.dimen.moment_player_ui_bottom_padding))
        }else{ //is have navigator
            binding.bgBottom.setPaddingBottom(resources.getDimensionPixelSize(R.dimen.moment_seekbar_over))
            binding.clBottomControl.setPaddingBottom(0)
            binding.flPlayerSeekProgressContainer.setPaddingBottom(0)
        }
    }

    fun updateDataMomentUiView(
        moment: MomentDetail? = null,
        showMetadata: Boolean = true,
        isPlaylistLayout: Boolean = false
    ) {
        this.moment = moment
        this.showMetadata = showMetadata
        this.isPlaylistLayout = isPlaylistLayout
        updateAllViews()
    }

    fun updateButtonLike(data: MomentDetail, countLike: String) {
        binding.llLike.isVisible = data.like.isShowLike == "1"
        if (data.like.statusLikeLocal == "1") {
            binding.ivLike.setImageResource(R.drawable.ic_heart_liked)
        } else {
            binding.ivLike.setImageResource(R.drawable.ic_heart_unliked)
        }
        Timber.d("thien test update LIKE ${data.like.countLocal}")
        binding.tvLike.text = countLike
    }

    fun updateCommentCount(data: MomentDetail, totalComment: String){
        binding.llComment.isVisible = data.comment.isShowComment == "1"
        binding.tvComment.text = totalComment
        Timber.d("thien test update COMMENT ${totalComment}")
    }

    private fun updateAllViews() {
//        updateCenterControl()
        updateBottomControl()
    }

    private fun updateBottomControl() {
        this.moment?.let {
            binding.tvDescription.setText(it.content.caption, refresh = true)
            // Update related
            val related = it.related
            if(related != null) {
                binding.tvTitleMetadata.checkToShowContent(related.title, false)
                binding.tvTagMetadata.text = StringUtils.getMetaText(
                    context,
                    related.priorityTag,
                    related.metaData,
                    separatePattern = R.string.dot_high_light
                )
                ImageProxy.load(
                    context = binding.root.context,
                    url = related.thumb,
                    width = binding.root.context.resources.getDimensionPixelOffset(R.dimen.moment_related_item_width),
                    height = binding.root.context.resources.getDimensionPixelOffset(R.dimen.moment_related_item_height),
                    target = binding.ivThumbnailMetadata,
                    placeHolderId = R.drawable.image_placeholder,
                    errorDrawableId = R.drawable.image_placeholder
                )
                binding.tvMaturity.checkToShowContent(related.maturityRating.advisories, true)
                binding.ctlMetadata.show()
            }
            if (related != null && showMetadata) {
                binding.ctlPlaylist.visible()
            } else {
                binding.ctlPlaylist.invisible()
            }
            // Update playlist
            updatePlaylist(it)

        }

    }

    fun showTitlePlaylistAndCaption() {
        binding.tvDescription.fadeIn(0)
        updatePlaylist(moment)
    }
    fun hideTitlePlaylistAndCaption() {
        binding.tvDescription.fadeOut(0)
    }

    private fun updatePlaylist(moment: MomentDetail?) {
        if(moment == null) return
        if(moment.content.typeContent == MomentDetail.Content.MomentContentType.Series || isPlaylistLayout) {
            if(!isPlaylistLayout) {
                if(!inAutoScrollCountdownMode) {
                    // auto scroll countdown only when series and not in playlist layout -> no need to check outside
                    this.moment?.let {
                        val related = it.related
                        if (related != null && showMetadata) {
                            binding.tvTagMetadata.text = StringUtils.getMetaText(
                                context,
                                related.priorityTag,
                                related.metaData,
                                separatePattern = R.string.dot_high_light
                            )
                        }
                    }

                }

            }
            binding.ivPlaylistIc.show()
            binding.ivPlayIc.gone()
        } else {
            // not series AND not in playlist layout -> show icon play circle
            if(moment.related?.type_related is MomentDetail.Related.MomentRelatedType.VOD) {
                binding.ivPlaylistIc.gone()
                binding.ivPlayIc.show()
            }else {
                binding.ivPlaylistIc.gone()
                binding.ivPlayIc.gone()
            }
        }

    }

    fun initButtonFunction(){
        this.moment?.let {
            binding.llComment.isVisible = it.comment.isShowComment == "1"
            binding.tvComment.text = it.comment.countLocal
            binding.llLike.isVisible = it.like.isShowLike == "1"
            binding.tvLike.text = it.like.countLocal
            if(it.like.statusLikeLocal == "1")
            binding.ivLike.setImageResource(R.drawable.ic_heart_liked)
            else binding.ivLike.setImageResource(R.drawable.ic_heart_unliked)
        }
    }

    fun showErrorView(title: String, description: String?, showTitleOnly: Boolean = false) {
        binding.ctlError.show()
        binding.tvErrorTitle.text = title.ifBlank {
            context.getString(R.string.moment_error_moment_unavailable_title)
        }
        if(showTitleOnly) {
            binding.tvErrorDes.invisible()
        } else {
            binding.tvErrorDes.text = (description ?: "").ifBlank {
                context.getString(R.string.moment_error_moment_unavailable_des)
            }
            binding.tvErrorDes.show()
        }
        binding.ibPlay.fadeOut(0)
    }

    fun hideErrorView() {
        binding.ctlError.invisible()
    }

    fun updateAutoScrollCountDown(progress: Int) {
        inAutoScrollCountdownMode = true
        binding.tvTagMetadata.text = context.getString(R.string.moment_auto_scroll_count_down, progress)
    }

    fun hideAutoScrollCountDown() {
        inAutoScrollCountdownMode = false
        updatePlaylist(moment)
    }
    //region Update -> Play/Pause Button
    fun updatePlayOrPause() {
//        if (!isVisible()) return
        if (player?.isPlaying() == true) {
            isPlaying = true
            isPaused = false
//            binding.ibPlay.setImageResource(R.drawable.ic_player_pause)
            if(binding.ibPlay.isShow() == true) {
                binding.ibPlay.fadeOut()
            }
        } else {
            isPlaying = false
            isPaused = true
            binding.ibPlay.setImageResource(R.drawable.ic_player_moment_play)
            if(binding.ibPlay.isShow() == false) {
                binding.ibPlay.fadeIn()
            }
        }
    }
    //endregion

    //region Seekbar


    fun onSeekbarStartTrackingTouch(seekBar: SeekBar?) {
    }

    fun onSeekbarStopTrackingTouch(seekBar: SeekBar?) {
        binding.llPlayerTime.hide()
        binding.clBottomControl.show()
        updatePlaylist(moment)
    }

    fun onSeekbarProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
        if (fromUser) {
            binding.apply {
                val totalDuration = player?.totalDuration() ?: errorTime
                tvCurrentTime.text = Util.convertTime(progress.toLong())
                if (totalDuration != errorTime) tvTotalDuration.text =
                    Util.convertTime(totalDuration)
                llPlayerTime.show()
                clBottomControl.hide()
            }
        }
    }
    //endregion

    // region Player Listener
    fun onPlayerBuffering() {}
    fun onPlayerEnd() {}
    fun onPlayerError(code: Int, name: String, detail: String) {}
    fun onPlayerErrorBehindInLive(code: Int, name: String, detail: String) {}
    fun onPlayerPause() {
        showTitlePlaylistAndCaption()
        updatePlayOrPause()
    }
    fun onPlayerPlay() {
        hideTitlePlaylistAndCaption()
        updatePlayOrPause()
    }
    fun onPlayerPrepare() {}
    fun onPlayerStop() {}
    fun onPlayerReady() {
        updatePlayOrPause()
    }
    fun onPlayerStart() {
        updatePlayOrPause()
    }

    fun onPlayerSeek() {}

    // endregion Player Listener

    private fun showUI() {
        if (isUIHidden) {
            isUIHidden = false
            isUIShown = true
//           binding.playerLayoutDim.fadeIn()
            binding.clCenterControl.fadeIn()
            updateAllViews()
        }
    }

    private fun hideUI() {
        if (isPlaying != isPaused) {
            isUIHidden = true
            isUIShown = false
            binding.clCenterControl.fadeOut()
        }
    }


    //region Handle double click for seek & change volume and brightness
    private val gestureListener: DoubleTapGestureListener =
        DoubleTapGestureListener(binding.playerLayoutTouch)
    private val gestureDetector: GestureDetectorCompat by lazy {
        GestureDetectorCompat(
            context,
            gestureListener
        )
    }

    @SuppressLint("ClickableViewAccessibility")
    private val gestureDetectorListener = OnTouchListener { view, event ->
//        Timber.tag("tam-moment").w("gestureDetectorListener OnTouchListener")
        lastInteractTimeMs = System.currentTimeMillis()
        // Update View
        gestureListener.view = view
        return@OnTouchListener gestureDetector.onTouchEvent(event)
    }


    /**
     * Gesture Listener for double tapping
     *
     * For more information which methods are called in certain situations look for
     * [GestureDetector.onTouchEvent][android.view.GestureDetector.onTouchEvent],
     * especially for ACTION_DOWN and ACTION_UP
     */
    inner class DoubleTapGestureListener(private val rootView: View) :
        GestureDetector.SimpleOnGestureListener() {

        private val mHandler = Handler(Looper.getMainLooper())
        private val mRunnable = Runnable {
            isDoubleTapping = false
            controls?.onDoubleTapFinished()
        }
        var view: View? = null
        var controls:  PlayerDoubleTapListener? = null
        var isDoubleTapping = false
        var doubleTapDelay: Long = 650

        /**
         * Resets the timeout to keep in double tap mode.
         *
         * Called once in [PlayerDoubleTapListener.onDoubleTapStarted]. Needs to be called
         * from outside if the double tap is customized / overridden to detect ongoing taps
         */
        fun keepInDoubleTapMode() {
            isDoubleTapping = true
            mHandler.removeCallbacks(mRunnable)
            mHandler.postDelayed(mRunnable, doubleTapDelay)
        }

        /**
         * Cancels double tap mode instantly by calling [PlayerDoubleTapListener.onDoubleTapFinished]
         */
        fun cancelInDoubleTapMode() {
            mHandler.removeCallbacks(mRunnable)
            isDoubleTapping = false
            controls?.onDoubleTapFinished()
        }

        override fun onDown(e: MotionEvent): Boolean {
            // Used to override the other methods
            if (isDoubleTapping) {
                if (view?.id == rootView.id) controls?.onDoubleTapProgressDown(e.x, e.y)
                return true
            }

            return view?.id == rootView.id
        }

        override fun onScroll(
            e1: MotionEvent?,
            e2: MotionEvent,
            distanceX: Float,
            distanceY: Float
        ): Boolean {
            return super.onScroll(e1, e2, distanceX, distanceY)
        }

        override fun onSingleTapUp(e: MotionEvent): Boolean {
            if (isDoubleTapping) {
                if (view?.id == rootView.id) controls?.onDoubleTapProgressUp(e.x, e.y)
                return true
            }
            return super.onSingleTapUp(e)
        }

        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            // Ignore this event if double tapping is still active
            // Return true needed because this method is also called if you tap e.g. three times
            // in a row, therefore the controller would appear since the original behavior is
            // to hide and show on single tap
            if (isDoubleTapping) return true
//            Timber.tag("tam-moment").d("onSingleTapConfirmed ${view?.id} - ${rootView.id}")
//            if (!binding.playerLayoutDim.isVisible) {
//                showUI()
//                startUIHidingTask()
//            } else {
//                if (view?.id == rootView.id) hideUI()
//            }
//            return true
            if (view?.id == rootView.id) {
                return rootView.performClick()
            } else {
                return true
            }
        }

        override fun onDoubleTap(e: MotionEvent): Boolean {
            // First tap (ACTION_DOWN) of both taps
            if (!isDoubleTapping) {
                isDoubleTapping = true
                keepInDoubleTapMode()
                if (view?.id == rootView.id) controls?.onDoubleTapStarted(e.x, e.y)
            }
            return true
        }

        override fun onDoubleTapEvent(e: MotionEvent): Boolean {
            // Second tap (ACTION_UP) of both taps
            if (e.actionMasked == MotionEvent.ACTION_UP && isDoubleTapping) {
                if (view?.id == rootView.id) controls?.onDoubleTapProgressUp(e.x, e.y)
                return true
            }
            return super.onDoubleTapEvent(e)
        }
    }


    interface PlayerDoubleTapListener {
        /**
         * Called when double tapping starts, after double tap gesture
         *
         * @param posX x tap position on the root view
         * @param posY y tap position on the root view
         */
        fun onDoubleTapStarted(posX: Float, posY: Float) {}

        /**
         * Called for each ongoing tap (also single tap) (MotionEvent#ACTION_DOWN)
         * when double tap started and still in double tap mode defined
         * by [DoubleTapPlayerView.getDoubleTapDelay]
         *
         * @param posX x tap position on the root view
         * @param posY y tap position on the root view
         */
        fun onDoubleTapProgressDown(posX: Float, posY: Float) {}

        /**
         * Called for each ongoing tap (also single tap) (MotionEvent#ACTION_UP}
         * when double tap started and still in double tap mode defined
         * by [DoubleTapPlayerView.getDoubleTapDelay]
         *
         * @param posX x tap position on the root view
         * @param posY y tap position on the root view
         */
        fun onDoubleTapProgressUp(posX: Float, posY: Float) {}

        /**
         * Called when [DoubleTapPlayerView.getDoubleTapDelay] is over
         */
        fun onDoubleTapFinished() {}
    }

    //endregion


    inner class PlayerListener : Player.Listener {
        override fun onPlaybackStateChanged(state: Int) {
            when (state) {
                Player.STATE_READY -> {
                    updatePlayOrPause()
                }
            }
        }

    }

    interface MomentDoubleTapListener {
        fun onDoubleTap()
    }


}