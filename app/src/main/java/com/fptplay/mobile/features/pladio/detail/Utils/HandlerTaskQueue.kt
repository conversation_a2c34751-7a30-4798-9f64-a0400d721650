package com.fptplay.mobile.features.pladio.detail.Utils

import com.xhbadxx.projects.module.util.logger.Logger

class HandlerTaskQueue {
    private val taskQueue: ArrayDeque<() -> Unit> = ArrayDeque()

    // Thêm task vào queue
    fun addTask(task: () -> Unit) {
        taskQueue.add(task)
        if (taskQueue.size == 1) {
            executeFirstTask()
        }
    }

    // Thực thi task tiếp theo trong queue
    fun executeNextTask() {
        if (taskQueue.isNotEmpty()) {
            taskQueue.removeFirstOrNull() // Xoá task đã chạy trước đó
            val nextTask = taskQueue.firstOrNull() // Lấy task đầu tiên trong queue
            nextTask?.let {
                nextTask.invoke() // Thực thi task
            }
        }
    }
    private fun executeFirstTask() {
        if (taskQueue.isNotEmpty()) {
            val nextTask = taskQueue.firstOrNull() // Lấy task đầu tiên trong queue
            nextTask?.let {
                nextTask.invoke() // Thực thi task
            }
        }
    }
}