package com.fptplay.mobile.features.event_trigger

import com.fptplay.mobile.common.utils.StringUtils.hash512
import com.fptplay.mobile.features.event_trigger.data.EventType
import com.xhbadxx.projects.module.domain.entity.fplay.common.TriggerEvent
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import java.util.HashMap

object EventTriggerUtils {
    private const val SEMICOLON = ";"
    private const val COMMA = ","
    private const val COLON = ":"
    fun getListEventId(value: List<TriggerEvent.Event>): String {
        var listString = ""
        if (value.isNotEmpty()) {
            listString = value.joinToString(COMMA) { "${it.eventId}$COLON${it.frequency}" }
        }
        return listString.hash512()
    }

    /**
     * format input value: "currentId;id1:number1,id2:number2,id3:number3"
     * */
    fun parseStringWithPrefix(value: String): Pair<String, HashMap<String, Int>> {

        val beforeSemicolon = value.substringBefore(SEMICOLON, "")
        val afterSemicolon = value.substringAfter(SEMICOLON, "")

        val map = parseStringToMap(afterSemicolon)

        return beforeSemicolon to map
    }

    /**
    * format input value: "currentId;id1:number1,id2:number2,id3:number3"
     * */
    private fun parseStringToMap(value: String) : HashMap<String, Int> {
        val pairs = value.split(COMMA)
        val map = hashMapOf<String, Int>()
        for (pair in pairs) {
            val (id, number) = pair.split(COLON)
            map[id] = number.toIntOrNull() ?: 0
        }
        return map
    }


    /**
     * format output value: "currentId;id1:number1,id2:number2,id3:number3"
     * */
    fun mapToString(currentId: String, map:HashMap<String, Int>) : String {
        val listString = map.entries.joinToString(COMMA) {"${it.key}:${it.value}"}
        val fullString = "$currentId$SEMICOLON$listString"
        return fullString
    }

    fun TriggerEvent.Event.getFrequency(): Int {
        return this.frequency.toIntOrNull() ?: 0
    }

    fun TriggerEvent.Event.hasSupported(): Boolean {
        return EventType.fromType(this.type) != EventType.UNKNOWN
    }

    fun clearEventTrigger(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveMetaEventTriger("")
        sharedPreferences.saveTriggerEventID("")
    }
}