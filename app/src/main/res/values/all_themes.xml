<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.FPTPlay" parent="Theme.MaterialComponents.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/accent</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:navigationBarColor">@android:color/transparent</item>

        <!-- Optional, if your app is drawing behind the status bar also. -->
        <item name="android:statusBarColor">@color/status_bar_color</item>
        <!-- Customize your theme here. -->
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar.FullWidth</item>
    </style>

    <style name="Theme.FPTPlay.SplashScreen" parent="Theme.FPTPlay">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@drawable/splash_bg</item>
        <item name="android:statusBarColor">@color/black</item>
    </style>

    <style name="BottomNavigation" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="android:background">@color/black</item>
        <item name="itemIconTint">@color/bottom_navigation_item_color_state</item>
        <item name="itemTextColor">@color/bottom_navigation_item_color_state</item>
        <item name="itemHorizontalTranslationEnabled">false</item>
        <item name="labelVisibilityMode">labeled</item>
        <item name="android:textSize">@dimen/_7ssp</item>

        <item name="itemIconPadding">@dimen/_4sdp</item>
    </style>

    <style name="FullScreenDialog" parent="Theme.FPTPlay">
        <item name="windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize|stateVisible</item>
        <item name="android:windowBackground">@color/dialog_dim_color</item>
        <item name="android:windowAnimationStyle">@style/FullScreenDialog.Animation</item>
    </style>

    <style name="FullScreenDialogWithoutDim" parent="FullScreenDialog">
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/FullScreenDialog.Animation</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
    </style>

    <style name="FullScreenDialogDark" parent="Theme.FPTPlay">
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/dialog_dim_dark_color</item>
        <item name="android:windowAnimationStyle">@style/FullScreenDialog.Animation</item>
    </style>

    <style name="FullScreenCameraDialogDark" parent="Theme.FPTPlay">
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/dialog_dim_dark_color</item>
        <item name="android:windowAnimationStyle">@style/FullScreenCameraDialogDark.Animation</item>
    </style>

    <style name="FullScreenCameraDialogDark.Animation">
        <item name="android:windowEnterAnimation">@anim/slide_bottom_to_top_in</item>
        <item name="android:windowExitAnimation">@anim/slide_top_to_bottom_out</item>
    </style>

    <style name="FullScreenDialogLight" parent="Theme.FPTPlay">
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">#E6000000</item>
        <item name="android:windowAnimationStyle">@style/FullScreenDialog.Animation</item>
    </style>
    <style name="FullScreenDialog.Animation">
        <item name="android:windowEnterAnimation">@android:anim/fade_in</item>
        <item name="android:windowExitAnimation">@android:anim/fade_out</item>
    </style>

    <style name="FullScreenTransparentDialog" parent="Theme.FPTPlay">
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/dialog_dim_dark_color</item>
        <item name="android:windowAnimationStyle">@style/FullScreenDialog.Animation</item>
    </style>

    <style name="FullScreenPairingControlTransparentDialog" parent="Theme.FPTPlay">
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/dialog_dim_dark_color</item>
    </style>

    <style name="MultiProfilePinProfileBottomSheetDialogTheme" parent="FullScreenDialog">
        <item name="android:windowSoftInputMode">stateAlwaysHidden</item>
    </style>
    <style name="MultiProfileResetPasswordBottomSheetDialogTheme" parent="FullScreenDialog">
        <item name="android:windowBackground">@color/transparent</item>
    </style>
    <!--Button-->
    <style name="AllButtonStyle">
        <item name="android:textColor">@color/all_button_text_color_state</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:background">@drawable/all_button_state</item>
        <item name="android:textSize">@dimen/all_button_text_size</item>
    </style>

    <style name="RoundButtonStyle">
        <item name="android:background">@drawable/round_button_state</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/app_content_textview_size</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="RoundButtonStyleTablet">
        <item name="android:background">@drawable/round_button_state</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/app_content_textview_size_tablet</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="RoundButtonStyleBookItem">
        <item name="android:background">@drawable/round_button_state_book_item</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/app_content_textview_size</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="AllButtonGley">
        <item name="android:textColor">@color/all_button_text_color_state</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:background">@drawable/all_button_disable</item>
        <item name="android:textSize">@dimen/all_button_text_size</item>
    </style>

    <!--Text-->
    <style name="BaseTextView">
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/_12ssp</item>
        <item name="singleLine">true</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="BaseTabletTextView">
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/_7ssp</item>
        <item name="singleLine">true</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>


    <!--  region from sprint15 use this  -->
    <style name="BaseTextViewV2">
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/base_text_size</item>
        <item name="singleLine">true</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>

        <item name="android:letterSpacing">0.02</item> <!--  Apply for all text view from sprint 15  -->
        <item name="android:lineSpacingExtra">2sp</item> <!--  Apply for all text view from sprint 15  -->
    </style>
    <!--  endregion from sprint15 use this  -->

    <style name="BlockHeaderText">
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/block_header_text_size</item>
        <item name="android:fontFamily">@font/sf_pro_display_bold</item>
    </style>

    <style name="DialogHeaderText">
        <item name="android:maxLines">2</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/header_text_size</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="DialogSubHeaderText">
        <item name="android:textColor">@color/app_sub_content_text_color</item>
        <item name="android:textSize">@dimen/sub_header_text_size</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="BlockSubHeaderText">
        <item name="android:textColor">@color/app_sub_content_text_color</item>
        <item name="android:textSize">@dimen/app_content_textview_size</item>
        <item name="android:fontFamily">@font/sf_pro_display_medium</item>
    </style>

    <style name="BlockSubHeaderTabletText">
        <item name="android:textColor">@color/app_sub_content_text_color</item>
        <item name="android:textSize">@dimen/app_content_textview_tablet_size</item>
        <item name="android:fontFamily">@font/sf_pro_display_medium</item>
    </style>

    <style name="AllContentTextStyle">
        <item name="android:textSize">@dimen/app_content_textview_size</item>
        <item name="android:textColor">@color/app_content_text_color</item>
    </style>

    <style name="TextLive">
        <item name="android:layout_width">@dimen/_25sdp</item>
        <item name="android:layout_height">@dimen/_14sdp</item>
        <item name="android:text">@string/live</item>
        <item name="android:background">@drawable/live_ic</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textAllCaps">true</item>
    </style>


    <!--Snackbar-->
    <style name="SnackbarTextViewTitle">
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textColor">#DEFFFFFF</item>
        <item name="android:textSize">@dimen/_12ssp</item>
    </style>

    <!--QR Code-->
    <style name="RoundedBackgroundBottomSheet" parent="@style/Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/qr_code_bottom_sheet_rounded_background</item>
    </style>

    <style name="RoundedDarkBackgroundBottomSheet" parent="@style/Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/qr_code_link_option_bottom_sheet_round_background</item>
    </style>

    <style name="QRCodeGuideBottomSheetDialogTheme" parent="@style/Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
        <item name="bottomSheetStyle">@style/RoundedBackgroundBottomSheet</item>
    </style>

    <style name="QRCodeLinkBottomSheetDialogTheme" parent="@style/Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
        <item name="bottomSheetStyle">@style/RoundedDarkBackgroundBottomSheet</item>
    </style>

    <style name="CommentBottomSheetTheme" parent="@style/Theme.Design.BottomSheetDialog">
<!--        <item name="bottomSheetStyle">@style/CommentBottomSheetStyle</item>-->
        <item name="android:windowSoftInputMode">stateAlwaysHidden|adjustResize</item>
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:windowIsFloating">false</item>
    </style>

    <style name="MomentTheme" parent="@style/Theme.FPTPlay">
        <item name="android:windowSoftInputMode">adjustNothing</item>
        <item name="android:windowIsFloating">false</item>
    </style>

    <style name="CommentBottomSheetStyle" parent="Widget.Design.BottomSheet.Modal"/>

    <!--TabLayout-->
    <style name="AllTabTextAppearance">
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="AllTabLayoutStyle" parent="Widget.Design.TabLayout">
        <item name="tabTextAppearance">@style/AllTabTextAppearance</item>
        <item name="tabSelectedTextColor">@android:color/white</item>
        <item name="tabMode">scrollable</item>
        <item name="tabTextColor">#99FFFFFF</item>
        <item name="tabIndicatorFullWidth">false</item>
        <item name="tabIndicatorColor">#FE592A</item>
    </style>

    <!--Centered Title Toolbar-->
    <style name="ToolbarTextViewTitle">
        <item name="android:fontFamily">@font/sf_pro_display_medium</item>
        <item name="android:textColor">@color/color_white</item>
        <item name="android:textSize">@dimen/_13ssp</item>
        <item name="singleLine">true</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>

    <!--Block-->
    <style name="BlockPoster" parent="">
        <item name="cornerSize">@dimen/block_item_corner_radius</item>
    </style>

    <style name="BlockPosterGame" parent="">
        <item name="cornerSize">@dimen/block_item_game_corner_radius</item>
    </style>

    <!--Payment package-->
    <style name="PaymentPackage" parent="">
        <item name="cornerSize">@dimen/package_radius</item>
    </style>

    <!-- Dialog -->
    <style name="TransparentDialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="android:windowAnimationStyle">@style/TransparentDialog.Animation</item>
    </style>

    <style name="TransparentDialog.Animation">
        <item name="android:windowEnterAnimation">@android:anim/fade_in</item>
        <item name="android:windowExitAnimation">@android:anim/fade_out</item>
    </style>

    <!--Payment-->
    <style name="PaymentTextAttrs">
        <item name="android:textSize">@dimen/_10ssp</item>
        <item name="android:textColor">@color/white_87</item>
        <item name="android:maxLines">3</item>
        <item name="android:ellipsize">end</item>
        <item name="android:gravity">center_vertical</item>
        <item name="fontFamily">@font/sf_pro_display_medium</item>
    </style>

    <style name="PaymentTextAttrsTablet">
        <item name="android:textSize">@dimen/_6ssp</item>
        <item name="android:textColor">@color/white_87</item>
        <item name="android:maxLines">3</item>
        <item name="android:ellipsize">end</item>
        <item name="android:gravity">center_vertical</item>
        <item name="fontFamily">@font/sf_pro_display_medium</item>
    </style>

    <style name="PaymentRegisterButtonWhite">
        <item name="android:background">@drawable/round_button_state</item>
        <item name="android:textColor">@color/white_87</item>
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="android:textAllCaps">false</item>
        <item name="fontFamily">@font/sf_pro_display_medium</item>
    </style>

    <style name="PaymentRegisterButtonAccent">
        <item name="android:background">@drawable/round_button_pressed</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="PaymentRegisterButtonAccentTablet">
        <item name="android:background">@drawable/round_button_pressed</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/_6ssp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="PaymentTitleText">
        <item name="android:textColor">@color/white_87</item>
        <item name="android:textSize">@dimen/_13ssp</item>
        <item name="android:textStyle">bold</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
    </style>

    <style name="PaymentTitleTextTablet">
        <item name="android:textColor">@color/white_87</item>
        <item name="android:textSize">@dimen/_7ssp</item>
        <item name="android:textStyle">bold</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
    </style>

    <style name="PaymentSuccessDialog" parent="BaseTextView">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
    </style>

    <style name="PaymentSuccessDialog.TextViewMainTitle">
        <item name="android:textSize">20sp</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingTop">24dp</item>
        <item name="android:paddingBottom">32dp</item>
        <item name="android:paddingLeft">32dp</item>
        <item name="android:paddingRight">32dp</item>
    </style>

    <style name="PaymentSuccessDialog.TextViewTitle">
        <item name="android:layout_marginTop">16dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:layout_marginStart">32dp</item>
    </style>

    <style name="PaymentSuccessDialog.TextViewValue">
        <item name="android:gravity">right|end</item>
        <item name="android:textSize">14sp</item>
        <item name="android:layout_marginEnd">32dp</item>
        <item name="android:layout_marginLeft">4dp</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
    </style>

    <style name="PaymentSuccessDialog.TextButton">
        <item name="android:background">?android:attr/selectableItemBackground</item>
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textSize">17sp</item>
        <item name="android:textColor">@color/color_accent</item>
        <item name="android:textStyle">bold</item>
        <item name="android:paddingTop">20dp</item>
        <item name="android:paddingBottom">20dp</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
        <item name="android:gravity">center</item>
    </style>

    <!--Vod Actor-->
    <style name="CircleImageActor" parent="">
        <item name="cornerSize">@dimen/_34sdp</item>
    </style>

    <!--Vod Actor-->
    <style name="CircleImageComment" parent="">
        <item name="cornerSize">@dimen/_14sdp</item>
    </style>

    <style name="CircleUserAvatarComment" parent="">
        <item name="cornerSize">@dimen/vod_comment_normal_avatar_corner</item>
    </style>

    <!--region Airline-->
    <style name="AirlineSwitchLanguage">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_10ssp</item>
    </style>

    <style name="AirlineBottomNavigation" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="android:background">#D9131313</item>
        <item name="itemIconTint">@color/airline_bottom_navigation_item_color_state</item>
        <item name="itemTextColor">@color/airline_bottom_navigation_item_color_state</item>
        <item name="itemHorizontalTranslationEnabled">false</item>
        <item name="labelVisibilityMode">labeled</item>
        <item name="elevation">0dp</item>
        <item name="itemIconPadding">@dimen/_4sdp</item>
    </style>

    <style name="AirlineVodTabLayout">
        <item name="fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_12ssp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="AirlineVodStructureItemTitle">
        <item name="fontFamily">@font/sf_pro_display_light</item>
        <item name="android:textSize">@dimen/_10ssp</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="singleLine">true</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="AirlineVodStructureGroupTitle">
        <item name="fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textColor">@color/app_content_text_color</item>
        <item name="android:textSize">@dimen/_12ssp</item>
    </style>

    <style name="AirlineVodStructureGroupViewMore" parent="AirlineVodStructureItemTitle">
        <item name="android:textColor">@color/app_sub_content_text_color</item>
    </style>

    <style name="ProgressBarHorizontal" parent="Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:indeterminateDrawable">@drawable/all_view_loading_horizontal_drawable</item>
        <item name="android:indeterminate">true</item>
    </style>
    <!--endregion-->

    <!--region Account-->
    <style name="Account.Toolbar.MenuTheme" parent="Theme.FPTPlay">
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_12ssp</item>
        <item name="actionMenuTextColor">#FE5A2A</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <!--region Account-->
    <style name="Account.Toolbar.Tablet.MenuTheme" parent="Theme.FPTPlay">
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_8ssp</item>
        <item name="actionMenuTextColor">#FE5A2A</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="CircleImageViewStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <style name="Account.UpdateInfo.Edittext" parent="Widget.MaterialComponents.TextInputLayout.FilledBox">
        <item name="hintEnabled">false</item>
        <item name="hintTextColor">#61FFFFFF</item>
        <item name="boxBackgroundColor">@android:color/transparent</item>
        <item name="endIconMode">clear_text</item>
        <item name="endIconDrawable">@drawable/ic_clear</item>
        <item name="boxStrokeColor">#1AFFFFFF</item>
        <item name="boxStrokeWidth">1dp</item>
        <item name="boxStrokeWidthFocused">1dp</item>
    </style>

    <style name="Account.PromotionCode.Edittext" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="hintEnabled">false</item>
        <item name="hintTextColor">#61FFFFFF</item>
        <item name="boxBackgroundColor">@color/all_button_disable</item>
        <item name="boxStrokeColor">@android:color/transparent</item>
        <item name="boxCornerRadiusTopStart">@dimen/_6sdp</item>
        <item name="boxCornerRadiusTopEnd">@dimen/_6sdp</item>
        <item name="boxCornerRadiusBottomStart">@dimen/_6sdp</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/_6sdp</item>
        <item name="endIconMode">clear_text</item>
        <item name="endIconDrawable">@drawable/ic_clear_account_promotion_code</item>
        <item name="errorEnabled">true</item>
        <item name="errorTextColor">@color/color_accent</item>
        <item name="errorIconDrawable">@null</item>
        <item name="errorIconTint">@color/color_accent</item>
        <item name="errorTextAppearance">@style/Account.PromotionCode.Edittext.Error</item>
        <item name="boxStrokeErrorColor">@color/color_accent</item>
    </style>

    <style name="Account.PromotionCode.Edittext.Error" parent="BaseTextView">
        <item name="android:textSize">@dimen/_9ssp</item>
    </style>

    <style name="Account.BottomSheetDialogTheme" parent="@style/Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
    </style>

    <style name="AccountDatePickerTheme" >
        <item name="android:textColorPrimary">@color/app_content_text_color</item>
        <item name="colorControlNormal">@color/app_content_text_color</item>
    </style>

    <style name="TransparentBackgroundBottomSheet" parent="@style/Widget.Design.BottomSheet.Modal">
        <item name="android:background">@null</item>
    </style>
    <!--endregion-->

    <!--region Pairing-->
    <style name="PairingRoundedBackgroundBottomSheet" parent="@style/Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/pairing_bottom_sheet_rounded_background</item>
    </style>

    <style name="PairingBottomSheetDialogTheme" parent="@style/Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
        <item name="bottomSheetStyle">@style/PairingRoundedBackgroundBottomSheet</item>
    </style>
    <!-- endregion -->

    <style name="LoyaltyCustomBottomSheetDialog" parent="Theme.FPTPlay">
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/colorBlack60</item>
        <item name="android:windowAnimationStyle">@style/FullScreenDialog.Animation</item>
    </style>

    <style name="MomentRelatedThumbnail" parent="">
        <item name="cornerSizeTopLeft">@dimen/moment_related_item_corner_radius</item>
        <item name="cornerSizeBottomLeft">@dimen/moment_related_item_corner_radius</item>
    </style>

    <style name="MomentPlaylistThumbnail" parent="">
        <item name="cornerSize">@dimen/moment_playlist_item_thumbnail_corner_radius</item>
    </style>

    <!--  region MultiProfile  -->
    <style name="MultiProfileAvatar" parent="">
        <item name="cornerSize">@dimen/multi_profile_profile_item_avatar_corner_radius</item>
    </style>

    <style name="MultiProfile.Toolbar.MenuTheme" parent="Theme.FPTPlay">
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
        <item name="android:textSize">@dimen/_12ssp</item>
        <item name="actionMenuTextColor">#FE5A2A</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <!--  endregion MultiProfile  -->

    <!--  region Report Player  -->
    <style name="ReportButtonStyle">
        <item name="android:textColor">@color/report_button_text_color_state</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:background">@drawable/all_button_state</item>
        <item name="android:textSize">@dimen/all_button_text_size</item>
    </style>

    <!--  endregion Report Player  -->

    <!--  region viewing history  -->

    <style name="MultiProfile.Toolbar.ViewingHistory" parent="Theme.FPTPlay">
        <item name="android:fontFamily">@font/sf_pro_display_semibold</item>
        <item name="android:textSize">@dimen/_11ssp</item>
        <item name="actionMenuTextColor">@color/white_87</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <!--  endregion MultiProfile  -->

    <style name="ViewMoreGameHorizontalSquareItemBlockPoster" parent="">
        <item name="cornerSize">@dimen/item_game_horizontal_square_radius</item>
    </style>


    <!-- region Pladio  -->
    <style name="PladioBottomNavigation" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="android:background">@color/black</item>
        <item name="itemIconTint">@color/pladio_navigation_item_color_state</item>
        <item name="itemTextColor">@color/pladio_navigation_item_color_state</item>
        <item name="itemTextAppearanceActive">@style/TextAppearance.BottomNavigationView.Active</item>
        <item name="itemTextAppearanceInactive">@style/TextAppearance.BottomNavigationView.Inactive</item>
        <item name="itemTextAppearanceActiveBoldEnabled">false</item>
        <item name="itemHorizontalTranslationEnabled">false</item>
        <item name="labelVisibilityMode">labeled</item>
        <item name="elevation"> 0dp</item>
        <item name="itemIconSize">@dimen/pladio_bottom_navigation_item_icon_size</item>
    </style>

    <!-- blank styles for better code readability-->
    <style name="TextAppearance"/>
    <style name="TextAppearance.BottomNavigationView"/>

    <!-- inactive tab icon style -->
    <style name="TextAppearance.BottomNavigationView.Inactive">
        <item name="android:textSize">@dimen/pladio_bottom_navigation_item_text_size</item>
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
    </style>

    <!-- active tab icon style -->
    <style name="TextAppearance.BottomNavigationView.Active">
        <item name="android:textSize">@dimen/pladio_bottom_navigation_item_text_size</item>
        <item name="android:fontFamily">@font/sf_pro_display_regular</item>
    </style>

    <style name="PladioSongThumbStyle" parent="">
        <item name="cornerSize">@dimen/pladio_playback_panel_song_thumb_corner</item>
    </style>

    <style name="PladioSongBarThumbStyle" parent="">
        <item name="cornerSize">@dimen/pladio_playback_bar_song_thumb_corner</item>
    </style>

    <style name="RoundedCornersImageViewStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">@dimen/_6sdp</item>
    </style>
    <!--  endregion Pladio  -->

    <style name="ReportFullScreenDialog" parent="FullScreenDialogDark">
        <item name="android:windowBackground">#CC000000</item>
    </style>

    <style name="SmallTitle" parent="BaseTextView">
        <item name="android:fontFamily">@font/sf_pro_display_bold</item>
        <item name="android:textColor">@color/white_87</item>
        <item name="android:textSize">@dimen/_10ssp</item>
        <item name="android:letterSpacing">0.02</item>
    </style>

    <style name="MediumBody" parent="BaseTextView">
        <item name="android:fontFamily">@font/sf_pro_display_medium</item>
        <item name="android:textColor">@color/white_87</item>
        <item name="android:textSize">@dimen/_10ssp</item>
        <item name="android:letterSpacing">0.02</item>
    </style>

    <style name="FullScreenDialogStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:windowFullscreen">true</item>
    </style>
    <style name="FullDialogFragmentTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowAnimationStyle">@style/DialogAnimation</item>

        <item name="android:windowSoftInputMode">stateAlwaysHidden|adjustResize</item>

    </style>

    <style name="DialogAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_up</item>
        <item name="android:windowExitAnimation">@anim/slide_down</item>
    </style>
</resources>