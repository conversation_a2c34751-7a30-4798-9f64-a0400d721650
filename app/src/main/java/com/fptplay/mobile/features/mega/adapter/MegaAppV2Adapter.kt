package com.fptplay.mobile.features.mega.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import coil.load
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.databinding.MegaAppItemBinding
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenuItem

class MegaAppV2Adapter(private val context: Context) : BaseAdapter<MegaMenuItem, MegaAppV2Adapter.AppViewHolder>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AppViewHolder {
        return AppViewHolder(
            MegaAppItemBinding.inflate(
                LayoutInflater.from(context),
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: AppViewHolder, position: Int) {
        holder.bind(differ.currentList[position])
    }


    inner class AppViewHolder(private val binding: MegaAppItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.setOnClickListener {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        it
                    )
                }
            }
        }
        fun bind(data: MegaMenuItem) {
            binding.apply {
                if(data.img.isNotBlank()) {
                    ivAppIcon.load(data.img)
                } else if(data.imgResId != null) {
                    ivAppIcon.setImageResource(data.imgResId!!)
                } else {
                    ivAppIcon.setImageResource(0)
                }

                tvAppTitle.text = data.title
            }
        }
    }

    companion object {
        const val APP_ITEMS_PER_ROW = 4
    }
}