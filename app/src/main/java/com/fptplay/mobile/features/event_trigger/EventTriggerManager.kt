package com.fptplay.mobile.features.event_trigger

import com.fptplay.mobile.features.event_trigger.EventTriggerUtils.getFrequency
import com.fptplay.mobile.features.event_trigger.EventTriggerUtils.getListEventId
import com.fptplay.mobile.features.event_trigger.EventTriggerUtils.hasSupported
import com.fptplay.mobile.features.event_trigger.EventTriggerUtils.mapToString
import com.fptplay.mobile.features.event_trigger.EventTriggerUtils.parseStringWithPrefix
import com.fptplay.mobile.features.event_trigger.data.EventType
import com.fptplay.mobile.features.event_trigger.listener.EventActionListener
import com.xhbadxx.projects.module.domain.entity.fplay.common.TriggerEvent
import timber.log.Timber

class EventTriggerManager {
    companion object {
        const val TAG = "EventTriggerManager"
    }
    private var config : EventTriggerConfig? = null
    private var actionListener: EventActionListener? = null

    fun setConfig(value: EventTriggerConfig) {
        config = value
    }

    fun setEventActionListener(value: EventActionListener?) {
        actionListener = value
    }

    fun startEventTrigger(events: List<TriggerEvent.Event>) {
        val listId = getListEventId(events)
        val oldListId = config?.getSharedPreferences()?.getTriggerEventID() ?: "" // get old id saved on SharedPreferences
        val meta = config?.getSharedPreferences()?.getMetaEventTriger() ?: ""  // get meta saved on SharedPreferences

        var pair : Pair<String,HashMap<String,Int>> = Pair("", hashMapOf())
        if (meta.isNotBlank()) {
            pair = parseStringWithPrefix(meta) // parse String to Pair<currentId, HashMap<id,count>>
        }
        val listEventWithCount = events.map {
            val count = pair.second[it.eventId] ?: 0
            Pair(it,count)
        }

        checkStartEvent(pair.first, listEventWithCount.toMutableList())
    }

    private fun checkStartEvent(currentId: String, events: MutableList<Pair<TriggerEvent.Event, Int>>) {
        val currentPosition = events.indexOfFirst {
            it.first.eventId == currentId
        }
        // If a high priority event exists then just start it else start next event
        val hasStartEventHighPriority = startEventHighPriority(events)
        if (!hasStartEventHighPriority) {
            // get next Event to Start
            events.nextEventWith(currentPosition)?.let {
                saveMetaEventTrigger(it, events)
                actionListener?.startEvent(it)
            } ?: kotlin.run {
                actionListener?.actionError("event not available or limit reached!")
            }
        }
    }

    private fun startEventHighPriority(
        events: MutableList<Pair<TriggerEvent.Event, Int>>
    ): Boolean {
        var eventStart :TriggerEvent.Event? = null
        if(events.isNotEmpty()) {
            events.forEach { pair ->
                if (EventType.fromType(pair.first.type) == EventType.EVENT_HOT && pair.first.getFrequency() == -1) {
                    eventStart = pair.first
                }
            }
        } else {
            eventStart = null
        }
        eventStart?.let {
            saveMetaEventTrigger(it, events)
            actionListener?.startEvent(it)
            return true
        } ?: kotlin.run {
            return false
        }
    }

    private fun saveMetaEventTrigger(
        current: TriggerEvent.Event,
        list: List<Pair<TriggerEvent.Event, Int>>
    ) {
        val id = getListEventId(list.map {
            it.first
        })
        config?.getSharedPreferences()?.saveTriggerEventID(id)

        val mapIdWithRemaining = hashMapOf<String, Int>()
        list.forEach {
            mapIdWithRemaining[it.first.eventId] = it.second
        }
        val meta = mapToString(current.eventId, mapIdWithRemaining)
        config?.getSharedPreferences()?.saveMetaEventTriger(meta)
    }

    private fun verifyEventAvailable(event: List<Pair<TriggerEvent.Event, Int>>): Boolean {
        var available = false
        return if(event.isNotEmpty()) {
            event.forEach {
                if (it.first.hasSupported() && it.second < it.first.getFrequency()) { // Check if the event is supported and the frequency limit is not reached
                    available = true
                }
            }
            available
        } else false
    }

    private fun MutableList<Pair<TriggerEvent.Event, Int>>.nextEventWith(currentPosition: Int): TriggerEvent.Event? {

        if(!verifyEventAvailable(this)) return null

        var nextPosition = currentPosition + 1
        val nextItem = if (nextPosition >= 0 && nextPosition < this.size) {
            this[nextPosition]
        } else {
            nextPosition = 0
            this.first()
        }

        return if (nextItem.second < nextItem.first.getFrequency() && nextItem.first.hasSupported()) {
            this[nextPosition] = Pair(nextItem.first, nextItem.second + 1)
            nextItem.first
        } else {
            nextEventWith(nextPosition)
        }
    }
}
