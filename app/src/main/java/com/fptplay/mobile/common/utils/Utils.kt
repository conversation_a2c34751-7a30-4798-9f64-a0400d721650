package com.fptplay.mobile.common.utils

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.pm.PackageManager
import android.graphics.Color
import android.graphics.Typeface
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.Uri
import android.os.Build
import android.telephony.TelephonyManager
import android.text.Html
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.util.Patterns
import android.webkit.URLUtil
import android.widget.TextView
import androidx.annotation.DimenRes
import androidx.collection.arrayMapOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import com.clevertap.android.sdk.CleverTapAPI
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.common.extensions.getDisplayWidth
import com.fptplay.mobile.common.extensions.isHostFptPlay
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.utils.DeeplinkUtils.isHostSupportFptPlay
import com.fptplay.mobile.features.ads.utils.AdsUtils
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.features.multi_profile.utils.TrackingLogProfile
import com.fptplay.mobile.player.utils.PlayerPiPHelper
import com.fptplay.mobile.player.utils.isEnableAutoScreenRotationInSettings
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import com.tear.modules.tracking.model.Infor
import com.xhbadxx.projects.module.domain.entity.fplay.common.AutoScrollConfig
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenuItem
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.BlockStyle
import com.xhbadxx.projects.module.domain.entity.fplay.user.UserInfo
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import timber.log.Timber
import java.lang.reflect.InvocationTargetException
import java.net.URL
import java.text.DecimalFormat
import java.text.NumberFormat
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.math.ceil


object Utils {

    // region Stream Header
    const val STREAM_SESSION_KEY = "x-id"
    // endregion

    //region Player Vod/Live/Sport/Download
    const val OPTION_DIALOG_BITRATE = "option_dialog_bitrate"
    const val OPTION_DIALOG_SUBTITLE = "option_dialog_subtitle"
    const val OPTION_DIALOG_EXPAND = "option_dialog_expand"
    const val OPTION_DIALOG_MORE = "option_dialog_more"

    const val OPTION_DIALOG_SPEED = "option_dialog_speed"
    const val OPTION_DIALOG_SPEED_KEY = "option_dialog_speed_key"
    const val OPTION_DIALOG_SPEED_ID_KEY = "option_dialog_speed_id_key"

    const val OPTION_DIALOG_DOWNLOAD_PAUSE = "option_dialog_download_pause"
    const val OPTION_DIALOG_DOWNLOAD_PAUSE_FOR_VOD = "option_dialog_download_pause_for_vod"
    const val OPTION_DIALOG_DOWNLOADED = "option_dialog_downloaded"
    const val OPTION_DIALOG_DOWNLOADED_BUT_EXPIRED = "option_dialog_downloaded_but_expired"
    const val OPTION_DIALOG_DOWNLOADING = "option_dialog_downloading"
    const val OPTION_DIALOG_DOWNLOADING_FOR_VOD = "option_dialog_downloading_for_vod"
    const val OPTION_DIALOG_ERROR = "option_dialog_error"
    const val OPTION_DIALOG_ERROR_FOR_VOD = "option_dialog_error_for_vod"
    const val OPTION_DIALOG_EXPIRED = "option_dialog_expired"
    const val OPTION_DIALOG_EXPIRED_FOR_VOD = "option_dialog_expired_for_vod"
    const val OPTION_DIALOG_DELETE_FROM_DOWNLOAD = "option_dialog_delete_from_download"
    const val OPTION_DIALOG_SET_STORAGE_FOR_DOWNLOAD = "option_dialog_set_storage_for_download"

    const val OPTION_DIALOG_BITRATE_KEY = "option_dialog_bitrate_key"
    const val OPTION_DIALOG_BITRATE_ID_KEY = "option_dialog_bitrate_id_key"
    const val OPTION_DIALOG_BITRATE_POSITION_KEY = "option_dialog_bitrate_position_key"

    const val OPTION_DIALOG_SUBTITLE_KEY = "option_dialog_subtitle_key"
    const val OPTION_DIALOG_SUBTITLE_ID_KEY = "option_dialog_subtitle_id_key"

    const val OPTION_DIALOG_AUDIO_TRACK_KEY = "option_dialog_audio_track_key"
    const val OPTION_DIALOG_AUDIO_TRACK_ID_KEY = "option_dialog_audio_track_id_key"

    const val OPTION_DIALOG_EXPAND_KEY = "option_dialog_expand_key"
    const val OPTION_DIALOG_EXPAND_ID_KEY = "option_dialog_expand_id_key"
    const val OPTION_DIALOG_EXPAND_POS_KEY = "option_dialog_expand_pos_key"

    const val OPTION_DIALOG_DOWNLOAD_KEY = "option_dialog_download_key"
    const val OPTION_DIALOG_DOWNLOAD_ID_KEY = "option_dialog_download_id_key"

    const val OPTION_DIALOG_MORE_KEY = "option_dialog_more_key"
    const val OPTION_DIALOG_MORE_ID_KEY = "option_dialog_more_id_key"

    const val OPTION_DIALOG_USER_REPORT_KEY = "option_dialog_user_report_key"
    const val OPTION_DIALOG_USER_REPORT_STATUS = "option_dialog_user_report_status"
    const val OPTION_DIALOG_USER_REPORT_MESSAGE = "option_dialog_user_report_message"

    //endregion

    //region Live TV
    const val LIVE_TV_SCHEDULE_CHOOSE_DAY = "live_tv_schedule_choose_day"
    const val LIVE_TV_SCHEDULE_CHOOSE_DAY_KEY = "live_tv_schedule_choose_day_key"
    //endregion

    //region Choi Hay Chia
    val LINK_GAME_CHOI_HAY_CHIA: String = "https://fptplay.vn/su-kien/choi-hay-chia"

    const val CHC_UPDATE_INFORMATION_KEY = "update_information_key"
    const val CHC_LOGIN_USERNAME_KEY = "login_username_key"
    const val CHC_LOGIN_CHANNEL_STREAM_KEY = "login_channel_stream_key"
    const val CHC_LOGIN_CHANNEL_URL_KEY = "login_channel_url_key"
    const val CHC_LOGIN_IS_HIDE_KEY = "login_is_hide_key"
    const val CHC_LOGIN_ROOM_KEY = "login_room_key"
    const val CHC_LOGIN_MSG1_KEY = "login_msg1_key"
    const val CHC_LOGIN_MSG2_KEY = "login_msg2_key"
    const val CHC_LOGIN_MSG3_KEY = "login_msg3_key"

    const val DIALOG_CHOI_HAY_CHIA_TYPE_SEND_QUESTION = "0"
    const val DIALOG_CHOI_HAY_CHIA_TYPE_SEND_ANSWER = "15"
    const val DIALOG_CHOI_HAY_CHIA_TYPE_ASK_BETS = "2"
    const val DIALOG_CHOI_HAY_CHIA_TYPE_RESULT_BETS = "16"
    const val DIALOG_CHOI_HAY_CHIA_TYPE_ASK_PLAY_OR_SHARE = "4"
    const val DIALOG_CHOI_HAY_CHIA_TYPE_RESULT_PLAY_OR_SHARE = "5"

    const val CHC_WARNING_DIALOG_BUNDLE_ID = "chc_warning_dialog_bundle_id"
    const val CHC_WARNING_DIALOG_KEY_ID = "chc_warning_dialog_key_id"

    //endregion


    //region Chat
    const val GROUP_CHAT_MESSAGE_TYPE_NOTE = "note"
    const val GROUP_CHAT_MESSAGE_TYPE_TEXT = "text"
    const val GROUP_CHAT_MESSAGE_TYPE_DELETED = "deleted"
    const val GROUP_CHAT_MESSAGE_TYPE_IMAGE = "image"
    //endregion
    // region moment
    const val MOMENT_DETAIL_COMMENT_ARRANGE = "moment_detail_comment_arrange"
    const val MOMENT_DETAIL_COMMENT_ARRANGE_KEY = "moment_detail_comment_arrange_key"
    const val MOMENT_COMMENT_BUNDLE_KEY = "moment_comment_bundle_key"
    const val MOMENT_COMMENT_BUNDLE_ID_KEY = "moment_comment_bundle_id_key"
    const val MOMENT_COMMENT_REQUIRE_LOGIN = "moment_comment_require_login"
    const val MOMENT_COMMENT_BUNDLE_TOTAL_KEY = "moment_comment_bundle_total_key"
    const val MOMENT_SEND_COMMENT_BUNDLE_KEY = "moment_send_comment_bundle_key"
    const val MOMENT_SEND_COMMENT_STATUS_BUNDLE_KEY = "moment_send_comment_status_bundle_key"

    const val MOMENT_COMMENT_ID_KEY = "moment_detail_comment_arrange"
    const val MOMENT_COMMENT_TOTAL_COMMENT_KEY = "moment_detail_comment_arrange"

    const val MOMENT_SELECT_PLAYLIST_EPISODE_BUNDLE_KEY = "moment_select_playlist_episode_bundle_key"
    const val MOMENT_SELECT_PLAYLIST_EPISODE_ID_KEY = "moment_select_playlist_episode_id_key"

    const val MOMENT_SELECTED_BUNDLE_KEY = "moment_selected_bundle_key"
    const val MOMENT_SELECTED_EPISODE_ID_KEY = "moment_selected_episode_id_key"

    //endregion
    const val VOD_DETAIL_COMMENT_ARRANGE = "vod_detail_comment_arrange"
    const val VOD_DETAIL_COMMENT_ARRANGE_KEY = "vod_detail_comment_arrange_key"

    const val VOD_DETAIL_TRACK_ARRANGE = "vod_detail_track_arrange"
    const val VOD_DETAIL_TRACK_ARRANGE_KEY = "vod_detail_track_arrange_key"

    const val VOD_DETAIL_TRAILER_ARRANGE = "vod_detail_trailer_arrange"
    const val VOD_DETAIL_TRAILER_ARRANGE_KEY = "vod_detail_trailer_arrange_key"

    const val VOD_ACTOR_DES = "vod_actor_des"
    const val VOD_ACTOR_DES_KEY = "vod_actor_des_key"

    const val VOD_ACTOR_FILM = "vod_actor_film"
    const val VOD_ACTOR_FILM_KEY = "vod_actor_film_key"

    const val VOD_PLAY_TYPE_TRAILER = "vod_play_type_trailer"
    const val VOD_PLAY_TYPE_SESSION = "vod_play_type_session"
    const val VOD_PLAY_TYPE_RELATED = "vod_play_type_related"
    const val VOD_PLAY_TYPE_NEXT_MOVIE = "vod_play_type_next_movie"
    const val VOD_PLAY_TYPE_NEXT_VOD_PLAYLIST = "vod_play_type_next_vod_playlist"

    // region Download

    // region Download Option Dialog Result Key
    const val DOWNLOAD_OPTION_DIALOG_DELETE_TYPE = "download_option_dialog_delete_type"

    const val DOWNLOAD_OPTION_DIALOG_DOWNLOAD_TYPE = "download_option_dialog_download_type"
    const val DOWNLOAD_OPTION_DIALOG_OPTION_WATCH_NOW_ID = "download_option_dialog_option_watch_now_id"
    const val DOWNLOAD_OPTION_DIALOG_OPTION_DOWNLOAD_ID = "download_option_dialog_option_download_id"

    const val DOWNLOAD_OPTION_DIALOG_STORAGE_TYPE = "download_option_dialog_storage_type"
    const val DOWNLOAD_OPTION_DIALOG_OPTION_EXTERNAL_STORAGE_ID = "download_option_dialog_option_external_storage_id"
    const val DOWNLOAD_OPTION_DIALOG_OPTION_INTERNAL_STORAGE_ID = "download_option_dialog_option_internal_storage_id"

    const val DOWNLOAD_OPTION_DIALOG_CHOSEN_OPTION_ID_KEY = "download_option_dialog_chosen_option_id_key"

    const val CHOOSE_IMAGE_OPTION_DIALOG_FRONT_TYPE = "choose_image_option_dialog_front_type"
    const val CHOOSE_IMAGE_OPTION_DIALOG_BACK_TYPE = "choose_image_option_dialog_back_type"

    const val CHOOSE_IMAGE_OPTION_DIALOG_KEY = "choose_image_option_dialog_key"
    const val CHOOSE_IMAGE_OPTION_DIALOG_TAKE_PHOTO = "choose_image_option_dialog_take_photo_id"
    const val CHOOSE_IMAGE_OPTION_DIALOG_GALLERY = "choose_image_option_dialog_gallery_id"

    // notification status loyalty ekyc
    const val TYPE_SUCCESS = "type_success"
    const val TYPE_ERROR = "type_error"
    const val TYPE_ACCESS = "type_access"
    const val TYPE_CLOSE = "type_close"
    const val TYPE_RETRY = "type_retry"
    const val TYPE_AGREE = "type_agree"
    const val TYPE_EXIT = "type_exit"
    const val TYPE_UNDERSTAND = "type_understand"

    const val EKYC_TAKE_IMAGE_KEY = "ekyc_take_image_key"
    const val EKYC_TAKE_IMAGE_FRONT_URI = "ekyc_take_image_front_uri"
    const val EKYC_TAKE_IMAGE_URI = "ekyc_take_image_uri"
    const val EKYC_TAKE_IMAGE_BACK_URI = "ekyc_take_image_back_uri"
    const val EKYC_TAKE_IMAGE_TYPE = "ekyc_take_image_type"

    const val EKYC_OPTION_TYPE = "ekyc_option_type"
    const val EKYC_OPTION_KEY = "ekyc_option_key"
    const val EKYC_OPTION_RETRY = "ekyc_option_retry"

    const val USE_OPTION_EXTERNAL_STORAGE = "external"
    const val USE_OPTION_INTERNAL_STORAGE = "internal"

    const val USE_SIZE_KB = "kb"
    const val USE_SIZE_MB = "mb"
    const val USE_SIZE_GB = "gb"
    const val USE_SIZE_TB = "tb"
    // endregion Download Option Dialog Result Key

    // region Download Finish Result Key
    const val DOWNLOAD_FINISH_NAVIGATE_LIBRARY = "download_finish_navigate_library"
    // endregion Download Finish Result Key

    // endregion Download
    const val EKYC_DATE_PICKER_TYPE = "date_picker_type"
    const val BIRTH_DAY_DATE_PICKER = "rirth_day"
    const val DATE_TO_PICKER = "to"
    const val DATE_FROM_PICKER = "from"

    // region Pairing Control
    const val PAIRING_DIALOG_TYPE = "pairing_dialog_type"
    const val PAIRING_DIALOG_TYPE_KEY = "pairing_dialog_type_key"
    const val PAIRING_CONTROL_QR_CODE = "pairing_dialog_qr_code"
    const val PAIRING_CONTROL_QR_CODE_KEY = "pairing_dialog_qr_key"
    const val PAIRING_CONTROL_NAVIGATE_TYPE = "pairing_dialog_navigate_type"
    const val PAIRING_CONTROL_NAVIGATE_TYPE_KEY = "pairing_dialog_navigate_type_key"
    // endregion

    //region Payment
    const val PAYMENT_TRANSACTION = "payment_transaction"
    const val PAYMENT_TRANSACTION_KEY = "payment_transaction_key"
    //endregion

    // region multi profile
    const val PROFILE_CHANGED_EVENT = "profile_type_changed"
    const val PROFILE_ONBOARD_SWITCH_PROFILE_CHANGED_EVENT = "profile_onboard_switch_profile_changed"
    const val PROFILE_ONBOARD_SWITCH_PROFILE_PIN_CHANGED_EVENT = "profile_onboard_switch_profile_pin_changed"

    const val PROFILE_ONBOARD_DISMISS_CHANGED_EVENT = "profile_onboard_dismiss_changed"

    const val PROFILE_TYPE_OLD = "profile_type_old"
    const val PROFILE_TYPE_NEW = "profile_type_new"
    const val PROFILE_ID_OLD = "profile_id_old"
    const val PROFILE_ID_NEW = "profile_id_new"
    const val PROFILE_CHANGE_SOURCE = "profile_type_change_source"

    const val PROFILE_REFRESH_LIST_PROFILE_EVENT = "profile_refresh_list_profile_event"
    const val PROFILE_REFRESH_LIST_MANAGE_PROFILE_EVENT = "profile_refresh_list_manage_profile_event"
    const val PROFILE_REFRESH_PROFILE_DETAIL_EVENT = "profile_refresh_profile_detail_event"
    const val PROFILE_HAVE_CHANGE_DATA = "profile_have_change_data"
    const val PROFILE_UPDATE_PIN_EVENT = "profile_update_pin_event"
    const val PROFILE_UPDATE_PIN_VALUE = "profile_update_pin_value"
    const val PROFILE_UPDATE_NAME_EVENT = "profile_update_name_event"
    const val PROFILE_UPDATE_NAME_VALUE = "profile_update_name_value"
    const val PROFILE_UPDATE_AVATAR_EVENT = "profile_update_avatar_event"
    const val PROFILE_UPDATE_AVATAR_URL = "profile_update_avatar_url"
    const val PROFILE_UPDATE_AVATAR_ID = "profile_update_avatar_id"

    // deeplink not supported dialog
    const val DEEPLINK_NOT_SUPPORTED_CONFIRM_EVENT = "deeplink_not_supported_confirm_event"

    // endregion multi profile

    const val RATING_EVENT = "rating_event"
    const val RATING_RESULT_EVENT = "rating_result_event"
    const val RATING_NEED_RELOAD = "rating_need_reload"
    const val RATING_EXIT = "rating_exit"

    fun clearUserData(sharedPreferences: SharedPreferences, clearSaleMode: Boolean = false) {
        sharedPreferences.apply {
            // clear user info
            saveUserId("")
            saveUserAvatar("")
            saveDisplayName("")
            saveUserPhone("")
            saveUserLogin(false)
            saveUserSession(0)
            saveRevision("")
            setLinkingToken("")
            // clear token session
            saveAccessToken("")
            saveAccessTokenType("")

            //Sale mode
            if(clearSaleMode) {
                saveEnableSalesMode(false)
            }

            // clear user ads type
            AdsUtils.saveUserType(sharedPreferences, haveDisAdsPackage = false)

            // clear profile info
            MultiProfileUtils.clearCurrentProfile(this)

            // Picture in Picture
            PlayerPiPHelper.saveSupportPictureInPicture(sharedPreferences, isSupport = false)

        }
        MainApplication.INSTANCE.isOpenOnBoardingAndWithProfile = true
        TrackingUtil.clearUserDataForLog()
        clearDataItm(sharedPreferences, MainApplication.INSTANCE.trackingInfo)
    }

    fun saveUserInfo(sharedPreferences: SharedPreferences, userInfo: UserInfo) {
        sharedPreferences.apply {
            saveUserLogin(true)

            // save user info
            val time = System.currentTimeMillis()
            saveUserId(userInfo.id ?: "")
            saveUserAvatar(userInfo.avatar ?: "")
            saveDisplayName(userInfo.name ?: "")
            saveUserPhone(userInfo.phone ?: "")

            // save profile info
            MultiProfileUtils.saveCurrentProfile(this, userInfo.profile)
            //when start app or login account and profile has loaded -> set profile session, profile id and send tracking log start
            TrackingUtil.saveProfileForLog(userInfo.profile)
            TrackingLogProfile.sendLogLoginProfileWhenStart()
        }

    }

    fun convertStringToInt(text: String?, defaultValue: Int): Int {
        var result = defaultValue
        if (!text.isNullOrEmpty()) {
            try {
                result = Integer.parseInt(text)
                return result
            } catch (e: Exception) {
                e.printStackTrace()
                return result
            }
        }
        return defaultValue
    }

    fun convertStringToDouble(text: String?, defaultValue: Double): Double {
        var result = defaultValue
        if (!text.isNullOrEmpty()) {
            try {
                result = text.toDouble()
                return result
            } catch (e: Exception) {
                e.printStackTrace()
                return result
            }
        }
        return defaultValue
    }

    fun convertStringToLong(text: String?, defaultValue: Long): Long {
        if (!text.isNullOrEmpty()) {
            return try {
                text.toLongOrNull() ?: 0L
            } catch (e: Exception) {
                e.printStackTrace()
                defaultValue
            }
        }
        return defaultValue
    }

    fun convertStringToBoolean(text: String?, defaultValue: Boolean): Boolean {
        if (!text.isNullOrEmpty()) {
            return try {
                text.toBoolean()
            } catch (e: Exception) {
                e.printStackTrace()
                defaultValue
            }
        }
        return defaultValue
    }

    fun convertStringToFloat(text: String?, defaultValue: Float): Float {
        if (!text.isNullOrEmpty()) {
            return try {
                text.toFloatOrNull() ?: defaultValue
            } catch (e: Exception) {
                e.printStackTrace()
                defaultValue
            }
        }
        return defaultValue
    }

    fun getImageSize(context: Context, itemInRow: Int = 1, ratioWH: Double): Array<Int> {
        val result = Array(2) { 0 }
        result[0] = (context.getDisplayWidth() / itemInRow * 0.7).toInt()
        result[1] = (result[0] / ratioWH).toInt()
        return result
    }

    fun getSizeInPixel(context: Context, @DimenRes resId: Int, reducePercent: Float = 0.7f): Int {
        return (context.resources.getDimensionPixelSize(resId) * reducePercent).toInt()
    }

    fun useVerticalImage(type: String): Boolean {
        return when (type) {
            BlockStyle.VerticalSliderMedium.id,
            BlockStyle.VerticalSliderSmall.id,
            BlockStyle.NumericRank.id,
            -> true
            else -> false
        }
    }


    fun convertByteToGigabyte(bytes: Long): String {
        val formatter: NumberFormat = DecimalFormat("#0.00")
        return if (bytes > 0) {
            formatter.format(bytes / 1024.0f / 1024.0f / 1024.0f) + " Gb"
        } else {
            "0 Gb"
        }
    }

    fun convertByteToCompatibleFormat(bytes: Long): String {
        val formatter: NumberFormat = DecimalFormat("#0.0")
        val KB = 1024.0f
        val MB = KB.times(KB)
        val GB = MB.times(KB)
        val TB  = GB.times(KB)

        return if (bytes >= TB) {
            formatter.format(bytes / TB) + " TB"
        } else if(bytes >= GB) {
            formatter.format(bytes / GB) + " GB"
        }else if(bytes >= MB) {
            formatter.format(bytes / MB) + " MB"
        }else if(bytes >= KB) {
            formatter.format(bytes / KB) + " KB"
        } else {
            "0 KB"
        }
    }

    fun convertByteToCompatibleFormatReturnForLong(bytes: Long,isSize : String): Long {
        val KB : Long = 1024
        val MB = KB.times(KB)
        val GB = MB.times(KB)
        val TB  = GB.times(KB)

        return when(isSize){
            USE_SIZE_KB -> bytes / KB
            USE_SIZE_MB -> bytes / MB
            USE_SIZE_GB -> bytes / GB
            USE_SIZE_TB -> bytes / TB
            else -> bytes / MB
        }
    }

    // region OmniShop
    fun parseOmniShopUrl(dataJson: String?): String? {
        if (dataJson == null || dataJson.isEmpty()) return null
        return try {
            var url: String? = null
            val data: JsonObject = JsonParser.parseString(dataJson).asJsonObject
            if (data.has("url")) {
                val urlValue: JsonElement = data.get("url")
                if (urlValue.isJsonPrimitive && urlValue.asJsonPrimitive.isString) {
                    url = urlValue.asString
                }
            }
            url
        } catch (e: Exception) {
            Timber.e(e, "parseOmniShopUrl")
            null

        }
    }

//    fun buildOmniDialogBundle(
//        url: String?,
//        includeParams: Boolean,
//        jsonData: String?,
//        sharedPreferences: SharedPreferences?
//    ): Bundle? {
//        if (url == null || url.isEmpty()) return null
//        val data = Bundle()
//        val userId = sharedPreferences?.userId()
//        val phone = sharedPreferences?.userPhone()
//        val omniShopUrl = buildOmniShopLink(url, includeParams, userId, phone)
//        return data
//    }

    fun buildOmniShopLink(
        url: String,
        includeParams: Boolean,
        userId: String,
        userPhone: String
    ): String {
        return if (includeParams) "$url?userid=$userId&phone=$userPhone" else url
    }
    // endregion OmniShop

    //region check network
    fun checkNetWorkType(context: Context): NetworkType {
        var result = NetworkType.UNKNOWN
        val connectivityManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val networkCapabilities = connectivityManager.activeNetwork ?: return NetworkType.NO_NETWORK
            val actNw =
                connectivityManager.getNetworkCapabilities(networkCapabilities) ?: return NetworkType.NO_NETWORK
            result = when {
                actNw.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkType.WIFI
                actNw.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> NetworkType.MOBILE
                actNw.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> NetworkType.ETHERNET
                else -> NetworkType.UNKNOWN
            }
        } else {
            connectivityManager.run {
                connectivityManager.activeNetworkInfo?.run {
                    result = when (type) {
                        ConnectivityManager.TYPE_WIFI -> NetworkType.WIFI
                        ConnectivityManager.TYPE_MOBILE -> NetworkType.MOBILE
                        ConnectivityManager.TYPE_ETHERNET -> NetworkType.ETHERNET
                        else -> NetworkType.UNKNOWN
                    }

                }
            }
        }

        return result
    }

    fun useMobileData(context: Context?): Boolean {
        if (context == null) return false
        return Utils.checkNetWorkType(context) == Utils.NetworkType.MOBILE
    }

    enum class NetworkType {
        WIFI, MOBILE, ETHERNET, UNKNOWN, NO_NETWORK
    }
    //endregion


    //region SIM card

    fun totalSimInMobileDevice(context: Context?): Int {
        if(context == null) return 1
        val tm = context.getSystemService(Context.TELEPHONY_SERVICE) as? TelephonyManager
        val phoneCount = if (tm != null) {
            if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                 tm.activeModemCount

            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                tm.phoneCount
            } else {
                1
            }
        } else {
            1
        }
        return phoneCount
    }

    fun getPositionSimOpen3G() : Int {
        val property = getProperty("gsm.network.type")
        if (property != null) {
            val listTypeSim: Array<String?> = property.split(",".toRegex()).toTypedArray()
            for (i in listTypeSim.indices) {
                if (listTypeSim[i] != null) {
                    val simItem = listTypeSim[i]!!.uppercase(Locale.getDefault())
                    if (simItem.contains("HSPAP")
                        || simItem.contains("HSPA+")
                        || simItem.contains("HSDPA")
                        || simItem.contains("HSUPA")
                        || simItem.contains("HSPA")
                        || simItem.contains("EHRPD")
                        || simItem.contains("EVDO_B")
                        || simItem.contains("EVDO_0")
                        || simItem.contains("EVDO_A")
                        || simItem.contains("UMTS")
                        || simItem.contains("WCDMA")
                        || simItem.contains("LTE")          //4G
                        || simItem.contains("NR")               //5G
                    ) {
                        return i
                    }
                }
            }
        }
        return -1

    }

    fun getSimCode(positon: Int, countSim: Int): String? {
        var simCode = ""
        val property = getProperty("gsm.operator.numeric")
        if (property != null) {
            simCode = if (countSim == 1) {
                property
            } else {
                property.split(",".toRegex()).toTypedArray()[positon]
            }
        }
        return simCode
    }


    @SuppressLint("PrivateApi")
    private fun getProperty(envName: String): String? {
        try {
            val systemPropertiesClass = Class.forName("android.os.SystemProperties")
            val getMethod = systemPropertiesClass.getMethod(
                "get", *arrayOf<Class<*>>(
                    String::class.java
                )
            )
            return getMethod.invoke(null, envName) as String
        } catch (e: ClassNotFoundException) {
            e.printStackTrace()
        } catch (e: NoSuchMethodException) {
            e.printStackTrace()
        } catch (e: SecurityException) {
            e.printStackTrace()
        } catch (e: IllegalAccessException) {
            e.printStackTrace()
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
        } catch (e: InvocationTargetException) {
            e.printStackTrace()
        }
        return null
    }

    //endregion SIM card

    //region String util
    fun getPriceMonthly(price: String): SpannableStringBuilder {
        val newPrice = addSeparator(price)
        val str = SpannableStringBuilder("$newPrice\nVND/1 tháng")
        str.setSpan(StyleSpan(Typeface.BOLD), 0, newPrice.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        return str
    }

    fun setTextApplyContract(color: String, color2: String, text1: String, text2: String): SpannableStringBuilder {
        val fullString = "$text1: ${text2}đ"
        val str = SpannableStringBuilder("$text1: ${text2}đ")
        str.setSpan(StyleSpan(Typeface.NORMAL), 0, text1.length + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        str.setSpan(ForegroundColorSpan(Color.parseColor(color)), 0, text1.length + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        str.setSpan(StyleSpan(Typeface.BOLD), text1.length + 2, fullString.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        str.setSpan(ForegroundColorSpan(Color.parseColor(color2)), text1.length + 2, fullString.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        return str
    }

    fun addSeparator(price: String): String {
        var s = ""
        try {
            s = String.format("%,d", price.toLong()).replace(",", ".")
        } catch (e: NumberFormatException) {
            e.printStackTrace()
        }
        return s
    }
    //endregion

    //endregion
    //region clever tap
    fun pushUserProfileCleverTap(context: Context?, userId: String?) {
        try {
            if (context != null && CheckValidUtil.checkValidString(userId)) {
                val profileUpdate = HashMap<String, Any?>()
                profileUpdate["Identity"] = userId
                profileUpdate["AndroidLatestActive"] = Date()
                CleverTapAPI.getDefaultInstance(context.applicationContext)?.onUserLogin(profileUpdate)
            }
        } catch (ex: java.lang.Exception) {
            Timber.e(ex)
        }
    }

    fun pushUserProfileCleverTap(context: Context?, sharedPreferences: SharedPreferences) {
        if (context != null && sharedPreferences.userLogin()) {
            pushUserProfileCleverTap(context, sharedPreferences.userId())
        }
    }
    //endregion

    fun fromHtml(tv: TextView, string: String) {
        tv.text = if (Build.VERSION.SDK_INT >= 24)
            Html.fromHtml(string, Html.FROM_HTML_MODE_LEGACY)
        else
            Html.fromHtml(string)
    }


    fun getRetryCountDownByRetryStepMs(retryStep : Int) : Long {
        return getRetryCountDownOriginalTimeMs(retryStep = retryStep) + getRetryCountdownPaddingTimeMs()
    }

    private fun getRetryCountDownOriginalTimeMs(retryStep: Int): Long {
        return when (retryStep) {
            0,
            1,
            2 -> 3_000L
            3 -> 6_000L
            4 -> 30_000L
            5 -> 60_000L
            6 -> 300_000L
            7 -> 600_000L
            else -> 900_000L
        }
    }

    private fun getRetryCountdownPaddingTimeMs(): Long {
        return (3..10).random() * 1000L
    }

    // region methods
    fun hasPermission(context: Context, permission: String?): Boolean {
        permission?.run {
            val result = context.checkCallingOrSelfPermission(permission)
            return result == PackageManager.PERMISSION_GRANTED
        }
        return true
    }
    //end region

    fun isEventEnd(endTime: Long): Boolean {
        val gmtOfCurrentTimeZone = TimeZone.getDefault().rawOffset.toLong() //milliseconds.
        val endTimeTimezone = endTime * 1000 + gmtOfCurrentTimeZone
        return endTimeTimezone <= (System.currentTimeMillis() + gmtOfCurrentTimeZone)
    }

    // region AppConfig
    //    private fun Int.isSettingEnabled(): Boolean = BuildConfig.DEBUG || this == settingEnableData
    fun Int.isSettingEnabled(): Boolean = this == settingEnableData
    private const val settingEnableData = 1

    // endregion AppConfig

    // region Mega Menu
    fun MegaMenuItem.isActive(): Boolean = this.status == "1"
    fun MegaMenuItem.requireLogin(): Boolean = this.requiredLogin == "1"
    // enderegion Mega Menu

    fun timeLateFromStartEvent(startTime: Long): Long {
        if (startTime == 0L) return 0L
        val gmtOfCurrentTimeZone = TimeZone.getDefault().rawOffset.toLong() //milliseconds.
        val startTimeTimezone = startTime * 1000 + gmtOfCurrentTimeZone
        return (System.currentTimeMillis() + gmtOfCurrentTimeZone) - startTimeTimezone
    }

    //trangttm5 - optimize function - thoi gian tra ve tu server va System.currentTimeMillis deu la GMT 0
    fun getTimeToStartEvent(startTime: Long) : Long {
        if (startTime == 0L) return 0L
//        val gmtOfCurrentTimeZone = TimeZone.getDefault().rawOffset.toLong() //milliseconds.
//        val startTimeTimezone = startTime * 1000 + gmtOfCurrentTimeZone
//        return startTimeTimezone - (System.currentTimeMillis() + gmtOfCurrentTimeZone)
        return startTime * 1000 - System.currentTimeMillis()
    }

    //trangttm5 - optimize function - thoi gian tra ve tu server va System.currentTimeMillis deu la GMT 0
    fun getTimeToEndEvent(endTime: Long) : Long {
        if (endTime == 0L) return 0L
//        val gmtOfCurrentTimeZone = TimeZone.getDefault().rawOffset.toLong() //milliseconds.
//        val endTimeTimezone = endTime * 1000 + gmtOfCurrentTimeZone
//        return endTimeTimezone - (System.currentTimeMillis() + gmtOfCurrentTimeZone)
        return endTime * 1000 - System.currentTimeMillis()
    }

    //trangttm5 - optimize function - thoi gian tra ve tu server va System.currentTimeMillis deu la GMT 0
    // State chuyển sang tính theo second thay vi miliseconds
    fun getTimeLiveState(beginTime: Long, endTime: Long) : Int {
        val currentTimezone = (ceil(System.currentTimeMillis().toDouble()/1000)).toLong()
        return if (currentTimezone >= endTime) 3   // Program end
            else if (currentTimezone >= beginTime) 2  // Program live
            else 1                                    // InComming
    }

    fun isEventStart(beginTime: String?): Boolean {
        val startTime = beginTime?.toLongOrNull()
        return startTime?.let { st ->
            val gmtOfCurrentTimeZone = TimeZone.getDefault().rawOffset.toLong() //milliseconds.
            val beginTimeTimezone = st * 1000 + gmtOfCurrentTimeZone
            return beginTimeTimezone <= (System.currentTimeMillis() + gmtOfCurrentTimeZone)
        } ?: true

    }

    val isTablet get() = MainApplication.INSTANCE.applicationContext.isTablet()

    /*fun set state portrait when show Login Dialog and Pause -> when hide resume*/
    fun setStateWhenShowLogin(fragmentListener: Fragment?, onShowListener: OnShowLoginListener){
        fragmentListener?.apply {
            if(activity?.isTablet() == false) {
                setFragmentResultListener(Constants.LOGIN_SHOW) { _, bundle ->
                    val isShow = bundle.getBoolean(Constants.LOGIN_SHOW, false)
                    if (isShow) {
                        activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                        onShowListener.onShow()
                    } else {
                        if (activity?.isEnableAutoScreenRotationInSettings() == true) {
                            activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR
                        }
                        onShowListener.onHide()
                    }
                }
            }
        }
    }

    fun getHeaderForPlayerRequest(streamSession: String) =
        if (streamSession.isNotBlank()) arrayMapOf(Pair(STREAM_SESSION_KEY, streamSession)) else arrayMapOf()

    // region Utm

    fun getLocalDataUtm(sharedPreferences: SharedPreferences, trackingInfo: Infor?): Pair<String, String> {
        // if utm deeplink save > 30 days, clear data
        val timeLimit = TimeUnit.DAYS.toMillis(Constants.INTERVAL_TIME_TO_SHOW_SAVE_UTM_DEEPLINK_IN_DAYS)
        val lastTimeSave = MainApplication.INSTANCE.sharedPreferences.lastTimeSaveUtmDeeplink()
        val currentTime = System.currentTimeMillis()
        return if(lastTimeSave <= 0 || (currentTime - lastTimeSave) >= timeLimit) {
            // if utm deeplink save > 30 days, clear data
            clearDataUtm(MainApplication.INSTANCE.sharedPreferences, trackingInfo)
            Pair("", "")
        } else {
            trackingInfo?.apply {
                updateUtm(sharedPreferences.utmDeeplink())
                updateUtmSession(sharedPreferences.utmSession())
                updateIsUtmInApp(if(sharedPreferences.isUtmInApp()) "1" else "0")
            }
            Pair(sharedPreferences.utmAffiliateSource(), sharedPreferences.utmTrafficId())
        }
    }
    fun clearDataUtm(sharedPreferences: SharedPreferences, trackingInfo: Infor?) {
        sharedPreferences.apply {
            // big data
            saveUtmDeeplink("")
            saveIsUtmInApp(false)
            saveUtmSession(-1)

            // BE
            updateLastTimeSaveUtmDeeplink(-1)
            saveUtmAffiliateSource("")
            saveUtmTrafficId("")
        }

        trackingInfo?.apply {
            updateUtm("")
            clearUtmSession()
            updateIsUtmInApp("")
        }
    }

    fun checkAndSaveUTM(deeplink: String, isDeeplinkCalledInApp: Boolean = false) {
        Logger.d("AppUTM > checkAndSaveUTM deeplink: $deeplink isDeeplinkCalledInApp: $isDeeplinkCalledInApp")
        val uri = try {
            Uri.parse(deeplink)
        } catch (e: Exception) {
            null
        }
        val host = uri?.host
        val trackingInfo = MainApplication.INSTANCE.trackingInfo

        val utmSource = uri?.getQueryParameter("utm_source")
        if((host.isHostFptPlay() || (host?.isHostSupportFptPlay() == true)) && !utmSource.isNullOrBlank()) {
            val utmSession = System.currentTimeMillis()
            MainApplication.INSTANCE.sharedPreferences.apply {
                // big data
                saveUtmDeeplink(deeplink)
                saveIsUtmInApp(isDeeplinkCalledInApp)
                saveUtmSession(utmSession)

                // BE
                saveUtmAffiliateSource(utmSource)
                updateLastTimeSaveUtmDeeplink(utmSession)
                saveUtmTrafficId(deeplink)
            }
            trackingInfo.apply {
                updateUtm(deeplink)
                updateUtmSession(utmSession)
                updateIsUtmInApp(if(isDeeplinkCalledInApp) "1" else "0")
            }

        } else {
            // if utm deeplink save > 30 days, clear data
            checkAndClearUtm()
        }
        Timber.e("AppUTM > utmSource $utmSource")
    }

    fun checkAndClearUtm() {
        // if utm deeplink save > 30 days, clear data
        val timeLimit = TimeUnit.DAYS.toMillis(Constants.INTERVAL_TIME_TO_SHOW_SAVE_UTM_DEEPLINK_IN_DAYS)
        val lastTimeSave = MainApplication.INSTANCE.sharedPreferences.lastTimeSaveUtmDeeplink()
        val currentTime = System.currentTimeMillis()
        Logger.d("AppUTM > checkAndClearUtm lastTimeSave: $lastTimeSave currentTime: $currentTime timeLimit: $timeLimit")
        if(lastTimeSave <= 0 || (currentTime - lastTimeSave) >= timeLimit) {
            // if utm deeplink save > 30 days, clear data
            clearUtmData()
        }
    }

    fun clearUtmData() {
        clearDataUtm(MainApplication.INSTANCE.sharedPreferences, MainApplication.INSTANCE.trackingInfo)
        Logger.d("AppUTM > Clear utm data")
    }
    // endregion Utm

    // region itm
    fun getLocalDataItm(sharedPreferences: SharedPreferences, trackingInfo: Infor?): Pair<String, Long> {
        // if utm deeplink save > time limit in config (3 days), clear data
        val timeLimit = TimeUnit.SECONDS.toMillis(sharedPreferences.timelineExpireItmSession())
        val lastTimeSave = MainApplication.INSTANCE.sharedPreferences.itmSession()
        val currentTime = System.currentTimeMillis()
        return if(lastTimeSave <= 0 || (currentTime - lastTimeSave) >= timeLimit) {
            // if utm deeplink save > time limit in config (3 days), clear data
            clearDataItm(MainApplication.INSTANCE.sharedPreferences, trackingInfo)
            Pair("", 0L)
        } else {
            trackingInfo?.apply {
                updateItm(sharedPreferences.itmSource())
                updateItmSession(sharedPreferences.itmSession())
            }
            Pair(sharedPreferences.itmSource(), sharedPreferences.itmSession())
        }
    }
    fun clearDataItm(sharedPreferences: SharedPreferences, trackingInfo: Infor?) {
        sharedPreferences.apply {
            // big data
            saveItmSource("")
            saveItmSession(-1)
        }

        trackingInfo?.apply {
            updateItm("")
            clearItmSession()
        }
    }
    fun saveItmData(itmSource: String) {
        if(itmSource.isNotBlank()) {
            val itmSession = System.currentTimeMillis()
            MainApplication.INSTANCE.sharedPreferences.apply {
                saveItmSource(itmSource)
                saveItmSession(itmSession)
            }
            MainApplication.INSTANCE.trackingInfo.apply {
                updateItm(itmSource)
                updateItmSession(itmSession)
            }
        } else {
            getLocalDataItm(MainApplication.INSTANCE.sharedPreferences, MainApplication.INSTANCE.trackingInfo)
        }
    }
    // endregion itm
    //region application
    fun restartApp(context: Context) {
        val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)
        intent?.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        intent?.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
        android.os.Process.killProcess(android.os.Process.myPid())
    }
    //endregion application

    fun getCurrentAutoScrollStatus(sharedPreferences: SharedPreferences, forceOffAutoScroll: Boolean = false): AutoScrollConfig {
        val currentSelected = AutoScrollConfig.values().find { it.rawValue == sharedPreferences.getCurrentVideoAutoScrollConfigureSelected() }
            ?: AutoScrollConfig.Unset
        Timber.tag("tam-mega").i("getCurrentAutoScrollStatus currentSelected ${currentSelected} forceOffAutoScroll: $forceOffAutoScroll")

        return if(forceOffAutoScroll) {
            AutoScrollConfig.Off
        } else {
            if(currentSelected == AutoScrollConfig.Unset) {
                Timber.tag("tam-mega").i("getCurrentAutoScrollStatus sharedPreferences.getVideoAutoScrollDefaultValue() ${sharedPreferences.getVideoAutoScrollDefaultValue()}")

                AutoScrollConfig.values().find { it.rawValue == sharedPreferences.getVideoAutoScrollDefaultValue() }
                    ?: AutoScrollConfig.On
            } else {
                currentSelected
            }
        }
    }

    fun isEmulator(): Boolean {
        // Check Build.PRODUCT, Build.MANUFACTURER, Build.MODEL, etc.
        return (Build.PRODUCT.contains("sdk") ||
                Build.PRODUCT.contains("genymotion") ||
                Build.PRODUCT.contains("emulator") ||
                Build.PRODUCT.contains("google_sdk") ||
                Build.MANUFACTURER.contains("Google") ||
                Build.MANUFACTURER.contains("Genymotion") ||
                Build.BRAND.startsWith("generic") ||
                Build.DEVICE.startsWith("generic") ||
                Build.MODEL.contains("google_sdk") ||
                Build.MODEL.contains("Emulator") ||
                Build.MODEL.contains("Android SDK built for x86") ||
                Build.HARDWARE.contains("goldfish") ||
                Build.HARDWARE.contains("ranchu") ||
                Build.HARDWARE.contains("vbox86") ||
                Build.FINGERPRINT.startsWith("generic") ||
                Build.FINGERPRINT.startsWith("unknown") ||
                Build.FINGERPRINT.contains("test-keys") ||
                Build.FINGERPRINT.contains("generic/sdk/generic") ||
                "google_sdk" == Build.PRODUCT)
    }

    fun isValidDomain(domain: String?): Boolean {
        Logger.d("DeeplinkUtil >> check domain: $domain")
        try {
            return domain?.let {
                val isValidUrl =
                    URLUtil.isValidUrl("http://$domain") || URLUtil.isValidUrl("https://$domain")
                return isValidUrl && domain.contains(".") && Patterns.DOMAIN_NAME.matcher(domain).matches()
            } ?: false

        } catch (ex: Exception) {
            ex.printStackTrace()
            return false
        }
    }

}
interface OnShowLoginListener {
    fun onShow()
    fun onHide()
}