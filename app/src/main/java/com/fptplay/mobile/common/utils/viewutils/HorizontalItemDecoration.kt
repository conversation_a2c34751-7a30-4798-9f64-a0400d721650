package com.fptplay.mobile.common.utils.viewutils

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

class HorizontalItemDecoration(
    private val spacingBetween: Int,
    private val spacingEdge: Int = 0,
    private val totalItem: Int = 0,
): RecyclerView.ItemDecoration() {
    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val position = parent.getChildAdapterPosition(view)  // item position
        val totalItem = parent.adapter?. let { (it.itemCount - 1) } ?: 0

        when (position) {
            0 -> {
                outRect.left =
                    spacingEdge
                outRect.right =
                    spacingBetween
            }
            totalItem -> {
                outRect.right = spacingEdge
            }
            else -> {
                outRect.right = spacingBetween
            }
        }

    }
}