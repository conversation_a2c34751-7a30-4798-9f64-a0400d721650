package com.fptplay.mobile.features.mqtt.database

import com.fptplay.mobile.features.mqtt.MqttUtil
import com.fptplay.mobile.features.mqtt.model.EmergencyRoomData
import com.fptplay.mobile.features.mqtt.model.MqttNotification
import com.fptplay.mobile.features.mqtt.model.MqttNotificationDetail
import com.fptplay.mobile.features.mqtt.model.MqttRoom
import com.google.firebase.firestore.*
import com.google.firebase.firestore.ktx.firestore
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import timber.log.Timber

class MqttNotificationListener(
    private val callback: IMqttNotificationListener? = null
){
    companion object {
        private const val LOG_TAG = "MqttConnectManager"
        private const val COLLECTION_PATH = "notification"
        private const val DOCUMENT_MQTT = "mqtt"
        private const val FIELD_MQTT = "detail"
    }

    private val fireStore: FirebaseFirestore? by lazy { FirebaseFirestore.getInstance() }
    private val emergencyRegistrations: MutableMap<String, ListenerRegistration> = mutableMapOf()
    private var isEmergencyListening: Boolean = false

    fun startListeningToEmergencyList(listEmergency: List<MqttRoom>) {
        if (isEmergencyListening) {
            Timber.tag(LOG_TAG).d("Already listening to emergency list, skipping duplicate listener setup")
            return
        }
        if (listEmergency.isNullOrEmpty()) {
            Timber.tag(LOG_TAG).d("No emergency rooms available to listen to")
            callback?.onMqttConfigError("No emergency rooms configured")
            return
        }
        Timber.tag(LOG_TAG).d("Starting to listen to emergency list with ${listEmergency.size} rooms")
        try {
            isEmergencyListening = true
            var successfulRegistrations = 0
            listEmergency.forEach { room ->
                if (room.roomId.isNotEmpty() && room.roomType.isNotEmpty()) {
                    val roomKey = "${room.roomType}_${room.roomId}"
                    val roomPath = "${room.roomType}/${room.roomId}"
                    if (emergencyRegistrations.containsKey(roomKey)) {
                        Timber.tag(LOG_TAG).d("Already listening to room: $roomPath")
                        return@forEach
                    }
                    if (room.roomId.isBlank()) return@forEach
                    if (room.roomType.isBlank()) return@forEach
                    Timber.tag(LOG_TAG).d("Setting up listener for emergency room: $roomPath")
                    fireStore?.clearPersistence()
                    val registration = fireStore?.collection(COLLECTION_PATH)
                        ?.document(room.roomType)
                        ?.collection(room.roomId)
                        ?.document(room.roomId)
                        ?.addSnapshotListener { querySnapshot: DocumentSnapshot?, e: FirebaseFirestoreException? ->
                            if (e != null) {
                                Timber.tag(LOG_TAG).w(e, "Listen failed for emergency room: $roomPath")
                                emergencyRegistrations.remove(roomKey)
                                callback?.onMqttConfigError("Emergency room listener failed for $roomPath: ${e.message}")
                                return@addSnapshotListener
                            }
                            if (querySnapshot != null && querySnapshot.exists()) {
                                Timber.tag(LOG_TAG).d("Received emergency room update for: $roomPath")
                                parseMqttNotificationDetails(querySnapshot, room)
                            } else {
                                Timber.tag(LOG_TAG).d("No data found for emergency room: $roomPath")
                            }
                        }
                    if (registration != null) {
                        emergencyRegistrations[roomKey] = registration
                        successfulRegistrations++
                        Timber.tag(LOG_TAG).d("Successfully created listener for room: $roomPath")
                    } else {
                        Timber.tag(LOG_TAG).e("Failed to create listener for room: $roomPath")
                    }
                }
            }
            if (successfulRegistrations > 0) {
                Timber.tag(LOG_TAG).d("Successfully set up $successfulRegistrations emergency room listeners")
            } else {
                isEmergencyListening = false
                Timber.tag(LOG_TAG).e("Failed to create any emergency room listeners")
                callback?.onMqttConfigError("Failed to create any emergency listeners")
            }

        } catch (e: Exception) {
            isEmergencyListening = false
            Timber.tag(LOG_TAG).e(e, "Error setting up emergency list listener")
            callback?.onMqttConfigError(e.message ?: "Emergency listener setup error")
        }
    }

    fun stopListeningToEmergencyList() {
        try {
            Timber.tag(LOG_TAG).d("Stopping ${emergencyRegistrations.size} emergency room listeners")
            emergencyRegistrations.forEach { (roomKey, registration) ->
                try {
                    registration.remove()
                    Timber.tag(LOG_TAG).d("Stopped listener for room: $roomKey")
                } catch (e: Exception) {
                    Timber.tag(LOG_TAG).e(e, "Error stopping listener for room: $roomKey")
                }
            }
            emergencyRegistrations.clear()
            isEmergencyListening = false
            Timber.tag(LOG_TAG).d("Stopped all emergency room listeners")

        } catch (e: Exception) {
            Timber.tag(LOG_TAG).e(e, "Error stopping emergency list listeners")
            emergencyRegistrations.clear()
            isEmergencyListening = false
        }
    }

    private fun parseMqttNotificationDetails(querySnapshot: DocumentSnapshot, room: MqttRoom) {
            try {
                val dataJson = querySnapshot.data
                Timber.tag(LOG_TAG).d("Query snapshot data for $dataJson}")
                dataJson?.forEach { (key, value) ->
                    if (key == FIELD_MQTT) {
                        val gson = Gson()
                        Timber.tag(LOG_TAG).d("Query snapshot data {FIELD_MQTT: $value} ")
                        val json: String = gson.toJson(value)
                        val notification = gson.fromJson(json, MqttNotificationDetail::class.java)
                        if (notification != null) {
                            Timber.tag(LOG_TAG).d("Parsed emergency room notification for ${room.roomId}")
                            callback?.onNotificationsReceived(room, notification)
                        }
                    }
                }

            } catch (e: Exception) {
                Timber.tag(LOG_TAG).e(e, "Error parsing emergency room data for ${room.roomId}")
                callback?.onMqttConfigError("Parse error for emergency room: ${e.message}")
            }
    }

    fun isEmergencyListening(): Boolean {
        return isEmergencyListening && emergencyRegistrations.isNotEmpty()
    }

    fun getActiveEmergencyListenersCount(): Int {
        return emergencyRegistrations.size
    }
}


