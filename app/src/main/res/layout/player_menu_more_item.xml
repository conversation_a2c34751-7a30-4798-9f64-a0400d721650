<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_container"
    android:layout_width="match_parent"
    android:layout_height="@dimen/player_menu_options_item_height"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/player_menu_options_item_check_size"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/margin_common"
        android:background="@color/transparent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/player_ic_subtitle" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/margin_common"
        android:layout_marginEnd="@dimen/margin_common"
        android:layout_weight="1"
        android:fontFamily="@font/sf_pro_display_regular"
        android:gravity="start|center_vertical"
        android:textColor="@color/white_87"
        android:textSize="@dimen/player_playlist_overlay_sub_title_text_size"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        tools:text="Title" />

    <ImageView
        android:id="@+id/iv_badge"
        android:layout_width="@dimen/player_menu_options_item_image_size"
        android:layout_height="@dimen/player_menu_options_item_image_size"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="@dimen/margin_common_small"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_value"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/margin_common"
        android:drawableEnd="@drawable/ic_next"
        android:fontFamily="@font/sf_pro_display_regular"
        android:gravity="start|center_vertical"
        android:textColor="@color/white_87"
        android:textSize="@dimen/player_playlist_overlay_sub_title_text_size"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        tools:text="Title" />

</LinearLayout>