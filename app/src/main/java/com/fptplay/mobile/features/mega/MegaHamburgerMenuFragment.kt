package com.fptplay.mobile.features.mega

import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.view.WarningDialogFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.TrackingConstants
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.databinding.MegaErrorLayoutBinding
import com.fptplay.mobile.databinding.MegaHamburgerMenuFragmentBinding
import com.fptplay.mobile.features.home.HomeMainFragment
import com.fptplay.mobile.features.mega.adapter.MegaMenuAdapter
import com.fptplay.mobile.features.mega.data.BlockAccountLogout
import com.fptplay.mobile.features.mega.data.BlockAppVersion
import com.fptplay.mobile.features.mega.util.CheckNavigateMegaUtils
import com.fptplay.mobile.features.mega.util.FoxpayUtils
import com.ftel.foxpay.foxsdk.feature.FoxSdkManager
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenu
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenuItem
import com.xhbadxx.projects.module.domain.entity.fplay.user.UserInfo
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class MegaHamburgerMenuFragment :
    BaseFragment<MegaViewModel.MegaState, MegaViewModel.MegaIntent>() {
    override val hasEdgeToEdge: Boolean = false

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    override val viewModel: MegaViewModel by activityViewModels()
    val megaNavigateViewModel: MegaNavigateViewModel by activityViewModels()
    private var _binding: MegaHamburgerMenuFragmentBinding? = null
    private val binding get() = _binding!!
    private var _errorBinding: MegaErrorLayoutBinding? = null
    private val errorBinding get() = _errorBinding!!
    private var isInReloadProcess: Boolean = false
    private var megaAppId = ""
    private var megaAppName = ""

    private var title = ""

    private val megaMenusV2 by lazy { arrayListOf<MegaMenu.Block>() }

    private val menuAdapter by lazy { MegaMenuAdapter(binding.root.context, sharedPreferences) }
    private var isFirstInit = true
    private var warningDialogFragment: WarningDialogFragment? = null

    private val sdkManager: FoxSdkManager.Companion by lazy {
        FoxpayUtils.initFoxpaySDK(sharedPreferences.linkingToken(), requireContext())
    }

    private val checkNavigateMegaUtils: CheckNavigateMegaUtils by lazy {
        CheckNavigateMegaUtils(
            context = requireContext(),
            sharedPreferences = MainApplication.INSTANCE.sharedPreferences,
            megaViewModel = viewModel,
            megaNavigateViewModel = megaNavigateViewModel,
            fragment = this@MegaHamburgerMenuFragment,
            navHostFragment = activity?.findNavHostFragment(),
            checkNavigateLoyaltyUtils = checkNavigateLoyaltyUtils,
            foxPaySdkManager = sdkManager
        )
    }

    override fun onResume() {
        super.onResume()
        if (isFirstInit) {
            viewModel.dispatchIntent(MegaViewModel.MegaIntent.GetMegaHamburgerMenu(miniAppSdkVersion = Constants.MINI_APP_SDK_SUPPORTED))
            menuAdapter.bind(megaMenusV2.copy())
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        megaAppId = TrackingUtil.currentAppId
        megaAppName = TrackingUtil.currentAppName
        lifecycle.addObserver(checkNavigateLoyaltyUtils)
        lifecycle.addObserver(checkNavigateMegaUtils)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = MegaHamburgerMenuFragmentBinding.inflate(inflater, container, false)
        _errorBinding = MegaErrorLayoutBinding.bind(binding.root)
        return binding.root
    }

    override fun showPageErrorV2OneButton(
        parentView: View?,
        toolbarTitle: String?,
        errorTitle: String?,
        errorMessage: String,
        navigationIcon: Int?,
        backgroundIcon: Int?,
        btnText: String?,
        onBtnClickBeforeRetryLoadPage: (() -> Unit)?,
        btnBackgroundRes: Int?
    ) {
        super.showPageErrorV2OneButton(
            parentView,
            toolbarTitle = title,
            errorTitle,
            errorMessage,
            navigationIcon = R.drawable.ic_arrow_left,
            backgroundIcon,
            btnText,
            onBtnClickBeforeRetryLoadPage,
            btnBackgroundRes
        )
    }

    override fun showNoInternetView(parentView: View?) {
        showPageErrorV2OneButton(
            errorTitle = getString(R.string.welcome_no_internet_title),
            errorMessage = getString(R.string.error_layout_troubleshoot),
            backgroundIcon = R.drawable.ic_no_internet

        )
    }
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        _errorBinding = null
        warningDialogFragment?.dismissAllowingStateLoss()
    }

    override fun onDestroy() {
        super.onDestroy()
        lifecycle.removeObserver(checkNavigateLoyaltyUtils)
        lifecycle.removeObserver(checkNavigateMegaUtils)

    }

    override fun bindComponent() {
        //
        if (context.isTablet()) {
            activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        }
        //

        title = getString(R.string.mega_account_hamburger_title)
        binding.toolbar.title = title
        binding.rvMega.apply {
            adapter = menuAdapter
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
        }
    }

    override fun bindData() {
        (parentFragment as? HomeMainFragment)?.hideNoInternetView()
        (parentFragment as? HomeMainFragment)?.hidePageError()

    }

    override fun bindEvent() {
        binding.toolbar.setNavigationOnClickListener {
            findNavController().navigateUp()
        }

        menuAdapter.eventListener = object : IEventListener<MegaMenu.Block> {

            override fun onClickedItem(position: Int, data: MegaMenu.Block) {

                TrackingUtil.currentAppId = megaAppId
                TrackingUtil.currentAppName = megaAppName

                checkNavigateMegaUtils.navigateToSelectedContent(data)
            }
        }

        menuAdapter.megaMenuItemClickListener = object : IEventListener<MegaMenuItem> {
            override fun onClickedItem(position: Int, data: MegaMenuItem) {
                TrackingUtil.currentAppId = megaAppId
                TrackingUtil.currentAppName = megaAppName

                val eventLog = if (data.blockType == MegaMenu.Type.MegaApp) {
                    "AccessApp"
                } else {
                    "AccessItem"
                }
                trackingProxy.sendEvent(
                    infor = InforMobile(
                        infor = trackingInfo,
                        appId = TrackingUtil.currentAppId,
                        appName = TrackingUtil.currentAppName,
                        logId = TrackingConstants.EVENT_LOG_ID_MEGA_FUNCTION_CLICK,
                        screen = TrackingConstants.SCREEN_NAME_MEGA_FUNCTION_CLICK,
                        event = eventLog,
                        itemId = data.id,
                        itemName = data.title
                    )
                )
                //
                if (data.blockType == MegaMenu.Type.MegaApp) {
                    TrackingUtil.currentAppId = data.id
                    TrackingUtil.currentAppName = data.title

                    megaNavigateViewModel.saveNavigateMenu(data)
                    parentFragment?.parentFragment?.findNavController()?.navigate(
                        NavHomeMainDirections.actionGlobalToNavMegaApp(
                            megaAppId = data.id,
                            fromDeeplink = false

                        )
                    )
                } else {
                    checkNavigateMegaUtils.navigateToSelectedContent(data)
                }

                Logger.d("trangtest logid = 108 $data")

            }
        }
        if (_errorBinding != null) {
            errorBinding.btnRetry.setOnClickListener {
                reloadPage()
            }
        }
    }


    override fun MegaViewModel.MegaState.toUI() {
        when (this) {
            is MegaViewModel.MegaState.ErrorRequiredLogin -> {
                if (this.intent is MegaViewModel.MegaIntent.GetUserInfo) {
                    viewModel.saveUserInfo(UserInfo())
                }
            }

            is MegaViewModel.MegaState.ErrorNoInternet -> {
                if (this.intent is MegaViewModel.MegaIntent.GetMegaHamburgerMenu) {
                    showNoInternetView()
                    isInReloadProcess = false
                }
            }

            is MegaViewModel.MegaState.Error -> {
                if (this.intent is MegaViewModel.MegaIntent.GetMegaHamburgerMenu) {
                    showErrorLayout()
                    isInReloadProcess = false
                }
            }

            is MegaViewModel.MegaState.ResultMegaMenusHamburger -> {
                Timber.tag("tam-mega").d("ResultMegaMenusHamburger ${data.blocks}")
                isFirstInit = false
                hideErrorLayout()
                isInReloadProcess = false
                for (block in data.blocks) {
                    megaMenusV2.updateItem(newMenu = block)
                }
                // add block app version
                megaMenusV2.updateItem(BlockAppVersion)

                Timber.tag("tam-mega").i("megaMenuHamburger ${megaMenusV2}")
                menuAdapter.bind(megaMenusV2.copy())
            }

            else -> {}
        }
    }

    // region Commons

    private fun showErrorLayout() {
        if (_binding != null) {
            binding.errorLayout.show()
        }
    }

    private fun hideErrorLayout() {
        if (_binding != null) {
            binding.errorLayout.hide()
        }
    }

    override fun retryLoadPage() {
        if (!isInReloadProcess && isFirstInit) {
            isInReloadProcess = true
            viewModel.dispatchIntent(MegaViewModel.MegaIntent.GetMegaHamburgerMenu(miniAppSdkVersion = Constants.MINI_APP_SDK_SUPPORTED))
            menuAdapter.bind(megaMenusV2.copy())
        }
    }

    private fun reloadPage() {
        if (!isInReloadProcess && isFirstInit) {
            isInReloadProcess = true
            viewModel.dispatchIntent(MegaViewModel.MegaIntent.GetMegaHamburgerMenu(miniAppSdkVersion = Constants.MINI_APP_SDK_SUPPORTED))
            menuAdapter.bind(megaMenusV2.copy())
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_LONG).show()
    }

    private fun navigateToLogin(navigationId: Int? = null) {
        navigationId?.let {
            parentFragment?.parentFragment?.navigateToLoginWithParams(
                isDirect = true,
                navigationId = it
            )
        } ?: run {
            parentFragment?.parentFragment?.navigateToLoginWithParams(isDirect = true)
        }
    }


    private fun ArrayList<MegaMenu.Block>.copy(): List<MegaMenu.Block> {
        val cloneOfMegaMenus = ArrayList<MegaMenu.Block>()
        forEach { item -> cloneOfMegaMenus.add(item.clone()) }
        return cloneOfMegaMenus
    }

    private fun MutableList<MegaMenu.Block>.updateItem(
        newMenu: MegaMenu.Block,
        indexInserted: Int = -1
    ): Int {
        if (this.isEmpty()) {
            add(newMenu)
            return size - 1
        }

        if (newMenu.blockType == MegaMenu.Type.Profile || newMenu.blockType == MegaMenu.Type.AccountLogout) {
            // if blockType Profile, replace if exist, not add new
            var indexMenu = -1
            for ((i, megaMenu) in this.withIndex()) {
                if (megaMenu.blockType == newMenu.blockType) {
                    this[i] = newMenu
                    indexMenu = i
                    break
                }
            }
            if (indexMenu == -1) {
                // Not in list menu more yet
                indexMenu = if (indexInserted in 0 until size) {
                    add(indexInserted, newMenu)
                    indexInserted
                } else {
                    add(newMenu)
                    size - 1
                }
            }
            return indexMenu
        }
        // if another block type, add new
        var indexMenu = if (indexInserted in 0 until size) {
            add(indexInserted, newMenu)
            indexInserted
        } else {
            if (get(size - 1) == BlockAccountLogout) {
                // if have block logout, add before log out
                add(size - 1, newMenu)
                size - 2
            } else {
                add(newMenu)
                size - 1
            }

        }
//        add(newMenu)
        return indexMenu
    }
    // endregion Commons
}