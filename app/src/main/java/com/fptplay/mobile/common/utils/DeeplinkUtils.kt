package com.fptplay.mobile.common.utils

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import com.clevertap.android.sdk.CleverTapAPI
import com.clevertap.android.sdk.Logger
import com.fptplay.mobile.HomeActivity
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.extensions.isHostAdjustTrueLink
import com.fptplay.mobile.common.extensions.isHostFptPlay
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.StringUtils.hash512
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.adjust.PackageFrom
import com.fptplay.mobile.features.adjust.SourcePage
import com.fptplay.mobile.features.game_30s.detail.PlayStartDetailFragmentDirections
import com.fptplay.mobile.features.game_30s.play_start.PlayStartFragmentDirections
import com.fptplay.mobile.features.game_30s.play_start.PlayStartVotingFragmentDirections
import com.fptplay.mobile.features.mega.account.util.AccountMegaScreen
import com.fptplay.mobile.features.mega.apps.fptpplayshop.FPTPlayShopFragment
import com.fptplay.mobile.features.mega.util.FoxpayUtils
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.features.payment.PaymentViewModel
import com.fptplay.mobile.features.payment.google_billing.BillingUtils
import com.fptplay.mobile.features.payment.util.PaymentTrackingUtil
import com.fptplay.mobile.features.pladio.data.PladioNavigateType
import com.fptplay.mobile.features.pladio.data.Song
import com.fptplay.mobile.features.tracking_ga4.TrackingGA4Proxy
import com.ftel.foxpay.foxsdk.feature.TypeScreen
import com.google.android.material.tabs.TabLayout
import com.tear.modules.tracking.model.Infor
import com.xhbadxx.projects.module.domain.entity.fplay.common.LandingPage
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenuItem
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.BlockStyle
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.util.common.Util
import timber.log.Timber


object DeeplinkUtils {

    const val NAVIGATION_ID_DEEP_LINK_HANDLE = -9999
    const val NAVIGATION_LINK_DEEP_LINK_KEY = "deeplink"


    private var tabLayout: TabLayout? = null
    private var homeActivity: HomeActivity? = null

    fun registerDeeplink(homeActivity: HomeActivity) {
        Timber.i("registerDeeplink ${homeActivity.javaClass}" )
        this.homeActivity = homeActivity
    }

    fun unregisterDeeplink(homeActivity: HomeActivity) {
        if(this.homeActivity == homeActivity) {
            Timber.e("unregisterDeeplink" )
            this.homeActivity = null
            this.tabLayout = null
        }
    }

    fun registerTabLayoutHome(
        tabLayout: TabLayout
    ) {
        Timber.i("registerTabLayoutHome $tabLayout" )
        this.tabLayout = tabLayout
    }

    fun unregisterTabLayoutHome() {
        Timber.e("unregisterTabLayoutHome" )
        this.tabLayout = null
    }

//    private fun getNavHostController(): NavController? {
//        return (homeMainFragment.childFragmentManager.findFragmentById(R.id.nav_host_fragment) as? NavHostFragment)?.navController
//    }


     fun openWebBrowser(url: String) {
         Timber.tag("tam-deeplink").i("openWebBrowser: $url")
         try {
            val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
            homeActivity?.startActivity(browserIntent)
        } catch (e: Exception) {
            Timber.e(e, "openWebBrowser error")
        }

    }

    fun getIdFromLink(link: String): String {
        val arr = link.split('-')
        val last = arr[arr.size - 1]
        return if (last.length == 24 && last.matches("-?[0-9a-fA-F]+".toRegex())) last else link
    }

    private fun getVodIdFromPathSegment(path: String): String? {
        val pathString = path.replace(".html", "")
        val data = pathString.split("-")
        return if(data.isNotEmpty()) {
            data[data.size - 1]
        } else {
            null
        }
    }

    fun String.isHostSupportFptPlay(): Boolean {
        return equals(DeeplinkConstants.HOST_SUPPORT_FPT_PLAY, true)
    }

    //parseDeepLinkAndExecute("https://fptplay.vn/hbo-go/xem-video/max-dien-con-duong-cuong-no-615c2b042089bd0182bfbf42")
    fun parseDeepLinkAndExecute(deeplink: String, useWebViewInApp: Boolean = false,
                                trackingInfo: Infor? = null, isDeeplinkCalledInApp: Boolean = false,
                                callFromAds: Boolean? = false) {
        // Example: https://fptplay.vn/hbo-go/xem-video/max-dien-con-duong-cuong-no-615c2b042089bd0182bfbf42
        // ~~~~     https://fptplay.vn/{screen}/{menu}/{subMenu}/{id}
        // ~~~~     screen => hbo-go
        // ~~~~     menu => xem-video
        // ~~~~     subMenu => max-dien-con-duong-cuong-no-615c2b042089bd0182bfbf42
        // ~~~~     getIdFromLink(subMenu) => 615c2b042089bd0182bfbf42


//        val deeplink = "https://fptplay.vn/xem-truyen-hinh/vinh-long-3"
//        val deeplink = "https://fptplay.vn/hbo-go"
        if(deeplink.isBlank()) {
            Timber.e("parseDeepLinkAndExecute deeplink blank")
            return
        }

        val uri = Uri.parse(deeplink)
        var host = uri.host
        if(host?.startsWith("www.") == true) {
            host = host.replaceFirst("www.", "")
        }

        if(host.isNullOrBlank()) {
            Timber.e("parseDeepLinkAndExecute deeplink wrong format: $deeplink")
            return
        }

        Logger.d("DeeplinkUtil >> startWithAppScheme >> $deeplink >> $host isValidDomain: ${Utils.isValidDomain(host)}")
        if(deeplink.startsWith("fptplay://") && !host.isHostFptPlay()) {
            Logger.d("DeeplinkUtil >> startWithAppScheme >> replace app scheme")
            val newDeeplink = deeplink.replaceFirst("fptplay://", "fptplay://fptplay.vn/")
            parseDeepLinkAndExecute(
                deeplink = newDeeplink,
                useWebViewInApp = useWebViewInApp,
                trackingInfo = trackingInfo,
                isDeeplinkCalledInApp = isDeeplinkCalledInApp,
                callFromAds = callFromAds,
            )
            return
        }

        // log crashlytics
        TrackingGA4Proxy.sendTrackingCrashReportOpenByDeepLink(isDeeplinkCalledInApp = isDeeplinkCalledInApp,deepLink = deeplink)

        //
        Timber.d("parseDeepLinkAndExecute ${uri.host} - $host - ${uri.scheme} - ${uri.pathSegments}")
        Timber.d("parseDeepLinkAndExecute $deeplink - $useWebViewInApp")

        TrackingUtil.resetAppIdToHome()
        if(callFromAds == true) {
            TrackingUtil.screen = TrackingUtil.screenAds
            TrackingUtil.blockId = TrackingUtil.submenuIdAds
        }
        val navHomeMain = findNavHomeMainController(homeActivity)
        val pathSegments = uri.pathSegments
        Timber.w("parseDeepLinkAndExecute $pathSegments")
        val screen = if (pathSegments.size >= 1) pathSegments[0] else ""
        val menu = if (pathSegments.size >= 2) pathSegments[1] else ""
        val subMenu = if (pathSegments.size >= 3) pathSegments[2] else ""
        val id = if (pathSegments.size >= 4) pathSegments[3] else ""
        val subMenuId = if (pathSegments.size >= 5) pathSegments[4] else ""
        val loginKey = uri.getQueryParameter("login_key")?: ""

        Utils.checkAndSaveUTM(deeplink, isDeeplinkCalledInApp)

        if(isDeeplinkBlocked(screen, menu, subMenu, id, subMenuId, useWebViewInApp, deeplink, pathSegments)) {
            Timber.d("""isDeeplinkBlocked 
            |screen: $screen - 
            |menu: $menu - 
            |subMenu: $subMenu - 
            |id: $id - 
            |subMenuId: $subMenuId - 
            |useWebViewInApp: $useWebViewInApp - 
            |originalLink: $deeplink""")
            navHomeMain?.navigate(NavHomeMainDirections.actionGlobalToDeeplinkNotSupportedDialog())
//            showDeeplinkBlockDialog()
            return
        }
        when {
            isSamsungSsoDeeplink(deeplink) -> handleDeeplinkSamsungSSO(deeplink)
            host.isHostAdjustTrueLink() ||
            host.isHostFptPlay() -> {
                handleDeepLinkInApp(screen, menu, subMenu, id, subMenuId, useWebViewInApp, deeplink, pathSegments, loginKey = loginKey)
            }
            host.isHostSupportFptPlay() -> {
//                homeMainFragment.launchSupportCenter()
                if(navHomeMain == null) {
                    // Have no navigation, auto redirect to browser
                    openWebBrowser(deeplink)
                    return
                }
                navigateToSupportCenter(navHomeMain)
            }
            else -> {
                openWebBrowser(deeplink)
            }

        }
    }

    fun handleDeepLinkInApp(
        screen: String,
        menu: String,
        subMenu: String,
        id: String,
        subMenuId: String,
        useWebViewInApp: Boolean = false,
        originalLink: String = "",
        pathSegments: List<String> = ArrayList(),
        loginKey:String = ""
    ) {
        Timber.d("""handleDeepLink 
            |screen: $screen - 
            |menu: $menu - 
            |subMenu: $subMenu - 
            |id: $id - 
            |subMenuId: $subMenuId - 
            |useWebViewInApp: $useWebViewInApp - 
            |originalLink: $originalLink""")
//        val navHomeMain = homeMainFragment.findNavController()
        val navHomeMain = findNavHomeMainController(homeActivity)
        if(navHomeMain == null) {
            // Have no navigation, auto redirect to browser
            openWebBrowser(originalLink)
            return
        }
        var handleSuccess = true
        if(screen.isBlank()) {
            navigateToHome(navController = navHomeMain, homeTabId = PageId.HomePageId.id)
            return
        }
        when (screen) {
            DeeplinkConstants.DEEPLINK__SCREEN__QUICK_LOGIN->{
                navigateToHomeTab(PageId.HomePageId.id, navController = navHomeMain) {
                    if(loginKey.isNotBlank()){
                        navHomeMain.navigate(NavHomeMainDirections.actionGlobalToQrCodeFragment(
                            isFromDeeplink = true,
                            loginKey = loginKey
                        ))
                    }
                }
            }
            DeeplinkConstants.DEEPLINK__SCREEN__WATCH_VOD,
            DeeplinkConstants.DEEPLINK__SCREEN__DETAIL_VOD -> {
                val vodId = getVodIdFromPathSegment(menu)
                navigateToHomeTab(PageId.HomePageId.id, navController = navHomeMain) {
                    if(vodId != null) {
                        navigateToVodDetail(navController = navHomeMain, vodId = vodId, screenProvider = PageId.HomePageId.id)
                    }
                }
            }
            DeeplinkConstants.DEEPLINK__SCREEN__VOD -> {
                if(subMenu.isNotEmpty()) {
                    // https://fptplay.vn/danh-muc/<page-id>/<category-id>
                    val menuId = when (menu) {
                        DeeplinkConstants.DEEPLINK__MENU__VOD__PHIMBO -> DeeplinkConstants.DEEPLINK__MENU__VOD__PHIMBO__PAGEID
                        DeeplinkConstants.DEEPLINK__MENU__VOD__PHIMLE -> DeeplinkConstants.DEEPLINK__MENU__VOD__PHIMLE__PAGE_ID
                        DeeplinkConstants.DEEPLINK__MENU__VOD__PHIMRAP -> DeeplinkConstants.DEEPLINK__MENU__VOD__PHIMRAP__PAGE_ID
                        DeeplinkConstants.DEEPLINK__MENU__VOD__DACSAC -> DeeplinkConstants.DEEPLINK__MENU__VOD__DACSAC__PAGE_ID
                        DeeplinkConstants.DEEPLINK__MENU__VOD__TVSHOW -> DeeplinkConstants.DEEPLINK__MENU__VOD__TVSHOW__PAGE_ID
                        DeeplinkConstants.DEEPLINK__MENU__VOD__ANIME -> DeeplinkConstants.DEEPLINK__MENU__VOD__ANIME__PAGE_ID
                        DeeplinkConstants.DEEPLINK__MENU__VOD__THIEUNHI -> DeeplinkConstants.DEEPLINK__MENU__VOD__THIEUNHI__PAGE_ID
                        DeeplinkConstants.DEEPLINK__MENU__VOD__HAI -> DeeplinkConstants.DEEPLINK__MENU__VOD__HAI__PAGE_ID
                        DeeplinkConstants.DEEPLINK__MENU__VOD__HOCONLINE -> DeeplinkConstants.DEEPLINK__MENU__VOD__HOCONLINE__PAGE_ID
                        DeeplinkConstants.DEEPLINK__MENU__VOD__MUSIC -> DeeplinkConstants.DEEPLINK__MENU__VOD__MUSIC__PAGE_ID
                        else -> ""
                    }
                    if (menuId.isNotEmpty()) {
                        navigateToHomeTab(PageId.HomePageId.id, navController = navHomeMain) {
                            navigateToVodPageDetail(navController = navHomeMain, pageId = menuId)
                        }
                    }


                } else if(menu.isNotEmpty()){
                    // https://fptplay.vn/danh-muc/<block-detail-id>
                    navigateToHomeTab(PageId.HomePageId.id, navController = navHomeMain) {
                        navigateToBlockHighlightDetail(navController = navHomeMain, id = menu)
                    }

                } else {
                    navigateToHome(navController = navHomeMain, PageId.HomePageId.id)
                }
            }
            DeeplinkConstants.DEEPLINK__SCREEN__LIVE_TV -> {
                navigateToHomeTab(PageId.TvPageId.id, navController = navHomeMain) {
                    if (menu.isNotEmpty()) {
                        navigateToLiveTv(navController = navHomeMain, channelId = menu)
                    }
                }
            }
            DeeplinkConstants.DEEPLINK__SCREEN__TV__FROM_LANDING_PAGE -> {
                navigateToHomeTab(null, navController = navHomeMain) {
                    if (menu.isNotEmpty()) {
                        navigateToLiveTv(navController = navHomeMain, channelId = menu)
                    }
                }
            }
            DeeplinkConstants.DEEPLINK__SCREEN__VOD__FROM_LANDING_PAGE -> {
                val vodId = getVodIdFromPathSegment(menu)
                navigateToHomeTab(null, navController = navHomeMain) {
                    if(vodId != null) {
                        navigateToVodDetail(navController = navHomeMain, vodId = vodId, screenProvider = PageId.HomePageId.id)
                    }
                }
            }
            DeeplinkConstants.DEEPLINK__SCREEN__EVENT__FROM_LANDING_PAGE -> {
                when {
                    menu == DeeplinkConstants.DEEPLINK__MENU__EVENT__CHOIHAYCHIA -> {
                        navigateToHomeTab(null, navController = navHomeMain) {
                            navHomeMain.navigate(NavHomeMainDirections.actionGlobalToChoihaychia())
                        }
                    }
                    menu.isNotEmpty() -> {
                        val eventId = getVodIdFromPathSegment(menu)
                        if(eventId != null) {
                            navigateToHomeTab(PageId.HomePageId.id, navController = navHomeMain) {
                                navHomeMain.navigate(
                                    NavHomeMainDirections.actionGlobalToPremiere(
                                        idToPlay = eventId
                                    )
                                )
                            }
                        } else handleSuccess = false
                    } else -> handleSuccess = false
                }
            }
            DeeplinkConstants.DEEPLINK__SCREEN__EVENT_TV__FROM_LANDING_PAGE -> {
                if(menu.isNotEmpty()) {
                    val eventId = getVodIdFromPathSegment(menu)
                    if(eventId != null) {
                        navigateToHomeTab(null, navController = navHomeMain) {
                            navHomeMain.navigate(
                                NavHomeMainDirections.actionGlobalToPremiere(
                                    idToPlay = eventId
                                )
                            )
                        }
                    } else handleSuccess = false
                } else {
                    handleSuccess = false
                }
            }

            DeeplinkConstants.DEEPLINK__SCREEN__GALAXY__PLAY -> {
                if(menu == DeeplinkConstants.DEEPLINK__SCREEN__WATCH_VOD) {

                    val vodId = getVodIdFromPathSegment(subMenu)
                    navigateToHomeTab(PageId.HomePageId.id, navController = navHomeMain) {
                        if(vodId != null) {
                            navigateToVodDetail(navController = navHomeMain, vodId = vodId, screenProvider = PageId.HomePageId.id)
                        }
                    }

                } else {
                    navigateToHome(navController = navHomeMain, homeTabId = PageId.HomePageId.id)
                }
            }

            DeeplinkConstants.DEEPLINK__SCREEN__SPORT -> {
                if(menu == DeeplinkConstants.DEEPLINK__SCREEN__SPORT__WATCH_VIDEO) {
                    val vodId = getVodIdFromPathSegment(subMenu)
                    navigateToHomeTab(PageId.SportPageId.id, navController = navHomeMain) {
                        if(vodId != null) {
                            navigateToVodDetail(navController = navHomeMain, vodId = vodId, screenProvider = PageId.SportPageId.id)
                        }
                    }

                } else {
                    navigateToHome(navController = navHomeMain, homeTabId = PageId.SportPageId.id)
                }
            }
            DeeplinkConstants.DEEPLINK__SCREEN__HBO_GO -> {
                when (menu) {
                    DeeplinkConstants.DEEPLINK__MENU__HBO_GO__LIVE -> {
                        navigateToHomeTab(PageId.TvPageId.id, navController = navHomeMain) {
                            if (subMenu.isNotEmpty()) {
                                navigateToLiveTv(navController = navHomeMain, channelId = subMenu)
                            }
                        }

                    }
                    DeeplinkConstants.DEEPLINK__MENU__HBO_GO__WATCH_VOD,
                    DeeplinkConstants.DEEPLINK__MENU__HBO_GO__DETAIL_VOD -> {
                        val vodId = getVodIdFromPathSegment(subMenu)
                        navigateToHomeTab(homeTabId = PageId.HboPageId.id, navigateToVodPage = true, navController = navHomeMain) {
                            if (vodId != null) {
                                navigateToVodDetail(
                                    navController = navHomeMain,
                                    vodId = vodId,
                                    screenProvider = PageId.HboPageId.id
                                )
                            }
                        }
                    }
                    else -> {
                        navigateToHome(navController = navHomeMain, homeTabId = PageId.HboPageId.id)
//                        navigateToHomeTab(PageId.HboPageId.id)
                    }
                }
            }
            DeeplinkConstants.DEEPLINK__SCREEN__BUY_PACKAGE -> {
                /**
                 *
                 * mua-goi/dich-vu/ -> layout sanh sách gói app
                 * mua-goi/dich-vu/<id-goi> -> layout chi tiết gói
                 *
                 * mua-goi/  -> layout sanh sách gói trong app
                 * mua-goi/<id-goi> -> webview
                 *
                 */
                if(menu.equals(DeeplinkConstants.DEEPLINK__MENU__BUY_PACKAGE__SERVICE)) {
                    PaymentTrackingUtil.showPackageFrom = PaymentTrackingUtil.PackageFrom.Deeplink
                    AdjustAllEvent.dataCur.packageFrom = PackageFrom.button
                    TrackingUtil.resetDataPlaying()
                    if(subMenu.isBlank()) {
                        TrackingGA4Proxy.saveTrackingTypeDeeplink(TrackingGA4Proxy.TypeCallPackage.deeplink)
                        navHomeMain.navigate(NavHomeMainDirections.actionGlobalToPayment())
                    } else {
                        TrackingGA4Proxy.saveTrackingTypeClickAndContentName(TrackingGA4Proxy.TypeCallDetail.deeplink, "")
                        navHomeMain.navigate(NavHomeMainDirections.actionGlobalToPayment(
                            viewType = BillingUtils.PACKAGE_VIEW_TYPE_DETAIL,
                            packageType = subMenu,
                            fromSource = PaymentViewModel.FromSource.MAIN.rawValue
                        ))
//                        navHomeMain.navigate(
//                            PackageFragmentDirections.actionPackageFragmentToNavPaymentDetail(
//                                packageType = subMenu,
//                                fromSource = PaymentViewModel.FromSource.MAIN.rawValue
//                            )
//                        )
//                        when (subMenu) {
//                            DeeplinkConstants.DEEPLINK__MENU__BUY_PACKAGE__3G -> {
//                                navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {
//                                    navHomeMain.navigate(
//                                        NavHomeMainDirections.actionGlobalToWebViewFragment(
//                                            url = Constants.MEGA_SERVICE_3G_URL,
//                                            title = homeActivity?.getString(R.string.mega_app_3g_service_title)
//                                                ?: ""
//                                        )
//                                    )
//                                }
//                            }
//                            else -> {
//                                navHomeMain.navigate(NavHomeMainDirections.actionGlobalToPayment())
//                                navHomeMain.navigate(
//                                    PackageFragmentDirections.actionPackageFragmentToNavPaymentDetail(
//                                        packageType = menu
//                                    )
//                                )
//                            }
//                        }
                    }
                } else if(menu.isBlank()){
                    TrackingGA4Proxy.saveTrackingTypeDeeplink(TrackingGA4Proxy.TypeCallPackage.deeplink)
                    navHomeMain.navigate(NavHomeMainDirections.actionGlobalToPayment())

                } else {
                    handleSuccess = false
                }

            }
            DeeplinkConstants.DEEPLINK__SCREEN__HIP_FEST -> {
                navHomeMain.navigate(NavHomeMainDirections.actionGlobalToQrCodeTicket())
            }
            DeeplinkConstants.DEEPLINK__SCREEN__SERVICE -> {
                /**
                 *
                 * dich-vu/3g -> webview (k redirect)
                 * dich-vu -> webview
                 * dich-vu/<id-goi-k-phai-3g> -> webview -> web redirect qua mua-goi/dich-vu/<id-goi>
                 *
                 */
                if(menu.equals(DeeplinkConstants.DEEPLINK__MENU__BUY_PACKAGE__3G, true)) {
                    navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {
                        navHomeMain.navigate(
                            NavHomeMainDirections.actionGlobalToWebViewFragment(
                                url = Constants.MEGA_SERVICE_3G_URL,
                                title = homeActivity?.getString(R.string.mega_app_3g_service_title)
                                    ?: ""
                            )
                        )
                    }
                } else {
                    handleSuccess = false // openWebview
                }

            }
            DeeplinkConstants.DEEPLINK__SCREEN__EVENT -> {
                when {
                    menu == DeeplinkConstants.DEEPLINK__MENU__EVENT__CHOIHAYCHIA -> {
                        navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {
                            navHomeMain.navigate(NavHomeMainDirections.actionGlobalToChoihaychia())
                        }
                    }
                    menu == DeeplinkConstants.DEEPLINK__MENU__EVENT__GAME30s -> {
                        val deeplinkData = if(subMenu.isNotEmpty()) {
                            when (subMenu) {
                                DeeplinkConstants.DEEPLINK__SUB_MENU__GAME30s__TEAM -> {
                                    if(id.isNotEmpty() && subMenuId.isNotEmpty()) {
                                        init30sDeeplinkData(
                                            screen = subMenu,
                                            gameId = id,
                                            teamId = subMenuId
                                        )
                                    } else {
                                        null
                                    }
                                }
                                DeeplinkConstants.DEEPLINK__SUB_MENU__GAME30s__VIDEO_DETAIL -> {
                                    val videoId = if (pathSegments.size >= 6) pathSegments[5] else ""
                                    if(id.isNotEmpty() && subMenuId.isNotEmpty() && videoId.isNotEmpty()) {
                                        init30sDeeplinkData(
                                            screen = subMenu,
                                            gameId = id,
                                            teamId = subMenuId,
                                            videoId  = videoId
                                        )
                                    } else {
                                        null
                                    }


                                }
                                DeeplinkConstants.DEEPLINK__SUB_MENU__GAME30s__GAME_RANKING -> {
                                    init30sDeeplinkData(
                                        screen = subMenu,
                                        gameId = id
                                    )
                                }
                                DeeplinkConstants.DEEPLINK__SUB_MENU__GAME30s__GAME_GUIDE -> {
                                    init30sDeeplinkData(
                                        screen = subMenu,
                                        gameId = id
                                    )
                                }
                                else -> {

                                    if(pathSegments.size == 3) {
                                        // Format: http://fptplay.vn/su-kien/ngoisao30s/gameId, have no extend path
                                        init30sDeeplinkData(
                                            screen = DeeplinkConstants.DEEPLINK__SUB_MENU__GAME30s__GAME,
                                            gameId = subMenu
                                        )
                                    } else {
                                        null
                                    }

                                }
                            }
                        } else {
                            null
                        }
                        navHomeMain.navigateSafe(
                            directions = NavHomeMainDirections.actionGlobalToGamePlayStart30s(),
                            extendArgs = if(deeplinkData != null) {
                                Bundle().apply {
                                    putBundle(
                                        DeeplinkConstants.DEEPLINK__30s__BUNDLE_NAME,
                                        deeplinkData
                                    )
                                }
                            } else {
                                null
                            })

                    }

                    menu.isNotEmpty() -> {
                        val eventId = getVodIdFromPathSegment(menu)
                        val uri = Uri.parse(originalLink)
                        if (!eventId.isNullOrEmpty()) {
                            when (uri.getQueryParameter("event")?.lowercase()) {
                                "event" -> {
                                    navigateToPremier(
                                        navController = navHomeMain,
                                        premierId = eventId
                                    )
                                }

                                null,
                                "eventtv" -> {
                                    navHomeMain.navigate(
                                        NavHomeMainDirections.actionGlobalToPremiere(
                                            idToPlay = eventId
                                        )
                                    )
                                }
                                else -> {
                                    handleSuccess = false
                                }
                            }
                        } else {
                            handleSuccess = false
                        }
                    }
                    else -> handleSuccess = false
                }
            }
            DeeplinkConstants.DEEPLINK__SCREEN__ACCOUNT -> {
                when (menu) {
                    DeeplinkConstants.DEEPLINK__MENU__ACCOUNT__ACCINFO -> {
                        navHomeMain.navigate(NavHomeMainDirections.actionGlobalToAccountInfo())
                    }
                    DeeplinkConstants.DEEPLINK__MENU__ACCOUNT__DEVICE -> {
                        navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {

                            navHomeMain.navigate(
                                NavHomeMainDirections.actionGlobalToAccountAction(
                                    targetScreen = AccountMegaScreen.DeviceManager

                                )
                            )

                        }
                    }
                    DeeplinkConstants.DEEPLINK__MENU__ACCOUNT__PROMOCODE -> {
                        val uri = Uri.parse(originalLink)
                        val codePromo = uri.getQueryParameter("code") ?: ""
//                        if(Utils.isTablet) {
//                            navHomeMain.navigate(NavHomeMainDirections.actionGlobalToPromotionCodeDialog(code = codePromo))
//                        } else {
//                            navHomeMain.navigate(NavHomeMainDirections.actionGlobalToPromotionCode(code = codePromo))
//                        }
                        navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {

                            navHomeMain.navigate(
                                NavHomeMainDirections.actionGlobalToAccountAction(
                                    targetScreen = AccountMegaScreen.PromotionCode,
                                    promotionCode = codePromo

                                )
                            )

                        }
                    }
                    DeeplinkConstants.DEEPLINK__MENU__ACCOUNT__PAIDHISTORY -> {
//                        navHomeMain.navigate(NavHomeMainDirections.actionGlobalToPaymentHistory())
                        navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {

                            navHomeMain.navigate(
                                NavHomeMainDirections.actionGlobalToAccountAction(
                                    targetScreen = AccountMegaScreen.PaymentHistory

                                )
                            )
                        }

                    }
                    DeeplinkConstants.DEEPLINK__MENU__ACCOUNT__PAIDPACKAGE -> {
//                        navHomeMain.navigate(NavHomeMainDirections.actionGlobalToPackageUser())
                        navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {

                            navHomeMain.navigate(
                                NavHomeMainDirections.actionGlobalToAccountAction(
                                    targetScreen = AccountMegaScreen.PackageUser

                                )
                            )
                        }
                    }
                    DeeplinkConstants.DEEPLINK__MENU__ACCOUNT__HISTORY_FRIEND -> {
                        navHomeMain.navigate(NavHomeMainDirections.actionGlobalToFptInviteFriends(isLoadHistory = true))

                    }
                    DeeplinkConstants.DEEPLINK__MENU__ACCOUNT__INVITE_FRIEND -> {
                        navHomeMain.navigate(NavHomeMainDirections.actionGlobalToFptInviteFriends(isLoadHistory = false))
                    }
                    DeeplinkConstants.DEEPLINK__MENU__ACCOUNT__MULTI_PROFILE -> {
                        navigateToHomeTab(PageId.HomePageId.id)
                    }
                    //DEEPLINK__MENU__ACCOUNT__FAVORITE -> {}
                    else -> handleSuccess = false
                }
            }
            DeeplinkConstants.DEEPLINK__SCREEN__REGISTER -> {
                AdjustAllEvent.dataCur.sourcePage = SourcePage.deeplink_module
                navHomeMain.navigate(NavHomeMainDirections.actionGlobalToLogin(enterRegister = true))
//                navHomeMain.navigate(
//                    LoginInputFragmentDirections.actionLoginInputFragmentToLoginWithPhoneFragment(
//                        LoginViewModel.CALL_FROM_REGIS, ""
//                    )
//                )
            }
            DeeplinkConstants.DEEPLINK__SCREEN__LOGIN -> {
                AdjustAllEvent.dataCur.sourcePage = SourcePage.deeplink_module
                navHomeMain.navigate(NavHomeMainDirections.actionGlobalToLogin())
            }
            DeeplinkConstants.DEEPLINK__SCREEN__NOTIFICATION -> {
                navHomeMain.navigate(NavHomeMainDirections.actionGlobalToNotification())
            }
            DeeplinkConstants.DEEPLINK__SCREEN__SEARCH -> {
                navHomeMain.navigate(NavHomeMainDirections.actionGlobalToSearchFragmentDialog())
            }
            DeeplinkConstants.DEEPLINK__SCREEN__REWARD -> {
                navHomeMain.navigate(NavHomeMainDirections.actionGlobalToFptPlayReward(section = menu))
            }
            DeeplinkConstants.DEEPLINK__SCREEN__PAGE_IZIOS -> {
                // https://fptplay.vn/trang/<page-id>
                // https://fptplay.vn/screen/menu/subMenu/id/subMenuId
                val shouldNavigateToVodPage = (menu != PageId.ShortVideoPageId.id) // short-videos should not navigate to page (only bottom navigation bar)
                if(subMenu.isNotEmpty()) {
                    val deeplinkData = initPageIziosDeeplinkData(id = menu, block = subMenu)
                    navigateToHomeTab(
                        homeTabId = menu,
                        navController = navHomeMain,
                        navigateToVodPage = shouldNavigateToVodPage,
                        extendsArgs = deeplinkData
                    )
                } else {
                    navigateToHomeTab(
                        homeTabId = menu,
                        navController = navHomeMain,
                        navigateToVodPage = shouldNavigateToVodPage
                    )
                }
            }
            DeeplinkConstants.DEEPLINK__SCREEN__BLOCK_IZIOS -> {
                val realId = getVodIdFromPathSegment(subMenu)
                if(menu.isNotEmpty() && !realId.isNullOrEmpty()) {
                    // https://fptplay.vn/block/<block-type>/<block-id>
                    getVodIdFromPathSegment(menu)
                    navigateToHomeTab(PageId.HomePageId.id, navController = navHomeMain) {
                        navigateToBlockHighlightDetail(navController = navHomeMain, id = realId, blockType = menu, type = menu)
                    }
                }
            }

            DeeplinkConstants.DEEPLINK__SCREEN__PREMIER__VOD -> {
                navigateToHomeTab(PageId.HomePageId.id, navController = navHomeMain) {
                    val eventId = getVodIdFromPathSegment(menu)
                    if (!eventId.isNullOrEmpty()) {
                        navigateToPremier(navController = navHomeMain, premierId = eventId)
                    }
                }
            }

            DeeplinkConstants.DEEPLINK__MEGAZONE -> {

                when (menu) {
                    DeeplinkConstants.DEEPLINK__FOXPAY -> {
                        when(subMenu) {
                            DeeplinkConstants.FOXPAY__PHONE__RECHARGE -> {
                                handleDeepLinkForFoxPay(TypeScreen.PhoneRecharge, navHomeMain)
                            }
                            DeeplinkConstants.FOXPAY__HOME -> {
                                handleDeepLinkForFoxPay(TypeScreen.Home, navHomeMain)
                            }
                            else -> {
                                navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {}
                            }
                        }
                    } else -> {
                        navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {
                            navHomeMain.navigate(
                                NavHomeMainDirections.actionGlobalToNavMegaApp(
                                    megaAppId = menu,
                                    deeplinkUrl = originalLink,
                                    fromDeeplink = true
                                )
                            )
                        }
                    }

                }
//                when (menu) {
//                    DeeplinkConstants.DEEPLINK__FOXPAY -> {
//                        when(subMenu) {
//                            DeeplinkConstants.FOXPAY__PHONE__RECHARGE -> {
//                                handleDeepLinkForFoxPay(TypeScreen.PhoneRecharge, navHomeMain)
//                            }
//                            DeeplinkConstants.FOXPAY__HOME -> {
//                                handleDeepLinkForFoxPay(TypeScreen.Home, navHomeMain)
//                            }
//                            else -> {
//                                navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {}
//                            }
//                        }
//                    }
//                    DeeplinkConstants.DEEPLINK__FPTPLAYSHOP -> {
//                        when(subMenu) {
//                            // https://dev.fptplay.vn/megazone/fptplayshop
//                            // https://dev.fptplay.vn/megazone/fptplayshop/danh-sach-don-hang
//                            "",
//                            DeeplinkConstants.DEEPLINK__FPTPLAYSHOP__LIST__ORDER -> {
//                                handleDeeplinkForFptPlayShopListOrder(navController = navHomeMain, uri = Uri.parse(originalLink))
//                            }
//
//                            // https://dev.fptplay.vn/megazone/fptplayshop/chi-tiet-don-hang/8869
//                            DeeplinkConstants.DEEPLINK__FPTPLAYSHOP__DETAIL__ORDER -> {
//                                handleDeeplinkForFptPlayShopDetailOrder(detailId = id, navController = navHomeMain, uri = Uri.parse(originalLink))
//                            }
//
//                            else -> {
//                                navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {}
//                            }
//                        }
//                    }
//                    DeeplinkConstants.DEEPLINK__LSTSB -> {
//                        //https://fptplay.vn/megazone/luot-song-tren-san-bong
//
//                        handleDeepLinkForMDBD(navHomeMain)
//                    }
//
//                    DeeplinkConstants.DEEPLINK__LOYALTY -> {
//                        navHomeMain.navigate(NavHomeMainDirections.actionGlobalToLoyalty())
//                    }
//
//                    DeeplinkConstants.DEEPLINK__PAGE_MEGAZONE__3G -> {
//                        // https://fptplay.vn/megazone/mien-phi-3g-4g
//                        navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {
//                            navHomeMain.navigate(
//                                NavHomeMainDirections.actionGlobalToWebViewFragment(
//                                    url = originalLink,
//                                    title = homeActivity?.getString(R.string.mega_app_3g_service_title) ?: ""
//                                )
//                            )
//                        }
//                    }
//
//                    else -> {
//                        navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {
//                            if(menu.isNotBlank()) {
////                                navHomeMain.navigate(
////                                    NavHomeMainDirections.actionGlobalToNavMiniApp(
////                                        megaAppId = menu,
////                                        deeplinkUrl = originalLink,
////                                    )
////                                )
//                            } else {
////                                handleSuccess = false
//                            }
//                        }
//                    }
//                }
            }

            DeeplinkConstants.DEEPLINK__SCREEN__SHORTS_VIDEO,
            DeeplinkConstants.DEEPLINK__SCREEN__MOMENT -> {
                if (menu.isNotEmpty()) {
                    // https://fptplay.vn/moments/<id_moment>/<id-chapter>

                    navHomeMain.navigate(
                        NavHomeMainDirections.actionGlobalToMomentsFragment(
                            clickItem = true,
                            momentId = menu,
                            chapterId = subMenu,
                            isFromDeeplink = true
                        )
                    )

//                    val deeplinkData = initPageIziosDeeplinkData(id = PageId.ShortVideoPageId.id, block = "")
//                    navigateToShortVideoTab(
//                        shortVideoId = menu,
//                        shortVideoChapter = subMenu,
//                        navController = navHomeMain,
//                        extendsArgs = deeplinkData
//                    )
                }
            }
            DeeplinkConstants.DEEPLINK__SCREEN__PLAYLIST -> {

                val playlistId = getVodIdFromPathSegment(menu) ?: ""
                Timber.d("deeplink playlist $playlistId")
                if (playlistId.isNotEmpty()) {
                    // https://fptplay.vn/playlist/<title+id_playlist>

                    navHomeMain.navigate(
                        NavHomeMainDirections.actionGlobalToPlaylist(
                            playlistId = playlistId
                        )
                    )
                }
            }
            DeeplinkConstants.DEEPLINK__SCREEN__SUPPORT -> {
               val megaMenuItem = MegaMenuItem.support()
                navigateToHomeTab(PageId.AppPageId.id) {
                    navHomeMain.navigate(
                        NavHomeMainDirections.actionGlobalToMegaSubmenu(
                            title = megaMenuItem.title,
                            id = megaMenuItem.id,
                        )
                    )
                }
            }
            DeeplinkConstants.DEEPLINK__SCREEN__DOWNLOAD -> {
                navHomeMain.navigate(
                    NavHomeMainDirections.actionGlobalToDownloadV2()
                )
            }
//            DeeplinkConstants.DEEPLINK__SCREEN__MULTI_PROFILE -> {
//                navigateToHomeTab(PageId.HomePageId.id)
//            }

            DeeplinkConstants.DEEPLINK__SCREEN__GAME__ZONE,
            DeeplinkConstants.DEEPLINK__SCREEN__PLAY__ZONE -> {
                navHomeMain.navigate(
                    NavHomeMainDirections.actionGlobalToGame(
                        gameId = menu,
                        deeplinkUrl = originalLink,
                        fromDeeplink = true
                    )
                )
            }

            DeeplinkConstants.DEEPLINK__SCREEN__SHOP_GAME -> {
                var title = ""
                var message = ""
                try {
                    title = MainApplication.INSTANCE.sharedPreferences.getUnsupportedFeatureTitle()
                    message = MainApplication.INSTANCE.sharedPreferences.getUnsupportedFeatureMessage()
                } catch (ex: Exception) {
                    ex.printStackTrace()
                }

                if(title.isBlank()) {
                    title = "Tính năng không được hỗ trợ"
                }
                if (message.isBlank()) {
                    message = "Liên hệ CSKH để được hướng dẫn hoặc quay lại trang chủ để tiếp tục xem thêm nội dung"
                }
                navHomeMain.navigate(
                    NavHomeMainDirections.actionGlobalToDeeplinkNotSupportedDialog(
                        title = title,
                        message = message
                    )
                )
            }

            DeeplinkConstants.DEEPLINK__SCREEN__PLADIO -> {
                if(subMenu.isNotBlank()) {
                    when(menu) {
                        DeeplinkConstants.DEEPLINK__MENU__PLADIO__SERIES -> {
                            navHomeMain.navigate(
                                NavHomeMainDirections.actionGlobalToNavPladio(
                                    contentId = subMenu,
                                    pladioContentType = Song.PladioContentType.Series.rawValue,
                                    navigateType = PladioNavigateType.NavigateWithPladioContentTypeAndType.rawValue
                                )
                            )
                        }
                        DeeplinkConstants.DEEPLINK__MENU__PLADIO__ALBUM -> {
                            navHomeMain.navigate(
                                NavHomeMainDirections.actionGlobalToNavPladio(
                                    contentId = subMenu,
                                    pladioContentType = Song.PladioContentType.Playlist.rawValue,
                                    pladioType = Song.PladioType.Music.rawValue,
                                    navigateType = PladioNavigateType.NavigateWithPladioContentTypeAndType.rawValue
                                )
                            )
                        }
                        DeeplinkConstants.DEEPLINK__MENU__PLADIO__PLAYLIST -> {
                            navHomeMain.navigate(
                                NavHomeMainDirections.actionGlobalToNavPladio(
                                    contentId = subMenu,
                                    pladioContentType = Song.PladioContentType.Playlist.rawValue,
                                    pladioType = Song.PladioType.Podcast.rawValue,
                                    navigateType = PladioNavigateType.NavigateWithPladioContentTypeAndType.rawValue
                                )
                            )
                        }
                        else -> {
                            // go to pladio home
                            navHomeMain.navigate(NavHomeMainDirections.actionGlobalToNavPladio())

                        }

                    }
                } else if(menu.isNotBlank()) {

                    if(menu == DeeplinkConstants.DEEPLINK__MENU__PLADIO__PLAY) {
                        val uri = Uri.parse(originalLink)
                        val pladioType = when(uri.getQueryParameter("type") ?: "") {
                            "music" -> Song.PladioType.Music
                            "podcast" -> Song.PladioType.Podcast
                            "live" -> Song.PladioType.Event
                            else -> null
                        }
                        val contentId = uri.getQueryParameter("content") ?: ""

                        if(pladioType == null || contentId.isBlank()) {
                            // if pladioType not defined or id blank, go to pladio home page
                            navHomeMain.navigate(NavHomeMainDirections.actionGlobalToNavPladio())
                            return
                        }
                        val playlistId = uri.getQueryParameter("list") ?: ""
                        val pladioContentType = if(playlistId.isNotBlank()) Song.PladioContentType.Playlist else Song.PladioContentType.Single
                        val startTime = try {
                            (uri.getQueryParameter("start") ?: "").toInt()
                        } catch(e: Exception) {
                            0
                        }
                        val autoplay = try {
                            (uri.getQueryParameter("autoplay") ?: "").toInt() == 1
                        } catch(e: Exception) {
                            true
                        }
                        val episodeId = uri.getQueryParameter("episode_id") ?: ""

                        navHomeMain.navigate(
                            NavHomeMainDirections.actionGlobalToNavPladio(
                                contentId = contentId,
                                pladioContentType = pladioContentType.rawValue,
                                pladioType = pladioType.rawValue,
                                navigateType = PladioNavigateType.OpenPlayerDialog.rawValue,
                                playlistId = playlistId,
                                startTime = startTime,
                                autoplay = autoplay,
                                episodeId = episodeId
                            )
                        )
                    } else {
                        // go to pladio home
                        navHomeMain.navigate(NavHomeMainDirections.actionGlobalToNavPladio())
                    }
                } else {
                    // go to pladio home
                    navHomeMain.navigate(NavHomeMainDirections.actionGlobalToNavPladio())

                }

            }
            else -> handleSuccess = false
        }
        Timber.i("handleSuccess $handleSuccess")

        if (!handleSuccess && originalLink.isNotBlank()) {
            Timber.e("handleSuccess error $originalLink - $useWebViewInApp")
            // if is host fplay, always use webview even if link is not defined
//            if (!useWebViewInApp) {
//                openWebBrowser(originalLink)
//                return
//            }
            navHomeMain.navigate(
                NavHomeMainDirections.actionGlobalToWebViewFragment(
                    url = originalLink,
                    title = "FPT Play"
                )
            )
        }
    }

    private fun handleDeeplinkForFptPlayShopListOrder(navController: NavController, uri: Uri) {
        if(uri.queryParameterNames.isNotEmpty()) {
            navigateToHomeTab(homeTabId = PageId.AppPageId.id, navController = navController) {
                navController.navigateSafe(
                    directions = NavHomeMainDirections.actionGlobalToFptPlayShop(screenType = FPTPlayShopFragment.SCREEN_LOAD_URL, url = uri.toString())
                )
            }
        } else {
            navigateToHomeTab(homeTabId = PageId.AppPageId.id, navController = navController) {
                navController.navigateSafe(
                    directions = NavHomeMainDirections.actionGlobalToFptPlayShop(screenType = FPTPlayShopFragment.SCREEN_LIST_ORDER)
                )
            }
        }
    }

    private fun handleDeeplinkForFptPlayShopDetailOrder(detailId: String, navController: NavController, uri: Uri) {
        if(uri.queryParameterNames.isNotEmpty()) {
            navigateToHomeTab(homeTabId = PageId.AppPageId.id, navController = navController) {
                navController.navigateSafe(
                    directions = NavHomeMainDirections.actionGlobalToFptPlayShop(screenType = FPTPlayShopFragment.SCREEN_LOAD_URL, url = uri.toString())
                )
            }
        } else {
            navigateToHomeTab(homeTabId = PageId.AppPageId.id, navController = navController) {
                navController.navigateSafe(
                    directions = NavHomeMainDirections.actionGlobalToFptPlayShop(screenType = FPTPlayShopFragment.SCREEN_DETAIL_ORDER, detailId = detailId)
                )
            }
        }
    }
//    fun handDeepLinkSportPage( navHomeMain : NavController,id:String ="",event:String="",eventId:String=""){
//        val deeplinkData = initSportDeeplinkData(id = id,event = event,eventId = eventId)
//        navigateToHomeTab(PageId.SportPageId.id, navController = navHomeMain) {
//            navHomeMain.navigateSafe(
//                directions = NavHomeMainDirections.actionGlobalToCategoryDetail(
//                    id = id,
//                    screenProvider = PageId.SportPageId.id,
//                ),
//                extendArgs = if(deeplinkData != null) {
//                    Bundle().apply {
//                        putBundle(
//                            DeeplinkConstants.DEEPLINK__SPORT__BUNDLE_NAME,
//                            deeplinkData
//                        )
//                    }
//                } else {
//                    null
//                })
//        }
//    }
    fun handleDeepLinkForFoxPay(typeScreen : TypeScreen, navHomeMain : NavController) {
        //https://fptplay.vn/megazone/foxpay/naptiendienthoai
        navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {
            if (MainApplication.INSTANCE.sharedPreferences.userLogin()) {
                val foxSdkManager = FoxpayUtils.initFoxpaySDK(
                    MainApplication.INSTANCE.sharedPreferences.linkingToken(),
                    navHomeMain.context
                )
                FoxpayUtils.openFoxpay(
                    navHomeMain.context, foxSdkManager, typeScreen
                )
            } else {
                navHomeMain.navigate(NavHomeMainDirections.actionGlobalToLogin())
            }
        }
    }

    fun handleDeepLinkForMDBD(navHomeMain : NavController){
        //https://fptplay.vn/megazone/mdbd
        navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {
            if (MainApplication.INSTANCE.sharedPreferences.userLogin()) {
                navHomeMain.navigate(NavHomeMainDirections.actionGlobalToGameMdbd())

            } else {
                navHomeMain.navigate(NavHomeMainDirections.actionGlobalToLogin(navigationId = R.id.action_global_to_game_mdbd))

            }
        }
    }
    fun handleDeeplinkInPageIzios(deeplinkData: Bundle?, currentFragment: Fragment? = null, currentNavController: NavController? = null){
        if(deeplinkData == null) {
            return
        }
        val navController = currentFragment?.findNavController() ?: currentNavController
        val targetScreen = deeplinkData.getString(DeeplinkConstants.DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE__SCREEN_KEY) ?: return
        val sessionId = deeplinkData.getString(DeeplinkConstants.DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE__PAGE_ID_KEY) ?: return
        Timber.tag("tam-deeplink").d("targetScreen: $targetScreen - sessionId $sessionId")
        if(targetScreen.isEmpty() || sessionId.isEmpty()) {
            return
        }
        val directions = when (targetScreen) {
            DeeplinkConstants.DEEPLINK__PAGE_IZIOS__BLOCK__LEAGUE_SCHEDULE_KEY -> {
//                if(sessionId.isNotEmpty() && navController.currentDestination?.id == R.id.category_detail_fragment){
                if(sessionId.isNotEmpty()){
                    NavHomeMainDirections.actionGlobalToSportTournamentScheduleAndResult(
                        sessionId,
                        1,
                        title = "",
                        description = ""
                    )
                } else {
                    null
                }
            }
            DeeplinkConstants.DEEPLINK__PAGE_IZIOS__BLOCK__LEAGUE_RESULT_KEY -> {
//                if(sessionId.isNotEmpty() && navController.currentDestination?.id == R.id.category_detail_fragment){
                if(sessionId.isNotEmpty()){
                    NavHomeMainDirections.actionGlobalToSportTournamentScheduleAndResult(
                        sessionId,
                        2,
                        title = "",
                        description =""
                    )
                } else {
                    null
                }
            }
            DeeplinkConstants.DEEPLINK__PAGE_IZIOS__BLOCK__LEAGUE_RANK_KEY -> {
//                if(sessionId.isNotEmpty() && navController.currentDestination?.id == R.id.category_detail_fragment){
                if(sessionId.isNotEmpty()){
                    NavHomeMainDirections.actionGlobalToSportTournamentTeamRank(sessionId,title = "",
                        description = "")
                } else {
                    null
                }
            }
            DeeplinkConstants.DEEPLINK__PAGE_IZIOS__BLOCK__SPORT_SCHEDULE_KEY -> {
                NavHomeMainDirections.actionGlobalToSportSchedule(sessionId,"")
            }
            else -> {
                null
            }
        }
        if(directions != null) {
            navController?.navigateSafe(
                directions = directions,
                extendArgs = Bundle().apply {
                    putBundle(DeeplinkConstants.DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE_NAME, deeplinkData)
                }
            )
        }
        currentFragment?.arguments?.remove(DeeplinkConstants.DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE_NAME)

    }
    fun handleDeeplinkInGame30s(deeplinkData: Bundle?, currentFragment: Fragment) {
        if(deeplinkData == null) {
            return
        }
        val navController = currentFragment.findNavController()
        val targetScreen = deeplinkData.getString(DeeplinkConstants.DEEPLINK__30s__BUNDLE__SCREEN_KEY) ?: return
        val gameId = deeplinkData.getString(DeeplinkConstants.DEEPLINK__30s__BUNDLE__GAME_ID_KEY) ?: return

        if(targetScreen.isEmpty() || gameId.isEmpty()) {
            return
        }

        val directions = when (targetScreen) {
            DeeplinkConstants.DEEPLINK__SUB_MENU__GAME30s__TEAM -> {
                val teamId = deeplinkData.getString(DeeplinkConstants.DEEPLINK__30s__BUNDLE__TEAM_ID_KEY) ?: ""
                if(teamId.isNotEmpty() && navController.currentDestination?.id == R.id.play_start_fragment){
                    PlayStartFragmentDirections.actionPlayStartFragmentToPlayStartDetailFragment(
                        gameId = gameId,
                        teamId = teamId
                    )
                } else {
                    null
                }

            }
            DeeplinkConstants.DEEPLINK__SUB_MENU__GAME30s__VIDEO_DETAIL -> {
                val teamId = deeplinkData.getString(DeeplinkConstants.DEEPLINK__30s__BUNDLE__TEAM_ID_KEY) ?: ""
                val videoId = deeplinkData.getString(DeeplinkConstants.DEEPLINK__30s__BUNDLE__VIDEO_ID_KEY) ?: ""

                if(teamId.isNotEmpty() && videoId.isNotEmpty()) {
                    when (navController.currentDestination?.id) {
                        R.id.play_start_fragment -> {
                            PlayStartFragmentDirections.actionPlayStartFragmentToPlayStartDetailFragment(
                                gameId = gameId,
                                teamId = teamId
                            )
                        }
                        R.id.play_start_detail_fragment -> {
                            PlayStartDetailFragmentDirections.actionDetailToShortVideo(
                                gameId = gameId,
                                teamId = teamId,
                                videoId = videoId
                            )
                        }
                        else -> {
                            null
                        }
                    }
                } else {
                    null
                }

            }
            DeeplinkConstants.DEEPLINK__SUB_MENU__GAME30s__GAME -> {
                if(navController.currentDestination?.id == R.id.play_start_fragment) {
                    PlayStartFragmentDirections.actionPlayStartFragmentToPlayStartVotingFragment(
                        gameId = gameId
                    )
                } else {
                    null
                }

            }
            DeeplinkConstants.DEEPLINK__SUB_MENU__GAME30s__GAME_GUIDE -> {
                when (navController.currentDestination?.id) {
                    R.id.play_start_fragment -> {
                        PlayStartFragmentDirections.actionPlayStartFragmentToPlayStartVotingFragment(
                            gameId = gameId
                        )
                    }
                    R.id.play_start_voting_fragment -> {
                        PlayStartVotingFragmentDirections.actionVotingToGuide(
                            gameId = gameId
                        )
                    }
                    else -> {
                        null
                    }
                }
            }
            DeeplinkConstants.DEEPLINK__SUB_MENU__GAME30s__GAME_RANKING -> {
                when (navController.currentDestination?.id) {
                    R.id.play_start_fragment -> {
                        PlayStartFragmentDirections.actionPlayStartFragmentToPlayStartVotingFragment(
                            gameId = gameId
                        )
                    }
                    R.id.play_start_voting_fragment -> {
                        PlayStartVotingFragmentDirections.actionVotingToRanking(
                            gameId = gameId
                        )
                    }
                    else -> {
                        null
                    }
                }
            }
            else -> {
                null
            }
        }

        if(directions != null) {
            navController.navigateSafe(
                directions = directions,
                extendArgs = Bundle().apply {
                    putBundle(DeeplinkConstants.DEEPLINK__30s__BUNDLE_NAME, deeplinkData)
                }
            )
        }
        currentFragment.arguments?.remove(DeeplinkConstants.DEEPLINK__30s__BUNDLE_NAME)
    }

    private fun init30sDeeplinkData(screen: String, gameId: String, teamId: String? = null, videoId: String? = null): Bundle {
        return Bundle().apply {
            putString(DeeplinkConstants.DEEPLINK__30s__BUNDLE__SCREEN_KEY, screen)
            putString(DeeplinkConstants.DEEPLINK__30s__BUNDLE__GAME_ID_KEY, gameId)
            if(!teamId.isNullOrEmpty())
                putString(DeeplinkConstants.DEEPLINK__30s__BUNDLE__TEAM_ID_KEY, teamId)
            if(!videoId.isNullOrEmpty())
                putString(DeeplinkConstants.DEEPLINK__30s__BUNDLE__VIDEO_ID_KEY, videoId)
        }

    }
    private fun initPageIziosDeeplinkData(id: String? = "", block: String? = ""):Bundle{
        return Bundle().apply {
            putString(DeeplinkConstants.DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE__SCREEN_KEY, block)
            if(!id.isNullOrEmpty())
                putString(DeeplinkConstants.DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE__PAGE_ID_KEY, id)
        }
    }
    private fun findNavHomeMainController(activity: HomeActivity?): NavController? {
        if(activity == null) {
            return null
        }
        val navController = activity.navHostFragment?.navController
        return if(navController?.graph?.id == R.id.nav_home_main) {
            navController
        } else {
            null
        }
    }

    // region logic block deeplink
    private fun isDeeplinkBlocked(screen: String,
                                  menu: String,
                                  subMenu: String,
                                  id: String,
                                  subMenuId: String,
                                  useWebViewInApp: Boolean = false,
                                  originalLink: String = "",
                                  pathSegments: List<String> = ArrayList()
    ): Boolean {
        Timber.d("isDeeplinkBlocked ${MultiProfileUtils.isCurrentProfileKid(sharedPreferences = MainApplication.INSTANCE.sharedPreferences)}")

        if(MultiProfileUtils.isCurrentProfileKid(sharedPreferences = MainApplication.INSTANCE.sharedPreferences)) {
            try {
                if(whiteListDeeplinkForProfileKid.contains(originalLink)) {
                    return false
                } else if (whiteListDeeplinkScreenForProfileKid.contains(screen)) {
                    return false
                }
                return true

            } catch(e: Exception) {
                // if fail, block deeplink unknown for profile kid
                return true
            }
        }

        return false
    }

    private val whiteListDeeplinkScreenForProfileKid = listOf(
        DeeplinkConstants.DEEPLINK__SCREEN__VOD__FROM_LANDING_PAGE,
        DeeplinkConstants.DEEPLINK__SCREEN__TV__FROM_LANDING_PAGE,
        DeeplinkConstants.DEEPLINK__SCREEN__EVENT__FROM_LANDING_PAGE,
        DeeplinkConstants.DEEPLINK__SCREEN__EVENT_TV__FROM_LANDING_PAGE
    )
    private val whiteListDeeplinkForProfileKid = listOf<String>() // full url white list
    // endregion logic block deeplink
    // region Navigation

    private fun navigateToSupportCenter(navHomeMain: NavController) {
        navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {
            navHomeMain.navigate(R.id.start_zendesk_dialog)
        }
    }
    private fun navigateToShortVideoTab(
        shortVideoId: String = "",
        shortVideoChapter:String = "",
        navController: NavController? = null,
        extendsArgs: Bundle? = null,
        block: (() -> Unit)? = null,
    ){
        val homeTabId = PageId.ShortVideoPageId.id
        try {
            val currentTabLayout = tabLayout
            if (currentTabLayout == null) {
                // Case TabLayout null but current screen is HomeMainFragment => Screen is not init fully
                // => Add argument to make screen then the screen's logic will automatically run when init is completed
                if (navController?.currentDestination?.id == R.id.home_main_fragment) {
                    Timber.d("*****setDefaultTabId: $homeTabId")
                    navController.currentBackStackEntry?.arguments?.putString("defaultTabId", homeTabId)
                    navController.currentBackStackEntry?.arguments?.putString("id", shortVideoId)
                    navController.currentBackStackEntry?.arguments?.putString("chapterId", shortVideoChapter)
                    navController.currentBackStackEntry?.arguments?.putBoolean("isFromDeeplink", true)
                    navController.currentBackStackEntry?.arguments?.putBundle(
                        DeeplinkConstants.DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE_NAME,
                        extendsArgs
                    )
                } else {
                    Timber.d("*****navigateToHomeMainFragment and select tabId: $homeTabId")
                    navController?.navigateSafe(
                        directions = NavHomeMainDirections.actionGlobalToShortVideos(
                            defaultTabId = homeTabId,
                            id = shortVideoId,
                            chapterId = shortVideoChapter,
                            isFromDeeplink = true
                        ),
                        extendArgs = if(extendsArgs != null) {
                            Bundle().apply {
                                putBundle(
                                    DeeplinkConstants.DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE_NAME,
                                    extendsArgs
                                )
                            }
                        } else {
                            null
                        }
                    )
                }
            } else {
                var isMenu = false
                for (index in 0..currentTabLayout.tabCount) {
                    if (currentTabLayout.getTabAt(index)?.tag == homeTabId) {
                        currentTabLayout.getTabAt(index)?.select()
                        isMenu = true
                        break
                    }
                }
                if(!isMenu) {
                    try {
                        val currentPosition = if(currentTabLayout.selectedTabPosition in 0 until currentTabLayout.tabCount)
                            currentTabLayout.selectedTabPosition
                        else
                            0
                        currentTabLayout.getTabAt(currentPosition)?.select()
                    } catch (ex: Exception) {
                        ex.printStackTrace()
                    }

                } else {
                    handleDeeplinkInPageIzios(extendsArgs, currentFragment = null, currentNavController = navController)
                }

            }
        } finally {
            block?.invoke()
        }
    }

    private fun navigateToHomeTab(
        homeTabId: String?,
        navController: NavController? = null,
        navigateToVodPage: Boolean = false, // navigate to vod page if tab layout currently not exist && pageId not at bottom navigation
        extendsArgs: Bundle? = null,
        block: (() -> Unit)? = null,
    ) {
        Timber.d("*****navigateToHomeTab $tabLayout with id: $homeTabId")
        try {
            val currentTabLayout = tabLayout
            if (currentTabLayout == null) {
                // Case TabLayout null but current screen is HomeMainFragment => Screen is not init fully
                // => Add argument to make screen then the screen's logic will automatically run when init is completed
                if (navController?.currentDestination?.id == R.id.home_main_fragment) {
                    Timber.d("*****setDefaultTabId: $homeTabId")
                    navController.currentBackStackEntry?.arguments?.putString("defaultTabId", homeTabId)
                    navController.currentBackStackEntry?.arguments?.putBoolean("navigateToVodPage", navigateToVodPage)
                    navController.currentBackStackEntry?.arguments?.putBundle(
                        DeeplinkConstants.DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE_NAME,
                        extendsArgs
                    )
                } else {
                    Timber.d("*****navigateToHomeMainFragment and select tabId: $homeTabId")
                    navController?.navigateSafe(
                        directions = NavHomeMainDirections.actionGlobalToHomeMainFragment(
                            defaultTabId = homeTabId,
                            navigateToVodPage = navigateToVodPage
                        ),
                        extendArgs = if(extendsArgs != null) {
                            Bundle().apply {
                                putBundle(
                                    DeeplinkConstants.DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE_NAME,
                                    extendsArgs
                                )
                            }
                        } else {
                            null
                        }
                    )
                }
            } else {
                var isMenu = false
                for (index in 0..currentTabLayout.tabCount) {
                    if (currentTabLayout.getTabAt(index)?.tag == homeTabId) {
                        currentTabLayout.getTabAt(index)?.select()
                        isMenu = true
                        break
                    }
                }
                if(!isMenu) {
                    if(homeTabId != null) {

                        if(navigateToVodPage) {
                            // allow navigate to vod page
                            navController?.let {
                                navigateToVodPageDetail(it, homeTabId, extendsArgs)
                            }
                        } else {
                            // not menu && not allow navigate to vod page, select current page pos or first page
                            // take note: case !isMenu && !navigateToVodPage how to handler?
                            Timber.w("setDefaultTabMenu select first page")
                            try {
                                val currentPosition = if(currentTabLayout.selectedTabPosition in 0 until currentTabLayout.tabCount)
                                    currentTabLayout.selectedTabPosition
                                else
                                    0
                                currentTabLayout.getTabAt(currentPosition)?.select()
                            } catch (ex: Exception) {
                                ex.printStackTrace()
                            }

                        }
                    }


                } else {
                    handleDeeplinkInPageIzios(extendsArgs, currentFragment = null, currentNavController = navController)
                }

            }
        } finally {
            block?.invoke()
        }
    }

    private fun navigateToHome(navController: NavController, homeTabId: String = PageId.HomePageId.id, bundle: Bundle? = null) {
        Timber.w("navigateToHome $homeTabId")
        Timber.w("navController.currentDestination?.id ${navController.currentDestination}")
        if(navController.currentDestination?.id != R.id.home_main_fragment && tabLayout != null) {
            navController.navigate(NavHomeMainDirections.actionGlobalToHomeMainFragment(
                defaultTabId = homeTabId
            ))
        } else {
            navigateToHomeTab(navController = navController, homeTabId = homeTabId)
        }
    }

    private fun navigateToVodPageDetail(navController: NavController, pageId: String, extendsArgs: Bundle? = null) {


        navController.navigateSafe(
            directions = NavHomeMainDirections.actionGlobalToCategoryDetail(
                id = pageId
            ),
            extendArgs = if(extendsArgs != null) {
                Bundle().apply {
                    putBundle(
                        DeeplinkConstants.DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE_NAME,
                        extendsArgs
                    )
                }
            } else {
                null
            })
    }

    private fun navigateToBlockHighlightDetail(navController: NavController,
                                               id: String,
                                               blockType: String = ItemType.CATEGORY_HIGHLIGHT.id,
                                               type: String = ItemType.CATEGORY_HIGHLIGHT.id) {
        Timber.tag("tamlog").d("${this.javaClass} navigateToBlockHighlightDetail ${PageId.HomePageId.id}")
        navController.navigate(NavHomeMainDirections.actionGlobalToViewMoreFragment(
            blockStyle = BlockStyle.HorizontalSliderSmall.id,
            blockType = blockType,
            id = id,
            header = "",
            subHeader = "",
            deeplink = true,
            screenProvider = PageId.HomePageId.id
        ))

    }
    private fun navigateToBHomeBaseDetail(navController: NavController, sessionId: String) {
        navController.navigate(
            NavHomeMainDirections.actionGlobalToCategoryDetail(id =sessionId)
        )
    }

    private fun navigateToLiveTv(navController: NavController, channelId: String) {
        TrackingUtil.setTrackingKey(TrackingUtil.TrackingKey.NONE)
        navController.navigate(
            NavHomeMainDirections.actionGlobalToTvDetail(
                idToPlay = channelId,
                timeShiftLimit = 0,
                timeShift = 0,
                useArgs = true
            )
        )

    }

    private fun navigateToVodDetail(navController: NavController, vodId: String, screenProvider: String) {
        navController.navigate(
            NavHomeMainDirections.actionGlobalToVod(
                id = vodId,
                screenProvider = screenProvider
            )
        )

    }

    private fun navigateToPremier(navController: NavController, premierId: String) {
        navController.navigate(
            NavHomeMainDirections.actionGlobalToPremiere(
                idToPlay = premierId
            )
        )
    }
    // endregion Navigation In App

    //region create deeplink manually
    fun createDeeplink(data: LandingPage?): String? {
        return data?.run {
            when (data.type) {
                Constants.LANDING_PAGE_FRAGMENT_VOD_TYPE -> createDeeplink(screen = DeeplinkConstants.DEEPLINK__SCREEN__VOD__FROM_LANDING_PAGE, contentId = data.objectId ?: "")
                Constants.LANDING_PAGE_FRAGMENT_LIVE_TV_TYPE -> createDeeplink(screen = DeeplinkConstants.DEEPLINK__SCREEN__TV__FROM_LANDING_PAGE, contentId = data.objectId ?: "")
                Constants.LANDING_PAGE_FRAGMENT_EVENT_TYPE -> createDeeplink(screen = DeeplinkConstants.DEEPLINK__SCREEN__EVENT__FROM_LANDING_PAGE, contentId = data.objectId ?: "")
                Constants.LANDING_PAGE_FRAGMENT_EVENT_TV_TYPE -> createDeeplink(screen = DeeplinkConstants.DEEPLINK__SCREEN__EVENT_TV__FROM_LANDING_PAGE, contentId = data.objectId ?: "")
                else -> null
            }
        }
    }

    fun createDeeplink(screen: String, contentId: String): String {
        return "https://${DeeplinkConstants.HOST_FPT_PLAY}/${screen}/${contentId}"
    }
    //endregion create deeplink manually


    // region Handle Intent Deeplink
    fun Intent.addFirebaseNotificationData(oldIntent: Intent?) {
        Timber.d("*****addFirebaseNotificationData ${oldIntent?.extras} - ${oldIntent?.data}")
        oldIntent?.let {
            if(it.hasExtra(Constants.FIREBASE_NOTIFICATION_NEW)) {
                Timber.d("it.intent.getBooleanExtra(Constants.FIREBASE_NOTIFICATION_NEW, false) ${it.getBooleanExtra(Constants.FIREBASE_NOTIFICATION_NEW, false)}")
                putExtra(
                    Constants.FIREBASE_NOTIFICATION_NEW,
                    it.getBooleanExtra(Constants.FIREBASE_NOTIFICATION_NEW, false)
                )
                Timber.d("it.intent.getStringExtra(Constants.FIREBASE_NOTIFICATION_TYPE) ${it.getStringExtra(Constants.FIREBASE_NOTIFICATION_TYPE)}")
                putExtra(
                    Constants.FIREBASE_NOTIFICATION_TYPE,
                    it.getStringExtra(Constants.FIREBASE_NOTIFICATION_TYPE)
                )
                Timber.d("it.intent.getStringExtra(Constants.FIREBASE_NOTIFICATION_TYPE_ID) ${it.getStringExtra(Constants.FIREBASE_NOTIFICATION_TYPE_ID)}")
                putExtra(
                    Constants.FIREBASE_NOTIFICATION_TYPE_ID,
                    it.getStringExtra(Constants.FIREBASE_NOTIFICATION_TYPE_ID)
                )
                Timber.d("it.intent.getStringExtra(Constants.FIREBASE_NOTIFICATION_URL) ${it.getStringExtra(Constants.FIREBASE_NOTIFICATION_URL)}")
                putExtra(
                    Constants.FIREBASE_NOTIFICATION_URL,
                    it.getStringExtra(Constants.FIREBASE_NOTIFICATION_URL)
                )
                Timber.d("it.intent.getStringExtra(Constants.FIREBASE_NOTIFICATION_TITLE) ${it.getStringExtra(Constants.FIREBASE_NOTIFICATION_TITLE)}")
                putExtra(
                    Constants.FIREBASE_NOTIFICATION_TITLE,
                    it.getStringExtra(Constants.FIREBASE_NOTIFICATION_TITLE)
                )
            }

            if (oldIntent.hasExtra(Constants.FIREBASE_NOTIFICATION_FILED_MESSAGE_ID)) {
                putExtra(Constants.FIREBASE_NOTIFICATION_NEW, true)

                if (it.hasExtra(Constants.FIREBASE_NOTIFICATION_FILED_TYPE)) {
                    putExtra(Constants.FIREBASE_NOTIFICATION_TYPE, it.getStringExtra(Constants.FIREBASE_NOTIFICATION_FILED_TYPE))
                }
                putExtra(Constants.FIREBASE_NOTIFICATION_TYPE_ID, it.getStringExtra(Constants.FIREBASE_NOTIFICATION_FILED_TYPE_ID) ?: "")
                putExtra(Constants.FIREBASE_NOTIFICATION_URL, it.getStringExtra(Constants.FIREBASE_NOTIFICATION_FILED_URL) ?: "")
            }

        }
    }
    fun Intent.addClevertapDeeplinkData(oldIntent: Intent?){
        Timber.d("addClevertapDeeplinkData $oldIntent")
        if(CleverTapAPI.getNotificationInfo(oldIntent?.extras).fromCleverTap){
            oldIntent?.let {
                putExtra(DeeplinkConstants.DEEPLINK__FROM_CLEVERTAP, true)
                putExtra(DeeplinkConstants.DEEPLINK__CLEVERTAP_CAMPAIN_ID, it.getStringExtra(DeeplinkConstants.DEEPLINK__CLEVERTAP_CAMPAIN_ID))
                putExtra(DeeplinkConstants.DEEPLINK__CLEVERTAP_TITLE, it.getStringExtra(DeeplinkConstants.DEEPLINK__CLEVERTAP_TITLE))
                putExtra(DeeplinkConstants.DEEPLINK__CLEVERTAP_DESCRIPTION, it.getStringExtra(DeeplinkConstants.DEEPLINK__CLEVERTAP_DESCRIPTION))
                putExtra(DeeplinkConstants.DEEPLINK__CLEVERTAP_LINK, it.getStringExtra(DeeplinkConstants.DEEPLINK__CLEVERTAP_LINK))
            }
        }

    }
    fun Intent.addFirebaseDeeplinkData(oldIntent: Intent?) {
        Timber.d("addFirebaseDeeplinkData")
        oldIntent?.let {
            if(it.hasExtra(DeeplinkConstants.FIREBASE_DYNAMIC_LINK_NEW_KEY)) {
                putExtra(
                    DeeplinkConstants.FIREBASE_DYNAMIC_LINK_NEW_KEY,
                    it.getBooleanExtra(DeeplinkConstants.FIREBASE_DYNAMIC_LINK_NEW_KEY, false)
                )
                putExtra(
                    DeeplinkConstants.FIREBASE_DYNAMIC_LINK_URL_KEY,
                    it.getStringExtra(DeeplinkConstants.FIREBASE_DYNAMIC_LINK_URL_KEY)
                )
            }
        }
    }

    fun Intent.addAdjustTrueLinkData(oldIntent: Intent?) {
        Timber.d("addAdjustTrueLinkData")
        oldIntent?.let {
            if(it.hasExtra(DeeplinkConstants.ADJUST_TRUE_LINK_NEW_KEY)) {
                putExtra(
                    DeeplinkConstants.ADJUST_TRUE_LINK_NEW_KEY,
                    it.getBooleanExtra(DeeplinkConstants.ADJUST_TRUE_LINK_NEW_KEY, false)
                )
                putExtra(
                    DeeplinkConstants.ADJUST_TRUE_LINK_URL_KEY,
                    it.getStringExtra(DeeplinkConstants.ADJUST_TRUE_LINK_URL_KEY)
                )
            }
        }
    }


    fun Intent.addFacebookDeeplinkData(oldIntent: Intent?) {
        Timber.d("addFacebookDeeplinkData")
        oldIntent?.let {
            if(it.hasExtra(DeeplinkConstants.APP_LINK_FACE_BOOK_NEW_KEY)) {
                putExtra(
                    DeeplinkConstants.APP_LINK_FACE_BOOK_NEW_KEY,
                    it.getBooleanExtra(DeeplinkConstants.APP_LINK_FACE_BOOK_NEW_KEY, false)
                )
                putExtra(
                    DeeplinkConstants.APP_LINK_FACE_BOOK_DATA_KEY,
                    it.getStringExtra(DeeplinkConstants.APP_LINK_FACE_BOOK_DATA_KEY)
                )
                putExtra(
                    DeeplinkConstants.APP_LINK_FACE_BOOK_VIA_SDK_KEY,
                    it.getBooleanExtra(DeeplinkConstants.APP_LINK_FACE_BOOK_VIA_SDK_KEY, false)
                )
            }
        }
    }
    // endregion Handle Intent Deeplink

    private fun checkUserLoginBeforeNavigate(
        showWarningDialog: Boolean = false,
        navigationId: Int? = null,
        extendsArgs: Bundle? = null,
        navigateFun: () -> Unit,
        navController: NavController? = null,
        popupAfterNavigate: Boolean = false
    ) {
        if (MainApplication.INSTANCE.sharedPreferences.userLogin()) {
            if(popupAfterNavigate)
                navController?.navigateUp()

            navigateFun()
        } else {
            if (showWarningDialog) {
                homeActivity?.let { activity ->
                    val warningDialogFragment = AlertDialog().apply {
                        setTextTitle(MainApplication.INSTANCE.sharedPreferences.getAccountLoginTitle().ifBlank { getString(R.string.login_title_default) })
                        setMessage(MainApplication.INSTANCE.sharedPreferences.getAccountLoginMsgRequire().ifBlank { getString(R.string.login_description_default) })
                        setTextExit(MainApplication.INSTANCE.sharedPreferences.getAccountLoginBtnNotOk().ifBlank { getString(R.string.login_description_cancel_default) })
                        setTextConfirm(MainApplication.INSTANCE.sharedPreferences.getAccountLoginBtnOk().ifBlank { getString(R.string.login_description_confirm_default) })
                        setOnlyConfirmButton(false)
                        setShowTitle(true)

                        setListener(object : AlertDialogListener {
                            override fun onExit() {
                                if (popupAfterNavigate)
                                    activity.navHostFragment?.findNavController()?.navigateUp()
                            }

                            override fun onConfirm() {
                                navigateToLogin(
                                    navigationId = navigationId,
                                    extendsArgs = extendsArgs
                                )
                            }

                            override fun onBackPress() {
                                if (popupAfterNavigate)
                                    activity.navHostFragment?.findNavController()?.navigateUp()
                            }

                        })
                    }

                    activity.navHostFragment?.childFragmentManager?.let {
                        warningDialogFragment.show(it, "WarningDialogLogin")

                    }
                }
            } else {
                navigateToLogin(
                    navigationId = navigationId,
                    extendsArgs = extendsArgs,
                    popupAfterNavigate = popupAfterNavigate,
                    navController = navController
                )
            }
        }
    }
    private fun navigateToLogin(
        navigationId: Int? = null,
        extendsArgs: Bundle? = null,
        requestRestartApp:Boolean = false,
        popupAfterNavigate: Boolean = false,
        navController: NavController? = null,
    ) {
        homeActivity?.run {
            if (popupAfterNavigate)
                navController?.navigateUp()

            navigationId?.let {
                navHostFragment?.navigateToLoginWithParams(
                    isDirect = true,
                    navigationId = it,
                    extendsArgs = extendsArgs,
                    requestRestartApp = requestRestartApp
                )
            } ?: run {
                navHostFragment?.navigateToLoginWithParams(
                    isDirect = true,
                    requestRestartApp = requestRestartApp
                )
            }
        }
    }

    // region Samsung SSO
    private fun isSamsungSsoDeeplink(deeplink: String): Boolean {
        val pathIdSamsungSso = "samsung-sso"
        return deeplink.contains(pathIdSamsungSso, ignoreCase = true)
    }

    private fun handleDeeplinkSamsungSSO(deeplink: String) {

        // if need to go to login, after login will go to deeplink utils again
        val extendsArgs = bundleOf(
            NAVIGATION_LINK_DEEP_LINK_KEY to deeplink
        )

        checkUserLoginBeforeNavigate(
            showWarningDialog = true,
            navigationId = NAVIGATION_ID_DEEP_LINK_HANDLE,
            navigateFun = { navigateToDeeplinkSamsungSSO() },
            extendsArgs = extendsArgs
        )
    }

    private fun navigateToDeeplinkSamsungSSO() {
        val samsungSsoLink = createSamsungSsoLink()
        val uri = Uri.parse(samsungSsoLink)

        /*** ID SSO được mã hóa URL từ bên thứ ba. Đây phải là mã định danh duy nhất cho mỗi
         * người dùng, vì đơn hàng và quản lý hạn mức EPP sẽ liên kết với ID này.
         */
        val ssoId = ssoId()

        /*** Dấu thời gian khi khách hàng nhấp vào liên kết trên trang SSO của bên thứ ba. Tham số
         * này được sử dụng để băm (hashing) và cũng là một phần của quá trình xác thực yêu cầu nhằm
         * đảm bảo yêu cầu chỉ có hiệu lực trong một khoảng thời gian ngắn. **Dấu thời gian tính theo giây**
         * và sử dụng múi giờ UTC (mặc định là 120 giây, có thể cấu hình tăng/giảm nhưng lưu ý rằng cấu
         * hình này áp dụng ở cấp máy chủ, do đó tất cả các quốc gia trên cùng máy chủ sẽ chia sẻ cùng
         * một giá trị).
         */
        val ssoTimeStamp = ssoTimeStamp()

        /***  Mã (SHA-512) của chuỗi **"?ud_s=xx_zzz_john.doe|ud_t=1712496354"** và được mã
        hóa URL. Lưu ý rằng hai tham số được phân tách bằng ký tự | và dấu ? ở đầu chuỗi là bắt buộc.
        Quy trình bao gồm băm trước, sau đó mã hóa URL.
         */
        val ssoHash = ssoHash(ssoId, ssoTimeStamp)

        val newUri = uri.buildUpon()
            .appendQueryParameter(ssoId.first, ssoId.second)
            .appendQueryParameter(ssoTimeStamp.first, ssoTimeStamp.second)
            .appendQueryParameter(ssoHash.first, ssoHash.second)
            .build()

        val navHomeMain = findNavHomeMainController(homeActivity)


        Timber.tag("tam-deeplink").d("samsung sso newUri: ${newUri.toString()}")

        openWebBrowser(newUri.toString())

    }


    private fun ssoId(): Pair<String, String> {
        /***
         *
         * ud_s– URL-encoded SSO ID from third party.
         * This must be a unique identifier for every
         * user as the orders and EPP quota
         * management will be linked to that ID.
         *
         * Format: xx_uuuuu_SSOID
         *
         * 1. xx is the country code in lowercase.
         * 2. uuuuu is the partner identifier. It must be 5 characters (to be
         * provided by Subsidiary).
         * 3. SSOID is the unique identifier of the partner user (It must contain only
         * alphanumeric characters, @,-,_)
         * e.g: <EMAIL>
         *
         * Final requirements: vn_FPLAY_SSOID
         * */
        val idSsoKey = "ud_s"
        return Pair(idSsoKey, "vn_FPLAY_${MainApplication.INSTANCE.sharedPreferences.userId()}")
    }

    private fun ssoTimeStamp(): Pair<String, String> {
        /***
         *
         * ud_t – The timestamp when the customer
         * clicks the link on the third party SSO site.
         * This is used for hashing purposes and also
         * forms part of the validation of the request
         * to ensure the request is only valid for a
         * short period of time
         *
         * Timestamp in VNT converts to UTC and then to seconds.
         *
         *
         * Final requirements: vn_FPLAY_SSOID
         * */
        val idSsoKey = "ud_t"
        return Pair(idSsoKey, (System.currentTimeMillis() / 1000).toString())
    }

    private fun ssoHash(
        ssoId: Pair<String, String>,
        ssoTimeStamp: Pair<String, String>
    ): Pair<String, String> {
        /***
         *
         * ud_h – URL-encoded hash (SHA-512) of
         * “?ud_s=xx_zzz_john.doe|ud_t=1712496354
         * ”. Note that the 2 parameters are pipeline-
         * separated (|) and the “?” at the beginning is
         * also required. You need to hash first and
         * then do a URL-encoding on it.
         *
         * ud_h - URL-encoded hash of ud_s and ud_t
         *
         * */

        val hashSsoKey = "ud_h"
        val hashSso = "?${ssoId.first}=${ssoId.second}|${ssoTimeStamp.first}=${ssoTimeStamp.second}"
        val key = Util.SAMSUNG_GEN_KEY
        return Pair(hashSsoKey, hashSso.hash512(secretKey = key))
    }

    private fun createSamsungSsoLink(): String {
        /***
         * https://shop.samsung.com/xx/multistore/yyy/zzzz
         *
         * This is the root URL of the EPP site where xx,
         * yyy and zzzz are variables and will be
         * provided by Samsung Vietnam to their
         * partners.
         *
         * Final requirements: https://shop.samsung.com/vn/multistore/vnepp/vn_fptplay/
        * */
        val xx = "vn"
        val yyy = "vnepp"
        val zzzz = "vn_fptplay"
        return "https://shop.samsung.com/$xx/multistore/$yyy/$zzzz/login/externalSSO"
    }

    // endregion Samsung SSO

}