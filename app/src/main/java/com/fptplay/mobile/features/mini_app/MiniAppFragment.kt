package com.fptplay.mobile.features.mini_app


import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity.RESULT_OK
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.webkit.*
import android.widget.RelativeLayout
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.Keep
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.app.ActivityOptionsCompat
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.work.WorkInfo
import com.android.billingclient.api.BillingClient
import com.fptplay.mobile.*
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.ActivityExtensions.openSettingForApp
import com.fptplay.mobile.common.extensions.edgeToEdge
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.extensions.runOnUiThread
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.utils.AppPermissionResultData
import com.fptplay.mobile.common.utils.DateTimeUtils
import com.fptplay.mobile.common.utils.DeeplinkUtils
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.common.utils.launch
import com.fptplay.mobile.common.utils.viewutils.ShareLinkLocalUtils
import com.fptplay.mobile.databinding.MiniAppFragmentBinding
import com.fptplay.mobile.features.mini_app.model.*
import com.fptplay.mobile.features.mini_app.snack_bar.ISnackBarDownloadListener
import com.fptplay.mobile.features.mini_app.snack_bar.SnackBarDownLoadProcessView
import com.fptplay.mobile.features.mini_app.utils.*
import com.fptplay.mobile.features.mini_app.viewmodel.MiniAppViewModel
import com.fptplay.mobile.features.payment.google_billing.BillingClientLifecycleV6
import com.fptplay.mobile.features.payment.google_billing.BillingUtils.isEnableGoogleBilling
import com.fptplay.mobile.features.payment.util.PaymentTrackingUtil
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.visible
import com.fptplay.mobile.services.player.BackgroundPlayerService
import com.google.android.material.snackbar.Snackbar
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.tear.modules.util.Utils.hide
import com.xhbadxx.projects.module.domain.entity.fplay.payment.MiniAppPaymentInfoList
import com.xhbadxx.projects.module.domain.entity.fplay.premium.PaymentVerifyTransaction
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import org.json.JSONArray
import org.json.JSONObject
import timber.log.Timber
import java.io.Serializable
import java.util.*
import java.util.concurrent.TimeUnit
import java.util.regex.Pattern
import javax.inject.Inject
import kotlin.collections.ArrayList
import kotlin.math.max
import kotlin.math.min

@AndroidEntryPoint
class MiniAppFragment :
    BaseFragment<MiniAppViewModel.MiniAppViewState, MiniAppViewModel.MiniAppViewIntent>() {

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    private var _binding: MiniAppFragmentBinding? = null
    private val binding get() = _binding!!
    override val hasEdgeToEdge: Boolean = false
    override val handleConfigurationChange = true
    override val viewModel: MiniAppViewModel by activityViewModels()
    private val safeArgs: MiniAppFragmentArgs by navArgs()
    private var savedViewInstance: View? = null
    private var mIsFirstLoad = false
    private val snackBarDownload: SnackBarDownLoadProcessView by lazy { SnackBarDownLoadProcessView(requireContext()) }
    private var url: String = ""
    private var megaAppId: String = ""
    private val downloadWorkManagement by lazy { DownloadWorkManagement(requireContext())}
    // Map<RequestCode, Queue<Pair<onGrantedFunction, onDeniedFunction>>>
    private val mapRequestPermissionHandle: HashMap<Int, Queue<Pair<() -> String, () -> String>>> =
        HashMap()
    private var countDownTimerShowLoading: CountDownTimer? = null

    // Tracking
    private var timeEnterGame = 0L

    //Billing
    private var billingClientLifecycle: BillingClientLifecycleV6? = null
    private var selectedPlan: MiniAppPaymentInfoList.MiniAppPaymentInfo? = null
    private var selectedProductId: String = ""
    private var currentRequestId: String? = null
    private var transactionId: String = ""
    private var roleName: String = ""
    private var paymentSuccessfulDialog: MiniAppPaymentSuccessfulDialog? = null
    private var paymentErrorDialog: AlertDialog? = null
    private var paymentProcessing = false
    private var closeAppInfo : CloseInfoAppMiniAppData?= null
    private val gatewaySdk by lazy {
        object : MiniAppGatewaySDK {
            override fun getSupportedMethods(requestId: String): String {
                val listFunctionProvide = ArrayList(MiniAppManagement.getSupportedMethods(
                    safeArgs.listFunctionMiniApp?.toList() ?: listOf()
                ))

                // add default function if not already have
                val listDefaultFunctionProvide = MiniAppManagement.getDefaultSupportedMethods()
                for( function in listDefaultFunctionProvide) {
                    if(!listFunctionProvide.contains(function)) {
                        listFunctionProvide.add(function)
                    }
                }
                return MiniAppManagement.getSupportedMethodsResponse(requestId = requestId, listJsonFunction = listFunctionProvide)

            }

            override fun getKeycode(requestId: String): String {
                return MiniAppManagement.keyCodeListResponse(
                    requestId = requestId,
                    keyCodeObj = MiniAppManagement.getListKeyCodes()
                )
            }

            override fun show(requestId: String) {
                //not implements in this phase
            }

            override fun hide(requestId: String) {
                //not implements in this phase
            }

            override fun destroy(requestId: String):String {
//                Timber.tag("tam-miniapp").i("destroy $requestId")
                binding.webView.apply {
                    postDelayed({
                        clearCache(true)
                        clearHistory()
                        try {
                            if (<EMAIL>) {
                                findNavController().navigateUp()
                            }
                        } catch(e: Exception) {
                            Timber.tag("tam-miniapp").e(e, "MiniAppGatewaySDK $requestId")

                        }
                    }, 200)
                }
                return MiniAppManagement.miniAppResponseDefault(requestId = requestId)
            }

            override fun getUserInfo(requestId: String): String {
                return MiniAppManagement.getUserInfo(requestId = requestId, viewModel = viewModel)
            }

            override fun shareString(requestId: String, content: String) :String{
                ShareLinkLocalUtils.onShareLink(context, content)
                return MiniAppManagement.miniAppResponseDefault(requestId = requestId)
            }

            override fun getContacts(requestId: String): String {
                return checkPermissionGetContacts(requestId)
            }

            override fun openLink(requestId: String, url: String):String{
                Timber.i("openLink $url handleDeeplink")
                if (url.isBlank()) return MiniAppManagement.miniAppResponseDefault(requestId = requestId)

                lifecycleScope.launch(Dispatchers.Main) {
                    DeeplinkUtils.openWebBrowser(url = url)
                }
                return MiniAppManagement.miniAppResponseDefault(requestId = requestId)

            }
            override fun copyString(requestId: String, content: String):String{
                val clipboardManager =
                    context?.getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
                val clipData = ClipData.newPlainText("text", content)
                clipboardManager?.setPrimaryClip(clipData)
                return MiniAppManagement.miniAppResponseDefault(requestId = requestId)

            }

            override fun callPhoneNumber(requestId: String, phoneNumber: String):String{
//                Timber.tag("tam-miniapp").i("callPhoneNumber $phoneNumber")
                val intent = Intent(Intent.ACTION_DIAL)
                intent.data = Uri.parse("tel:${Uri.encode(phoneNumber)}")
                context?.startActivity(intent)
                return MiniAppManagement.miniAppResponseDefault(requestId = requestId)
            }

            override fun getDeepLink(requestId: String): String {
                return MiniAppManagement.deeplinkResponse(requestId, safeArgs.deeplinkUrl ?: "")
            }

            override fun downloadImage(
                requestId: String,
                url: String,
                fileName: String,
                shareAfterDownload: java.lang.Boolean
            ): String {
                return checkPermissionDownload(
                    MiniAppDownloadRequest(
                        requestId = requestId,
                        url = url,
                        mineType = MiniAppDownloadRequestType.IMAGE,
                        fileName = fileName,
                        isShared = shareAfterDownload.booleanValue()
                    ),

                )

            }

            override fun downloadVideo(
                requestId: String,
                url: String,
                fileName: String,
                shareAfterDownload: java.lang.Boolean
            ): String {
                return checkPermissionDownload(
                    MiniAppDownloadRequest(
                        requestId = requestId,
                        url = url,
                        mineType = MiniAppDownloadRequestType.VIDEO,
                        fileName = fileName,
                        isShared = shareAfterDownload.booleanValue()
                    ),

                )
            }

            override fun showLoading(requestId: String, timeout: java.lang.Integer):String {
                showLoadWithTimeout(timeout = timeout.toLong() / 1000)
                return MiniAppManagement.miniAppResponseDefault(requestId = requestId)

            }

            override fun hideLoading(requestId: String):String{
                runOnUiThread {
                    hideLoading()
                }
                return MiniAppManagement.miniAppResponseDefault(requestId = requestId)
            }

            override fun shareMedia(requestId: String):String{
                checkPermissionPickImage(requestId = requestId)
                return MiniAppManagement.miniAppResponseDefault(requestId = requestId)
            }

            override fun genApiToken(requestId: String):String{
                viewModel.dispatchIntent(MiniAppViewModel.MiniAppViewIntent.GetPartnerToken(requestId = requestId,miniAppId = megaAppId))
                    return MiniAppManagement.blockingHtmlThread()
            }
            override fun openDeepLink(requestId: String, url: String) :String{
                /**
                 * note : function only called deeplink and close web view if running deep link
                 */
                lifecycleScope.launch(Dispatchers.Main) {
                   closeMiniAppAndOpenDeepLink(requestId = requestId, url = url)
                }
                return MiniAppManagement.miniAppResponseDefault(requestId = requestId)
            }

            override fun getUserID(requestId: String): String {
                return MiniAppManagement.userIdResponse(requestId, sharedPreferences.userId() ?: "")
            }

            override fun purchaseItem(
                requestId: String,
                productInfo: JSONObject?
            ): String {
                if (!paymentProcessing) {
                    resetPaymentData()
                    paymentProcessing = true
                    viewModel.dispatchIntent(
                        MiniAppViewModel.MiniAppViewIntent.GetProductList(requestId = requestId, productInfo = productInfo)
                    )
                }
//                return MiniAppManagement.miniAppResponseDefault(requestId = requestId)
                return MiniAppManagement.blockingHtmlThread()
            }

            override fun getStoreProducts(requestId: String, productIDs: JSONArray?): String {
                val arrProductIds = arrayListOf<String>()
                if(productIDs != null) {
                    for (productIndex in 0 until productIDs.length()) {
                        try {
                            (productIDs.get(productIndex) as? String)?.let {
                                arrProductIds.add(it)
                            }
                        } catch(ex: Exception) {
                            Timber.tag("tam-miniapp").e(ex, "getStoreProducts ex")

                        }
                    }
                }
                viewModel.dispatchIntent(MiniAppViewModel.MiniAppViewIntent.GetStoreProductList(requestId = requestId, gameId = safeArgs.megaAppId, productIds = arrProductIds))
                return MiniAppManagement.blockingHtmlThread()
//                return MiniAppManagement.miniAppResponseDefault(requestId = requestId)
            }
        }
    }
    private val miniAppJavascriptInterface by lazy {
        MiniAppJavascriptInterface(gatewaySDK = gatewaySdk)
    }

    // activity result launcher
    private val miniAppLifecycleObserver by lazy {
        MiniAppLifeCycleObserver(registry = requireActivity().activityResultRegistry)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        miniAppLifecycleObserver.setResultListener(object : MiniAppLifeCycleObserver.ResultListener {
            override fun onMiniAppResultListener(result: AppPermissionResultData) {
                Timber.d("Mini app request code: ${result.requestCode} data: ${result.grantResults}")
                runMiniAppPermissionResultListener(result)
            }
        })
        lifecycle.addObserver(miniAppLifecycleObserver)
    }

    override fun setUpEdgeToEdge() {
        when(safeArgs.theme?.lowercase()) {
            "fullscreen" -> {
                edgeToEdge(true)
            }
            else -> {
                edgeToEdge(false)
            }
        }
    }

    override fun bindOrientationStateChange(newConfig: Configuration) {
        Logger.d("bindOrientationStateChange")
        if (context.isTablet() && safeArgs.theme?.lowercase() == "fullscreen") {
            refreshFloatingCloseButtonPosition(binding.fbtnClose, newConfig.orientation)
        }
    }

    private fun handleMiniAppTheme(theme: String) {
        when(theme.lowercase()) {
            "fullscreen" -> {
                configHideToolBar()
                bindFloatingButtonClose()
            }

            else -> {
                configShowToolBar()
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Tracking
        trackingInfo.updatePlayingSession(time = System.currentTimeMillis())
        sendGameTracking(logId = "182", event = "EnterGame", realtimePlaying = "")
        //
        return if (savedViewInstance != null) {
            mIsFirstLoad = false
            savedViewInstance
        } else {
            _binding = MiniAppFragmentBinding.inflate(layoutInflater, container, false)
            savedViewInstance = binding.root
            mIsFirstLoad = true
            savedViewInstance
        }
    }

    private fun configHideToolBar() {
        binding.ctlHeader.gone()
        binding.fbtnClose.visible()
    }

    private fun configShowToolBar() {
        binding.ctlHeader.visible()
        binding.fbtnClose.hide()
    }

    private fun checkInitGoogleBilling() {
        safeArgs.listFunctionMiniApp?.let {
            if (it.contains(MiniAppConstants.PURCHASE_ITEM)) {
                initGoogleBilling()
            }
        }
    }

    override fun onStart() {
        super.onStart()
        timeEnterGame = System.currentTimeMillis() - timeEnterGame
    }

    override fun onResume() {
        super.onResume()
        if(viewModel.getListMiniAppPermission().contains(MiniAppConstants.KEEP_SCREEN_ON)) {
            binding.webView.keepScreenOn = true
        } else {
            binding.webView.keepScreenOn = false
        }
    }

    override fun onPause() {
        binding.webView.keepScreenOn = false
        super.onPause()
    }

    override fun onStop() {
        timeEnterGame = System.currentTimeMillis() - timeEnterGame
        super.onStop()
    }

    override fun bindComponent() {
        url = safeArgs.url
        megaAppId = safeArgs.megaAppId
        closeAppInfo = safeArgs.closeAppInfo
        if (mIsFirstLoad) {
            retryLoadPage()
            initWebViewContent()
        } else {
            binding.webView.apply {
                clearHistory()
            }
        }

        // Stop native player services
        triggerStopNativePlayer()

        checkInitGoogleBilling()
    }

    override fun bindData() {
        safeArgs.title?.let {
            if (it.isNotBlank()) {
                binding.tvTitle.text = it
            }
        }
        handleMiniAppTheme(safeArgs.theme ?: "")
        viewModel.saveListMiniAppPermission(safeArgs.listPermissionsMiniApp?.toList() ?: emptyList<String>())
        miniAppJavascriptInterface.miniAppMethods = safeArgs.listFunctionMiniApp?.toList() ?: arrayListOf()
    }

    override fun bindEvent() {
        val homeActivity = activity as? HomeActivity
        homeActivity?.onKeyDownObserver?.observe(this) {
            Timber.tag("tam-miniapp").d("safeArgs.listEventsMiniApp ${safeArgs.listEventsMiniApp}")
            if (safeArgs.listEventsMiniApp?.contains(MiniAppConstants.MiniAppEvent.KeyDown.id) == true) {
                val callback = MiniAppUtils.callback(
                    MiniAppManagement.onKeyDownEventRequest(
                        eventId = MiniAppConstants.MiniAppEvent.KeyDown.id,
                        keyCode = it
                    )
                )
                Timber.tag("tam-miniapp").d("callback $callback")
                evaluateJavascript(callback, valueCallback = {})
            }
        }
        binding.ivClose.onClickDelay {
            closeMiniApp()
        }
        downloadWorkManagement.addWorkManagerCallbacks(callbacks = workManagerCallbacks)
    }


    //region close button
    @SuppressLint("ClickableViewAccessibility")
    private fun bindFloatingButtonClose() {
        var dX = 0f
        var dY = 0f
        var initialX = 0f
        var initialY = 0f
        var isDragging = false
        val clickThreshold = 10
        binding.fbtnClose.onClickDelay {
            closeMiniApp()
        }
        binding.fbtnClose.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // Record initial touch point and button position
                    dX = v.x - event.rawX
                    dY = v.y - event.rawY
                    initialX = event.rawX
                    initialY = event.rawY
                    isDragging = false
                    true
                }

                MotionEvent.ACTION_MOVE -> {
                    val deltaX = Math.abs(event.rawX - initialX)
                    val deltaY = Math.abs(event.rawY - initialY)

                    // If the movement exceeds the click threshold, treat it as dragging
                    if (deltaX > clickThreshold || deltaY > clickThreshold) {
                        isDragging = true
                        v.y = event.rawY + dY // only move in vertical direction
                    }
                    true
                }

                MotionEvent.ACTION_UP -> {
                    if (isDragging) {
                        // Snap the button to the closest screen edge when dragging ends
                        refreshFloatingCloseButtonPosition(
                            v,
                            MainApplication.INSTANCE.resources.configuration.orientation
                        )
                    } else {
                        // Handle click action if it's not a drag
                        v.performClick()
                    }
                    true
                }

                else -> false
            }
        }
    }

    private fun refreshFloatingCloseButtonPosition(v: View, e: Int) {
        val paddingVertical = context?.resources?.getDimensionPixelSize(R.dimen.mini_app__floating_close_button_margin_top)?.toFloat() ?: 0f
        if(v.width == 0 || v.height == 0) return

        val parent = v.parent as ConstraintLayout
        Logger.d("refreshPosition ${parent.width} ${parent.height}")
        Logger.d("Current position: ${v.x} ${v.y}")
        val (parentWidth, parentHeight) = if(e == Configuration.ORIENTATION_LANDSCAPE){
            Logger.d("setup for landscape")
            max(parent.width, parent.height) to min(parent.width, parent.height)
        } else {
            Logger.d("setup for portrait")
            min(parent.width, parent.height) to max(parent.width, parent.height)
        }

        val centerY = v.y + v.height / 2

        val closestEdgeY = if (v.y in paddingVertical..(parentHeight.toFloat() - v.height - paddingVertical)) {
            v.y
        } else {
            if (centerY < parentHeight / 2) paddingVertical else (parentHeight - v.height - paddingVertical)
        }


        v.animate()
            .y(closestEdgeY)
            .setDuration(300)
            .start()
    }
    //endregion

    // region destroyView
    override fun onDestroyView() {
        super.onDestroyView()
        Timber.tag("tam-mininapp").d("onDestroyView")
        downloadWorkManagement.cancelAllRequestDownload()
        cancelCountdownTimer()
        binding.webView.apply {
            keepScreenOn = false
            removeJavascriptInterface(JAVASCRIPT_INTERFACE_NAME)
            loadUrl(HTML_BLANK_PAGE)
            clearCache(true)
            clearHistory()
        }
        // Tracking
        sendGameTracking(
            logId = "183",
            event = "ExitGame",
            realtimePlaying = TimeUnit.MILLISECONDS.toSeconds(timeEnterGame).toString()
        )
        trackingInfo.updatePlayingSession(0L)
        //
        _binding = null
        resetPaymentData()
        billingClientLifecycle = null
        paymentSuccessfulDialog?.dismissAllowingStateLoss()
    }
    // endregion destroyView

    // region back pressed
    override val handleBackPressed = true
    override fun backHandler() {
        Timber.tag("tam-mininapp").d("backHandler")
    }

    // endregion back pressed
    override fun retryLoadPage() {
        if (url.isBlank()) {
            showMiniAppErrorDialog(
                title = getString(R.string.notification),
                message = getString(R.string.all_not_have_data),
                onConfirm = { findNavController().navigateUp() })
        } else {
            viewModel.urlMiniApp = url
            binding.webView.loadUrl(url)
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initWebViewContent() {
        if (BuildConfig.DEBUG) {
            /**
             * link debug :chrome://inspect/devices#devices
             * **/
            WebView.setWebContentsDebuggingEnabled(true)
        }

        binding.webView.apply {
            settings.apply {
                javaScriptEnabled = true
                javaScriptCanOpenWindowsAutomatically = true
                domStorageEnabled = true
                allowUniversalAccessFromFileURLs = true
                allowFileAccessFromFileURLs = true
                loadWithOverviewMode = true
                useWideViewPort = true
                layoutAlgorithm = WebSettings.LayoutAlgorithm.SINGLE_COLUMN
                //              setInitialScale(1)
//                isScrollbarFadingEnabled = false
//                scrollBarStyle = WebView.SCROLLBARS_OUTSIDE_OVERLAY
            }
            setBackgroundColor(android.graphics.Color.TRANSPARENT)

            addJavascriptInterface(miniAppJavascriptInterface, JAVASCRIPT_INTERFACE_NAME)

            webChromeClient = object : WebChromeClient() {
                @Deprecated("Deprecated in Java")
                override fun onConsoleMessage(
                    message: String?,
                    lineNumber: Int,
                    sourceID: String?
                ) {
                    super.onConsoleMessage(message, lineNumber, sourceID)
                    Timber.tag("tam-miniapp")
                        .d("onConsoleMessage: $message - $lineNumber - $sourceID")
                }


            }

            webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView, url: String) {
                    hideLoading()
                }


                override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                    Logger.d("shouldOverrideUrlLoading: $url")
                    Logger.d("WildCard: ${safeArgs.allowWildCard?.toList()}")
                    if (isUrlValidateWithMainUrl(url, safeArgs.url) || isUrlValidateWithWildCard(url = url, wildCards = safeArgs.allowWildCard?.toList())
                    ) {
                        return false
                    } else {
                        openExternalBrowser(url)
                        return true
                    }
                }

                override fun onReceivedError(
                    view: WebView?,
                    request: WebResourceRequest?,
                    error: WebResourceError?
                ) {
                    super.onReceivedError(view, request, error)
                }

                override fun onRenderProcessGone(
                    view: WebView,
                    detail: RenderProcessGoneDetail
                ): Boolean {
                    return super.onRenderProcessGone(view, detail)
                }
            }
        }
    }

    private fun evaluateJavascript(script: String, valueCallback: ValueCallback<String>) {
        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
            binding.webView.evaluateJavascript(script, valueCallback)
        }
    }

    private fun isUrlValidateWithWildCard(url: String, wildCards: List<String>?): Boolean {
        if (wildCards == null) {
            return true
        } else {
            for (pattern in wildCards) {
                val regex = pattern.replace("*", ".*").replace("?", ".")
                if (Pattern.matches(regex, url)) {
                    return true
                }
            }
            return false
        }
    }

    private fun isUrlValidateWithMainUrl(requestUrl: String, mainUrl: String): Boolean {
        return requestUrl == mainUrl
    }

    private fun openExternalBrowser(url: String) {
        lifecycleScope.launch(Dispatchers.Main) {
            DeeplinkUtils.openWebBrowser(url = url)
        }
    }

    private fun callBackResponseAfterBlocking(responseEvent: String){
        val callback = MiniAppUtils.callback(responseEvent)
        evaluateJavascript(callback, valueCallback = {})
    }

    private fun showMiniAppErrorDialog(
        title: String? = null,
        message: String,
        onConfirm: (() -> Unit)
    ) {
        Timber.d("Show error dialog")
        AlertDialog().apply {
            setTextTitle(title = title ?: <EMAIL>(R.string.notification))
            setMessage(message = message)
            setShowTitle(true)
            setOnlyConfirmButton(true)
            setTextConfirm(text = <EMAIL>(R.string.all_string_known))
            setListener(object : AlertDialogListener {
                override fun onConfirm() {
                    dismissAllowingStateLoss()
                }
            })
        }.show(childFragmentManager, "showMiniAppErrorDialog")
    }


    // region permission
    private fun closeMiniApp(){
        if (closeAppInfo!=null){
            showWarningDialog(
                textTitle = closeAppInfo?.title,
                message = closeAppInfo?.message?:"",
                isOnlyConfirmButton = false,
                textConfirm = <EMAIL>(R.string.all_string_out_game),
                textClose = <EMAIL>(R.string.alert_close),
                onConfirm = {
                 findNavController().navigateUp()
                }
            )
        }
        else{
            findNavController().navigateUp()
        }
    }
    private fun runMiniAppPermissionResultListener(result: AppPermissionResultData) {
        val isGranted = result.grantResults.values.all { it }
        if (isGranted) {
            val data = result.extraBundle?.getSerializable(BUNDLE_EXTRAS_DATA) as MiniAppRequest
            when (result.requestCode) {
                GET_CONTACT_PERMISSION_REQUEST_CODE -> {
                    if(data is MiniAppContactRequest){
                        checkPermissionGetContacts(requestId =data.requestId?:"")
                    }
                }
                PICK_MEDIA_REQUEST_CODE -> {
                    if(data is MiniAppPickMediaRequest){
                        pickImageFromGallery()
                    }
                }
                GET_DOWNLOAD_PERMISSION_REQUEST_CODE, -> {
                    if(data is MiniAppDownloadRequest){
                        checkPermissionDownload(miniAppDownloadRequest = data)
                    }
                }
                GET_DOWNLOAD_PERMISSION_AND_SHARE_REQUEST_CODE -> {
                    if (data is MiniAppDownloadRequest) {
                        checkPermissionDownload(miniAppDownloadRequest = data)
                    }
                }
                else->{

                }
            }
        } else {
            // case : do nothing
        }
    }

    private fun openSystemSettingForApp() {
        activity?.openSettingForApp()
    }

    private fun showWarningPermissionDenied(requestCode:Int, onConfirm: () -> Unit) {
        val message: String = when (requestCode) {
            GET_CONTACT_PERMISSION_REQUEST_CODE -> {
                getString(R.string.mini_app_permission_get_contact_denied)
            }
            GET_DOWNLOAD_PERMISSION_REQUEST_CODE -> {
                getString(R.string.mini_app_permission_download_denied)
            }
            GET_DOWNLOAD_PERMISSION_AND_SHARE_REQUEST_CODE -> {
                getString(R.string.mini_app_permission_download_and_share_denied)
            }
            PICK_MEDIA_REQUEST_CODE -> {
                getString(R.string.mini_app_permission_access_gallery_denied)
            }
            else -> {
                getString(R.string.mini_app_warning_dialog_no_permission_default_message)
            }
        }
        AlertDialog().apply {
            setTextTitle(<EMAIL>(R.string.mini_app_title_permission_denied))
            setShowTitle(true)
            setMessage(message)
            setTextConfirm(<EMAIL>(R.string.mini_app_warning_dialog_request_permission_confirm_button))
            setTextExit(<EMAIL>(R.string.mini_app_warning_dialog_request_permission_cancel_button))
            setOnlyConfirmButton(false)
            setListener(object : AlertDialogListener {
                override fun onConfirm() {
                    onConfirm()
                }
            })
        }.show(childFragmentManager, "MiniAppPermissionDenied")
    }

    private val activityForResultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                val selectedImageUri = result?.data?.data
                selectedImageUri?.apply {
                    ShareLinkLocalUtils.onShareFile(
                        context = context,
                        selectedImageUri = selectedImageUri
                    )
                }
            }
        }
    private val workManagerCallbacks by lazy {
        object : WorkManagerCallbacks {
          override fun onDownloading(requestId: String, workInfo: WorkInfo, progress: Float) {
              showSnackBarProcess(
                  requestId = requestId ?: "",
                  text = getString(
                      R.string.mini_app_process_downloading,
                      progress.toInt().toString() + "%"
                  ),
                  isDownload = true
              )
          }
          override fun onDownloadSuccess(
              requestId: String,
              workInfo: WorkInfo,
              pathFile: String,
              success: String,
              isShared: Boolean
          ) {
              if (isShared) {
                  hideSnackBarProcess()
                  if(workInfo.state.isFinished){
                      // case : Don't share which cancelled download
                      ShareLinkLocalUtils.onShareFile(
                          context = context,
                          Uri.parse(pathFile)
                      )
                  }
              } else {
                  showSnackBarProcess(
                      requestId = requestId ?: "",
                      text = success,
                      isDestroy = true,
                      isDownload = false
                  )
              }
              // send response for web with requestId
              val downloadResponse = MiniAppManagement.downloadResponse(
                  isSuccess = true,
                  valueCode = 1,
                  requestId = requestId ?: "",
                  jsonResult = true,
                  jsonError = null
              )
              callBackResponseAfterBlocking(responseEvent = downloadResponse)
          }

          override fun onDownloadFailure(
              requestId: String,
              workInfo: WorkInfo,
              errorMessage: String,
              isErrorInternet: Boolean
          ) {
              showSnackBarProcess(
                  requestId = requestId ?: "",
                  text = errorMessage?:"",
                  isDestroy = true,
                  isDownload = false
              )
              // send response for web with requestId
              val downloadResponse = MiniAppManagement.downloadResponse(
                  isSuccess = false,
                  valueCode = -1,
                  requestId = requestId ?: "",
                  jsonResult = false,
                  jsonError = MiniAppResponseError(
                      code = MiniAppConstants.ERROR_CODE_DEFAULT,
                      message = context?.getString(R.string.mini_app_message_download_error)
                          ?: "",
                      detail = null
                  )
              )
              callBackResponseAfterBlocking(responseEvent = downloadResponse)
          }
      }
    }
    private fun checkPermissions(
        requestId: String,
        listPermissions: List<String>,
        requestCode: Int,
        onGranted: () -> String,
        onDenied: () -> String,
        extraBundle: Bundle? = null,
        launchOptions: ActivityOptionsCompat? = null
    ): String {
        val listPermissionsNeeded: MutableList<String> = ArrayList()
        for (permission in listPermissions) {
            if (ContextCompat.checkSelfPermission(
                    requireActivity(),
                    permission
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                listPermissionsNeeded.add(permission)
            }
        }
        when {
            listPermissionsNeeded.isEmpty() -> {
                // if granted, process and return data
                return onGranted()
            }
            shouldShowRequestPermissionRationale(listPermissions.first())->{
                // if granted all , process and return data
                showWarningPermissionDenied(requestCode = requestCode) {
                    openSystemSettingForApp()
                }

                val queueRequestPermissionHandle =
                    if (mapRequestPermissionHandle[requestCode] != null) {
                        mapRequestPermissionHandle[requestCode]!!
                    } else {
                        LinkedList()
                    }
                queueRequestPermissionHandle.add(Pair(onGranted, onDenied))
                mapRequestPermissionHandle[requestCode] = queueRequestPermissionHandle
                return MiniAppManagement.blockingHtmlThread()
            }
            else -> {
                // if have not granted, request permission from user while return blocking thread for html
                val queueRequestPermissionHandle =
                    if (mapRequestPermissionHandle[requestCode] != null) {
                        mapRequestPermissionHandle[requestCode]!!
                    } else {
                        LinkedList()
                    }
                queueRequestPermissionHandle.add(Pair(onGranted, onDenied))
                mapRequestPermissionHandle[requestCode] = queueRequestPermissionHandle
                checkPermissions(
                    permissions = listPermissions.toTypedArray(),
                    requestCode = requestCode,
                    extraBundle = extraBundle,
                    launchOptions = launchOptions
                )
                return MiniAppManagement.blockingHtmlThread()
            }
        }
    }

    private fun checkPermissions(permissions: Array<String>, requestCode: Int,extraBundle: Bundle? = null, launchOptions: ActivityOptionsCompat? = null) {
        miniAppLifecycleObserver.miniAppLauncher?.launch(
            requestCode = requestCode,
            permissions = permissions,
            extraBundle = extraBundle,
            launchOptions = launchOptions
        )
    }

    private fun showNoPermissionNotify() {
        Snackbar
            .make(
                binding.root,
                R.string.mini_app_warning_dialog_no_permission_read_contact,
                Snackbar.LENGTH_INDEFINITE
            )
            .setAction(R.string.mini_app_permission_action_ok) {
                activityForResultLauncher.launch(
                    MiniAppUtils.getAppSettingsIntent(
                        requireContext()
                    )
                )
            }
            .show()
    }

    // permission contact
    private fun checkPermissionGetContacts(requestId: String): String {
        val permissions = listOf(
            Manifest.permission.READ_CONTACTS
        )
        return checkPermissions(
            requestId = requestId,
            listPermissions = permissions,
            requestCode = GET_CONTACT_PERMISSION_REQUEST_CODE,
            onGranted = {
                return@checkPermissions getContactList(requestId)
            },
            onDenied = {
                return@checkPermissions MiniAppManagement.miniAppError(
                    requestId = requestId,
                    code = MiniAppConstants.ERROR_CODE_PERMISSION_DENIED,
                    message = context?.getString(R.string.mini_app_permission_get_contact_denied)
                        ?: "",
                    detail = context?.getString(R.string.mini_app_permission_get_contact_denied)
                        ?: ""
                )
//                showWarningPermissionDenied(title = getString(R.string.mini_app_warning_dialog_no_permission_read_contact)) {
//                    checkPermissionGetContacts(requestId)
//                }
            },
            extraBundle = bundleOf(BUNDLE_EXTRAS_DATA to MiniAppContactRequest(requestId = requestId))
        )
    }

    @SuppressLint("IntentReset")
    private fun pickImageFromGallery(): String {
        val intent = Intent(Intent.ACTION_PICK)
        intent.type = "image/*, video/*"
        activityForResultLauncher.launch(intent = intent, requestCode = PICK_MEDIA_REQUEST_CODE)
        return ""
    }

    private fun getContactList(requestId: String): String {
        val contactList = MiniAppManagement.getContactList(activity)
        return MiniAppManagement.contactListResponse(requestId, contactList)

    }

    private fun checkPermissionPickImage(requestId: String): String {
        var permissions = listOf<String>()
        permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            listOf(
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.READ_MEDIA_VIDEO,
            )
        } else {
            listOf(
                Manifest.permission.READ_EXTERNAL_STORAGE,
            )
        }
        return checkPermissions(
            requestId = requestId,
            listPermissions = permissions,
            requestCode = PICK_MEDIA_REQUEST_CODE,
            onGranted = {
                return@checkPermissions pickImageFromGallery()
            },
            onDenied = {
                return@checkPermissions MiniAppManagement.miniAppError(
                    requestId = requestId,
                    code = MiniAppConstants.ERROR_CODE_PERMISSION_DENIED,
                    message = context?.getString(R.string.mini_app_permission_access_gallery_denied)
                        ?: "",
                    detail = context?.getString(R.string.mini_app_permission_access_gallery_denied)
                        ?: ""
                )
            },
            extraBundle = bundleOf(BUNDLE_EXTRAS_DATA to MiniAppPickMediaRequest(requestId = requestId))
        )
    }
    private fun closeMiniAppAndRequestUserLogin(){
        binding.webView.apply {
            postDelayed({
                clearCache(true)
                clearHistory()
                try {
                    if (<EMAIL>) {
                        findNavController().navigateUp().run {
                            activity?.findNavHostFragment()?.navigateToLoginWithParams(isDirect = true)
                        }
                    }
                } catch(e: Exception) {
                    if(<EMAIL>){
                        findNavController().navigateUp()
                    }

                }
            }, 200)
        }
    }
    private fun closeMiniAppAndOpenDeepLink(requestId: String,url: String){
        binding.webView.apply {
            postDelayed({
                clearCache(true)
                clearHistory()
                try {
                    if (<EMAIL>) {
                        findNavController().navigateUp().run {
                            DeeplinkUtils.parseDeepLinkAndExecute(
                                deeplink = url,
                                useWebViewInApp = false,
                                trackingInfo = MainApplication.INSTANCE.trackingInfo,
                                isDeeplinkCalledInApp = true
                            )
                        }
                    }
                } catch(e: Exception) {
                    Timber.tag("tam-miniapp").e(e, "MiniAppGatewaySDK $requestId")
                }
            }, 200)
        }
    }
    //permissions downloaded
    private  fun checkPermissionDownload(
        miniAppDownloadRequest: MiniAppDownloadRequest
    ): String {
        var permissions = listOf<String>()
        permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            listOf(
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.READ_MEDIA_VIDEO,
            )
        } else {
            listOf(
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.READ_EXTERNAL_STORAGE,
            )
        }
        return checkPermissions(
            requestId = miniAppDownloadRequest.requestId ?: "",
            listPermissions = permissions,
            requestCode = if(miniAppDownloadRequest.isShared)  GET_DOWNLOAD_PERMISSION_AND_SHARE_REQUEST_CODE else GET_DOWNLOAD_PERMISSION_REQUEST_CODE,
            onGranted = {//
                return@checkPermissions  downloadUrlAndShare(
                    miniAppDownloadRequest = miniAppDownloadRequest,
                )
            },
            onDenied = {
                return@checkPermissions MiniAppManagement.miniAppError(
                    requestId = miniAppDownloadRequest.requestId ?: "",
                    code = MiniAppConstants.ERROR_CODE_PERMISSION_DENIED,
                    message = if (miniAppDownloadRequest.isShared) getString(R.string.mini_app_permission_download_and_share_denied) else getString(
                        R.string.mini_app_permission_download_denied
                    ),
                    detail = if (miniAppDownloadRequest.isShared) getString(R.string.mini_app_permission_download_and_share_denied) else getString(
                        R.string.mini_app_permission_download_denied
                    ),
                    )
            },
            extraBundle = bundleOf(BUNDLE_EXTRAS_DATA to miniAppDownloadRequest),
        )
    }
    private fun downloadUrlAndShare(
        miniAppDownloadRequest: MiniAppDownloadRequest,
    ): String {
        var downloadResponse = MiniAppManagement.blockingHtmlThread()
        CoroutineScope(Dispatchers.Main).launch {
            downloadWorkManagement.addWorkRequest(miniAppDownloadRequest = miniAppDownloadRequest)
        }
        return downloadResponse
    }
    private fun showLoadWithTimeout(timeout: Long) {
        runOnUiThread {
            try {
                if (timeout > 0) {
                    cancelCountdownTimer()
                    showLoading()
                    countDownTimerShowLoading = object : CountDownTimer(TimeUnit.SECONDS.toMillis(timeout), 1000) {
                        override fun onTick(millisUntilFinished: Long) {}
                        override fun onFinish() {
                            hideLoading()
                            cancelCountdownTimer()
                        }
                    }
                    countDownTimerShowLoading?.start()
                }
                else{
                    showLoading()
                }
            }
            catch (e: Exception) {
                hideLoading()
            }
        }
    }
    private fun cancelCountdownTimer() {

        countDownTimerShowLoading?.cancel()
        countDownTimerShowLoading = null
    }
    private fun cancelRequestDownload(requestId: String){
        downloadWorkManagement.cancelRequestDownload(workRequestId = requestId)
        val downloadResponse = MiniAppManagement.downloadResponse(
            isSuccess = false,
            valueCode = -1,
            requestId = requestId ?: "",
            jsonResult = false,
            jsonError = MiniAppResponseError(
                code = MiniAppConstants.ERROR_CODE_DEFAULT,
                message = context?.getString(R.string.mini_app_message_download_error)
                    ?: "",
                detail = null
            )
        )
        callBackResponseAfterBlocking(responseEvent = downloadResponse)
        hideSnackBarProcess()
    }
    fun showSnackBarProcess(requestId: String,text: String,isDestroy: Boolean = false,isDownload:Boolean = true) {
        if (snackBarDownload.parent != null) { hideSnackBarProcess() }
        val params = RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, RelativeLayout.LayoutParams.MATCH_PARENT).apply {
            addRule(RelativeLayout.CENTER_HORIZONTAL)
            addRule(RelativeLayout.ALIGN_PARENT_BOTTOM)
        }
        try {
            snackBarDownload.setText(text = text)
            snackBarDownload.hideCancelButton(isDownload)
            (view as? ViewGroup)?.addView(snackBarDownload, params)
            if(isDestroy){
                snackBarDownload.postDelayed({
                    hideSnackBarProcess()
                }, 2000)
            }
            snackBarDownload.setListener(object :ISnackBarDownloadListener{
                override fun onCancelRequestDownload() {
                  cancelRequestDownload(requestId)
                }
            })
        } catch (ex: IllegalStateException) {
            (view as? ViewGroup)?.removeView(snackBarDownload)
            ex.printStackTrace()
        }
    }

    private fun hideSnackBarProcess() {
        try {
            (view as? ViewGroup)?.removeView(snackBarDownload)
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    override fun MiniAppViewModel.MiniAppViewState.toUI() {
        when (this) {
            is MiniAppViewModel.MiniAppViewState.Loading -> {
//                showLoading()
            }
            is MiniAppViewModel.MiniAppViewState.Error -> {
                hideLoading()
                when(this.intent) {
                    is MiniAppViewModel.MiniAppViewIntent.GetPartnerToken -> {
                        // return output for sdk
                        val tokenResponse = MiniAppManagement.genTokenResponse(
                            isSuccess = false,
                            valueCode = -1,
                            requestId = this.intent.requestId ?: "",
                            jsonResult = null,
                            jsonError = MiniAppResponseError(
                                code = MiniAppConstants.ERROR_CODE_DEFAULT,
                                message = this.message
                                    ?: "",
                                detail = null
                            )
                        )
                        callBackResponseAfterBlocking(responseEvent =  tokenResponse)
                    }
                    is MiniAppViewModel.MiniAppViewIntent.GetStoreProductList -> {
                        // return output for sdk
                        val getStoreProductResponse = MiniAppManagement.getStoreProductResponse(
                            isSuccess = false,
                            valueCode = -1,
                            requestId = this.intent.requestId ?: "",
                            jsonResult = null,
                            jsonError = MiniAppResponseError(
                                code = MiniAppConstants.ERROR_CODE_DEFAULT,
                                message = this.message
                                    ?: "",
                                detail = null
                            )
                        )
                        callBackResponseAfterBlocking(responseEvent =  getStoreProductResponse)
                    }
                    is MiniAppViewModel.MiniAppViewIntent.GetProductList,
                    is MiniAppViewModel.MiniAppViewIntent.CreateGoogleBillingTransaction,
                    is MiniAppViewModel.MiniAppViewIntent.VerifyGoogleBillingTransaction -> {
                        val requestId = when (intent) {
                            is MiniAppViewModel.MiniAppViewIntent.GetProductList -> intent.requestId
                            is MiniAppViewModel.MiniAppViewIntent.CreateGoogleBillingTransaction -> intent.requestId
                            is MiniAppViewModel.MiniAppViewIntent.VerifyGoogleBillingTransaction -> intent.requestId
                            else ->  ""
                        }
                        Timber.tag("tam-miniapp").e("Error error $intent ${message}")
                        handlePurchaseItemFail(requestId, GamePurchaseItemResponse.GamePurchaseItemError.SystemError)
                        showErrorDialog(message)
                        resetPaymentData()
                    }
                    else -> {
                    }
                }
            }
            is MiniAppViewModel.MiniAppViewState.ErrorNoInternet -> {
                hideLoading()
                when(intent) {
                    is MiniAppViewModel.MiniAppViewIntent.GetProductList,
                    is MiniAppViewModel.MiniAppViewIntent.CreateGoogleBillingTransaction,
                    is MiniAppViewModel.MiniAppViewIntent.VerifyGoogleBillingTransaction -> {

                        val requestId = when (intent) {
                            is MiniAppViewModel.MiniAppViewIntent.GetProductList -> intent.requestId
                            is MiniAppViewModel.MiniAppViewIntent.CreateGoogleBillingTransaction -> intent.requestId
                            is MiniAppViewModel.MiniAppViewIntent.VerifyGoogleBillingTransaction -> intent.requestId
                            else ->  ""
                        }
                        Timber.tag("tam-miniapp").e("ErrorNoInternet error $intent ${message}")
                        handlePurchaseItemFail(requestId, GamePurchaseItemResponse.GamePurchaseItemError.UnknownError())
                        resetPaymentData()
                    }
                    else -> {}
                }
            }
            is MiniAppViewModel.MiniAppViewState.ErrorRequiredLogin -> {
                hideLoading()
                when(intent) {

                    is MiniAppViewModel.MiniAppViewIntent.GetPartnerToken -> {
                        val tokenResponse = MiniAppManagement.genTokenResponse(
                            isSuccess = false,
                            valueCode = -1,
                            requestId = this.intent.requestId ?: "",
                            jsonResult = null,
                            jsonError = MiniAppResponseError(
                                code = MiniAppConstants.ERROR_CODE_DEFAULT,
                                message = this.message ?: "",
                                detail = null
                            )
                        )
                        callBackResponseAfterBlocking(responseEvent =  tokenResponse)
                        closeMiniAppAndRequestUserLogin()
                    }
                    is MiniAppViewModel.MiniAppViewIntent.GetStoreProductList -> {

                        // return output for sdk
                        val getStoreProductResponse = MiniAppManagement.getStoreProductResponse(
                            isSuccess = false,
                            valueCode = -1,
                            requestId = this.intent.requestId ?: "",
                            jsonResult = null,
                            jsonError = MiniAppResponseError(
                                code = MiniAppConstants.ERROR_CODE_DEFAULT,
                                message = this.message
                                    ?: "",
                                detail = null
                            )
                        )
                        callBackResponseAfterBlocking(responseEvent =  getStoreProductResponse)
                        closeMiniAppAndRequestUserLogin()
                    }

                    is MiniAppViewModel.MiniAppViewIntent.GetProductList,
                    is MiniAppViewModel.MiniAppViewIntent.CreateGoogleBillingTransaction,
                    is MiniAppViewModel.MiniAppViewIntent.VerifyGoogleBillingTransaction -> {

                        val requestId = when (intent) {
                            is MiniAppViewModel.MiniAppViewIntent.GetProductList -> intent.requestId
                            is MiniAppViewModel.MiniAppViewIntent.CreateGoogleBillingTransaction -> intent.requestId
                            is MiniAppViewModel.MiniAppViewIntent.VerifyGoogleBillingTransaction -> intent.requestId
                            else ->  ""
                        }
                        handlePurchaseItemFail(requestId, GamePurchaseItemResponse.GamePurchaseItemError.UnknownError())
                        resetPaymentData()
                        Timber.tag("tam-miniapp").e("ErrorRequiredLogin error $intent ${message}")


                    }
                    else -> {}
                }
            }
            is MiniAppViewModel.MiniAppViewState.Done -> {
                hideLoading()
            }
            is MiniAppViewModel.MiniAppViewState.ResultUserInfo -> {

            }
            is MiniAppViewModel.MiniAppViewState.ResultGetInfoVodDetail -> {

            }

            is MiniAppViewModel.MiniAppViewState.ResultMegaMenuItem -> {}
            is MiniAppViewModel.MiniAppViewState.ResultMiniAppManifest -> {}
            is MiniAppViewModel.MiniAppViewState.ResultGetPartnerToken -> {
                // return output for sdk
                val tokenResponse = MiniAppManagement.genTokenResponse(
                    isSuccess = true,
                    valueCode = 1,
                    requestId = requestId ?: "",
                    jsonResult = this.token,
                    jsonError = null
                )
                callBackResponseAfterBlocking(responseEvent = tokenResponse)
            }

            is MiniAppViewModel.MiniAppViewState.ResultProductList -> {
                if (data.status == 1 && data.data.isNotEmpty()) {
                    selectedPlan = data.data.first()
                    selectedProductId = data.data.first().productId
                    currentRequestId = requestId
                    roleName = getValueOfJsonObject(productInfo, MiniAppConstants.USER_NAME)
                    val utmData = Utils.getLocalDataUtm(sharedPreferences, trackingInfo)
                    viewModel.dispatchIntent(
                        MiniAppViewModel.MiniAppViewIntent.CreateGoogleBillingTransaction(
                            requestId = requestId,
                            planId = data.data.first().planId,
                            productInfo = productInfo,
                            affiliateSource = utmData.first,
                            trafficId = utmData.second
                        )
                    )
                    PaymentTrackingUtil.sendTrackingClickBuyItemForMiniApp(
                        trackingProxy = trackingProxy,
                        trackingInfo = trackingInfo,
                        monthPrepaid = selectedPlan?.planId?: "",
                        idPackage = selectedPlan?.planName?: "",
                        price = selectedPlan?.amountStr?: ""
                    )
                } else {
                    //awaited api add message field to update message of dialog
                    val error = if(data.status != 1) {
                        GamePurchaseItemResponse.GamePurchaseItemError.SystemError
                    } else if(data.data.isEmpty()) {
                        GamePurchaseItemResponse.GamePurchaseItemError.ItemNotFound
                    } else {
                        GamePurchaseItemResponse.GamePurchaseItemError.UnknownError()
                    }
                    handlePurchaseItemFail(requestId = requestId, error = error)
                    showErrorDialog(data.message.ifBlank { <EMAIL>(R.string.mini_app_payment_default_error) })
                    resetPaymentData()
                }
            }

            is MiniAppViewModel.MiniAppViewState.ResultCreateGoogleBillingTransaction -> {
                if (data.status == 1 && data.isPurchase) {
                    if(billingClientLifecycle?.queryProductDetail(listOf(planId), data.ggSkuType) != true) {
                        handlePurchaseItemFail(requestId = requestId, error = GamePurchaseItemResponse.GamePurchaseItemError.FeatureNotSupported)
                    }
                    transactionId = data.transactionId
                } else {
                    handlePurchaseItemFail(requestId = requestId, error = GamePurchaseItemResponse.GamePurchaseItemError.SystemError)
                    showErrorDialog(data.message)
                    resetPaymentData()
                }
            }
            is MiniAppViewModel.MiniAppViewState.ResultVerifyGoogleBillingTransaction -> {
                var status: String
                if (data.status == 1) {
                    val timeStamp = System.currentTimeMillis()
                    showPaymentSuccessfulDialog(
                        MiniAppPaymentResponse(
                            accountName = roleName,
                            packageName = selectedPlan?.planName ?: "",
                            amount = selectedPlan?.amountStr ?: "",
                            transactionId = transactionId,
                            status = data.status,
                            message = data.message,
//                            transactionTime = DateTimeUtils.getCurrentPaymentTime()
                            transactionTime = DateTimeUtils.formatTimeToYourFormat(timeStamp, "HH:mm dd/MM/yyyy")
                        )
                    )
                    status = PaymentTrackingUtil.rentSuccess
                    handlePurchaseItemSuccess(requestId = requestId, data = data, timeStamp = timeStamp)
                } else {
                    showErrorDialog(data.message)
                    handlePurchaseItemFail(requestId = requestId, error = GamePurchaseItemResponse.GamePurchaseItemError.VerifyTransactionFail)
                    status = PaymentTrackingUtil.rentFail
                }

                PaymentTrackingUtil.sendTrackingRentForMiniApp(
                    trackingProxy,
                    trackingInfo,
                    status,
                    "",
                    "",
                    selectedPlan?.planId ?: "",
                    selectedPlan?.planName ?: "",
                    selectedPlan?.amountStr ?: ""
                )
                resetPaymentData()
            }
            is  MiniAppViewModel.MiniAppViewState.ResultRetryVerifyGoogleBillingTransaction -> {

            }

            is MiniAppViewModel.MiniAppViewState.ResultStoreProductList -> {
                // return output for sdk
                handleStoreProductData(requestId, data)

            }
        }
    }

    private fun getValueOfJsonObject(productInfo: JSONObject, name: String): String {
        return try {
            productInfo.optString(name)
        } catch (e: Exception) {
            ""
        }
    }

    companion object {
        const val JAVASCRIPT_INTERFACE_NAME = "megaSdk"
        const val GET_CONTACT_PERMISSION_REQUEST_CODE = 100
        const val GET_DOWNLOAD_PERMISSION_REQUEST_CODE = 101
        const val GET_DOWNLOAD_PERMISSION_AND_SHARE_REQUEST_CODE = 102
        const val PICK_MEDIA_REQUEST_CODE = 103
        const val BUNDLE_EXTRAS_DATA = "BUNDLE_EXTRAS_DATA"
        const val HTML_BLANK_PAGE = "about:blank"

        const val TRACKING_GAME_CONTENT_TYPE = "game"

    }

    // region Tracking
    private fun sendGameTracking(
        logId: String,
        event: String,
        realtimePlaying: String
    ) {
        when (safeArgs.contentType) {
            TRACKING_GAME_CONTENT_TYPE -> {
                trackingProxy.sendEvent(
                    InforMobile(
                        infor = trackingInfo,
                        logId = logId,
                        appId = if (safeArgs.deeplinkUrl.isNullOrEmpty()) TrackingUtil.currentAppId else "DEEPLINK",
                        appName = if (safeArgs.deeplinkUrl.isNullOrEmpty()) TrackingUtil.currentAppName else  "DEEPLINK",
                        screen = safeArgs.partnerId,
                        event = event,
                        itemId = safeArgs.megaAppId,
                        itemName = safeArgs.title ?: "",
                        realTimePlaying = realtimePlaying
                    )
                )
            }
            else -> {}
        }
    }

    // endregion Tracking


    // region Player Service
    private fun triggerStopNativePlayer() {
        try {
            // Stop background audio
            context?.stopService(Intent(context, BackgroundPlayerService::class.java))
            // TODO: Stop mini player, pip

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    // endregion

    // region Payment
    private fun initGoogleBilling() {
        if (isEnableGoogleBilling() && activity != null) {
            billingClientLifecycle = BillingClientLifecycleV6.getInstance(requireActivity().applicationContext)
            billingClientLifecycle?.let { clientLifecycle ->
                viewLifecycleOwner.lifecycle.addObserver(clientLifecycle)

                clientLifecycle.purchasesInApp.observe(viewLifecycleOwner) { data ->
                    clientLifecycle.handlerPurchase(
                        data, BillingClientLifecycleV6.PurchaseType.ResponseInAppPurchase,
                        onPurchaseSuccess = object : BillingClientLifecycleV6.OnPurchaseSuccess {
                            override fun onPurchaseSuccess(
                                orderId: String,
                                planId: String,
                                googlePurchaseToken: String
                            ) {
                                val utmData = Utils.getLocalDataUtm(sharedPreferences, trackingInfo)
                                viewModel.dispatchIntent(
                                    MiniAppViewModel.MiniAppViewIntent.RetryVerifyGoogleBillingTransaction(
                                        planId = planId,
                                        googlePurchaseToken = googlePurchaseToken,
                                        affiliateSource = utmData.first,
                                        trafficId = utmData.second
                                    )
                                )
                            }
                        },
                        onPurchasePending = null,
                        onPurchaseFail = null
                    )
                }
                clientLifecycle.purchasesSubs.observe(viewLifecycleOwner) { data ->
                    clientLifecycle.handlerPurchase(
                        data, BillingClientLifecycleV6.PurchaseType.ResponseSubsPurchase,
                        onPurchaseSuccess = object : BillingClientLifecycleV6.OnPurchaseSuccess {
                            override fun onPurchaseSuccess(
                                orderId: String,
                                planId: String,
                                googlePurchaseToken: String
                            ) {
                                val utmData = Utils.getLocalDataUtm(sharedPreferences, trackingInfo)
                                viewModel.dispatchIntent(
                                    MiniAppViewModel.MiniAppViewIntent.RetryVerifyGoogleBillingTransaction(
                                        planId = planId,
                                        googlePurchaseToken = googlePurchaseToken,
                                        isRetry = 1,
                                        affiliateSource = utmData.first,
                                        trafficId = utmData.second
                                    )
                                )
                            }
                        },
                        onPurchasePending = null,
                        onPurchaseFail = null
                    )
                }
                clientLifecycle.purchaseUpdateEvent.observe(viewLifecycleOwner) { data ->
                    clientLifecycle.handlerPurchase(data, BillingClientLifecycleV6.PurchaseType.CurrentPurchase,
                        onPurchaseSuccess = object : BillingClientLifecycleV6.OnPurchaseSuccess {
                            override fun onPurchaseSuccess(
                                orderId: String,
                                planId: String,
                                googlePurchaseToken: String
                            ) {
//                                activity?.runOnUiThread {
//                                    hideLoading()
                                val utmData = Utils.getLocalDataUtm(sharedPreferences, trackingInfo)
                                viewModel.dispatchIntent(
                                        MiniAppViewModel.MiniAppViewIntent.VerifyGoogleBillingTransaction(
                                            requestId = currentRequestId ?: "",
                                            orderId = orderId,
                                            planId = planId,
                                            googlePurchaseToken = googlePurchaseToken,
                                            transId = transactionId,
                                            affiliateSource = utmData.first,
                                            trafficId = utmData.second
                                        )
                                    )
//                                }
                            }
                        },
                        onPurchasePending = object : BillingClientLifecycleV6.OnPurchasePending {
                            override fun onPurchasePending() {
//                                activity?.runOnUiThread {
//                                    showLoading()
//                                }
                            }
                        },
                        onPurchaseFail = object : BillingClientLifecycleV6.OnPurchaseFail {
                            override fun onPurchaseFail(errorMessage: String) {
                                activity?.runOnUiThread {
//                                    hideLoading()
                                    showErrorDialog(errorMessage)
                                    resetPaymentData()
                                    handlePurchaseItemFail(requestId = currentRequestId ?: "", error = GamePurchaseItemResponse.GamePurchaseItemError.UnknownError())

                                }
                            }
                        })
                }
                clientLifecycle.productDetail.observe(viewLifecycleOwner) { data ->
                    if (data != null && data.productId.isNotBlank() && sharedPreferences.userId().isNotBlank()) {
                        activity?.let {
                            clientLifecycle.launchBillingFlowV6(it, data, 0, sharedPreferences.userId())
                        }
                    }
                }
                clientLifecycle.errorResponse.observe(viewLifecycleOwner) { data ->

                    if (data.second.isNotBlank()) {
                        Toast.makeText(context, data.second, Toast.LENGTH_SHORT).show()
                    }

                    when (data.first) {
                        BillingClient.BillingResponseCode.USER_CANCELED -> {
                            selectedPlan?.let {
                                PaymentTrackingUtil.cancelBuyPackageOrRent(
                                    trackingProxy,
                                    trackingInfo,
                                    promoCode = "",
                                    monthPrepaid = "",
                                    idMovie = it.planId,
                                    idService = it.planName,
                                    price = it.amountStr
                                )
                            }
                            handlePurchaseItemFail(requestId = currentRequestId ?: "", error = GamePurchaseItemResponse.GamePurchaseItemError.UserCancelled)
                        }

                        else -> {
                            selectedPlan?.let {
                                PaymentTrackingUtil.sendTrackingRentMovieShow(
                                    trackingProxy,
                                    trackingInfo,
                                    PaymentTrackingUtil.rentFail,
                                    "",
                                    monthPrepaid = "",
                                    idMovie = it.planId,
                                    idService = it.planName,
                                    price = it.amountStr
                                )
                            }
                            handlePurchaseItemFail(requestId = currentRequestId ?: "", error = GamePurchaseItemResponse.GamePurchaseItemError.UnknownError())
                        }
                    }
                    resetPaymentData()

                }
                resetPaymentData()
            }
        }
    }

    private fun showPaymentSuccessfulDialog(miniAppPaymentResponse : MiniAppPaymentResponse) {
        paymentSuccessfulDialog?.dismiss()
        paymentSuccessfulDialog = MiniAppPaymentSuccessfulDialog(
            miniAppPaymentResponse
        ).apply {
            show(<EMAIL>, null)
        }
    }

    private fun showErrorDialog(message: String, onConfirm: (() -> Unit)? = null) {
        paymentErrorDialog?.dismiss()
        paymentErrorDialog = AlertDialog().apply {
            setMessage(message)
            setTextConfirm(<EMAIL>(R.string.alert_confirm))
            setOnlyConfirmButton(true)
            setHandleBackPress(true)
            setListener(object : AlertDialogListener {
                override fun onConfirm() {
                    onConfirm?.invoke()
                }
            })
        }
        paymentErrorDialog?.show(childFragmentManager, "MiniAppPaymentErrorDialog")
    }

    private fun resetPaymentData() {
        selectedPlan = null
        selectedProductId = ""
        currentRequestId = null
        transactionId = ""
        roleName = ""
        paymentProcessing = false
    }

    private fun handlePurchaseItemFail(requestId: String, error: GamePurchaseItemResponse.GamePurchaseItemError) {
        val response = GamePurchaseItemResponse(
            status = GamePurchaseItemResponse.PurchaseStatus.Fail.id,
            detail = null,
            errorCode = error.code,
            errorMessage = error.message
        )
        val purchaseItemResponse = MiniAppManagement.purchaseItemResponse(
            isSuccess = true,
            valueCode = 1,
            requestId = requestId ?: "",
            jsonResult = response,
            jsonError = null
        )
        callBackResponseAfterBlocking(responseEvent = purchaseItemResponse)

    }

    private fun handlePurchaseItemSuccess(requestId: String, data: PaymentVerifyTransaction, timeStamp: Long) {
        val response = GamePurchaseItemResponse(
            status = GamePurchaseItemResponse.PurchaseStatus.Success.id,
            detail = GamePurchaseItemResponse.GamePurchaseItemDetail(
                productId = selectedProductId,
                orderNumber = data.orderNumber,
                timestamp = timeStamp
            )
        )
        val purchaseItemResponse = MiniAppManagement.purchaseItemResponse(
            isSuccess = true,
            valueCode = 1,
            requestId = requestId ?: "",
            jsonResult = response,
            jsonError = null
        )
        callBackResponseAfterBlocking(responseEvent = purchaseItemResponse)

    }

    private fun handleStoreProductData(requestId: String, data: JSONObject) {
        val status = try {
            data.optInt("status")
        } catch(ex: Exception) {
            Timber.tag("tam-miniapp").e(ex, "handleStoreProductData otp status error")
            0
        }
        val storeProductList = try {
            data.optJSONArray("msg_data")
        } catch(ex: Exception) {
            Timber.tag("tam-miniapp").e(ex, "handleStoreProductData otp storeProductList error")
            null
        }


        if(status != 1 || storeProductList == null) {
            val message = try {
                data.optString("msg")
            } catch(ex: Exception) {
                Timber.tag("tam-miniapp").e(ex, "handleStoreProductData otp status error")
                ""
            }
            // error
            // return output for sdk
            val getStoreProductResponse = MiniAppManagement.getStoreProductResponse(
                isSuccess = false,
                valueCode = -1,
                requestId = requestId ?: "",
                jsonResult = null,
                jsonError = MiniAppResponseError(
                    code = MiniAppConstants.ERROR_CODE_DEFAULT,
                    message = message.ifBlank { this.getString(R.string.mini_app_payment_default_error) } ,
                    detail = null
                )
            )
            callBackResponseAfterBlocking(responseEvent =  getStoreProductResponse)
            return
        }
        val storeProductResponse = MiniAppManagement.getStoreProductResponse(
            isSuccess = true,
            valueCode = 1,
            requestId = requestId ?: "",
            jsonResult = storeProductList,
            jsonError = null
        )
        callBackResponseAfterBlocking(responseEvent = storeProductResponse)
    }
    // endregion Payment
}

@Keep
data class CloseInfoAppMiniAppData(
    val title: String,
    val message: String
): Serializable