package com.fptplay.mobile.player.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.adapter.BaseViewHolder
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.databinding.PlayerBottomItemViewBinding
import com.fptplay.mobile.player.model.PlayerBottomControlData
import com.fptplay.mobile.player.utils.PlayerConstants
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.visible
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger

class PlayerBottomControlAdapter() :
    BaseAdapter<PlayerBottomControlData, PlayerBottomControlAdapter.PlayerBottomControlItemViewHolder>() {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): PlayerBottomControlItemViewHolder {
        return PlayerBottomControlItemViewHolder(
            PlayerBottomItemViewBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun onBindViewHolder(
        holder: PlayerBottomControlItemViewHolder,
        position: Int
    ) {
        holder.bind(differ.currentList[position])
    }


    fun setControlEnabled(type: PlayerConstants.PlayerBottomControlType, enabled: Boolean) {
        Logger.d("PlayerBottomControlAdapter > Set Control Enabled > $type > $enabled")
        val currentList = differ.currentList.toMutableList()
        val index = currentList.indexOfFirst { it._id == type }
        if (index != -1) {
            currentList[index].isEnable = enabled
            notifyItemChanged(index)
        }
    }

    fun setControlTitle(type: PlayerConstants.PlayerBottomControlType, title: String) {
        Logger.d("PlayerBottomControlAdapter > Set Control Title > $type > $title")
        val currentList = differ.currentList.toMutableList()
        val index = currentList.indexOfFirst { it._id == type }
        if (index != -1) {
            currentList[index].value = title
            notifyItemChanged(index)
        }
    }


    inner class PlayerBottomControlItemViewHolder(val viewBinding: PlayerBottomItemViewBinding) :
        BaseViewHolder(viewBinding) {
        init {
            viewBinding.root.onClickDelay {
                if (absoluteAdapterPosition in differ.currentList.indices) {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        differ.currentList[absoluteAdapterPosition]
                    )
                }
            }
        }

        fun bind(data: PlayerBottomControlData) {
            if (data.isEnable) {
                Logger.d("PlayerBottomControlAdapter > Bind Item > ${data._id} > ${data.isEnable}")
                var valueToDisplay = data.value

                if (data._id == PlayerConstants.PlayerBottomControlType.ID_AUDIO_SETTING) {
                    MainApplication.INSTANCE.appConfig.audioReplacementName.forEach { audioPatternData ->
                        if (data.value.contains(audioPatternData.pattern)) {
                            valueToDisplay = data.value.replace(audioPatternData.pattern, "")
                            viewBinding.ivBadge.apply {
                                visible()
                                ImageProxy.load(
                                    context = viewBinding.root.context,
                                    url = audioPatternData.thumb,
                                    width = resources.getDimensionPixelSize(R.dimen.player_control_bottom_button_badge_icon_size),
                                    height = resources.getDimensionPixelSize(R.dimen.player_control_bottom_button_badge_icon_size),
                                    target = viewBinding.ivBadge,
                                )
                            }
                        } else {
                            viewBinding.ivBadge.gone()
                        }
                    }
                }

                viewBinding.tvTitle.visible()
                viewBinding.ivIcon.visible()
                viewBinding.ivIcon.setImageResource(data.iconRes)
                viewBinding.tvTitle.text = valueToDisplay
            } else {
                viewBinding.tvTitle.gone()
                viewBinding.ivIcon.gone()
                viewBinding.ivBadge.gone()
            }
        }
    }
}