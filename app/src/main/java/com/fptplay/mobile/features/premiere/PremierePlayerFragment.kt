package com.fptplay.mobile.features.premiere

import android.animation.LayoutTransition
import android.annotation.SuppressLint
import android.content.res.Configuration
import android.os.*
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.OptIn
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.fplay.module.downloader.model.VideoTaskItem
import com.fptplay.dial.connection.FConnectionManager
import com.fptplay.dial.connection.android_tv.model.FAndroidTVCustomMessageResponse
import com.fptplay.dial.connection.android_tv.model.FAndroidTVCustomPlayContentResponse
import com.fptplay.dial.connection.android_tv.model.FAndroidTVCustomStopSessionResponse
import com.fptplay.dial.connection.models.FAndroidTVObjectReceiver
import com.fptplay.dial.connection.models.FAndroidTVObjectReceiverExternal
import com.fptplay.dial.connection.models.FBoxObjectReceiver
import com.fptplay.dial.connection.models.FSamsungObjectReceiver
import com.fptplay.dial.connection.models.FSamsungObjectReceiverExternal
import com.fptplay.dial.connection.models.ObjectReceiver
import com.fptplay.dial.connection.models.transfer_model.*
import com.fptplay.dial.model.DeviceInfo
import com.fptplay.dial.model.FBoxDeviceInfoV2
import com.fptplay.dial.scanner.interfaces.FScannerAwakeListener
import com.fptplay.mobile.BuildConfig
import com.fptplay.mobile.HomeActivity
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.*
import com.fptplay.mobile.common.extensions.ActivityExtensions.checkToDismissFragmentDialogInPlayer
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.ActivityExtensions.isInPiPMode
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.log.PremierPlaybackLogger
import com.fptplay.mobile.common.log.data.LoadType
import com.fptplay.mobile.common.log.data.LogEnterPlayerScreenSource
import com.fptplay.mobile.common.log.data.LogStreamInfo
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertInfoDialog
import com.fptplay.mobile.common.utils.*
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.StringUtils.truncateString
import com.fptplay.mobile.databinding.PremierePlayerFragmentBinding
import com.fptplay.mobile.features.adjust.AbandonedReason
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.age_retrictions.AgeRestrictionHandler
import com.fptplay.mobile.features.age_retrictions.SituationWarningHandler
import com.fptplay.mobile.features.livetv_detail.LiveTVDetailViewModel.LiveTVDetailIntent.GetMqttBackup
import com.fptplay.mobile.features.livetv_detail.LiveTVDetailViewModel.LiveTVDetailState.ResultMqttBackup
import com.fptplay.mobile.features.livetv_detail.LiveTVPlayerFragment.Companion.getCurrentChannelDetail
import com.fptplay.mobile.features.mqtt.MqttConnectManager
import com.fptplay.mobile.features.mqtt.MqttUtil
import com.fptplay.mobile.features.mqtt.MqttUtil.DEFAULT_MODE
import com.fptplay.mobile.features.mqtt.MqttUtil.getCurrentTimeInSeconds
import com.fptplay.mobile.features.mqtt.MqttUtil.mapToMessageArrived
import com.fptplay.mobile.features.mqtt.MqttUtil.mapPingToMessageArrived
import com.fptplay.mobile.features.mqtt.MqttUtil.mapToPublisher
import com.fptplay.mobile.features.mqtt.listener.LimitCcuActionListener
import com.fptplay.mobile.features.mqtt.model.ActionDetails
import com.fptplay.mobile.features.mqtt.model.LimitCcuData
import com.fptplay.mobile.features.mqtt.model.MessageArrived
import com.fptplay.mobile.features.mqtt.model.MqttContentType
import com.fptplay.mobile.features.mqtt.model.MqttOptionData
import com.fptplay.mobile.features.mqtt.model.Publisher
import com.fptplay.mobile.features.pairing_control.Utils.isFailure
import com.fptplay.mobile.features.pairing_control.Utils.isMyMessage
import com.fptplay.mobile.features.pairing_control.Utils.isSessionRunning
import com.fptplay.mobile.features.pairing_control.Utils.shouldShowStopSessionToast
import com.fptplay.mobile.features.pairing_control.Utils.isSuccess
import com.fptplay.mobile.features.pairing_control.model.CastingItemState
import com.fptplay.mobile.features.payment.google_billing.BillingUtils
import com.fptplay.mobile.features.payment.util.PaymentTrackingUtil
import com.fptplay.mobile.features.premiere.data.EnableReview
import com.fptplay.mobile.features.premiere.data.EventTvPreviewPlayerInfo
import com.fptplay.mobile.features.premiere.data.PremiereLiveState
import com.fptplay.mobile.features.preview_timer.PreviewTimer
import com.fptplay.mobile.features.sport_interactive.SportInteractiveViewModel
import com.fptplay.mobile.player.PlayerUtils
import com.fptplay.mobile.player.PlayerView
import com.fptplay.mobile.features.report.utils.PlayerReportUtils
import com.fptplay.mobile.player.config.PlayerConfigBuilder
import com.fptplay.mobile.features.user_realtime_playing.UserRealtimePlayingTracker
import com.fptplay.mobile.player.dialog.data.LiveMoreData
import com.fptplay.mobile.player.handler.PlayerHandler
import com.fptplay.mobile.player.interfaces.IPlayerUIListener
import com.fptplay.mobile.player.model.PlayerBottomControlData
import com.fptplay.mobile.player.retry.PlayerGetStreamRetryHandler
import com.fptplay.mobile.player.retry.PlayerPiPRetryHandler
import com.fptplay.mobile.player.retry.PlayerRetryHandler
import com.fptplay.mobile.player.utils.PlayerConstants
import com.fptplay.mobile.player.utils.TrackHelper.findTrackById
import com.fptplay.mobile.player.utils.TrackHelper.findTrackByName
import com.fptplay.mobile.player.utils.afterMeasured
import com.fptplay.mobile.player.utils.gone
import androidx.media3.common.VideoSize
import androidx.media3.common.util.UnstableApi
import com.tear.modules.player.exo.ExoPlayerProxy
import com.tear.modules.player.util.IPlayer
import com.tear.modules.player.util.PlayerControlView
import com.tear.modules.player.util.PlayerUtils.getHdrType
import com.tear.modules.player.util.TrackType
import com.tear.modules.player.util.UnValidResponseCode
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.CommonInfor
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.common.Stream
import com.xhbadxx.projects.module.domain.entity.fplay.drm.DrmKey
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.xhbadxx.projects.module.domain.required.LivePreviewInfo
import com.xhbadxx.projects.module.util.common.Util
import com.xhbadxx.projects.module.util.common.Util.fromBase64Default
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.safeInflate
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.collections.ArrayList


@OptIn(UnstableApi::class)
@AndroidEntryPoint
class PremierePlayerFragment :
    BaseFragment<PremiereViewModel.PremiereState, PremiereViewModel.PremiereIntent>() {

    private val TAG = this::class.java.simpleName

    override val handleConfigurationChange = true

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    @Inject
    lateinit var userRealtimePlayingTracker: UserRealtimePlayingTracker

    @Inject
    lateinit var ageRestrictionHandler : AgeRestrictionHandler

    @Inject
    lateinit var situationWarningHandler: SituationWarningHandler

    @Inject
    lateinit var playerRetryHandler: PlayerRetryHandler

    @Inject
    lateinit var playerGetStreamRetryHandler: PlayerGetStreamRetryHandler

    @Inject
    lateinit var playerPiPRetryHandler: PlayerPiPRetryHandler

    private var _binding: PremierePlayerFragmentBinding? = null
    private val binding get() = _binding!!

    override val viewModel: PremiereViewModel by activityViewModels()
    val sportInteractiveViewModel: SportInteractiveViewModel by activityViewModels()

    private var stream: Stream? = null
    private var currentDrmKey: DrmKey? = null
    // Tracking
    private var handlerTrackingHeartBeat: Handler? = null
    private var runnableTrackingHeartBeat = Runnable {
        sendTrackingHeartBeat()
    }
    private var currentDuration = 0L

    private var userClickChangeBitrate = false

    private val premierPlaybackLogger: PremierPlaybackLogger by lazy {
        PremierPlaybackLogger(
            sharedPreferences = sharedPreferences,
            trackingProxy = trackingProxy,
            trackingInfo = trackingInfo,
            details = null
        ).apply {
            enterEventPlayback(
                LogEnterPlayerScreenSource(
                    appId = TrackingUtil.currentAppId,
                    appName = TrackingUtil.currentAppName,
                    subMenuId = TrackingUtil.blockId,
                    blockId = TrackingUtil.blockId,
                    blockPosition = TrackingUtil.blockIndex,
                )
            )
            setScreen(TrackingUtil.screen)
        }
    }


    // Retry task
    private var countDownTimerRetry: CountDownTimer? = null
    private var playerRetryDialog: AlertInfoDialog? = null
    //

    // Retry when internet available
    private var isPlayerErrorByInternet = false

    //
    private var currentDetailId = ""

    // Show Ip And Matrix Ip
    private var tvShowIp: TextView? = null
    private var cdtShowIp: CountDownTimer? = null
    private var rlShowMatrixIp: RelativeLayout? = null
    private var cdtShowMatrixIp: CountDownTimer? = null
    //

    // Countdown check end time for vod premiere
    private var handlerCheckEndTime: Handler? = null
    private var runnableCheckEndTime = Runnable {
        handleEndEvent()
    }

    //
    private var isPlayerScaled = false

    // Pairing Control
    private val pairingScanner by lazy { MainApplication.INSTANCE.pairingScannerHelper }
    private val pairingConnection by lazy { MainApplication.INSTANCE.pairingConnectionHelper }
    //log kibana ---
    private var inforMobile = InforMobile() //save infor lại khi start -> stop log đúng infor này

    //region preview
    private var previewEndTimeDialog: AlertDialog? = null
    private val eventTvPreviewPlayerInfo by lazy {
        EventTvPreviewPlayerInfo()
    }
    @Inject
    lateinit var previewTimer : PreviewTimer
    private var isPreviewEnded : Boolean = false
    //endregion preview

    //MQTT
    private var mqttPublisher : Publisher? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = PremierePlayerFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        when (PlayerUtils.getPlayingType()) {
            PlayerView.PlayingType.Local -> {
                sendTrackingStopLiveShow()
                binding.player.stop(force = true)

                //MQTT
                publishEndToTopic()
            }

            PlayerView.PlayingType.Cast -> {
                binding.player.stopPlayerLocal(isClearRequest = true)
            }
        }
        //
        binding.player.setPlayerEventsListener(listener = null)
        //
        // Tracking
        inforMobile = InforMobile() //reset informobile
        removeTrackingHeartBeat()
        stopCountDownTimerRetry()
        TrackingUtil.resetIsRecommend()
        //
        // Tooltips
        if (MainApplication.INSTANCE.sharedPreferences.shouldShowTooltip(Constants.TOOLTIP_SETTING_PLAYER)) {
            MainApplication.INSTANCE.sharedPreferences.setShouldShowTooltip(
                Constants.TOOLTIP_SETTING_PLAYER,
                shouldShow = false
            )
        }
        //

        // Pairing Control
        removePairingControlListener()
        // Player error handler
        removePlayerRetryHandlerListener()
        // Player get stream error handler
        removePlayerGetStreamRetryListener()
        //
        viewModel.cancelGetStreamJob()

        // User realtime playing
        userRealtimePlayingTracker.removeRealtimePlayingTrackingCallback(listener = userRealtimePlayingTrackerListener)
        // Age restriction handler
        ageRestrictionHandler.destroy()
        ageRestrictionHandler.removeListener()
        //
        situationWarningHandler.destroy()
        situationWarningHandler.removeListener()
        viewLifecycleOwner.lifecycle.removeObserver(situationWarningHandler)
        // remove preview timer
        previewTimer.removePreviewTimerListener()
        // current Detail ID
        currentDetailId = ""
        _binding = null
    }

    override fun onDestroy() {
        removeHandlerCheckEndTime()
        super.onDestroy()
    }

    override fun onPause() {
        //
        checkToDismissFragmentDialogInPlayer()
        //
        super.onPause()
    }

    override fun bindOrientationStateChange(newConfig: Configuration) {
        super.bindOrientationStateChange(newConfig)
        if (_binding != null) {
            binding.player.configurationChanged(newConfig)
        }
    }

    override fun initData() {
        viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.GetPublicIp)
        viewModel.saveBitrateId(bitrateId = if (isPremiere()) sharedPreferences.bitrateVod() else sharedPreferences.bitrateLive())
    }

    override fun bindComponent() {
        binding.player.initPlayer(
            fragmentActivity = activity,
            viewLifecycleOwner = viewLifecycleOwner,
            screenType = PlayerHandler.ScreenType.Premiere,
            viewModel = viewModel,
            fragmentManager = childFragmentManager
        )
        Utils.setStateWhenShowLogin(this.parentFragment, object : OnShowLoginListener {
            override fun onShow() {
                binding.player.pause()
            }

            override fun onHide() {
                binding.player.play()
            }
        })
        resetPlayerLayout()
    }

    override fun bindEvent() {
        observeData()
        bindEventFragmentResult()
        bindEventInternetListener()
        // Pairing Control
        addPairingControlListener()
        // User realtime playing
        userRealtimePlayingTracker.addRealtimePlayingTrackingCallback(listener = userRealtimePlayingTrackerListener)
        // Age restriction handler
        ageRestrictionHandler.setup(userRealtimePlayingTracker = userRealtimePlayingTracker)
        ageRestrictionHandler.setListener(listener = ageRestrictionHandlerListener)
        // Situation warning handler
        situationWarningHandler.setup(userRealtimePlayingTracker = userRealtimePlayingTracker)
        situationWarningHandler.setListener(listener = situationWarningListener)
        viewLifecycleOwner.lifecycle.addObserver(situationWarningHandler)
        // Player error handler
        setPlayerRetryHandlerListener()
        // Player get stream retry
        setPlayerGetStreamRetryListener()
        // set preview timer
        previewTimer.setPreviewTimerListener(previewTimerListener = previewTimerListener)
        binding.player.apply {
            // Player UI Events
            setPlayerUIListener(object : IPlayerUIListener {
                override fun onNext(position: Int, curData: Details.Episode, isAuto: Boolean, isSendEventToRemote: Boolean) {}
                override fun onPrevious(position: Int, curData: Details.Episode, isAuto: Boolean) {}
                override fun onNextOffline(
                    position: Int,
                    curData: VideoTaskItem,
                    isAuto: Boolean
                ) {
                }

                override fun onPreviousOffline(
                    position: Int,
                    curData: VideoTaskItem,
                    isAuto: Boolean
                ) {
                }

                override fun onPlayToggle() {}
                override fun onMulticam() {
                    viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.TriggerMulticam)
                }

                override fun onCast() {
                    // Pairing control
                    if (sharedPreferences.userLogin()) {
                        if (!pairingConnection.isConnected) { // Navigate to search devices
                            navigateToPairingControl(type = 0)
                        } else {
                            navigateToPairingControl(type = 3)
                        }
                    } else {
                        parentFragment?.parentFragment?.navigateToLoginWithParams(navigationId = R.id.nav_pairing_control_host)
                    }
                    //
                }

                override fun onShare() {
                    // Send tracking
                    sendTracking(logId = "516", event = "Share")
                }

                override fun onExpand(curResolutionId: String) {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_EXPAND)
                            viewModel.savePlayerOptionsCurItem(idCurItem = curResolutionId)
                            parentFragment?.findNavController()
                                ?.navigateSafe(PremiereFragmentDirections.actionPremiereFragmentToPlayerOptionDialogFragment())
                        }

                        PlayerView.PlayingType.Cast -> {
                            pairingConnection.showToast(getString(R.string.pairing_cast_not_support))
                        }
                    }
                }

                override fun onMore(moreData: List<PlayerBottomControlData>) {}
                override fun onLiveChatClick() {
                    if (binding.player.isFullscreen()) {
                        if(sharedPreferences.userLogin()) {
                            isPlayerScaled = !isPlayerScaled
                            viewModel.dispatchIntent(
                                PremiereViewModel.PremiereIntent.TriggerOpenLiveChatFullScreen(
                                    isOpen = isPlayerScaled
                                )
                            )
                        } else {
                            val extras = Bundle()
                            extras.putString(Constants.ACTION_AFTER_LOGIN_KEY, Constants.ACTION_OPEN_CHAT)
                            parentFragment?.parentFragment?.navigateToLoginWithParams(extendsArgs = extras)
                        }
                    }
                }

                override fun onSportInteractiveClick() {
                    //    viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.TriggerOpenLiveChatFullScreen(isOpen = false))
                    //  viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.TriggerOpenTabDataFeelExistFullScreen)

                    if (context.isTablet()) {
                        if (viewModel.isFullScreen.value?.second == false) {
                            viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.TriggerOpenTabDataFeelExistFullScreen)
                        } else {
                            //open sport interactive full screen
                            navigateToSportInteractive()
                        }
                    } else {
                        if (isPlayerScaled) {
                            isPlayerScaled = !isPlayerScaled
                            viewModel.dispatchIntent(
                                PremiereViewModel.PremiereIntent.TriggerOpenLiveChatFullScreen(
                                    isOpen = isPlayerScaled
                                )
                            )
                            binding.player.setPlayerUIViewVisible(false)
                        }
                        navigateToSportInteractive()
                    }
                }

                override fun onFullScreen(
                    isFullscreen: Boolean,
                    isLandscapeMode: Boolean
                ) { // orientation change
                    if (context.isTablet()) {
                        viewModel.triggerFullScreen(binding.player.isFullscreen(), isLandscapeMode, isPlayerScaled)
                        if (isLandscapeMode) {
                            if (isFullscreen) {
                                if (!isPlayerScaled) {
                                    view?.run {
                                        (this as? ViewGroup)?.layoutTransition?.enableTransitionType(
                                            LayoutTransition.CHANGING
                                        )
                                        val lp = ConstraintLayout.LayoutParams(
                                            ConstraintLayout.LayoutParams.MATCH_PARENT,
                                            ConstraintLayout.LayoutParams.MATCH_PARENT
                                        )
                                        layoutParams = lp
                                    }
                                }
                            } else {
                                tabletPlayerLayout()
                                // Live chat
                                isPlayerScaled = false
                            }
                        } else {
                            if (isFullscreen) {
                                view?.run {
                                    (this as? ViewGroup)?.layoutTransition?.enableTransitionType(
                                        LayoutTransition.CHANGING
                                    )
                                    val lp = ConstraintLayout.LayoutParams(
                                        ConstraintLayout.LayoutParams.MATCH_PARENT,
                                        ConstraintLayout.LayoutParams.MATCH_PARENT
                                    )
                                    layoutParams = lp
                                }
                            } else {
                                resetPlayerLayout()
                            }
                        }
                    } else {
                        viewModel.triggerFullScreen(binding.player.isFullscreen(), isLandscapeMode, isPlayerScaled)
                        if (binding.player.isFullscreen()) {
                            if(!isPlayerScaled) {
                                view?.run {
                                    val lp = ConstraintLayout.LayoutParams(
                                        ConstraintLayout.LayoutParams.MATCH_PARENT,
                                        ConstraintLayout.LayoutParams.MATCH_PARENT
                                    )
                                    layoutParams = lp
                                }
                            }
                        } else {
                            resetPlayerLayout()
                            // Live chat
                            isPlayerScaled = false
                        }
                    }
                }

                override fun onSetting(bitrates: List<PlayerControlView.Data.Bitrate>?) {
                    when (PlayerUtils.getPlayingType()) {
                        is PlayerView.PlayingType.Local -> {
                            if (!bitrates.isNullOrEmpty()) {
                                viewModel.saveCurrentPlayerBitrates(bitrates)
                                viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_BITRATE)
                                val bitrateIndex = binding.player.playerData.bitrateIndex ?: 0
                                viewModel.savePlayerOptionsCurItem(idCurItem = bitrates[if (bitrateIndex < 0) 0 else bitrateIndex].id)
                                parentFragment?.findNavController()?.navigateSafe(PremiereFragmentDirections.actionPremiereFragmentToPlayerOptionDialogFragment())
                            }
                        }

                        is PlayerView.PlayingType.Cast -> {
                            pairingConnection.showToast(getString(R.string.pairing_cast_not_support))
                        }
                    }
                }

                override fun onPlayerSpeed() {

                }

                override fun onAudioAndSubtitle(tracks: List<PlayerControlView.Data.Track>?) {
                    viewModel.saveTracks(tracks)
                    viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_SUBTITLE)
                    parentFragment?.findNavController()
                        ?.navigateSafe(PremiereFragmentDirections.actionPremiereFragmentToPlayerOptionDialogFragment())
                }

                override fun onOpenAudioSelection(tracks: List<PlayerControlView.Data.Track>?) {
                    viewModel.saveTracks(tracks)
                    viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_AUDIO)
                    parentFragment?.findNavController()
                        ?.navigateSafe(PremiereFragmentDirections.actionPremiereFragmentToPlayerOptionDialogFragment())
                }

                override fun onOpenSubtitleSelection(tracks: List<PlayerControlView.Data.Track>?) {
                    viewModel.saveTracks(tracks)
                    viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_SUBTITLE)
                    parentFragment?.findNavController()
                        ?.navigateSafe(PremiereFragmentDirections.actionPremiereFragmentToPlayerOptionDialogFragment())
                }

                override fun onOpenFrameOverlay(type: PlayerConstants.PlayerFrameOverlayType?) {

                }

                override fun onWatchCredit(isSendEventToRemote: Boolean) {}
                override fun onRecommendClose() {}
                override fun onRecommendWatchNow(related: Details.RelatedVod?) {}

                override fun onRecommendPlayTrailer(related: Details.RelatedVod?) {}

                override fun onEpisodeChangedFromBackground(pos: Int, currentDuration: Long) {}

                override fun onVideoSizeChanged(videoSize: VideoSize) {
                    sendTrackingChangeResolution()
                    userClickChangeBitrate = false
                }

                override fun onTracksInfoChanged() {}
                override fun onReport() {
                    when (PlayerUtils.getPlayingType()) {
                        PlayerView.PlayingType.Local -> {
                            if (sharedPreferences.userLogin()) {
                                navigateToReportPlayerVod()
                            } else {
                                // parentFragment?.parentFragment?.navigateToLoginWithParams(navigationId = R.id.action_vod_detail_fragment_to_player_report_dialog_fragment, isDirect = false)
                                parentFragment?.parentFragment?.navigateToLoginWithParams(isDirect = false)
                            }
                        }
                        PlayerView.PlayingType.Cast -> {
                            pairingConnection.showToast(getString(R.string.pairing_cast_not_support))
                        }

                    }
                }

                override fun onPictureInPictureModeChanged(isInPictureInPictureMode: Boolean) {
                    if (!isInPictureInPictureMode) {
                        // Handle Player Retry
                        val isPlayerRetryProcessing = playerPiPRetryHandler.isProcessing()
                        if (isPlayerRetryProcessing && _binding?.player?.isPlaying() != true) {
                            when (val processData = playerPiPRetryHandler.getProcessData()) {
                                is PlayerPiPRetryHandler.PlayerRetryData -> {
                                    playerRetryHandlerListener.onShowPlayerError(
                                        shouldCountDown = processData.shouldCountDown,
                                        countdownTimeMs = playerPiPRetryHandler.getCurrentTickMs(),
                                        code = processData.code,
                                        responseCode = processData.responseCode
                                    )
                                }
                                is PlayerPiPRetryHandler.PlayerGetStreamRetryData -> {
                                    playerGetStreamRetryListener.onShowGetStreamError(
                                        shouldCountDown = processData.shouldCountDown,
                                        countdownTimeMs = playerPiPRetryHandler.getCurrentTickMs(),
                                        errorMessage = processData.errorMessage,
                                    )
                                }
                            }
                        }
                        playerPiPRetryHandler.stopRetryFlow()
                    }
                    // Hide popup
                    if (isInPictureInPictureMode) {
                        checkToDismissFragmentDialogInPlayer()
                        //
                        tvShowIp?.hide()
                        //
                    }
                    scalePlayerWhenPipChange(isInPictureInPictureMode)
                }
            })

            // Player Events
            setPlayerEventsListener(object : IPlayer.IPlayerCallback {
                var startBufferTime = 0L
                override fun onBandwidth(message: String) {
                    Logger.d("$TAG onBandwidth")
                    //
                    userRealtimePlayingTracker.playerEvents.onBandwidth(message = message)
                    playerRetryHandler.playerEvents.onBandwidth(message = message)
                }

                override fun onBuffering() {
                    //
                    userRealtimePlayingTracker.playerEvents.onBuffering()
                    playerRetryHandler.playerEvents.onBuffering()
                }

                override fun startBuffering() {
                    Logger.d("$TAG onBuffering")
                    sendTrackingBuffering(
                        "StartBuffering",
                        binding.player.getTrackingBandWith(),
                        "0"
                    )
                    startBufferTime = System.currentTimeMillis()
                }

                override fun endBuffering() {
                    sendTrackingBuffering(
                        "EndBuffering",
                        binding.player.getTrackingBandWith(),
                        (System.currentTimeMillis() - startBufferTime).toString()
                    )
                }

                override fun onPause() {
                    Logger.d("$TAG onPause")
                    //
                    userRealtimePlayingTracker.playerEvents.onPause()
                    playerRetryHandler.playerEvents.onPause()
                }

                override fun onEnd() {
                    Logger.d("$TAG onEnd")
                    if (isPremiere()) {
                        removeHandlerCheckEndTime()
                        handleEndEvent()
                    }
                    //
                    userRealtimePlayingTracker.playerEvents.onEnd()
                    playerRetryHandler.playerEvents.onEnd()
                }

                override fun onDrmKeysLoaded() {
                    Logger.d("$TAG onDrmKeysLoaded")
                    premierPlaybackLogger.logDrmKeyLoadedSuccess(
                        loadedTime = System.currentTimeMillis() - viewModel.getPrepareSourceTimeInMs(),
                        streamInfo = getStreamInfoForLogger(),
                    )
                }

                override fun onError(
                    code: Int,
                    name: String,
                    detail: String,
                    error403: Boolean,
                    responseCode: Int
                ) {
                    Logger.d("$TAG onError")
                    isPlayerErrorByInternet = code == 2001

                    if(code in 6000..6006) {
                        premierPlaybackLogger.logDrmKeyLoadedFailed(
                            streamInfo = getStreamInfoForLogger()
                        )
                    }

                    // region preview
                    if (isPlayingPreview()) {
                        handleEndPreviewOnError()
                        previewTimer.playerEvents.onError(code, name, detail, error403, responseCode)
                        return
                    }
                    if (isPlayingVipTrailerInEnablePreview()) return
                    // endregion preview
                    userRealtimePlayingTracker.playerEvents.onError(code, name, detail, error403, responseCode)
                    playerRetryHandler.playerEvents.onError(code, name, detail, error403, responseCode)
                }

                override fun onErrorBehindInLive(code: Int, name: String, detail: String) {
                    Logger.d("$TAG onErrorBehindLiveWindow")
                    // region preview
                    if (isPlayingPreview()) {
                        handleEndPreviewOnError()
                        previewTimer.playerEvents.onErrorBehindInLive(code, name, detail)
                        return
                    }
                    if (isPlayingVipTrailerInEnablePreview()) return
                    // endregion preview
                    userRealtimePlayingTracker.playerEvents.onErrorBehindInLive(code, name, detail)
                    playerRetryHandler.playerEvents.onErrorBehindInLive(code, name, detail)
                }

                override fun onErrorCodec(
                    code: Int,
                    name: String,
                    detail: String,
                    responseCode: Int,
                    isDrm: Boolean,
                    codec: IPlayer.CodecType
                ) {
                    Logger.d("$TAG onErrorCodec")

                    // region preview
                    if (isPlayingPreview()) {
                        handleEndPreviewOnError()
                        previewTimer.playerEvents.onErrorCodec(code, name, detail, responseCode, isDrm, codec)
                        return
                    }
                    if (isPlayingVipTrailerInEnablePreview()) return
                    // endregion preview

                    userRealtimePlayingTracker.playerEvents.onErrorCodec(code, name, detail, responseCode, isDrm, codec)
                    playerRetryHandler.playerEvents.onErrorCodec(code, name, detail, responseCode, isDrm, codec)
                }


                override fun onError6006(code: Int, name: String, detail: String) {
                    //
                    Logger.d("$TAG onError6006")
                    // region preview
                    if (isPlayingPreview()) {
                        handleEndPreviewOnError()
                        previewTimer.playerEvents.onError6006(code = code, name = name, detail = detail)
                        return
                    }
                    if (isPlayingVipTrailerInEnablePreview()) return
                    // endregion preview
                    userRealtimePlayingTracker.playerEvents.onError6006(code = code, name = name, detail = detail)
                    playerRetryHandler.playerEvents.onError6006(code = code, name = name, detail = detail)
                }

                override fun onError6006WhenPreview(code: Int, name: String, detail: String, responseCode: Int) {
                    //
                    Logger.d("$TAG onError6006WhenPreview")

                    if (isPlayingPreview()) {
                        handleEndPreviewOnError()
                        previewTimer.playerEvents.onError6006WhenPreview(code = code, name = name, detail = detail, responseCode = responseCode)
                        return
                    }
                    if (isPlayingVipTrailerInEnablePreview()) return

                    userRealtimePlayingTracker.playerEvents.onError6006WhenPreview(code = code, name = name, detail = detail, responseCode = responseCode)
                    playerRetryHandler.playerEvents.onError6006WhenPreview(code = code, name = name, detail = detail, responseCode = responseCode)
                }

                override fun onPrepare() {
                    Logger.d("$TAG onPrepare")
                    binding.loading.gone()
                    TrackingUtil.saveStreamProfile("")
                    premierPlaybackLogger.updateStreamProfile("")
                    initTrackingHeartBeat()
                    if ((viewModel.getVipRequired()?.first == false && _binding != null) || binding.player.getPlayerConfig().isPlayPreview) {
                        premierPlaybackLogger.logPlayAttempt(logStreamInfo = getStreamInfoForLogger(), isRetry = playerRetryHandler.getIsAutoRetry())
                    }

                    premierPlaybackLogger.setEnterOnPrepareTime(System.currentTimeMillis())

                    viewModel.savePrepareSourceTimeInMs(System.currentTimeMillis())

                    //
                    userRealtimePlayingTracker.playerEvents.onPrepare()

                    playerRetryHandler.playerEvents.onPrepare()
                }

                override fun onReady() {
                    Logger.d("$TAG onReady")
                    binding.loading.gone()
                    getDetails()?.let {
                        if (isPremiere() && !Utils.isEventEnd(
                                endTime = Utils.convertStringToLong(
                                    it.endTime,
                                    0L
                                )
                            ) && viewModel.getVipRequired()?.first == false
                        ) {
                            binding.player.seek(
                                duration = Utils.timeLateFromStartEvent(
                                    startTime = Utils.convertStringToLong(
                                        it.beginTime,
                                        0L
                                    )
                                )
                            )
                        }
                        sendTrackingWhenStartPlayLiveShow()

                        if ((viewModel.getVipRequired()?.first == false && _binding != null) || binding.player.getPlayerConfig().isPlayPreview) {
                            premierPlaybackLogger.logStartFirstFrame(
                                logStreamInfo = getStreamInfoForLogger(),
                                loadType = if(viewModel.getIsRetryPlay()) LoadType.Retry else LoadType.Initial,
                                clickTime = viewModel.getClickTimeToPlay(),
                                startFirstFrameTime = System.currentTimeMillis()
                            )
                        }
                        viewModel.saveClickTimeToPlay(0)
                    }
                    //
                    setupUserRealtimePlayingTracker()
                    //
                    setupAgeRestrictionHandler()
                    //
                    setupSituationWarningHandler()
                    //
                    if (binding.player.getPlayerConfig().isPlayPreview) {
                        setupDataForPreviewTimer()
                        previewTimer.playerEvents.onReady()
                    }
                    userRealtimePlayingTracker.playerEvents.onReady()
                    playerRetryHandler.playerEvents.onReady()
                }

                override fun onStart() {
                    Logger.d("$TAG onStart")
                    //
                    userRealtimePlayingTracker.playerEvents.onStart()
                    playerRetryHandler.playerEvents.onStart()
                }

                override fun onStop() {
                    Logger.d("$TAG onStop")
                    //
                    userRealtimePlayingTracker.playerEvents.onStop()
                    playerRetryHandler.playerEvents.onStop()

                    if (binding.player.getPlayerConfig().isPlayPreview) {
                        previewTimer.playerEvents.onStop()
                    }
                }

                override fun onFetchBitrateSuccess(bitrates: ArrayList<IPlayer.Bitrate>) {
                    Logger.d("$TAG => OnFetchBitrateSuccess: $bitrates")
                    mappingBitrate(playerDataBitrate = bitrates)
                    processChangeBitrate()
                    //
                    userRealtimePlayingTracker.playerEvents.onFetchBitrateSuccess(bitrates)
                    playerRetryHandler.playerEvents.onFetchBitrateSuccess(bitrates)
                }


                override fun onFetchBitrateAll(
                    bitrates: ArrayList<IPlayer.Bitrate>,
                    audioTracks: List<PlayerControlView.Data.Track>?
                ) {
                    val data = bitrates.joinToString(";") { data -> data.dataTracking() }
                    TrackingUtil.saveStreamProfile(data)
                    Logger.d(data)
                }

                override fun onPlay() {
                    //
                    userRealtimePlayingTracker.playerEvents.onPlay()
                    playerRetryHandler.playerEvents.onPlay()
                }

                override fun onResume() {
                    //
                    userRealtimePlayingTracker.playerEvents.onResume()
                    playerRetryHandler.playerEvents.onResume()
                }

                override fun onRelease() {
                    //
                    userRealtimePlayingTracker.playerEvents.onRelease()
                    playerRetryHandler.playerEvents.onRelease()

                    if (binding.player.getPlayerConfig().isPlayPreview) {
                        previewTimer.playerEvents.onRelease()
                    }
                }
            })
        }
    }

    private fun scalePlayerWhenPipChange(isInPictureInPictureMode: Boolean) {
        if(isInPictureInPictureMode) {
            if (binding.player.isFullscreen() && isPlayerScaled) {
                isPlayerScaled = false
                viewModel.dispatchIntent(
                    PremiereViewModel.PremiereIntent.TriggerOpenLiveChatFullScreen(
                        isOpen = isPlayerScaled
                    )
                )
            }
        }
    }

    private fun navigateToSportInteractive() {
        parentFragment?.setFragmentResult(Constants.SPORT_INTERACTIVE_OPEN_REQUEST,
            bundleOf(
                Constants.SPORT_INTERACTIVE_OPEN_REQUEST_VALUE to viewModel.getPremiereId()
            ).apply {
                binding.player.displayCutoutsHelper.let { cutoutData ->
                    if (cutoutData.currentPadding.size > 1) {
                        this.putInt(
                            Constants.SPORT_INTERACTIVE_OPEN_REQUEST_PADDING_START,
                            binding.player.displayCutoutsHelper.currentPadding[0]
                        )
                        this.putInt(
                            Constants.SPORT_INTERACTIVE_OPEN_REQUEST_PADDING_END,
                            binding.player.displayCutoutsHelper.currentPadding[1]
                        )
                        this.putInt(
                            Constants.SPORT_INTERACTIVE_OPEN_REQUEST_ROTATION,
                            binding.player.displayCutoutsHelper.currentRotation
                        )
                    }
                }
            }
        )
        binding.player.setPlayerUIViewVisible(false)
    }
    private fun navigateToReportPlayerVod() {
        when (PlayerUtils.getPlayingType()) {
            PlayerView.PlayingType.Local -> {
                if (findNavController().currentDestination?.id == R.id.premiere_fragment) {
                    if (NetworkUtils.isNetworkAvailable()){
                        val playerReportDetails = PlayerReportUtils.ReportPlayerEvent.mappingDataReportDetailEvent(premierDetail = viewModel.getDataDetail())
                        parentFragment?.findNavController()?.navigateSafe( directions = PremiereFragmentDirections.actionPremiereFragmentToReportPlayerDialogFragment() ,
                            extendArgs = Bundle().apply {
                                putSerializable(Constants.PLAYER_REPORT_DETAIL_KEY, playerReportDetails)
                            })
                    }
                    else {
                        viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.TriggerShowMsgUserReport(
                            isReported = false,
                            message = <EMAIL>(R.string.report_vod_error_no_internet_message)
                        ))
                    }
                }
            }
            PlayerView.PlayingType.Cast -> {
                parentFragment?.findNavController()
                    ?.navigateSafe(PremiereFragmentDirections.actionPremiereFragmentToReportPlayerDialogFragment())
            }
        }
    }

    private fun getStreamInfoForLogger(): LogStreamInfo {
        return LogStreamInfo(
            bitrate = binding.player.getTrackingBitrate(),
            bandwidth = binding.player.getTrackingBandWith(),
            streamBandwidth = binding.player.debugViewData.videoInfo.getBitrateRawValue(),
            resolution = binding.player.getVideoSize(),
            videoQuality = getBitrateNameForTracking(bitrateId = getBitrateIdForTracking()),

            audioName = binding.player.currentSelectedAudioTrack?.name ?: "",
            audioBandwidth = binding.player.debugViewData.audioInfo.getBitrateRawValue(),

            url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
            urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getExtraForUrlMode(sharedPreferences)}",
            status = if (binding.player.getPlayerConfig().isPlayPreview) LogStreamInfo.LogPlaybackStatus.Preview else LogStreamInfo.LogPlaybackStatus.None,
        )
    }

    override fun PremiereViewModel.PremiereState.toUI() {
        when (this) {
            is PremiereViewModel.PremiereState.Loading -> {

            }

            is PremiereViewModel.PremiereState.ResultDetail -> {
                //
                removeHandlerCheckEndTime()

                //
                if (_binding != null) {
                    binding.player.clearRequest()

                    // Update detail premiere
                    binding.player.updateDetailPremiere(details = this.data)
                    premierPlaybackLogger.updateEventDetail(details = this.data, eventId = viewModel.getPremiereId())
                    //
                }

                //
                resetGetStreamRetryStep()
            }
            is PremiereViewModel.PremiereState.ResultTriggerGetStream -> {
                binding.loading.gone()
                getDetails()?.let {
                    getStream(requireCheckPackage = this.requireCheckPackage, isRetry = false)
                } ?: run {
                    Logger.d("*****Premier data null")
                }
                // Tracking

            }

            is PremiereViewModel.PremiereState.ResultTriggerUnlockPlayerControl -> {
                if (_binding != null) {
                    binding.player.unlockControl()
                }
            }

            is PremiereViewModel.PremiereState.ResultTriggerUpdateCheckEndTime -> {
                // Countdown check end time for vod premiere
                executeCheckEndTime()
                //
            }

            is PremiereViewModel.PremiereState.ResultTriggerStopPlayer -> {
                if (_binding != null) {
                    binding.player.stop(force = true)
                    binding.player.stopServices()
                }
                //
                stream = null
                //
                clearAgeRestriction()
                userRealtimePlayingTracker.clearData()
                //
                situationWarningHandler.stopAll()
                //
                Logger.d("PreviewTimer - ResultTriggerStopPlayer")
                previewTimer.clearData()
            }

            is PremiereViewModel.PremiereState.ResultTriggerUpdatePlayState -> {
                when (getEventType()) {
                    PremiereViewModel.EventType.EVENT -> {
                        getDetails()?.let {
                            val state = Utils.getTimeLiveState(
                                beginTime = Utils.convertStringToLong(
                                    it.beginTime,
                                    0L
                                ), endTime = Utils.convertStringToLong(it.endTime, 0L)
                            )
                            if (state == 1) { // Is coming
                                if (viewModel.getVipRequired()?.first == true) { // require package
                                    binding.player.onShowThumb(isShow = false)
                                } else {
                                    binding.player.stop(force = true)
                                    binding.player.onShowThumb(
                                        isShow = true,
                                        url = it.posterImage,
                                        detailPremiere = getDetails()
                                    )
                                }
                            } else if (state == 2) {// Live
                                binding.player.onShowThumb(isShow = false)

                                // Countdown check end time for vod premiere
                                executeCheckEndTime()
                                //
                            } else { // End
                                if (viewModel.getVipRequired()?.first == true) { // require package
                                    binding.player.onShowThumb(isShow = false)
                                } else {
                                    if (isPremiere() || getEventType() == PremiereViewModel.EventType.EVENT) {
                                        handleEndEvent()
                                    } else { // Live
                                        if (binding.player.request() == null) { // Case: Premiere end.
                                            handleEndEvent()
                                        }
                                    }
                                }
                            }
                        }
                    }

                    PremiereViewModel.EventType.EVENT_TV -> {
                        binding.player.onShowThumb(isShow = false)
                    }
                }
            }

            is PremiereViewModel.PremiereState.ResultStream -> {
                stream = this.data
                currentDrmKey = this.drmKey
                premierPlaybackLogger.updateStreamInfo(streamInfo = this.data)
                when (PlayerUtils.getPlayingType()) {
                    is PlayerView.PlayingType.Local -> {
                        sendTrackLogLiveShow(false)
                        when (getEventType()) {
                            PremiereViewModel.EventType.EVENT -> {
                                if (this.intent.requireCheckPackage) return
                                getDetails()?.let {
                                    val state = Utils.getTimeLiveState(
                                        beginTime = Utils.convertStringToLong(
                                            it.beginTime,
                                            0L
                                        ), endTime = Utils.convertStringToLong(it.endTime, 0L)
                                    )
                                    if (state == 2) { // Live
                                        playerPlayNow(data = this.data, drmKey = this.drmKey)
                                    }

                                    //MQTT
                                    MqttConnectManager.INSTANCE.setLimitCcuActionListener(listener = limitCcuActionListener)
                                    publishStartToTopic(
                                        contentType = if (isPremiere())
                                            MqttContentType.Premiere
                                        else
                                            MqttContentType.Event,
                                        iType = ItemType.Event,
                                        pingStart = data.pingMqtt,
                                        drmPartner = data.merchant,
                                        mqttMode = data.mqttMode
                                    )

                                } ?: run {
                                    Logger.d("*****Premier data null")
                                }


                            }

                            PremiereViewModel.EventType.EVENT_TV -> {
                                playerPlayNow(data = this.data, drmKey = this.drmKey)

                                //MQTT
                                MqttConnectManager.INSTANCE.setLimitCcuActionListener(listener = limitCcuActionListener)
                                publishStartToTopic(
                                    contentType = MqttContentType.EventTV,
                                    iType = ItemType.EventTV,
                                    pingStart = data.pingMqtt,
                                    drmPartner = data.merchant,
                                    mqttMode = data.mqttMode
                                )
                            }
                        }
                    }

                    is PlayerView.PlayingType.Cast -> {
                        // Case: Change local -> send signal to remote
                        binding.player.preparePlayerPremiereCast(
                            playerConfig = PlayerConfigBuilder()
                                .setEnableReportPlayer(false)
                                .setSupportAudioMode(isAudioMode = this.data.isAudio, backgroundAudioOverlay = this.data.audioBackgroundImageUrl)
                                .setEnableReportPlayer(getDetails()?.enableReport?:false)
                                .build(),
                            isPremiere = isPremiere(),
                            autoProfile = getDetails()?.autoProfile ?: "",
                            details = getDetails(),
                            data = this.data,
                            onEvents = object : PlayerHandler.OnEvents {}
                        )

                        sendPlayContentMessage()
                        //

                        getDetails()?.let { detail ->
                            val state = Utils.getTimeLiveState(beginTime = Utils.convertStringToLong(detail.beginTime, 0L), endTime = Utils.convertStringToLong(detail.endTime, 0L))
                            if (state == 3 && getEventType() != PremiereViewModel.EventType.EVENT_TV) {
                                if (MainApplication.INSTANCE.appConfig.bgEndEvent.isNotBlank()) {
                                    binding.player.onShowThumb(
                                        isShow = true,
                                        url = MainApplication.INSTANCE.appConfig.bgEndEvent,
                                        detailPremiere = getDetails()
                                    )
                                }
                            } else {
                                binding.player.onShowThumb(
                                    isShow = true,
                                    url = detail.posterImage,
                                    detailPremiere = detail
                                )
                            }
                        }
                    }
                }

                //
                if (!intent.requireCheckPackage) {
                    resetGetStreamRetryStep()
                }
            }

            is PremiereViewModel.PremiereState.ResultTriggerPlayerLayout -> {
                if (context.isTablet()) {
                    adjustPlayerLayoutTablet(isScale = isScale)
                } else {
                    adjustPlayerLayout(isScale = isScale)
                }
            }

            is PremiereViewModel.PremiereState.ResultSwitchPlayerMode -> {
                if (_binding != null) {
                    if (this.modeFullscreen) {
                        binding.player.enterFullscreenMode()
                    } else {
                        binding.player.exitFullscreenMode()
                    }
                }
            }

            is PremiereViewModel.PremiereState.Error -> {
                when (intent) {
                    is PremiereViewModel.PremiereIntent.GetStream -> {
                        if (!intent.requireCheckPackage) {
                            //
                            notifyGetStreamError(errorMessage = this.message)
                        }
                    }
                    is PremiereViewModel.PremiereIntent.GetMqttBackup -> {
                        if (intent.retryCount < intent.option.maxRetryBackupApi) {
                            viewModel.dispatchIntent(
                                PremiereViewModel.PremiereIntent.GetMqttBackup(
                                    itemId = getCurrentChannelDetail()?.id ?: "",
                                    chapterId = "",
                                    episodeId = "",
                                    playlistId = "",
                                    option = intent.option,
                                    retryCount = intent.retryCount + 1
                                )
                            )
                        }
                    }

                    else -> {}
                }
            }

            is PremiereViewModel.PremiereState.StopSportInteractive -> {
//                binding.player.stopSportInteractive()
            }

            is PremiereViewModel.PremiereState.ResultReflectionDetailUI -> {
                if (_binding != null) {
                    binding.player.updateSportInteractiveButton(
                        contentId = viewModel.getPremiereId(),
                        detailsContent = getDetails(),
                        viewModel = sportInteractiveViewModel
                    )
                }
            }

            is PremiereViewModel.PremiereState.Done -> {}
            is PremiereViewModel.PremiereState.ErrorRequiredLogin -> {
                if (intent is PremiereViewModel.PremiereIntent.GetStream) {
                    //
                    if (!intent.requireCheckPackage) {
                        resetGetStreamRetryStep()
                    }
                }
            }
            is PremiereViewModel.PremiereState.ErrorRequiredVip -> {
                if (getEventType() == PremiereViewModel.EventType.EVENT || requiredVip?.enablePreview == false) {
                    sendTrackLogLiveShow(true)
                }
                if (intent is PremiereViewModel.PremiereIntent.GetStream) {
                    //
                    if (!intent.requireCheckPackage) {
                        resetGetStreamRetryStep()
                    }
                }
            }
            is PremiereViewModel.PremiereState.ErrorNoInternet -> {
                if (intent is PremiereViewModel.PremiereIntent.GetStream) {
                    //
                    if (!intent.requireCheckPackage) {
                        isPlayerErrorByInternet = true

                        resetGetStreamRetryStep()
                    }
                }
            }

            is PremiereViewModel.PremiereState.ErrorItemNotFound -> {
                sendTrackingErrorItemNotFound(errMessage = this.message)
            }

            is PremiereViewModel.PremiereState.ResultReflectionUpdateLiveState -> {
                updateLiveChatWhenLiveState(state)
            }

            is PremiereViewModel.PremiereState.ResultMqttBackup -> {
                Timber.tag("MqttConnectManager").d("result Data: $data")
                handleLimitCcuWithMqtt(
                    message = data.mapPingToMessageArrived(),
                    topic = MqttConnectManager.INSTANCE.getContentTopic(mqttPublisher?.itemType?.id ?: "",mqttPublisher?.itemId ?: "")
                )
            }

            else -> {}
        }
    }

    private fun playerPlayNow(data: Stream, drmKey: DrmKey?) {

        setupPlayerRetryHandler(channelId = getDetails()?.id ?: "", streamId = getDetails()?.autoProfile ?: "")
        binding.player.preparePlayerPremiere(
            playerConfig = PlayerConfigBuilder()
                .setEnableReportPlayer(getDetails()?.enableReport?:false)
                .setSupportAudioMode(isAudioMode = data.isAudio, backgroundAudioOverlay = data.audioBackgroundImageUrl)
                .build(),
            eventId = viewModel.getPremiereId(),
            isPremiere = isPremiere(),
            autoProfile = getDetails()?.autoProfile ?: "",
            details = getDetails(),
            data = data,
            onEvents = object : PlayerHandler.OnEvents {
                override fun showError(reason: String) {
                    Logger.d("$TAG showError $reason")
                    showWarningDialog(message = reason)
                }

                override fun navigateToRequiredVip(
                    planId: String,
                    fromSource: String,
                    idToPlay: String,
                    vipBackground: String,
                    title: String,
                    description: String,
                    btnActive: String,
                    btnSkip: String,
                    trailerUrl: String,
                ) {

                }

                override fun navigateToRequireLogin(message: String, idToPlay: String) {
                    parentFragment?.parentFragment?.navigateToLoginWithParams(
                        title = message,
                        idToPlay = idToPlay,
                        isDirect = true
                    )
                }

                override fun sendTrackingPingPlayCcu(
                    actionType: CommonInfor.PingStreamActionType,
                    message: String,
                    showFingerprint: Boolean,
                    type: String
                ) {
                    // Send Tracking
                    if (actionType == CommonInfor.PingStreamActionType.PING_STREAM_SHOW_ACTION) {
                        sendTrackingShowIpSuccess(
                            event = "ShowSuccessfully",
                            errorMessage = "",
                            typeId = type,
                            message = message
                        )
                    } else if (actionType == CommonInfor.PingStreamActionType.PING_STREAM_RECEIVER_ACTION) {
                        sendTrackingReceiveShowIp(
                            event = "ReceivedSuccessfully",
                            errorMessage = "",
                            typeId = type,
                            message = message
                        )
                    }
                }

                override fun hideShowIp() {
                    tvShowIp?.hide()
                }
            },
            onCastSessionEvents = object : PlayerHandler.OnCastSessionEvents {
                override fun checkCastSession(): Boolean {
                    return pairingConnection.isConnected
                }
            },
            processShowIp = { duration, x, y ->
                if (!activity.isInPiPMode()) {
                    viewLifecycleOwner.lifecycleScope.launch {
                        val ipPublic = viewModel.getPublicIp()
                        withContext(Dispatchers.Main) {
                            try {
                                val root = binding.player.getViewStubShowIP().safeInflate()
                                if (root != null) tvShowIp = (root as TextView)
                                tvShowIp?.let { tvShowIp ->
                                    val value =
                                        String.format("%s - %s", ipPublic, sharedPreferences.userId())
                                    tvShowIp.text = value
                                    tvShowIp.x = 0f
                                    tvShowIp.y = 0f
                                    tvShowIp.afterMeasured {
                                        context?.let {
                                            val newX = binding.player.width * (x / 100f) - tvShowIp.width
                                            val newY = binding.player.height * (y / 100f) - tvShowIp.height
                                            tvShowIp.x = if (newX >= 0) newX else 0f
                                            tvShowIp.y = if (newY >= 0) newY else 0f
                                        }
                                    }
                                    tvShowIp.show()
                                    cdtShowIp?.cancel()
                                    cdtShowIp = object : CountDownTimer(
                                        TimeUnit.SECONDS.toMillis(if (duration == 0L) 60 else duration),
                                        1000
                                    ) {
                                        override fun onTick(p0: Long) {}
                                        override fun onFinish() {
                                            tvShowIp.hide()
                                        }
                                    }.start()
                                    binding.player.sendTrackingPingPlayCcu(
                                        actionType = CommonInfor.PingStreamActionType.PING_STREAM_SHOW_ACTION,
                                        showFingerprint = true, message = value
                                    )
                                }
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                        }
                    }
                }
            },
            processShowMatrixIp = {
                // Mobile platform: Disable actively show ip logic
                /*viewLifecycleOwner.lifecycleScope.launch {
                    val ipPublic = viewModel.getPublicIp()
                    withContext(Dispatchers.Main) {
                        try {
                            val userId = sharedPreferences.userId()
                            val root = binding.player.getViewStubShowMatrix().safeInflate()
                            if (root != null) rlShowMatrixIp = (root as RelativeLayout)
                            rlShowMatrixIp?.let { rlShowMatrixIp ->
                                val value = String.format("%s - %s", ipPublic, userId)
                                for (index in 0 until rlShowMatrixIp.childCount) {
                                    val tvIp = rlShowMatrixIp.getChildAt(index)
                                    if (tvIp is TextView) tvIp.text = value
                                }
                                rlShowMatrixIp.show()
                                cdtShowMatrixIp?.cancel()
                                cdtShowMatrixIp = object : CountDownTimer(TimeUnit.SECONDS.toMillis(60), 1000) {
                                    override fun onTick(p0: Long) {}
                                    override fun onFinish() {
                                        rlShowMatrixIp.hide()
                                    }
                                }.start()
                                binding.player.sendTrackingPingPlayCcu(
                                    actionType = CommonInfor.PingStreamActionType.PING_STREAM_SHOW_ACTION,
                                    showFingerprint = true, message = "$value - FullScreen"
                                )
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }*/
            },
            processReloadStream = {
                tryRetry()
            },
            headers = Utils.getHeaderForPlayerRequest(data.streamSession),
            drmKey = drmKey?.drmKeyBase64?.fromBase64Default(),
        )
    }

    //region Handler Check End Time
    private fun executeCheckEndTime() {
        getDetails()?.let {
            //TODO: Logic end add case event + premiere
            if (isPremiere() || getEventType() == PremiereViewModel.EventType.EVENT) {
                val timeToEnd =
                    Utils.getTimeToEndEvent(endTime = Utils.convertStringToLong(it.endTime, 0L))
                runHandlerCheckEndTime(timeToHandlerCheckEndTime = timeToEnd)
            }
        }
    }

    private fun removeHandlerCheckEndTime() {
        handlerCheckEndTime?.removeCallbacks(runnableCheckEndTime)
        handlerCheckEndTime = null
    }

    private fun runHandlerCheckEndTime(timeToHandlerCheckEndTime: Long) {
        removeHandlerCheckEndTime()
        if (handlerCheckEndTime == null) {
            Looper.getMainLooper()?.run { handlerCheckEndTime = Handler(this) }
        }
        handlerCheckEndTime?.run {
            postDelayed(runnableCheckEndTime, timeToHandlerCheckEndTime)
        }
    }
    //endregion

    private fun observeData() {
        viewModel.initPlayer.observe(viewLifecycleOwner) {
            it?.run {
                binding.player.updateScreenType(screenType = PlayerHandler.ScreenType.Premiere)
                binding.player.updateIsPlayingTimeShift(isPlayingTimeshift = false)
            }
        }
        viewModel.playPremiere.observe(viewLifecycleOwner) { premiere ->
            premiere?.run {
                binding.player.updateIsPlayingTimeShift(isPlayingTimeshift = false)
                // Reset bitrate when change channel
                viewModel.saveBitrateId(bitrateId = if (isPremiere()) sharedPreferences.bitrateVod() else sharedPreferences.bitrateLive())
            }
        }
        viewModel.restartPlayer.observe(viewLifecycleOwner) {
            it?.let {
                binding.player.updateScreenType(screenType = PlayerHandler.ScreenType.Premiere)
                getDetails()?.let {
                    getStream(isRetry = false)
                }
            }
        }
        viewModel.playRequiredVipTrailer.observe(viewLifecycleOwner) { url ->
            url?.run {
                binding.loading.gone()
                binding.player.onShowThumb(isShow = false)
                binding.player.updateScreenType(screenType = PlayerHandler.ScreenType.Premiere)
                binding.player.preparePlayerVipTrailer(
                    playerConfig = PlayerConfigBuilder()
                        .setEnableReportPlayer(getDetails()?.enableReport?:false)
                        .build(),
                    url = url,
                    linkToShare = getDetails()?.websiteUrl ?: "",
                    detailPremiere = getDetails()
                )

                // Pairing Control
                pairingConnection.resetRemoteData()
                //

                //MQTT
                val iType = if (getEventType() == PremiereViewModel.EventType.EVENT) {
                    ItemType.Event
                } else {
                    ItemType.EventTV
                }
                publishStartToTopic(contentType = MqttContentType.Trailer, iType = iType, pingStart = viewModel.getVipRequired()?.second?.pingMqtt ?: false)
            }
        }
        viewModel.playPreview.observe(viewLifecycleOwner) { info ->
            Logger.d("$TAG playPreview $info")
            info?.let {
                playPreview(info)

                //MQTT
                publishStartToTopic(contentType = MqttContentType.PreviewLive, iType = ItemType.EventTV, pingStart = viewModel.getVipRequired()?.second?.pingMqtt ?: false)
            }
        }
        viewModel.buyPackageForPreview.observe(viewLifecycleOwner) { showRequirePackage ->
            Logger.d("$TAG buyPackageForPreview $showRequirePackage")
            showRequirePackage?.let {
                if (it) {
                    requiredBuyPackageForPreview()
                } else {
                    handleBuyPackageForPreview()
                }
            }
        }

        MqttConnectManager.INSTANCE.messageArrived()?.let {
            try {
                it.observe(viewLifecycleOwner) { messages ->
                    if (messages.isNotEmpty()) {
                        val latestMessage = messages[0]
                        val essageArrived = latestMessage.mapToMessageArrived()
                        essageArrived?.let { message ->
                            handleLimitCcuWithMqtt(message, latestMessage.topic)
                        }
                    }
                }
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }
    }

    private fun handleLimitCcuWithMqtt(message: MessageArrived, topic: String) {
        if(message.itemId == getCurrentChannelDetail()?.id){
            MqttConnectManager.INSTANCE.cancelLimitCcuBackup()
            Timber.tag("LimitCCU").d("MessageArrived: ${message}")
            if (message.action == MqttUtil.ACTION_LIMIT_CCU) {
                binding.player.setCodeLimitCcu(message.code)
                when(message.code) {
                    MqttUtil.SUCCESS_CODE -> {
                        Timber.tag("LimitCCU").d("Not limit ccu")
                        when (getEventType()) {
                            PremiereViewModel.EventType.EVENT -> {
                                getDetails()?.let {
                                    val state = Utils.getTimeLiveState(
                                        beginTime = Utils.convertStringToLong(
                                            it.beginTime,
                                            0L
                                        ), endTime = Utils.convertStringToLong(it.endTime, 0L)
                                    )
                                    if (state == 2) { // Live
                                        stream?.let {
                                            playerPlayNow(data = it, drmKey = currentDrmKey)
                                        }
                                    }
                                } ?: run {
                                    Logger.d("*****Premier data null")
                                }
                            }
                            PremiereViewModel.EventType.EVENT_TV -> {
                                stream?.let {
                                    playerPlayNow(data = it, drmKey = currentDrmKey)
                                }
                            }
                        }
                        currentDrmKey = null
                    }
                    MqttUtil.LIMIT_CODE -> {
                        Timber.tag("LimitCCU").d("Limit ccu title: ${message.data.action.title} desc:${message.data.action.desc}")
                        MqttConnectManager.INSTANCE.sendLogLimitCCU(message, topic)
                        binding.player.onShowThumb(isShow = true, url = getCurrentChannelDetail()?.thumb ?: "")
                        binding.player.showAlertLimitCcu(message.data.action.title, message.data.action.desc, binding.root.resources.getString(R.string.all_retry)) {
                            tryRetry()
                        }
                    }
                }
            }
        }
    }

    private fun updatePlayingSession(time: Long) {
        trackingInfo.updatePlayingSession(time)
    }

    private fun bindEventFragmentResult() {
        parentFragment?.run {
            setFragmentResultListener(Utils.OPTION_DIALOG_BITRATE_KEY) { _, bundle ->
                val bitrateId = bundle.getString(Utils.OPTION_DIALOG_BITRATE_ID_KEY, "")

                if (bitrateId.isNotEmpty()) {
                    userClickChangeBitrate = true
                    val oldBitrate = viewModel.getBitrateId()
                    viewModel.saveBitrateId(bitrateId = bitrateId)
                    //
                    if (isPremiere()) sharedPreferences.saveBitrateVod(bitrateId = bitrateId) else sharedPreferences.saveBitrateLive(bitrateId = bitrateId)
                    //
                    if (!processChangeBitrate()) {
                        getStream(isRetry = true)
                    }

                    // Tracking
                    sendTrackingChangeVideoQuality(oldBitrate = oldBitrate, curBitrate = bitrateId)
                }
            }

            setFragmentResultListener(Utils.OPTION_DIALOG_SUBTITLE_KEY) { _, bundle ->
                val subtitleId = bundle.getString(Utils.OPTION_DIALOG_SUBTITLE_ID_KEY, "")
                if (subtitleId.isNullOrEmpty()) {
                    return@setFragmentResultListener
                }
                viewModel.getTracks()?.findTrackById(id = subtitleId, type = TrackType.TEXT.ordinal)?.let {
                    when (PlayerUtils.getPlayingType()) {
                        is PlayerView.PlayingType.Local -> {
                            binding.player.changeTrack(track = it)
                        }

                        is PlayerView.PlayingType.Cast -> {
                            viewModel.getDataDetail()?.let { detail ->
                                pairingConnection.sendEventChangeTrack(id = detail.id, refId = detail.refId, selectId = it.id, type = "sub")
                            }
                        }
                    }

                    // Tracking
                    sendTrackingChangeSubtitlesAudio(value = it.name, isChangeAudio = false)
                }
            }

            setFragmentResultListener(Utils.OPTION_DIALOG_AUDIO_TRACK_KEY) { _, bundle ->
                val audioTrackName = bundle.getString(Utils.OPTION_DIALOG_AUDIO_TRACK_ID_KEY, "")
                if (audioTrackName.isNullOrEmpty()) {
                    return@setFragmentResultListener
                }
                viewModel.getTracks()?.findTrackByName(name = audioTrackName, type = TrackType.AUDIO.ordinal)?.let {
                    when (PlayerUtils.getPlayingType()) {
                        is PlayerView.PlayingType.Local -> {
                            binding.player.changeTrack(track = it)
                        }

                        is PlayerView.PlayingType.Cast -> {
                            viewModel.getDataDetail()?.let { detail ->
                                pairingConnection.sendEventChangeTrack(id = detail.id, refId = detail.refId, selectId = it.id, type = "audio")
                            }
                        }
                    }
                    // Tracking
                    sendTrackingChangeSubtitlesAudio(value = it.name, isChangeAudio = true)
                }
            }

            setFragmentResultListener(Utils.OPTION_DIALOG_EXPAND_KEY) { _, bundle ->
                val id = bundle.getInt(Utils.OPTION_DIALOG_EXPAND_ID_KEY, 0)
                val pos = bundle.getInt(Utils.OPTION_DIALOG_EXPAND_POS_KEY, 0)
                binding.player.setResizeMode(resizeMode = id, position = pos)
            }

            setFragmentResultListener(Utils.OPTION_DIALOG_MORE_KEY) { _, bundle ->
                val id = bundle.getString(Utils.OPTION_DIALOG_MORE_ID_KEY, "")
                try {
                    when (LiveMoreData.Type.valueOf(id)) {
                        LiveMoreData.Type.Share -> {
                            binding.player.onShareLink(url = getDetails()?.websiteUrl ?: "")
                        }

                        LiveMoreData.Type.Follow -> {
                            viewModel.dispatchIntent(
                                PremiereViewModel.PremiereIntent.AddFollow(
                                    id = viewModel.getId(),
                                    isPremiere = isPremiere()
                                )
                            )
                        }

                        LiveMoreData.Type.UnFollow -> {
                            viewModel.dispatchIntent(
                                PremiereViewModel.PremiereIntent.DeleteFollow(
                                    id = viewModel.getId(),
                                    isPremiere = isPremiere()
                                )
                            )
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            setFragmentResultListener(Utils.PAIRING_DIALOG_TYPE) { _, bundle ->
                val type = bundle.getInt(Utils.PAIRING_DIALOG_TYPE_KEY, 0)

                when (type) {
                    0 -> {
                        try {
                            pairingConnection.let {
                                it.getSelectDevice()?.run {
                                    when (this) {
                                        is FBoxDeviceInfoV2 -> { // Only logic for BoxC
                                            if (this.response.state == "running") {
                                                it.connect(this)
                                            } else {
                                                pairingScanner.awake(deviceInfo = this, lifecycleScope = lifecycleScope, callback = object :
                                                    FScannerAwakeListener {
                                                    override fun awakeDeviceCallBack(deviceInfo: DeviceInfo?, isSuccess: Boolean, isRunning: Boolean) {
                                                        if (isRunning) {
                                                            if (deviceInfo is FBoxDeviceInfoV2) {
                                                                it.setSelectDevice(data = FBoxDeviceInfoV2(device = <EMAIL>, response = deviceInfo.response))
                                                                it.getSelectDevice()?.let { selectDevice ->
                                                                    it.connect(selectDevice)
                                                                }
                                                            }
                                                        } else {
                                                            it.showToast(message = binding.root.context.getString(R.string.pairing_control_waiting_connection, pairingConnection.getReceiverName()))
                                                            it.showToast(message = binding.root.context.getString(R.string.pairing_cast_title_connect_error))
                                                        }
                                                    }
                                                }
                                                )
                                            }
                                        }

                                        else -> {
                                            it.connect(this)
                                        }
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }

                    1 -> {}
                    2 -> {}
                    else -> {}
                }
            }

            setFragmentResultListener(Utils.PAIRING_CONTROL_NAVIGATE_TYPE) { _, bundle ->
                val type = bundle.getString(Utils.PAIRING_CONTROL_NAVIGATE_TYPE_KEY, "")
                if (!type.isNullOrBlank()) {
                    try {
                        pairingConnection.getCastingItemMapped()?.let {
                            if (it.id != viewModel.getPremiereId()) {
                                if (ItemType.valueOf(type) == ItemType.Event) {
                                    viewModel.triggerPlayPremiere(it.id)
                                }
                            }
                        }
                    } catch (_: Exception) {
                    }
                }
            }

            setFragmentResultListener(Constants.SPORT_INTERACTIVE_CLOSE_REQUEST) { _, _ ->
                binding.player.setPlayerUIViewVisible(true)
            }

            setFragmentResultListener(Constants.SPORT_INTERACTIVE_NAVIGATE_REQUEST_KEY) { _, bundle ->
                val contentId =
                    bundle.getString(Constants.SPORT_INTERACTIVE_NAVIGATE_REQUEST_CONTENT_ID, "")
                val itemTypeString =
                    bundle.getString(Constants.SPORT_INTERACTIVE_NAVIGATE_REQUEST_ITEM_TYPE, "")
                val itemType = ItemType.valueOf(itemTypeString)
                if (itemType == ItemType.Event || itemType == ItemType.EventTV) {
                    viewModel.triggerPlayPremiere(id = contentId)
                } else {
                    checkBeforePlayUtil.navigateToSelectedContent(
                        data = StructureItem(
                            id = contentId,
                            highlightId = contentId,
                            itype = itemType
                        )

                    )
                }
            }
            setFragmentResultListener(Utils.OPTION_DIALOG_USER_REPORT_KEY) { _, bundle ->
                val isReported = bundle.getBoolean(Utils.OPTION_DIALOG_USER_REPORT_STATUS, false)
                val message = bundle.getString(Utils.OPTION_DIALOG_USER_REPORT_MESSAGE, "")
                when (PlayerUtils.getPlayingType()) {
                    PlayerView.PlayingType.Local -> {
                        if(isReported) {
                            viewModel.dispatchIntent(
                                PremiereViewModel.PremiereIntent.TriggerShowMsgUserReport(
                                    isReported = isReported, message = message
                                )
                            )
                        }
                    }
                    PlayerView.PlayingType.Cast -> {}
                }
            }
        }

    }

    //region Common
    private fun isPremiere() = viewModel.getDataDetail()?.isPremier == "1"

    private fun getDetails() = viewModel.getDataDetail()

    private fun getEventType() = viewModel.getEventType()

    fun request() = _binding?.let { binding.player.request() } ?: kotlin.run { null }
    //endregion


    //region Dialogs
    private fun showPlayerErrorWithRetryDialog(message: String) {
        if (playerRetryDialog != null) {
            playerRetryDialog?.dismissAllowingStateLoss()
            playerRetryDialog = null
        }
        if (playerRetryDialog == null) {
            playerRetryDialog = AlertInfoDialog().apply {
                setTextTitle(title = <EMAIL>(R.string.notification))
                setMessage(text = message)
                setUserId(text = sharedPreferences.userId())
                setDeviceName(text = "${Util.manufacturer()} ${Util.model()}")
                setMac(text = Util.deviceId(<EMAIL>()))
                setVersion(text = "iZiOS ${BuildConfig.VERSION_NAME}")
                setTime(
                    text = DateTimeUtils.addTimeWithCurrentLocationAndFormat(
                        currentMilliseconds = System.currentTimeMillis(),
                        timeFormat = "dd/MM/yyyy - HH:mm"
                    )
                )
                setType(text = kotlin.run {
                    when (getEventType()) {
                        PremiereViewModel.EventType.EVENT -> if (isPremiere()) "Premiere" else "Event"
                        PremiereViewModel.EventType.EVENT_TV -> "Event TV"
                    }
                })
                setMessageContent(text = viewModel.getDataDetail()?.title ?: "")
                setTextExit(text = <EMAIL>(R.string.all_exit))
                setTextConfirm(text = <EMAIL>(R.string.all_retry))
                setListener(object : AlertDialogListener {
                    override fun onExit() {
                        stopCountDownTimerRetry()
                    }

                    override fun onConfirm() {
                        tryRetry()
                        stopCountDownTimerRetry()
                    }
                })
            }
            playerRetryDialog?.show(childFragmentManager, "PlayerErrorWithRetryDialog")
        }
    }

    private fun showEndEventWarning(message: String) {
        AlertDialog().apply {
            setTextTitle(
                <EMAIL>().getString(R.string.notification)
            )
            setShowTitle(true)
            setMessage(message)
            setOnlyConfirmButton(true)
            setTextConfirm(
                <EMAIL>().getString(R.string.alert_confirm)
            )
            setHandleBackPress(true)
            setListener(object : AlertDialogListener {
                override fun onConfirm() {
                    findNavController().popBackStack()
                }
            })
        }.show(childFragmentManager, TAG)
    }

    private fun showPlayerErrorDialog(message: String) {
        AlertInfoDialog().apply {
            setTextTitle(title = <EMAIL>(R.string.notification))
            setMessage(text = message)
            setUserId(text = sharedPreferences.userId())
            setDeviceName(text = "${Util.manufacturer()} ${Util.model()}")
            setMac(text = Util.deviceId(<EMAIL>()))
            setVersion(text = "iZiOS ${BuildConfig.VERSION_NAME}")
            setTime(
                text = DateTimeUtils.addTimeWithCurrentLocationAndFormat(
                    currentMilliseconds = System.currentTimeMillis(),
                    timeFormat = "dd/MM/yyyy - HH:mm"
                )
            )
            setType(text = kotlin.run {
                when (getEventType()) {
                    PremiereViewModel.EventType.EVENT -> if (isPremiere()) "Premiere" else "Event"
                    PremiereViewModel.EventType.EVENT_TV -> "Event TV"
                }
            })
            setMessageContent(text = viewModel.getDataDetail()?.title ?: "")
            setTextExit(text = <EMAIL>(R.string.all_exit))
            setTextConfirm(text = <EMAIL>(R.string.all_retry))
            setListener(object : AlertDialogListener {
                override fun onConfirm() {
                    tryRetry()
                }
            })
        }.show(childFragmentManager, "PlayerErrorDialog")
    }
    //endregion

    private fun tabletPlayerLayout() {
        view?.run {
            val width = context.getDisplayWidth()
            val height = context.getDisplayHeight()
            var realWidth = if (width > height) width else height
            realWidth = (realWidth * (Constants.PLAYER_SCALED_RATIO)).toInt()
            layoutParams = ConstraintLayout.LayoutParams(
                realWidth,
                (realWidth / Constants.PLAYER_RATIO).toInt()
            )
        }
    }

    private fun resetPlayerLayout() {
        view?.run {
            val width = context.getDisplayWidth()
            val height = context.getDisplayHeight()
            val realWidth = if (width < height) width else height
            layoutParams = ConstraintLayout.LayoutParams(
                realWidth,
                (realWidth / Constants.PLAYER_RATIO).toInt()
            )
        }
    }

    //region Apis
    private fun mappingBitrate(playerDataBitrate: ArrayList<IPlayer.Bitrate>) {
        val cacheListBitrateApi = mutableListOf<com.xhbadxx.projects.module.domain.entity.fplay.premier.Details.Bitrate>()
        cacheListBitrateApi.addAll(getDetails()?.apiBitrates ?: listOf())

        val processBitrateList = mutableListOf<com.xhbadxx.projects.module.domain.entity.fplay.premier.Details.Bitrate>().apply {
            addAll(cacheListBitrateApi.map {
                playerDataBitrate.firstOrNull { bitrate -> bitrate.name == it.id }?.let { _ ->
                    it.copy()
                } ?: com.xhbadxx.projects.module.domain.entity.fplay.premier.Details.Bitrate(
                    id = it.name,
                    name = it.name + "p",
                    requiredPayment = true, // can't found from API map
                )
            }.filter { bitrate -> !bitrate.requiredPayment })

            // Always add auto profile
            cacheListBitrateApi.firstOrNull { bitrate -> bitrate.type() == "auto_default" }?.let { autoBitrate ->
                add(autoBitrate)
            }
        }

        getDetails()?.appBitrates?.apply {
            clear()
            addAll(processBitrateList)
        }

        Logger.d("$TAG => MappingBitrate => => Bitrates: ${getDetails()?.appBitrates}")
    }

    private fun processChangeBitrate(): Boolean {
        _binding?.let {
            val bitrateId = getDetails()?.findBitrateId() ?: ""
            //
            binding.player.playerData.bindBitrateFromEvent(data = getDetails()?.appBitrates)
            //
            val result = binding.player.changeBitrate(
                playerRequestId = playerRequestId(),
                key = bitrateId
            )
            Logger.d("$TAG => ProcessChangeBitrate => BitrateId: $bitrateId => Bitrates: ${binding.player.playerData.bitrates} => Result: $result")
            return result
        } ?: return true
    }

    private fun playerRequestId(): String {
        return getDetails()?.id ?: ""
    }


    private fun com.xhbadxx.projects.module.domain.entity.fplay.premier.Details?.findBitrateId(): String {
        var userBitrateId = viewModel.getBitrateId()
        if (this == null) {
            userBitrateId = ""
        } else {
            if (appBitrates.isEmpty()) userBitrateId = ""
            else {
                val result = appBitrates.firstOrNull { it.id == userBitrateId }
                if (result == null) {
                    userBitrateId = appBitrates[appBitrates.lastIndex].id
                }
            }
        }
        viewModel.saveBitrateId(bitrateId = userBitrateId)
        return userBitrateId
    }
    //

    private fun getStream(requireCheckPackage: Boolean = false, delay: Long = 0L, isRetry: Boolean) {
        val enableReview = if(getEventType() == PremiereViewModel.EventType.EVENT_TV) EnableReview.TRUE else EnableReview.FALSE
        viewModel.getDataDetail()?.let {
            viewModel.dispatchIntent(
                PremiereViewModel.PremiereIntent.GetStream(
                    requireCheckPackage = requireCheckPackage,
                    isPremiere = isPremiere(),
                    id = it.id,
                    episodeId = it.episodeId,
                    bitrateId = it.autoProfile,
                    delay = delay,
                    enablePreview = enableReview.value,
                    isRetry = isRetry
                )
            )
        }
    }

    /**
     * Bind all bitrates to display for user
     */
    private fun PlayerControlView.Data.bindBitrateFromEvent(data: List<com.xhbadxx.projects.module.domain.entity.fplay.premier.Details.Bitrate>?) {
        //
        fun List<com.xhbadxx.projects.module.domain.entity.fplay.premier.Details.Bitrate>?.getBitrateIndex(): Int {
            if (this.isNullOrEmpty()) return -1
            val userBitrateIndex = this.indexOfFirst { it.id == viewModel.getBitrateId() }
            return if (userBitrateIndex >= 0 && userBitrateIndex < this.size) userBitrateIndex
            else this.size - 1
        }
        //
        if (data.isNullOrEmpty()) this.bitrates = emptyList()
        else {
            bitrates = data.map { bitrate ->
                PlayerControlView.Data.Bitrate(
                    id = bitrate.id,
                    name = bitrate.name,
                    iconVip = "",
                    type = bitrate.type()
                )
            }
            bitrateIndex = data.getBitrateIndex()
        }
    }
    //endregion

    //region Retry Task
    private fun bindEventInternetListener() {
        MainApplication.INSTANCE.networkDetector.observe(this) {
            it?.let { hasInternet ->
                when (PlayerUtils.getPlayingType()) {
                    is PlayerView.PlayingType.Local -> {
                        if(_binding != null) {
                            Logger.d("$TAG -> NetworkDetector: isPlayerErrorByInternet: $isPlayerErrorByInternet $hasInternet")
                            if (isPlayerErrorByInternet && hasInternet) {
                                if(isPlayingPreview() || isPlayingVipTrailerInEnablePreview()) {
                                    viewModel.triggerPlayRequiredVipTrailer(viewModel.getVipRequired()?.second?.livePreviewInfo?.trailerUrl ?: "")
                                } else {
                                    tryRetry()
                                }
                            }
                        }
                    }

                    is PlayerView.PlayingType.Cast -> {}
                }
                isPlayerErrorByInternet = false
            }
        }
    }
    //endregion

    //region Handle -> End event
    private fun handleEndEvent() {
        when (getEventType()) {
            PremiereViewModel.EventType.EVENT -> {
                val message = MainApplication.INSTANCE.appConfig.msgEndEvent.ifBlank {
                    <EMAIL>().getString(R.string.msg_end_event)
                }

                // Show hover image
                if (MainApplication.INSTANCE.appConfig.bgEndEvent.isNotBlank()) {
                    if (_binding != null) {
                        binding.player.onShowThumb(
                            isShow = true,
                            url = MainApplication.INSTANCE.appConfig.bgEndEvent,
                            detailPremiere = getDetails()
                        )
                    }
                    sendTrackingShowBackDrop()
                }

                //showEndEventWarning(message)
                if (_binding != null) {
                    binding.player.stop(force = true)
                    binding.player.stopServices()
                }
                // Age Restriction
                clearAgeRestriction()
                userRealtimePlayingTracker.clearData()
                //
                situationWarningHandler.stopAll()
                //
            }

            PremiereViewModel.EventType.EVENT_TV -> {
                val message = MainApplication.INSTANCE.appConfig.msgEndEvent.ifBlank {
                    <EMAIL>().getString(R.string.msg_end_event)
                }
                showEndEventWarning(message)
            }
        }

        // Unlock player control
        if (_binding != null) {
            binding.player.unlockControl()
        }

        // Trigger End time
        viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.TriggerEndTime)
    }
    //endregion

    //region Adjust -> Player Layout
    private fun adjustPlayerLayout(isScale: Boolean) {
        if (MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            if (isScale) {
                view?.run {
                    val lp = ConstraintLayout.LayoutParams(
                        0,
                        0
                    )
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.matchConstraintPercentWidth = Constants.PLAYER_SCALED_RATIO
                    layoutParams = lp
                }
            } else {
                view?.run {
                    val lp = ConstraintLayout.LayoutParams(
                        ConstraintLayout.LayoutParams.MATCH_PARENT,
                        ConstraintLayout.LayoutParams.MATCH_PARENT
                    )
                    layoutParams = lp
                }
            }
        }
    }

    private fun adjustPlayerLayoutTablet(isScale: Boolean) {
        if (isScale) {
            if (viewModel.isFullScreen.value?.first == true && viewModel.isFullScreen.value?.second == true) {
                view?.run {
                    val lp = ConstraintLayout.LayoutParams(0, 0)
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.matchConstraintPercentWidth = Constants.PLAYER_SCALED_RATIO
                    layoutParams = lp
                }
            }
        } else {
            view?.run {
                if (viewModel.isFullScreen.value?.second == true) { // landscape
                    if (viewModel.isFullScreen.value?.first == true) { // fullscreen
                        layoutParams = ConstraintLayout.LayoutParams(
                            ConstraintLayout.LayoutParams.MATCH_PARENT,
                            ConstraintLayout.LayoutParams.MATCH_PARENT
                        )
                    } else {
                        tabletPlayerLayout()
                    }
                } else { // portrait
                    if (viewModel.isFullScreen.value?.first == true) {
                        layoutParams = ConstraintLayout.LayoutParams(
                            ConstraintLayout.LayoutParams.MATCH_PARENT,
                            ConstraintLayout.LayoutParams.MATCH_PARENT
                        )
                    } else {
                        resetPlayerLayout()
                    }
                }
            }
        }
    }
    //endregion

    //region Tracking
    private fun initTrackingHeartBeat() {
        removeTrackingHeartBeat()
        if (handlerTrackingHeartBeat == null) {
            Looper.getMainLooper()?.run {
                handlerTrackingHeartBeat = Handler(this)
            }
        }
        handlerTrackingHeartBeat?.run {
            post(runnableTrackingHeartBeat)
        }
    }

    private fun removeTrackingHeartBeat() {
        handlerTrackingHeartBeat?.removeCallbacks(runnableTrackingHeartBeat)
        handlerTrackingHeartBeat = null
    }

    @SuppressLint("SimpleDateFormat")
    private fun sendTrackingHeartBeat() {
        Logger.d("trangtest sendTrackingHeartBeat === ${binding.player.isPlaying()}")
        if (binding.player.isPlaying() && sharedPreferences.userLogin() && (viewModel.getVipRequired()?.first != true || binding.player.getPlayerConfig().isPlayPreview)) {
            if (pairingConnection.isConnected) return
            Timber.d("*****Ping: video: ${getBitrateIdForTracking()} audio: ${binding.player.currentSelectedAudioTrack} subtitle: ${binding.player.currentSelectedSubtitleTrack} ")
            val screen =
                if (viewModel.getEventType() == PremiereViewModel.EventType.EVENT && isPremiere())
                    TrackingConstants.PING_PREMIERE
                else {
                    if (binding.player.getPlayerConfig().isPlayPreview)
                        TrackingConstants.PING_PREVIEW_SHOW
                    else
                        TrackingConstants.PING_LIVE_SHOW
                }
            trackingProxy.sendEvent(
                InforMobile(
                    infor = trackingInfo,
                    logId = "111",
                    appId = TrackingUtil.currentAppId,
                    appName = TrackingUtil.currentAppName,
                    screen = screen,
                    event = "Ping",
                    itemId = getDetails()?.id ?: "",
                    chapterId = "0",
                    EpisodeID = viewModel.getPremiereId(),
                    itemName = getDetails()?.title ?: "",
                    url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                    boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                    dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                    elapsedTimePlaying = (binding.player.currentDuration() / 1000).toString(),
                    realTimePlaying = getRealTimePlayingForLog(),
                    videoQuality = getBitrateNameForTracking(bitrateId = getBitrateIdForTracking()),
                    audio = binding.player.currentSelectedAudioTrack?.name ?: "",
                    subtitle = binding.player.currentSelectedSubtitleTrack?.name ?: "",
                    subMenuId = TrackingUtil.blockId,
                    businessPlan = getDetails()?.payment?.requireVipPlan ?: "",
                    CDNName = "",
                    Bitrate = binding.player.getTrackingBitrate(),
                    Resolution = binding.player.getVideoSize(),
                    drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                    refItemId = TrackingUtil.contentPlayingInfo.refId,
                    refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                    refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                    bandwidth =  binding.player.getTrackingBandWith(),
                    streamBandwidth = binding.player.debugViewData.videoInfo.getBitrateRawValue(),
                    streamBandwidthAudio = binding.player.debugViewData.audioInfo.getBitrateRawValue(),
                    dimension = TrackingUtil.getDimensionForTracking(width = sharedPreferences.getDisplayWidth(), height = sharedPreferences.getDisplayHeight()),

                    videoCodec = sharedPreferences.videoCodecInfo(),
                    videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                    audioCodec = sharedPreferences.audioCodecInfo(),

                    deviceFingerprint = sharedPreferences.deviceFingerprint(),
                    urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                    streamProfile = TrackingUtil.getStreamProfile(),

                    totalByteLoaded = binding.player.getPlayer()?.currentByteLoaded()?.toString() ?: "",
                    streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",

                    hdrType = "${MainApplication.INSTANCE.applicationContext.getHdrType()}",
                    isRecommend = TrackingUtil.isRecommend,
                    position = TrackingUtil.position,
                    appSource = viewModel.getDataDetail()?.appId ?: ""

                )
            )
        }

        handlerTrackingHeartBeat?.run {
            postDelayed(runnableTrackingHeartBeat, 60000)
        }
    }

    private fun sendTrackingPingStartStop(stop: Boolean = false) { //for start and stop log
        if (pairingConnection.isConnected) return
        val screen =
            if (viewModel.getEventType() == PremiereViewModel.EventType.EVENT && isPremiere())
                TrackingConstants.PING_PREMIERE
            else {
                if (binding.player.getPlayerConfig().isPlayPreview)
                    TrackingConstants.PING_PREVIEW_SHOW
                else
                    TrackingConstants.PING_LIVE_SHOW
            }
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "111",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = screen,
                event = "Ping",
                itemId = inforMobile.itemId,
                chapterId = inforMobile.chapterId,
                EpisodeID = inforMobile.EpisodeID,
                itemName = inforMobile.itemName,
                url = inforMobile.url,
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                elapsedTimePlaying = (binding.player.currentDuration() / 1000).toString(),
                subMenuId = TrackingUtil.blockId,
                realTimePlaying = if(stop && !binding.player.isPlayingVipTrailer()) {
                    userRealtimePlayingTracker.getRealTimePlaying().toString()
                } else {
                    getRealTimePlayingForLog()
                },
                videoQuality = getBitrateNameForTracking(bitrateId = getBitrateIdForTracking()),
                businessPlan = inforMobile.businessPlan,
                isLinkDRM = inforMobile.isLinkDRM,
                refItemId = inforMobile.refItemId,
                refPlaylistID = inforMobile.refPlaylistID,
                refEpisodeID = inforMobile.refPlaylistID,
                audio = binding.player.currentSelectedAudioTrack?.name ?: "",
                subtitle = binding.player.currentSelectedSubtitleTrack?.name ?: "",
                Bitrate = binding.player.getTrackingBitrate(),
                Resolution = binding.player.getVideoSize(),
                drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                bandwidth =  binding.player.getTrackingBandWith(),
                streamBandwidth = binding.player.debugViewData.videoInfo.getBitrateRawValue(),
                streamBandwidthAudio = binding.player.debugViewData.audioInfo.getBitrateRawValue(),
                dimension = TrackingUtil.getDimensionForTracking(width = sharedPreferences.getDisplayWidth(), height = sharedPreferences.getDisplayHeight()),

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),

                totalByteLoaded = binding.player.getPlayer()?.currentByteLoaded()?.toString() ?: "",
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",

                hdrType = "${MainApplication.INSTANCE.applicationContext.getHdrType()}",
                isRecommend = TrackingUtil.isRecommend,
                position = TrackingUtil.position,
                appSource = viewModel.getDataDetail()?.appId ?: ""
                
            )
        )
    }

    private fun sendTrackingChangeResolution() {
        if (pairingConnection.isConnected) return
        if (_binding == null) return
        if (viewModel.getVipRequired()?.first != true)
            trackingProxy.sendEvent(
                InforMobile(
                    infor = trackingInfo,
                    logId = "113",
                    appId = TrackingUtil.currentAppId,
                    appName = TrackingUtil.currentAppName,
                    screen = "ChangeResolution",
                    event = "ChangeResolution",
                    bandwidth = binding.player.getTrackingBandWith(),
                    isManual = if (userClickChangeBitrate) "1" else "0",
                    itemId = getDetails()?.id ?: "",
                    chapterId = "0",
                    EpisodeID = viewModel.getPremiereId(),
                    itemName = getDetails()?.title ?: "",
                    url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                    boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                    dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                    elapsedTimePlaying = (binding.player.currentDuration() / 1000).toString(),
                    realTimePlaying = getRealTimePlayingForLog(),
                    videoQuality = getBitrateNameForTracking(bitrateId = getBitrateIdForTracking()),
                    audio = binding.player.currentSelectedAudioTrack?.name ?: "",
                    subtitle = binding.player.currentSelectedSubtitleTrack?.name ?: "",
                    subMenuId = TrackingUtil.blockId,
                    businessPlan = getDetails()?.payment?.requireVipPlan ?: "",
                    CDNName = "",
                    Bitrate = binding.player.getTrackingBitrate(),
                    Resolution = binding.player.getVideoSize(),
                    refItemId = TrackingUtil.contentPlayingInfo.refId,
                    refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                    refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

                    videoCodec = sharedPreferences.videoCodecInfo(),
                    videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                    audioCodec = sharedPreferences.audioCodecInfo(),

                    deviceFingerprint = sharedPreferences.deviceFingerprint(),
                    urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                    streamProfile = TrackingUtil.getStreamProfile(),
                    streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                    idRelated = TrackingUtil.idRelated,
                    position = TrackingUtil.position
                )
            )
    }


    private fun sendTrackingChangeSubtitlesAudio(value: String, isChangeAudio: Boolean) {
        if (pairingConnection.isConnected) return
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "518",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = TrackingUtil.screen,
                event = if (isChangeAudio) "ChangeAudio" else "ChangeSubtitles",
                itemId = getDetails()?.id ?: "",
                chapterId = "0",
                EpisodeID = viewModel.getPremiereId(),
                itemName = value,
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                blocKPosition = TrackingUtil.blockIndex,
                subMenuId = TrackingUtil.blockId,
                businessPlan = getDetails()?.payment?.requireVipPlan ?: "",
                isLinkDRM = getDetails()?.isDrm?.toString() ?: "",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.position,
                isRecommend = TrackingUtil.isRecommend
            )
        )
    }

    private fun sendTrackingBuffering(event: String, bandwidth: String, bufferLength: String) {
        if (pairingConnection.isConnected) return
        if (viewModel.getVipRequired()?.first != true)
            trackingProxy.sendEvent(
                InforMobile(
                    infor = trackingInfo,
                    logId = "112",
                    appId = TrackingUtil.currentAppId,
                    appName = TrackingUtil.currentAppName,
                    screen = "Buffering",
                    event = event,
                    bandwidth = bandwidth,
                    bufferLength = bufferLength,
                    itemId = getDetails()?.id ?: "",
                    chapterId = "0",
                    EpisodeID = viewModel.getPremiereId(),
                    itemName = getDetails()?.title ?: "",
                    url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                    boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                    dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                    elapsedTimePlaying = (binding.player.currentDuration() / 1000).toString(),
                    realTimePlaying = getRealTimePlayingForLog(),
                    subMenuId = TrackingUtil.blockId,
                    businessPlan = getDetails()?.payment?.requireVipPlan ?: "",
                    isLinkDRM = getDetails()?.isDrm?.toString() ?: "",
                    CDNName = "",
                    Bitrate = binding.player.getTrackingBitrate(),
                    Resolution = binding.player.getVideoSize(),
                    refItemId = TrackingUtil.contentPlayingInfo.refId,
                    refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                    refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

                    videoCodec = sharedPreferences.videoCodecInfo(),
                    videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                    audioCodec = sharedPreferences.audioCodecInfo(),

                    deviceFingerprint = sharedPreferences.deviceFingerprint(),
                    urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                    streamProfile = TrackingUtil.getStreamProfile(),
                    streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                    idRelated = TrackingUtil.idRelated,
                    position = TrackingUtil.position,
                    appSource = viewModel.getDataDetail()?.appId ?: ""
                )
            )
    }

    private fun sendTrackingStartLiveShow() {
        if (pairingConnection.isConnected) return
        inforMobile = InforMobile(
            infor = trackingInfo,
            logId = "171",
            appId = TrackingUtil.currentAppId,
            appName = TrackingUtil.currentAppName,
            screen = if (pairingConnection.isConnected) "Casting" else TrackingUtil.screen,
            event = "StartLiveShow",
            itemId = getDetails()?.id ?: "",
            chapterId = getDetails()?.episodeId?: "0",
            EpisodeID = viewModel.getPremiereId(),
            itemName = getDetails()?.title ?: "",
            url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
            boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
            startTime = TrackingUtil.startTime,
            isLive = TrackingUtil.isLive,
            blocKPosition = TrackingUtil.blockIndex,
            price = "0",
            subMenuId = TrackingUtil.blockId,
            videoQuality = getBitrateIdForTracking(),
            businessPlan = getDetails()?.payment?.requireVipPlan ?: "",
            isLinkDRM = getDetails()?.isDrm?.toString() ?: "",
            refItemId = TrackingUtil.contentPlayingInfo.refId,
            refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
            refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

            videoCodec = sharedPreferences.videoCodecInfo(),
            videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
            audioCodec = sharedPreferences.audioCodecInfo(),

            deviceFingerprint = sharedPreferences.deviceFingerprint(),
            urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
            streamProfile = TrackingUtil.getStreamProfile(),
            streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
            status = if (binding.player.getPlayerConfig().isPlayPreview) TrackingConstants.STATUS_PREVIEW else TrackingConstants.STATUS_NONE,
            idRelated = TrackingUtil.idRelated,
            position = TrackingUtil.position,
            isRecommend = TrackingUtil.isRecommend
        )
        trackingProxy.sendEvent(inforMobile)
        sendTrackingPingStartStop() //send ping đầu

    }

    private fun sendTrackingStopLiveShow() {
        if (pairingConnection.isConnected) return
        if(inforMobile.logId == "171") { //nếu có startliveshow -> send stop
            //log Adjust
            AdjustAllEvent.sendVideoViewEvent(
                watchDuration = if (binding.player.isPlayingVipTrailer()) getRealTimePlayingForLog() else userRealtimePlayingTracker.getRealTimePlaying().toString(),
                isComplete = "false",
                abandonedReason = AbandonedReason.user_initiated
            )
            sendTrackingPingStartStop(stop = true) //send ping cuối
            trackingProxy.sendEvent(
                InforMobile(
                    infor = trackingInfo,
                    logId = "172",
                    appId = TrackingUtil.currentAppId,
                    appName = TrackingUtil.currentAppName,
                    screen = inforMobile.screen,
                    event = "StopLiveShow",
                    itemId = inforMobile.itemId,
                    chapterId = inforMobile.chapterId,
                    EpisodeID = inforMobile.EpisodeID,
                    itemName = inforMobile.itemName,
                    url = inforMobile.url,
                    boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                    startTime = TrackingUtil.startTime,
                    isLive = TrackingUtil.isLive,
                    blocKPosition = TrackingUtil.blockIndex,
                    price = "0",
                    subMenuId = TrackingUtil.blockId,
                    realTimePlaying = if (binding.player.isPlayingVipTrailer()) getRealTimePlayingForLog() else userRealtimePlayingTracker.getRealTimePlaying().toString(),
                    videoQuality = getBitrateIdForTracking(),
                    businessPlan = inforMobile.businessPlan,
                    isLinkDRM = inforMobile.isLinkDRM,
                    refItemId = inforMobile.refItemId,
                    refPlaylistID = inforMobile.refPlaylistID,
                    refEpisodeID = inforMobile.refPlaylistID,

                    videoCodec = sharedPreferences.videoCodecInfo(),
                    videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                    audioCodec = sharedPreferences.audioCodecInfo(),

                    deviceFingerprint = sharedPreferences.deviceFingerprint(),
                    urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                    streamProfile = TrackingUtil.getStreamProfile(),
                    streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                    status = if (binding.player.getPlayerConfig().isPlayPreview) TrackingConstants.STATUS_PREVIEW else TrackingConstants.STATUS_NONE,
                    idRelated = TrackingUtil.idRelated,
                    position = TrackingUtil.position,
                    isRecommend = TrackingUtil.isRecommend
                )
            )
            TrackingUtil.resetIsRecommend()
            TrackingUtil.resetPosition()
            if (TrackingUtil.getPreviewProcessItem().first == viewModel.getId()) {
                // Don't reset playing session
            } else {
                updatePlayingSession(0)
                inforMobile = InforMobile() // reset informMobile
            }
        }
    }

    private fun sendTrackingShowBackDrop() {
        if (pairingConnection.isConnected) return
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "177",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "Backdrop",
                event = "ShowBackdrop",
                itemId = getDetails()?.id ?: "",
                chapterId = "0",
                EpisodeID = viewModel.getPremiereId(),
                itemName = getDetails()?.title ?: "",
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                isLive = TrackingUtil.isLive,
                blocKPosition = TrackingUtil.blockIndex,
                price = "0",
                subMenuId = TrackingUtil.blockId,
                realTimePlaying = getRealTimePlayingForLog(),
                videoQuality = getBitrateIdForTracking(),
                businessPlan = getDetails()?.payment?.requireVipPlan ?: "",
                isLinkDRM = getDetails()?.isDrm?.toString() ?: "",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                status = if (binding.player.getPlayerConfig().isPlayPreview) TrackingConstants.STATUS_PREVIEW else TrackingConstants.STATUS_NONE,
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.position,
                isRecommend = TrackingUtil.isRecommend

            )
        )
    }
    private fun sendTrackingErrorItemNotFound(errMessage:String) {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "17",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                event = "Error",
                errorCode = "410", //tạm thơì hardcode errCode la 410 vi core ko trả về errorCode
                errorMessage = errMessage,
                issueId = TrackingUtil.createIssueId(),
            )
        )
    }

    private fun sendTrackingRequestPackage() {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "170",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = TrackingUtil.screen,
                event = "RequestPackage",
                itemId = getDetails()?.id ?: "",
                chapterId = viewModel.getDataDetail()?.episodeId ?: "",
                EpisodeID = viewModel.getPremiereId(),
                itemName = getDetails()?.title ?: "",
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                isLive = TrackingUtil.isLive,
                blocKPosition = TrackingUtil.blockIndex,
                price = "0",
                subMenuId = TrackingUtil.blockId,
                realTimePlaying = getRealTimePlayingForLog(),
                videoQuality = getBitrateIdForTracking(),
                businessPlan = getDetails()?.payment?.requireVipPlan ?: "",
                isLinkDRM = getDetails()?.isDrm?.toString() ?: "",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.position,
                isRecommend = TrackingUtil.isRecommend
            )
        )
    }

    private fun sendTrackLogLiveShow(requireCheckPackage: Boolean) {
        sendTrackingStopLiveShow() //send stopliveshow trước đó (nếu có)
        updatePlayingSession(System.currentTimeMillis())
        TrackingUtil.idRelated = currentDetailId
        TrackingUtil.savePreviewProcessItem(vodId = "", episodeId = "")
        if(requireCheckPackage){
            sendTrackingRequestPackage()
        }else {
            sendTrackingEnterDetailLiveShow()
        }
    }
    private fun sendTrackingWhenStartPlayLiveShow(){
        sendTrackingStopLiveShow() //send stopliveshow trước đó (nếu có)
//        trackingInfo.updatePlayingSession(time = System.currentTimeMillis())
        sendTrackingStartLiveShow()
    }

    private fun sendTrackingEnterDetailLiveShow() {
        if (pairingConnection.isConnected) return
        getDetails()?.let {
            val info = InforMobile(
                infor = trackingInfo,
                logId = "170",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = TrackingUtil.screen,
                event = "EnterDetailLiveShow",
                chapterId = it.episodeId,
                EpisodeID = viewModel.getPremiereId(),
                itemId = it.id,
                itemName = it.title,
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                isLive = TrackingUtil.isLive,
                blocKPosition = TrackingUtil.blockIndex,
                price = "0",
                subMenuId = TrackingUtil.blockId,
                videoQuality = viewModel.getBitrateId(),
                businessPlan = it.payment.requireVipPlan,
                isLinkDRM = it.isDrm.toString(),
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.position,
                isRecommend = TrackingUtil.isRecommend
            )
            trackingProxy.sendEvent(info)
            currentDetailId = getDetails()?.id ?: ""
            Logger.d("trangtest === sendevent === $info")
        }
    }


    private fun sendTrackingChangeVideoQuality(oldBitrate: String, curBitrate: String) {
        if (pairingConnection.isConnected) return
        AdjustAllEvent.sendQualityChangeEvent(getBitrateNameForTracking(bitrateId = curBitrate))
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "416",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "ChangeVideoQuality",
                event = "ChangeVideoQuality",
                itemId = getDetails()?.id ?: "",
                chapterId = "0",
                EpisodeID = viewModel.getPremiereId(),
                itemName = getBitrateNameForTracking(bitrateId = curBitrate),
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                blocKPosition = TrackingUtil.blockIndex,
                videoQuality = getBitrateNameForTracking(bitrateId = oldBitrate),
                groupChannel = TrackingUtil.blockId,
                businessPlan = getDetails()?.payment?.requireVipPlan ?: "",
                isLinkDRM = getDetails()?.isDrm?.toString() ?: "",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",
                subMenuId = TrackingUtil.blockId,
                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.position,
                isRecommend = TrackingUtil.isRecommend
            )
        )
    }


    private fun sendTrackingReceiveShowIp(
        event: String,
        errorMessage: String,
        typeId: String,
        message: String
    ) {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "26",
                appId = "DIAL",
                appName = "FingerPrint",
                screen = "Announcement",
                errorMessage = errorMessage,
                event = event,
                itemId = typeId,
                itemName = message,
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
            )
        )
    }

    private fun sendTrackingShowIpSuccess(
        event: String,
        errorMessage: String,
        typeId: String,
        message: String
    ) {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "29",
                appId = TrackingUtil.currentAppId,
                appName = "FingerPrint",
                screen = "Announcement",
                errorMessage = errorMessage,
                event = event,
                itemId = typeId,
                itemName = message,
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
            )
        )
    }

    private fun sendTracking(
        logId: String,
        screen: String = if (pairingConnection.isConnected) "Casting" else TrackingUtil.screen,
        event: String,
        errorCode: String = "",
        errorMessage: String = "",
        issueId: String = ""
    ) {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = logId,
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                appSource = viewModel.getDataDetail()?.appId ?: "",
                screen = screen,
                event = event,
                itemId = getDetails()?.id ?: "",
                chapterId = "0",
                EpisodeID = viewModel.getPremiereId(),
                itemName = viewModel.getDataDetail()?.title ?: "",
                url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                groupChannel = TrackingUtil.blockId,
                blocKPosition = TrackingUtil.blockIndex,
                realTimePlaying = getRealTimePlayingForLog(),
                isLive = TrackingUtil.isLive,
                errorCode = errorCode,
                errorMessage = errorMessage,
                errUrl = (binding.player.getPlayer() as? ExoPlayerProxy)?.tracking?.urlError ?: "",
                errHeader = (binding.player.getPlayer() as? ExoPlayerProxy)?.tracking?.responseHeaderWhenError ?: "",
                videoQuality = getBitrateIdForTracking(),
                businessPlan = viewModel.getDataDetail()?.payment?.requireVipPlan ?: "",
                drmPartner = binding.player.request()?.drm?.type?.toString() ?: "",
                isRoot = sharedPreferences.isDeviceRooted(),
                widevineLevel = sharedPreferences.widevineLevel(),
                buildNumber = sharedPreferences.buildNumber(),
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,

                videoCodec = sharedPreferences.videoCodecInfo(),
                videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
                audioCodec = sharedPreferences.audioCodecInfo(),

                deviceFingerprint = sharedPreferences.deviceFingerprint(),
                urlMode = "${binding.player.getPlayer()?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}",
                streamProfile = TrackingUtil.getStreamProfile(),
                streamHeaderSession = binding.player.request()?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: "",

                issueId = issueId,

                idRelated = TrackingUtil.idRelated,
                position = TrackingUtil.position,
                isRecommend = TrackingUtil.isRecommend
            )
        )
    }

    private fun getBitrateIdForTracking(): String {
        return viewModel.getBitrateId()
    }

    private fun getBitrateNameForTracking(bitrateId: String): String {
        getDetails()?.let {
            if (it.appBitrates.isEmpty()) {
                return ""
            } else {
                val result = it.appBitrates.firstOrNull { bitrate -> bitrate.id == bitrateId }
                if (result != null) {
                    return result.name
                } else {
                    return ""
                }
            }
        } ?: kotlin.run { return "" }
    }

    //endregion


    // region Pairing Control
    private fun safeUpdateUI(block: () -> Unit = {}) {
        _binding?.let {
            block()
        }
    }

    /**
     * type: 0 -> Device Bottom Sheet
     * 1 -> Pairing with pin code
     * 2 -> Device Management
     * 3 -> Popup Remote Control
     */
    private fun navigateToPairingControl(type: Int) {
        val navController = (activity as? HomeActivity)?.navHostFragment?.navController
        val navigation = if (navController?.graph?.id == R.id.nav_home_main) {
            navController
        } else {
            null
        }
        navigation?.navigateSafe(NavHomeMainDirections.actionGlobalToPairingControl(type = type))
    }

    private fun sendPlayContentMessage() {
        pairingConnection.getCastingItemMapped()?.let {
            if (viewModel.getPremiereId() == it.id) {
                when (pairingConnection.getCastingItemState()) {
                    CastingItemState.CAN_CAST -> {
                        if (pairingConnection.isSessionRunning) {
                            return
                        }
                    }

                    CastingItemState.NOT_SUPPORT -> {
                        safeUpdateUI {
//                            pairingConnection.setSessionRunning(isRunning = false)
                            binding.player.updateIsSupportCast(isSupport = false)
                        }
                        MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                            navHostFragment = activity?.findNavHostFragment(),
                            onStopCastAndNavigate = {
                                MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                            }
                        )
                        return
                    }

                    else -> {}
                }
            }
        }
        getDetails()?.let { detail ->
            when (getEventType()) {
                PremiereViewModel.EventType.EVENT -> {
                    val state = Utils.getTimeLiveState(beginTime = Utils.convertStringToLong(detail.beginTime, 0L), endTime = Utils.convertStringToLong(detail.endTime, 0L))
                    when (state) {
                        2 -> {
                            binding.player.updateIsSupportCast(isSupport = true)
                            if (viewModel.getVipRequired()?.first == false) {
                                pairingConnection.sendEventPlayContent(
                                    id = viewModel.getDataDetail()?.id ?: "",
                                    highlightId = viewModel.getPremiereId(),
                                    refId = detail.refId,
                                    appId = detail.appId,
                                    title = detail.title,
                                    image = detail.posterImage,
                                    indexChapter = "",
                                    isPlay = "1",
                                    isLive = "1",
                                    timeWatched = "0",
                                    type = "event",
                                    deepLink = detail.websiteUrl,
                                    token = sharedPreferences.accessToken(),
                                    userId = sharedPreferences.userId(),
                                    userPhone = sharedPreferences.userPhone(),
                                    username = sharedPreferences.displayName(),
                                    beginTime = detail.beginTime,
                                    endTime = detail.endTime,
                                    autoProfiles = kotlin.run {
                                        try {
                                            listOf(getDetails()?.autoProfile ?: "")
                                        } catch (e: Exception) {
                                            listOf()
                                        }
                                    },
                                    currentAutoProfile = detail.autoProfile
                                )
                            }
                        }

                        1 -> {
                            safeUpdateUI {
                                binding.player.updateIsSupportCast(isSupport = false)
                            }
                        }

                        3 -> {
                            safeUpdateUI {
                                binding.player.updateIsSupportCast(isSupport = false)
                            }
                        }
                    }
                }

                PremiereViewModel.EventType.EVENT_TV -> {
                    binding.player.updateIsSupportCast(isSupport = true)
                    if (viewModel.getVipRequired()?.first == false) {
                        pairingConnection.sendEventPlayContent(
                            id = viewModel.getDataDetail()?.id ?: "",
                            highlightId = viewModel.getPremiereId(),
                            refId = detail.refId,
                            appId = detail.appId,
                            title = detail.title,
                            image = detail.posterImage,
                            indexChapter = "",
                            isPlay = "1",
                            isLive = "1",
                            timeWatched = "0",
                            type = "eventtv",
                            deepLink = detail.websiteUrl,
                            token = sharedPreferences.accessToken(),
                            userId = sharedPreferences.userId(),
                            userPhone = sharedPreferences.userPhone(),
                            username = sharedPreferences.displayName(),
                            beginTime = detail.beginTime,
                            endTime = detail.endTime,
                            autoProfiles = kotlin.run {
                                try {
                                    listOf(getDetails()?.autoProfile ?: "")
                                } catch (e: Exception) {
                                    listOf()
                                }
                            },
                            currentAutoProfile = detail.autoProfile
                        )
                    }
                }
            }

        }
    }

    private fun addPairingControlListener() {
        pairingConnection.apply {
            addConnectionListener(connectionListener)
            addCommunicationListener(communicationListener)
        }
    }

    private fun removePairingControlListener() {
        pairingConnection.apply {
            removeConnectionListener(connectionListener)
            removeCommunicationListener(communicationListener)
        }
    }


    private val connectionListener = object : FConnectionManager.FConnectionListener {
        override fun onConnectError(errorCode: Int, message: String) {
            removeIntervalUpdateProgress()
            if (errorCode != 2) {
                getStream(isRetry = false)
            }
            runOnUiThread {
                pairingConnection.showToast(message = if (errorCode == 2) binding.root.context.getString(R.string.pairing_cast_description_connect_error_disable_cast, pairingConnection.getReceiverName().truncateString()) else binding.root.context.getString(R.string.pairing_cast_title_connect_error))
                binding.player.run {
                    getDetails()?.let {
                        binding.player.onShowThumb(isShow = false)
                    }
                }
            }
        }

        override fun onConnectSuccess(message: String) {
            runOnUiThread {
                sendPlayContentMessage()
                pairingConnection.showToast(message = binding.root.context.getString(R.string.pairing_cast_title_connect_success, pairingConnection.getReceiverName()))
                binding.player.run {
                    stopServices()
                    stopPlayerLocal(force = true, isClearRequest = true)
                    getDetails()?.let { detail ->
                        val state = Utils.getTimeLiveState(beginTime = Utils.convertStringToLong(detail.beginTime, 0L), endTime = Utils.convertStringToLong(detail.endTime, 0L))
                        if (state == 3 && getEventType() != PremiereViewModel.EventType.EVENT_TV) {
                            if (MainApplication.INSTANCE.appConfig.bgEndEvent.isNotBlank()) {
                                binding.player.onShowThumb(
                                    isShow = true,
                                    url = MainApplication.INSTANCE.appConfig.bgEndEvent,
                                    detailPremiere = getDetails()
                                )
                            }
                        } else {
                            binding.player.onShowThumb(
                                isShow = true,
                                url = detail.posterImage,
                                detailPremiere = detail
                            )
                        }
                    }
                }
            }

            // Send tracking
            sendTracking(logId = "516", event = "CastToDevice")
        }

        override fun onDisconnectError(message: String) {
        }

        override fun onDisconnectSuccess(message: String) {
            runOnUiThread {
                removeIntervalUpdateProgress()
                pairingConnection.showToast(message = binding.root.context.getString(R.string.pairing_cast_title_disconnect_success, pairingConnection.getReceiverName()))
                if (viewModel.canPreview) {
                    Logger.d("$TAG trigger play preview: $eventTvPreviewPlayerInfo")
                    preparePlayPreviewForEventTv(eventTvPreviewPlayerInfo)
                } else {
                    getStream(isRetry = false)
                }
                binding.player.run {
                    getDetails()?.let {
                        binding.player.onShowThumb(isShow = false)
                    }
                }
            }
        }
    }


    private val communicationListener = object : FConnectionManager.FCommunicationListener {
        override fun onMessage(message: ObjectReceiver) {
            runOnUiThread {
                when (message) {
                    is FBoxObjectReceiver -> {
                        when (message.data) {
                            is ReceivePlayContent -> {
                                (message.data as? ReceivePlayContent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            startIntervalUpdateProgress()
                                        }
                                    }
                                    if (result.isFailure() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            if (result == "-1") { // Box Iptv (ref_id=-1) => Not stop session, continue play current content (if exist)
                                                MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                                    navHostFragment = activity?.findNavHostFragment(),
                                                    message = binding.root.context.getString(R.string.pairing_control_content_receiver_not_support_message_with_error_code, "#CP001"),
                                                    onCancel = {
                                                        findNavController().popBackStack()
                                                    },
                                                    onStopCastAndNavigate = {
                                                        MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                                    },
                                                    allowCancelByOutside = false
                                                )
                                            } else {
                                                safeUpdateUI {
                                                    pairingConnection.setSessionRunning(isRunning = false)
                                                    binding.player.updateIsSupportCast(isSupport = false)
                                                }
                                                MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                                    navHostFragment = activity?.findNavHostFragment(),
                                                    message = binding.root.context.getString(R.string.pairing_control_content_receiver_not_support_message),
                                                    onStopCastAndNavigate = {
                                                        MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                                    }
                                                )
                                            }
                                        }
                                    }
                                }
                            }

                            is ReceiveSetTrack -> {
                                (message.data as? ReceiveSetTrack)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage() && isMyContentId(data?.id)) {
                                        if (isSessionRunning()) {
                                            viewModel.getDataDetail()?.let {
                                                pairingConnection.sendEventGetStatusWatching(id = it.id, refId = it.refId)
                                            }
                                        }
                                    }
                                }
                            }

                            is ReceiveStopSession -> {
                                (message.data as? ReceiveStopSession)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (!isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                            if (shouldShowStopSessionToast()) {
                                                runOnUiThread {
                                                    pairingConnection.showToast(binding.root.context.getString(R.string.pairing_cast_title_player_stop_toast, pairingConnection.getReceiverName()))
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            is ReceiveExceptionEvent -> {
                                (message.data as? ReceiveExceptionEvent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                        }
                                    }
                                }
                            }

                        }
                    }

                    is FSamsungObjectReceiver,
                    is FSamsungObjectReceiverExternal -> {
                        when (message.data) {
                            is ReceivePlayContent -> {
                                (message.data as? ReceivePlayContent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            startIntervalUpdateProgress()
                                        }
                                    }
                                    if (result.isFailure() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                                navHostFragment = activity?.findNavHostFragment(),
                                                message = binding.root.context.getString(R.string.pairing_control_content_receiver_not_support_message),
                                                onStopCastAndNavigate = {
                                                    MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                                }
                                            )
                                        }
                                    }
                                }
                            }

                            is ReceiveStopSession -> {
                                (message.data as? ReceiveStopSession)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (!isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                            if (shouldShowStopSessionToast()) {
                                                runOnUiThread {
                                                    pairingConnection.showToast(binding.root.context.getString(R.string.pairing_cast_title_player_stop_toast, pairingConnection.getReceiverName()))
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            is ReceiveExceptionEvent -> {
                                (message.data as? ReceiveExceptionEvent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                        }
                                    }
                                }
                            }
                        }
                    }

                    is FAndroidTVObjectReceiver -> {
                        when (message.data) {
                            is FAndroidTVCustomMessageResponse -> {
                                (message.data as? FAndroidTVCustomMessageResponse)?.let {
                                    if (!isSessionRunning()) {
                                        removeIntervalUpdateProgress()
                                        if (shouldShowStopSessionToast()) {
                                            runOnUiThread {
                                                pairingConnection.showToast(binding.root.context.getString(R.string.pairing_cast_title_player_stop_toast, pairingConnection.getReceiverName()))
                                            }
                                        }
                                    }
                                }
                            }

                            is FAndroidTVCustomPlayContentResponse -> {
                                (message.data as? FAndroidTVCustomPlayContentResponse)?.run {
                                    if (result.isFailure()) {
                                        if (isSessionRunning()) {
                                            safeUpdateUI {
                                                pairingConnection.setSessionRunning(isRunning = false)
                                                binding.player.updateIsSupportCast(isSupport = false)
                                            }
                                            MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                                navHostFragment = activity?.findNavHostFragment(),
                                                message = binding.root.context.getString(R.string.pairing_control_content_receiver_not_support_message),
                                                onStopCastAndNavigate = {
                                                    MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                                }
                                            )
                                        }
                                    }
                                }
                            }

                            is FAndroidTVCustomStopSessionResponse -> {
                                (message.data as? FAndroidTVCustomStopSessionResponse)?.run {
                                    if (result.isSuccess()) {
                                        if (!isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                            if (shouldShowStopSessionToast()) {
                                                runOnUiThread {
                                                    pairingConnection.showToast(binding.root.context.getString(R.string.pairing_cast_title_player_stop_toast, pairingConnection.getReceiverName()))
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    is FAndroidTVObjectReceiverExternal -> {
                        when (message.data) {
                            is ReceivePlayContent -> {
                                (message.data as? ReceivePlayContent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            startIntervalUpdateProgress()
                                        }
                                    }
                                    if (result.isFailure() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                                navHostFragment = activity?.findNavHostFragment(),
                                                message = binding.root.context.getString(R.string.pairing_control_content_receiver_not_support_message),
                                                onStopCastAndNavigate = {
                                                    MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                                }
                                            )
                                        }
                                    }
                                }
                            }

                            is ReceiveStopSession -> {
                                (message.data as? ReceiveStopSession)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (!isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                            if (shouldShowStopSessionToast()) {
                                                runOnUiThread {
                                                    pairingConnection.showToast(binding.root.context.getString(R.string.pairing_cast_title_player_stop_toast, pairingConnection.getReceiverName()))
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            is ReceiveExceptionEvent -> {
                                (message.data as? ReceiveExceptionEvent)?.run {
                                    if (result.isSuccess() && senderId.isMyMessage()) {
                                        if (isSessionRunning()) {
                                            removeIntervalUpdateProgress()
                                        }
                                    }
                                }
                            }

                            else -> {}
                        }
                    }

                    else -> {}
                }
            }
        }

        override fun onSendMessageCallback(
            messageType: Int,
            isSuccess: Boolean,
            errorCode: Int,
            errorMessage: String
        ) {

        }
    }

    //region Handle progress countdown
    private var numGetTrackRequest = 0
    private var maxTrackRequest = 3


    private var handlerProgress: Handler? = null
    private var runnableProgress = Runnable {
        intervalUpdateProgress()
    }

    private fun startIntervalUpdateProgress() {
        numGetTrackRequest = 0
        removeIntervalUpdateProgress()
        if (handlerProgress == null) {
            Looper.getMainLooper()?.run { handlerProgress = Handler(this) }
        }
        handlerProgress?.post(runnableProgress)
    }

    private fun intervalUpdateProgress() {
        handlerProgress?.run {
            pairingConnection.getRemoteData()?.let {
                if (it.remotePlayer.tracks == null
                    && numGetTrackRequest < maxTrackRequest
                    && (it.remotePlayer.currentDuration % (5 * 1000L) == 0L)
                ) {
                    numGetTrackRequest += 1
                    triggerGetStatusWatching()
                    postDelayed(runnableProgress, 1000)
                }
            } ?: kotlin.run {
                pairingConnection.sendEventGetBoxInfo()
            }
        }
    }

    private fun removeIntervalUpdateProgress() {
        handlerProgress?.removeCallbacks(runnableProgress)
        handlerProgress = null
    }

    private fun triggerGetStatusWatching() {
        viewModel.getDataDetail()?.let {
            pairingConnection.sendEventGetStatusWatching(id = it.id, refId = it.refId)
        }
    }
    //endregion

    private fun isMyContentId(remoteId: String?): Boolean {
        if (remoteId == null) return false
        return remoteId == viewModel.getDataDetail()?.id
    }

    // endregion Pairing Control


    //region Handle User Realtime Playing
    private fun setupUserRealtimePlayingTracker() {
        if (isCalculateUserRealtimePlaying()) {
            Logger.d("logid setRealTimePlaying = ${viewModel.getPremiereId()} ==== ${getDetails()?.episodeId}")
            userRealtimePlayingTracker.setData(contentId = viewModel.getPremiereId(), extraId = getDetails()?.episodeId ?: "0")
        }
    }

    private fun isCalculateUserRealtimePlaying(): Boolean {
        Logger.d("logid CheckRealTimePlaying = ${viewModel.getVipRequired()?.first} ==== ${_binding?.player?.getPlayerConfig()?.isPlayPreview}")
        return (sharedPreferences.userLogin() && viewModel.getVipRequired()?.first == false) || _binding?.player?.getPlayerConfig()?.isPlayPreview ?: false || isPlayingVipTrailerInEnablePreview()
    }

    private val userRealtimePlayingTrackerListener = object : UserRealtimePlayingTracker.UserRealtimePlayingTrackerListener {
        override fun onRealtimePlayingChanged(timeWatched: Long) {
            _binding?.let {
                currentDuration = binding.player.currentDuration()
            }
            Logger.d("$TAG onRealtimePlayingChanged => Time watched : $timeWatched | CurrentDuration: $currentDuration")
        }

        override fun onRealtimePlayingAtCheckpoint(timeWatched: Long, checkPoint: Long) {}
        override fun getCheckpointForRealtimeTracking(): List<Long> {
            return listOf()
        }
    }
    //endregion

    //region Age Restriction
    private fun setupAgeRestrictionHandler() {
        if (isPremiere()) {
            ageRestrictionHandler.initAgeRestrictionsData(
                vodId = viewModel.getPremiereId(),
                episodeId = "0",
                contentDuration = _binding?.player?.totalDuration() ?: 0L,
                value = getDetails()?.maturityRating?.value ?: "",
                prefix = getDetails()?.maturityRating?.prefix ?: "",
                position = getDetails()?.maturityRating?.position ?: "",
                advisories = getDetails()?.maturityRating?.advisories ?: "",
                startTime = Utils.convertStringToLong(text = MainApplication.INSTANCE.appConfig.maturityRating.startTime, 3L) * 1000,
                duration = Utils.convertStringToLong(text = MainApplication.INSTANCE.appConfig.maturityRating.duration, 10L) * 1000
            )
        }
    }

    private val ageRestrictionHandlerListener = object : AgeRestrictionHandler.AgeRestrictionListener {
        override fun onShow(checkPoint: Long, value: String, position: String, advisories: String, duration: Long) {
            if (getEventType() == PremiereViewModel.EventType.EVENT && isPremiere()) {
                _binding?.player?.showAgeRestriction(checkPoint, value, position, advisories, duration)
                Logger.d("$TAG => AgeRestrictionHandlerListener => onShow => checkpoint: $checkPoint")
            }
        }

        override fun onStop() {
            Logger.d("$TAG => AgeRestrictionHandlerListener => onStop")
            clearAgeRestriction()
        }
    }

    private fun clearAgeRestriction() {
        _binding?.let {
            it.player.hideAgeRestriction()
            it.player.clearAgeRestrictionData()
        }
    }
    //endregion

    //region Situation Warning Content Handler
    private fun setupSituationWarningHandler() {
        if (getEventType() == PremiereViewModel.EventType.EVENT && isPremiere()) {
            situationWarningHandler.initData(data = stream?.warnings ?: listOf())
        }
    }

    private val situationWarningListener = object : SituationWarningHandler.SituationWarningListener {
        override fun getPlayCurrentDuration(): Long {
            return _binding?.player?.currentDuration() ?: 0L
        }
        override fun onShow(content: Stream.WarningScenario) {
            if (getEventType() == PremiereViewModel.EventType.EVENT && isPremiere()) {
                _binding?.let {
                    Logger.d("$TAG situationWarningListener onShow = ${content}")
                }
            }
        }

        override fun onHide() {
            _binding?.let {
                Logger.d("$TAG situationWarningListener onHide")
            }
        }
    }
    //endregion

    //region for log
    private fun getRealTimePlayingForLog(): String{
        val extraId = if (binding.player.isPlayingVipTrailer()) "-2" else getDetails()?.episodeId?: "0"
        return userRealtimePlayingTracker.getRealTimePlaying(contentId = viewModel.getPremiereId(), extraId = extraId).toString()
    }
    //endregion

    //region Handle Player Error
    private fun setupPlayerRetryHandler(channelId: String, streamId: String) {
        playerRetryHandler.setupData(channelId = channelId, streamId = streamId)
    }

    private fun setPlayerRetryHandlerListener() {
        playerRetryHandler.setListener(listener = playerRetryHandlerListener)
    }

    private fun removePlayerRetryHandlerListener() {
        playerRetryHandler.removeListener()
    }

    private val playerRetryHandlerListener = object : PlayerRetryHandler.PlayerRetryHandlerListener {
        override fun getRealtimePlayingMs(): Long {
            return userRealtimePlayingTracker.getRealtimePlaying() * 1000L
        }

        override fun onRetryGetStream() {
            tryRetry()
        }

        override fun onShowPlayerError(shouldCountDown: Boolean, countdownTimeMs: Long, code: Int, responseCode: Int) {
            showPlayerError(shouldCountDown = shouldCountDown, countdownTimeMs = countdownTimeMs, code = code, responseCode = responseCode)
        }

        override fun onSendTrackingPlayerError(
            isAutoRetry: Boolean,
            screen: String,
            errorCode: String,
            errorMessage: String
        ) {
            sendTrackingPlayerError(isAutoRetry = isAutoRetry, screen = screen, errorCode = errorCode, errorMessage = errorMessage)
        }
    }

    private fun showPlayerError(shouldCountDown: Boolean, countdownTimeMs: Long, code: Int, responseCode: Int) {
        if (activity.isInPiPMode()) {
            playerPiPRetryHandler.startRetryFlow(
                processData = PlayerPiPRetryHandler.PlayerRetryData(
                    shouldCountDown = shouldCountDown,
                    countdownTimeMs = countdownTimeMs,
                    code = code,
                    responseCode = responseCode
                ),
                onCompleted = {
                    tryRetry()
                }
            )
        } else {
            if (shouldCountDown) {
                showPlayerErrorWithRetryDialog(message = getErrorMessage(code, responseCode))
                stopCountDownTimerRetry()
                startCountDownTimerRetry(timeCountDown = countdownTimeMs / 1000L)
            } else {
                showPlayerErrorDialog(message = getErrorMessage(code, responseCode))
            }
        }
    }



    //region Retry task
    private fun tryRetry(delay: Long = 0L) {
        binding.player.stop(force = true)
        getStream(delay = delay, isRetry = true)
    }

    private fun getErrorMessage(code: Int, responseCode: Int): String {
        val message =
            MainApplication.INSTANCE.appConfig.msgPlayerError.ifBlank {
                <EMAIL>().getString(
                    R.string.msg_player_error
                )
            }
        return if (responseCode != UnValidResponseCode) {
            "$message (Mã lỗi ${code}-${responseCode})"

        } else {
            "$message (Mã lỗi $code)"
        }
    }

    private fun startCountDownTimerRetry(timeCountDown: Long) {
        if (countDownTimerRetry == null) {
            countDownTimerRetry = object : CountDownTimer(timeCountDown * 1000 + 1000, 1000) {
                override fun onTick(millis: Long) {
                    if (playerRetryDialog?.isShow() == true) {
                        playerRetryDialog?.updateTextConfirm(
                            "${
                                <EMAIL>(
                                    R.string.all_retry
                                )
                            } (${millis / 1000})"
                        )
                    }
                }

                override fun onFinish() {
                    stopCountDownTimerRetry()
                    playerRetryDialog?.dismissAllowingStateLoss()
                    tryRetry()
                }
            }
            countDownTimerRetry?.start()
        }
    }

    private fun stopCountDownTimerRetry() {
        if (countDownTimerRetry != null) {
            countDownTimerRetry?.cancel()
            countDownTimerRetry = null
        }
    }
    //endregion

    //region Tracking player retry
    private fun sendTrackingPlayerError(
        isAutoRetry: Boolean,
        screen: String,
        errorCode: String,
        errorMessage: String
    ) {
        sendTracking(
            logId = TrackingConstants.EVENT_LOG_ID_ERROR_STREAM_VOD,
            screen = screen,
            event = "PlaybackError",
            errorCode = errorCode,
            errorMessage = errorMessage
        )
        if (!isAutoRetry) {
            sendTrackingShowPopupRetry(screen, errorCode, errorMessage)
        }
    }

    private fun sendTrackingShowPopupRetry(
        screen: String,
        errorCode: String,
        errorMessage: String
    ) {
        sendTracking(
            logId = "17",
            screen = screen,
            event = "Error",
            errorCode = errorCode,
            errorMessage = errorMessage,
            issueId = TrackingUtil.createIssueId(),
        )
    }
    //endregion


    //endregion


    //region Handle Get Stream Error
    private fun setPlayerGetStreamRetryListener() {
        playerGetStreamRetryHandler.setListener(listener = playerGetStreamRetryListener)
    }

    private fun removePlayerGetStreamRetryListener() {
        playerGetStreamRetryHandler.setListener(listener = null)
    }

    private fun resetGetStreamRetryStep() {
        playerGetStreamRetryHandler.resetGetStreamRetryStep()
    }

    private fun notifyGetStreamError(errorMessage: String) {
        playerGetStreamRetryHandler.notifyGetStreamError(errorMessage = errorMessage)
    }

    private val playerGetStreamRetryListener = object : PlayerGetStreamRetryHandler.PlayerGetStreamRetryListener {
        override fun onRetryGetStream() {
            tryRetry(delay = PlayerGetStreamRetryHandler.FIRST_TIME_DELAY_TIMES_MS)
        }

        override fun onShowGetStreamError(
            shouldCountDown: Boolean,
            countdownTimeMs: Long,
            errorMessage: String
        ) {
            if (activity.isInPiPMode()) {
                playerPiPRetryHandler.startRetryFlow(
                    processData = PlayerPiPRetryHandler.PlayerGetStreamRetryData(
                        shouldCountDown = shouldCountDown,
                        countdownTimeMs = countdownTimeMs,
                        errorMessage = errorMessage
                    ),
                    onCompleted = {
                        tryRetry()
                    }
                )
            } else {
                if (shouldCountDown) {
                    showPlayerErrorWithRetryDialog(message = errorMessage)
                    stopCountDownTimerRetry()
                    startCountDownTimerRetry(timeCountDown = countdownTimeMs / 1000L)
                } else {
                    showPlayerErrorDialog(message = errorMessage)
                }
            }
        }

        override fun onSendTrackingGetStreamError(
            isAutoRetry: Boolean,
            screen: String,
            errorMessage: String
        ) {
            sendTracking(
                logId = "17",
                screen = screen,
                event = "Error",
                errorCode = AppErrorConstants.GET_EVENT_STREAM_CODE,
                errorMessage = errorMessage,
                issueId = TrackingUtil.createIssueId(),
            )
        }

    }

    //endregion

    //region handle preview

    private fun playPreview(info: LivePreviewInfo) {
        when (PlayerUtils.getPlayingType()) {
            is PlayerView.PlayingType.Local -> {
                sendTrackLogLiveShow(false)
                when (getEventType()) {
                    PremiereViewModel.EventType.EVENT_TV -> {
                        preparePlayPreviewForEventTv(info.mapToEventTvPreviewInfo())
                    }
                    PremiereViewModel.EventType.EVENT -> {
                        // playPreview not supported for EVENT type
                    }
                }
            }
            is PlayerView.PlayingType.Cast -> {}
        }
        // PairingControl
        // disconnect pairingControl when play preview
        pairingConnection.disconnect()
        //
    }

    private fun preparePlayPreviewForEventTv(info: EventTvPreviewPlayerInfo) {
        setupPlayerRetryHandler(channelId = getDetails()?.id ?: "", streamId = getDetails()?.autoProfile ?: "")
        isPreviewEnded = false
        binding.player.preparePlayerEventTvPreview(
            playerConfig = PlayerConfigBuilder()
                .setEnableReportPlayer(getDetails()?.enableReport?:false)
                .setIsPlayPreview(isPlayPreview = true)
                .build(),
            eventId = viewModel.getPremiereId(),
            isPremiere = isPremiere(),
            autoProfile = getDetails()?.autoProfile ?: "",
            details = getDetails(),
            info = info,
            onEvents = object : PlayerHandler.OnEvents {
                override fun showError(reason: String) {
                    Logger.d("$TAG showError $reason")
                    showWarningDialog(message = reason)
                }

                override fun navigateToRequiredVip(
                    planId: String,
                    fromSource: String,
                    idToPlay: String,
                    vipBackground: String,
                    title: String,
                    description: String,
                    btnActive: String,
                    btnSkip: String,
                    trailerUrl: String,
                ) {

                }

                override fun navigateToRequireLogin(message: String, idToPlay: String) {
                    parentFragment?.parentFragment?.navigateToLoginWithParams(
                        title = message,
                        idToPlay = idToPlay,
                        isDirect = true
                    )
                }

                override fun sendTrackingPingPlayCcu(
                    actionType: CommonInfor.PingStreamActionType,
                    message: String,
                    showFingerprint: Boolean,
                    type: String
                ) {
                    // Send Tracking
                    if (actionType == CommonInfor.PingStreamActionType.PING_STREAM_SHOW_ACTION) {
                        sendTrackingShowIpSuccess(
                            event = "ShowSuccessfully",
                            errorMessage = "",
                            typeId = type,
                            message = message
                        )
                    } else if (actionType == CommonInfor.PingStreamActionType.PING_STREAM_RECEIVER_ACTION) {
                        sendTrackingReceiveShowIp(
                            event = "ReceivedSuccessfully",
                            errorMessage = "",
                            typeId = type,
                            message = message
                        )
                    }
                }

                override fun hideShowIp() {
                    tvShowIp?.hide()
                }
            },
            onCastSessionEvents = object : PlayerHandler.OnCastSessionEvents {
                override fun checkCastSession(): Boolean {
                    return pairingConnection.isConnected
                }
            },
            processShowIp = { duration, x, y ->
                if (!activity.isInPiPMode()) {
                    viewLifecycleOwner.lifecycleScope.launch {
                        val ipPublic = viewModel.getPublicIp()
                        withContext(Dispatchers.Main) {
                            try {
                                val root = binding.player.getViewStubShowIP().safeInflate()
                                if (root != null) tvShowIp = (root as TextView)
                                tvShowIp?.let { tvShowIp ->
                                    val value =
                                        String.format("%s - %s", ipPublic, sharedPreferences.userId())
                                    tvShowIp.text = value
                                    tvShowIp.x = 0f
                                    tvShowIp.y = 0f
                                    tvShowIp.afterMeasured {
                                        context?.let {
                                            val newX = binding.player.width * (x / 100f) - tvShowIp.width
                                            val newY = binding.player.height * (y / 100f) - tvShowIp.height
                                            tvShowIp.x = if (newX >= 0) newX else 0f
                                            tvShowIp.y = if (newY >= 0) newY else 0f
                                        }
                                    }
                                    tvShowIp.show()
                                    cdtShowIp?.cancel()
                                    cdtShowIp = object : CountDownTimer(
                                        TimeUnit.SECONDS.toMillis(if (duration == 0L) 60 else duration),
                                        1000
                                    ) {
                                        override fun onTick(p0: Long) {}
                                        override fun onFinish() {
                                            tvShowIp.hide()
                                        }
                                    }.start()
                                    binding.player.sendTrackingPingPlayCcu(
                                        actionType = CommonInfor.PingStreamActionType.PING_STREAM_SHOW_ACTION,
                                        showFingerprint = true, message = value
                                    )
                                }
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                        }
                    }
                }
            },
            processShowMatrixIp = {
                // Mobile platform: Disable actively show ip logic
                /*viewLifecycleOwner.lifecycleScope.launch {
                    val ipPublic = viewModel.getPublicIp()
                    withContext(Dispatchers.Main) {
                        try {
                            val userId = sharedPreferences.userId()
                            val root = binding.player.getViewStubShowMatrix().safeInflate()
                            if (root != null) rlShowMatrixIp = (root as RelativeLayout)
                            rlShowMatrixIp?.let { rlShowMatrixIp ->
                                val value = String.format("%s - %s", ipPublic, userId)
                                for (index in 0 until rlShowMatrixIp.childCount) {
                                    val tvIp = rlShowMatrixIp.getChildAt(index)
                                    if (tvIp is TextView) tvIp.text = value
                                }
                                rlShowMatrixIp.show()
                                cdtShowMatrixIp?.cancel()
                                cdtShowMatrixIp = object : CountDownTimer(TimeUnit.SECONDS.toMillis(60), 1000) {
                                    override fun onTick(p0: Long) {}
                                    override fun onFinish() {
                                        rlShowMatrixIp.hide()
                                    }
                                }.start()
                                binding.player.sendTrackingPingPlayCcu(
                                    actionType = CommonInfor.PingStreamActionType.PING_STREAM_SHOW_ACTION,
                                    showFingerprint = true, message = "$value - FullScreen"
                                )
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }*/
            },
            processReloadStream = {
                tryRetry()
            },
            headers = Utils.getHeaderForPlayerRequest(info.streamSession),
            drmKey = info.drmKey?.drmKeyBase64?.fromBase64Default(),
        )
    }

    private fun requiredBuyPackageForPreview() {
        val requiredBuyPackageTitle = getRequiredBuyPackageTitle()
        val requiredBuyPackageMessage = getRequiredBuyPackageMessage()
        showRequireBuyPackageDialog(title = requiredBuyPackageTitle, message = requiredBuyPackageMessage, "PreviewEndTimeDialog")
        sendTrackLogLiveShow(true)
    }

    private fun showRequireBuyPackageDialog(title: String, message: String, tag: String = "") {
        previewEndTimeDialog?.dismissAllowingStateLoss()
        previewEndTimeDialog = AlertDialog().apply {
            setShowTitle(true)
            setTextTitle(
                title
            )
            setMessage(
                message
            )
            setTextConfirm(viewModel.getTextButtonBellowPlayer(binding.root.context))
            setTextExit(<EMAIL>(R.string.close))
            setOnlyConfirmButton(false)
            setListener(object : AlertDialogListener {
                override fun onExit() {
                    previewEndTimeDialog?.dismissAllowingStateLoss()
                }
                override fun onConfirm() {
                    handleBuyPackageForPreview()
                    previewEndTimeDialog?.dismissAllowingStateLoss()
                }
            })
            isCancelable = false
        }
        previewEndTimeDialog?.show(childFragmentManager, tag)
        sendTrackingShownPopup(message)
    }

    private fun getRequiredBuyPackageTitle(): String {
        val requiredBuyPackageTitle = viewModel.getVipRequired()?.second?.livePreviewInfo?.previewTitle ?: ""
        return requiredBuyPackageTitle.ifBlank { <EMAIL>(R.string.title_buy_package_for_live_preview) }
    }

    private fun getRequiredBuyPackageMessage(): String {
        val requiredBuyPackageMessage = viewModel.getVipRequired()?.second?.livePreviewInfo?.previewMessage ?: ""
        return requiredBuyPackageMessage.ifBlank { <EMAIL>(R.string.message_buy_package_for_live_preview) }
    }

    private fun handleBuyPackageForPreview() {
        TrackingUtil.savePreviewProcessItem(vodId = viewModel.getDataDetail()?.id ?: "", episodeId = "")
        val requireVipPlan = viewModel.getVipRequired()?.second?.livePreviewInfo?.requiredVipPlan ?: ""
        parentFragment?.parentFragment?.findNavController()?.navigateSafe(
            NavHomeMainDirections.actionGlobalToPayment(
                idToPlay = viewModel.getPremiereId(),
                extraId = "",
                bitrateId = viewModel.getDataDetail()?.autoProfile ?:"",
                type = "eventtv",
                isPremiere = false,
                viewType = BillingUtils.PACKAGE_VIEW_TYPE_PREVIEW,
                packageType = requireVipPlan,
                popupToId = R.id.nav_premiere
            )
        )
        AdjustAllEvent.sendPackageRecommendClickEvent(packageId = viewModel.getVipRequired()?.second?.livePreviewInfo?.requiredVipPlan ?: "",
            packageName = viewModel.getVipRequired()?.second?.requireVipName?: "")
        PaymentTrackingUtil.accessViewListPackagePayment(trackingProxy, trackingInfo)
    }

    private fun LivePreviewInfo.mapToEventTvPreviewInfo(): EventTvPreviewPlayerInfo {
        val ttlPreview = <EMAIL>
        return eventTvPreviewPlayerInfo.apply {
            playScheduleStream = false
            url = <EMAIL>
            urlH265 = ""
            drmKey = null
            requiredVipTitle = previewTitle
            requiredVipDescription = previewMessage
            available = <EMAIL>
            merchant = <EMAIL>
            streamSession = <EMAIL>
            session = <EMAIL>
            pingSession = <EMAIL>
            pingEnable = <EMAIL>
            pingEnc = <EMAIL>
            pingQnet = <EMAIL>
            overlayLogo =<EMAIL>
            ttlPreviewInSecond = if (ttlPreview != 0) ttlPreview else PreviewTimer.DEFAULT_PREVIEW_END_TIME
        }
    }

    private fun isPlayingPreview(): Boolean {
        return binding.player.getPlayerConfig().isPlayPreview
    }

    private fun isPlayingVipTrailerInEnablePreview(): Boolean {
        return viewModel.hasPreview && binding.player.isPlayingVipTrailer()
    }

    private fun handleEndPreviewOnError() {
        if (!isPreviewEnded) {
            Logger.d("PreviewTimer - End preview")
            isPreviewEnded = true
            requiredBuyPackageForPreview()
            binding.player.exitFullscreenMode()
            userRealtimePlayingTracker.clearData()
            previewTimer.clearData()
            if(NetworkUtils.isNetworkAvailable()) {
                viewModel.triggerPlayRequiredVipTrailer(viewModel.getVipRequired()?.second?.livePreviewInfo?.trailerUrl ?: "")
            }
        }
    }

    private fun sendTrackingShownPopup(message: String) {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "191",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "Popup",
                event = "ShowPopup",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                chapterId = "",
                itemId = getDetails()?.id?:"",
                itemName = message
            )
        )
    }

    private fun setupDataForPreviewTimer() {
        previewTimer.setData(eventTvPreviewPlayerInfo.ttlPreviewInSecond)
    }

    private val previewTimerListener = object: PreviewTimer.PreviewTimerListener {
        override fun onEndPreviewTime() {
            handleEndPreviewOnError()
        }
    }
    //endregion

    //MQTT
    private fun publishStartToTopic(contentType: MqttContentType, iType : ItemType, pingStart: Boolean = false, drmPartner: String = "", mqttMode: Int = DEFAULT_MODE) {
        if (mqttPublisher != null) {
            publishEndToTopic()
        }
        if (pingStart) {
            mqttPublisher = Publisher(
                action = MqttUtil.ACTION_START,
                createdTime = getCurrentTimeInSeconds(),
                isRetry = 0,
                uid = sharedPreferences.userId(),
                contract = "",
                netMode = NetworkUtils.getNetworkMode(),
                appVer = BuildConfig.VERSION_NAME,
                profileId = sharedPreferences.profileId(),
                contentType = contentType.value.toString(),
                playlistId = "",
                chapterId = "",
                episodeId ="",
                itemId = viewModel.getPremiereId(),
                refPlaylistId ="",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refEpisodeId = TrackingUtil.contentPlayingInfo.refEpisodeId,
                appSource = getDetails()?.appId ?: "",
                isLinkDrm = if (getDetails()?.isDrm == true) "1" else "0",
                mode = mqttMode.toString(),
                sourceProvider = getDetails()?.sourceProvider ?: "",
                drmPartner = drmPartner,
                businessPlan = getDetails()?.payment?.requireVipPlan ?: "",
                isFree = "",
            ).apply {
                itemType = iType
                MqttConnectManager.INSTANCE.publishToTopic(
                    publisher = this,
                    type = itemType.id,
                    typeId = itemId
                )
            }
        }
    }
    private fun publishEndToTopic() {
        mqttPublisher?.let { publisher ->
            MqttConnectManager.INSTANCE.publishToTopic(
                publisher = publisher.copy(action = MqttUtil.ACTION_END, createdTime = getCurrentTimeInSeconds()),
                type = publisher.itemType.id,
                typeId = publisher.itemId
            )
            mqttPublisher = null
        } ?: kotlin.run { return }
    }

    private fun updateLiveChatWhenLiveState(state: PremiereLiveState) {
        binding.player.isEnableLiveChat = when(state) {
            PremiereLiveState.ComingSoon -> {
                false
            }
            PremiereLiveState.CountDown,
            PremiereLiveState.Live,
            PremiereLiveState.End -> {
                true
            }
        }
    }
    private val limitCcuActionListener = object : LimitCcuActionListener {
        override fun checkPingCcuBackup(option: MqttOptionData) {
            viewModel.dispatchIntent(
                PremiereViewModel.PremiereIntent.GetMqttBackup(
                    itemId = getDetails()?.id ?: "",
                    chapterId = getDetails()?.episodeId?: "",
                    episodeId = "",
                    playlistId = "",
                    option = option
                )
            )
        }
    }
}