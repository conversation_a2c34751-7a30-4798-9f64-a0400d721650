plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'dagger.hilt.android.plugin'
    id 'androidx.navigation.safeargs.kotlin'
    // Apply the Crashlytics Gradle plugin
    id 'com.google.firebase.crashlytics'
}

def keystoreDebugPropertiesFile = file("../resources/properties/debug/config-field.properties")
def keystoreReleasePropertiesFile = file("../resources/properties/release/config-field-release.properties")
// Initialize a new Properties() object called keystoreProperties.
def keystoreDebugProperties = new Properties()
def keystoreReleaseProperties = new Properties()

// Load your keystore.properties file into the keystoreProperties object.
keystoreDebugProperties.load(new FileInputStream(keystoreDebugPropertiesFile))

// Load release properties if file exists, otherwise fail with clear instructions
if (keystoreReleasePropertiesFile.exists()) {
    keystoreReleaseProperties.load(new FileInputStream(keystoreReleasePropertiesFile))
} else {
    // Fail the build gracefully with clear instructions
    throw new GradleException("""
    ❌ Release configuration file not found: ${keystoreReleasePropertiesFile.absolutePath}
    
    📋 To build release versions, you need to set up the release configuration:
    
    1️⃣  Copy the template:
        cp resources/properties/release/config-field-release.properties.template resources/properties/release/config-field-release.properties
    
    2️⃣  Fill in your actual values in the copied file
    
    3️⃣  Then run your release build
    
    💡 For development, use debug builds instead:
        ./gradlew assembleDebug
        ./gradlew assembleDevDebug
        ./gradlew assembleBillingDebug
    
    ℹ️  See SETUP.md for detailed instructions
    """)
}

android {
    compileSdk Versions.compile_sdk

    defaultConfig {
        applicationId MetaData.application_id
        minSdk Versions.min_sdk
        targetSdk Versions.target_sdk
        versionCode Versions.version_code
        versionName Versions.version_name
        ndk.abiFilters "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        multiDexEnabled true
        setProperty("archivesBaseName", MetaData.apk_name)
    }

    signingConfigs {
        debug {
            // Dev key configuration
            keyAlias keystoreDebugProperties['KSA']
            keyPassword keystoreDebugProperties['KSAP']
            storeFile file(keystoreDebugProperties['KSF'])
            storePassword keystoreDebugProperties['KSP']
        }

        release {
            // Release key configuration
            keyAlias keystoreReleaseProperties['KSA']
            keyPassword keystoreReleaseProperties['KSAP']
            storeFile file(keystoreReleaseProperties['KSF'])
            storePassword keystoreReleaseProperties['KSP']
        }

        normalRelease {
            // Release key configuration for normal flavor
            keyAlias keystoreReleaseProperties['KSA']
            keyPassword keystoreReleaseProperties['KSAP']
            storeFile file(keystoreReleaseProperties['KSF'])
            storePassword keystoreReleaseProperties['KSP']
        }

        devRelease {
            // Debug key configuration for dev flavor
            keyAlias keystoreDebugProperties['KSA']
            keyPassword keystoreDebugProperties['KSAP']
            storeFile file(keystoreDebugProperties['KSF'])
            storePassword keystoreDebugProperties['KSP']
        }

        billingRelease {
            // Debug key configuration for billing flavor
            keyAlias keystoreDebugProperties['KSA']
            keyPassword keystoreDebugProperties['KSAP']
            storeFile file(keystoreDebugProperties['KSF'])
            storePassword keystoreDebugProperties['KSP']
        }
    }

    flavorDimensions "partner"

    productFlavors {
        normal {
            dimension "partner"
            minSdkVersion Versions.min_sdk
            versionCode Versions.version_code
            manifestPlaceholders = [
                    clevertap_pj_id: "888-98Z-985Z",
                    clevertap_pj_token: "1bc-bbb",
                    clevertap_sender_id: "748621279622"
            ]

            buildConfigField("String", "ADJUST_APP_TOKEN", "\"${keystoreReleaseProperties['ADJUST_APP_TOKEN']}\"")
            buildConfigField("boolean", "IS_PRODUCTION", "true")

            // Configure signing for normal flavor
            signingConfig signingConfigs.normalRelease
        }

        dev {
            dimension "partner"
            minSdkVersion Versions.min_sdk
            versionCode Versions.version_code
            manifestPlaceholders = [
                    clevertap_pj_id: "4W5-565-W95Z",
                    clevertap_pj_token: "565-504",
                    clevertap_sender_id: "412164377112"
            ]

            buildConfigField("String", "ADJUST_APP_TOKEN", "\"${keystoreDebugProperties['ADJUST_APP_TOKEN']}\"")
            buildConfigField("boolean", "IS_PRODUCTION", "false")

            // Configure signing for dev flavor
            signingConfig signingConfigs.devRelease
        }

        billing {
            dimension "partner"
            minSdkVersion Versions.min_sdk
            applicationIdSuffix = ".billing"
            versionCode 1202
            manifestPlaceholders = [
                    clevertap_pj_id: "4W5-565-W95Z",
                    clevertap_pj_token: "565-504",
                    clevertap_sender_id: "412164377112"
            ]

            buildConfigField("String", "ADJUST_APP_TOKEN", "\"${keystoreDebugProperties['ADJUST_APP_TOKEN']}\"")
            buildConfigField("boolean", "IS_PRODUCTION", "false")

            // Configure signing for billing flavor
            signingConfig signingConfigs.billingRelease
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            resValue "string", "app_name", MetaData.app_name_debug
            signingConfig signingConfigs.debug

//            applicationIdSuffix = ".debug"
            buildConfigField('String', 'ZENDESK_URL', "\"${keystoreDebugProperties['ZENDESK_URL']}\"")
            buildConfigField('String', 'ZENDESK_APP_ID', "\"${keystoreDebugProperties['ZENDESK_APP_ID']}\"")
            buildConfigField('String', 'ZENDESK_CLIENT_ID', "\"${keystoreDebugProperties['ZENDESK_CLIENT_ID']}\"")
            buildConfigField('String', 'ZENDESK_KEY_VALUE_ENCRYPT', "\"${keystoreDebugProperties['ZENDESK_KEY_VALUE_ENCRYPT']}\"")
        }

        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            resValue "string", "app_name", MetaData.app_name

            buildConfigField('String', 'ZENDESK_URL', "\"${keystoreReleaseProperties['ZENDESK_URL']}\"")
            buildConfigField('String', 'ZENDESK_APP_ID', "\"${keystoreReleaseProperties['ZENDESK_APP_ID']}\"")
            buildConfigField('String', 'ZENDESK_CLIENT_ID', "\"${keystoreReleaseProperties['ZENDESK_CLIENT_ID']}\"")
            buildConfigField('String', 'ZENDESK_KEY_VALUE_ENCRYPT', "\"${keystoreReleaseProperties['ZENDESK_KEY_VALUE_ENCRYPT']}\"")

            kotlinOptions {
                freeCompilerArgs = [
                    '-Xno-param-assertions',
                    '-Xno-call-assertions',
                    '-Xno-receiver-assertions'
                ]
            }
        }
    }

    compileOptions {
        // Flag to enable support for the new language APIs
        coreLibraryDesugaringEnabled true

        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    buildFeatures {
        viewBinding true
    }

    kotlinOptions {
        jvmTarget = '17'
        freeCompilerArgs += "-Xjvm-default=all" // Only required for 2.x.
    }
    namespace 'com.fptplay.mobile'
}

/* 
 * Media3 Local Maven Instructions:
 * 
 * 1. Uncompress the media3.zip file:
 *    Command: unzip media3.zip -d media3_unzipped
 * 
 * 2. The artifact will be installed at:
 *    ~/.m2/repository/androidx/media3/media3/1.6.1.rc01
 * 
 * 3. Sync.
 */

repositories {
    maven { url 'https://zendesk.jfrog.io/zendesk/repo' }
}

dependencies {

    implementation "androidx.core:core-ktx:$ktx_version"
    implementation "androidx.appcompat:appcompat:$app_compat_version"
    implementation "com.google.android.material:material:$material_version"
    implementation 'androidx.lifecycle:lifecycle-process:2.4.1'
    implementation 'androidx.lifecycle:lifecycle-service:2.5.0'
    implementation 'androidx.window:window:1.1.0'
    implementation 'androidx.palette:palette-ktx:1.0.0'
    testImplementation "junit:junit:$junit_version"
    androidTestImplementation "androidx.test.ext:junit:$ext_junit"
    androidTestImplementation "androidx.test.espresso:espresso-core:$core_espresso"

    // Core aar
//    implementation fileTree(dir: "libs", include: ["*.aar"])

//    implementation project(path: ":downloader")

    devImplementation fileTree(dir: "libs/debug", include: ["*.aar"])
    billingImplementation fileTree(dir: "libs/debug", include: ["*.aar"])
    normalImplementation fileTree(dir: "libs/release", include: ["*.aar"])

    //region Common
    implementation "androidx.multidex:multidex:$multidex_version"
    implementation "androidx.recyclerview:recyclerview:$recyclerview_version"
    implementation "androidx.constraintlayout:constraintlayout:$constraint_layout_version"
    implementation "androidx.cardview:cardview:$cardview_version"
    implementation "com.intuit.sdp:sdp-android:$sp_dp_version"
    implementation "com.intuit.ssp:ssp-android:$sp_dp_version"
    implementation "androidx.fragment:fragment-ktx:$ktx_fragment_version"
    implementation "androidx.activity:activity-ktx:$ktx_activity_version"
    implementation "androidx.preference:preference-ktx:$ktx_preference_version"

    // Coroutine
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$kotlin_coroutine_version"

    // Glide
    implementation "com.github.bumptech.glide:glide:$glide_version"
    kapt "com.github.bumptech.glide:compiler:$glide_version"

    // Coil
    implementation "io.coil-kt:coil:$coil_version"
    implementation "io.coil-kt:coil-gif:$coil_version"

    // Worker
    implementation "androidx.work:work-runtime:$work_version"
    implementation "androidx.work:work-runtime-ktx:$work_version"

    // Lifecycle
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-viewmodel-savedstate:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-common-java8:$lifecycle_version"

    // Navigation
    implementation "androidx.navigation:navigation-fragment-ktx:$nav_version"
    implementation "androidx.navigation:navigation-ui-ktx:$nav_version"
    implementation "androidx.navigation:navigation-runtime-ktx:$nav_version"

    // Hilt
    implementation "com.google.dagger:hilt-android:$hilt_dagger_version"
    kapt "com.google.dagger:hilt-compiler:$hilt_dagger_version"
    implementation "androidx.hilt:hilt-navigation-fragment:$hilt_fragment_version"
    implementation "androidx.hilt:hilt-work:$hilt_work_manager_version"
    kapt "androidx.hilt:hilt-compiler:$hilt_compiler_version"

    // Gson
    implementation "com.google.code.gson:gson:$gson_version"

    // Timber
    implementation "com.jakewharton.timber:timber:$timber_version"

    // Google billing
    implementation "com.android.billingclient:billing:$google_billing_version"
    implementation("com.android.billingclient:billing-ktx:$google_billing_version")


    // Sigma + Media3
    implementation ("com.sigma.packer:$sigma_packer_version"){
        exclude group: 'org.checkerframework', module: 'checker-qual'
        exclude group: 'androidx.media3', module: 'media3-exoplayer'
        exclude group: 'androidx.media3', module: 'media3-database'
        exclude group: 'androidx.media3', module: 'media3-decoder'
        exclude group: 'androidx.media3', module: 'media3-container'
        exclude group: 'androidx.media3', module: 'media3-datasource'
        exclude group: 'androidx.media3', module: 'media3-common'
        exclude group: 'androidx.media3', module: 'media3-extractor'
    }

    implementation("androidx.media3:media3-exoplayer:$media3_version")
    implementation("androidx.media3:media3-exoplayer-dash:$media3_version")
    implementation("androidx.media3:media3-exoplayer-hls:$media3_version")
    implementation("androidx.media3:media3-exoplayer-smoothstreaming:$media3_version")
    implementation("androidx.media3:media3-exoplayer-rtsp:$media3_version")
    implementation("androidx.media3:media3-datasource-cronet:$media3_version")
    implementation("androidx.media3:media3-datasource-okhttp:$media3_version")
    implementation("androidx.media3:media3-datasource-rtmp:$media3_version")
    implementation("androidx.media3:media3-ui:$media3_version")
    implementation("androidx.media3:media3-session:$media3_version")

    // Exoplayer
    implementation("com.google.android.exoplayer:exoplayer:$exoplayer_version")
    implementation("com.google.android.exoplayer:exoplayer-core:$exoplayer_version")
    implementation("com.google.android.exoplayer:exoplayer-ui:$exoplayer_version")
    implementation("com.google.android.exoplayer:exoplayer-dash:$exoplayer_version")
    implementation("com.google.android.exoplayer:exoplayer-hls:$exoplayer_version")
    implementation("com.google.android.exoplayer:exoplayer-smoothstreaming:$exoplayer_version")
    implementation("com.google.android.exoplayer:exoplayer-rtsp:$exoplayer_version")
    implementation("com.google.android.exoplayer:exoplayer-database:$exoplayer_version")
    implementation("com.google.android.exoplayer:exoplayer-datasource:$exoplayer_version")
    implementation("com.google.android.exoplayer:exoplayer-decoder:$exoplayer_version")
    implementation("com.google.android.exoplayer:exoplayer-extractor:$exoplayer_version")



    // Player-Sigma - remove from TDM V3
//    implementation "com.sigma.packer:sigma-packer:1.0.2"

    // BarcodeScanner ZXing
    implementation "me.dm7.barcodescanner:zxing:$barcodescanner_zxing"

    // Lottie
    implementation "com.airbnb.android:lottie:$lottie_version"

    // Facebook
    implementation "com.facebook.android:facebook-login:$facebook_login_version"
    // App link
    implementation "com.parse.bolts:bolts-tasks:$applink_version"
    implementation "com.parse.bolts:bolts-applinks:$applink_version"

    //region Dependencies for core module
    implementation "com.squareup.retrofit2:retrofit:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-moshi:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-scalars:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-gson:$retrofit_version"
    implementation "com.squareup.okhttp3:logging-interceptor:$okhttp_logging_version"
    // Moshi
    implementation "com.squareup.moshi:moshi:$moshi_version"
    implementation "com.squareup.moshi:moshi-kotlin:$moshi_version"
    kapt "com.squareup.moshi:moshi-kotlin-codegen:$moshi_version"
    // Room
    implementation "androidx.room:room-runtime:$room_version"
    kapt "androidx.room:room-compiler:$room_version"
    implementation "androidx.room:room-ktx:$room_version"
    // optional - Kotlin Extensions and Coroutines support for Room

    // Swipe to refresh
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:$swipe_refresh_version"

    implementation "com.google.android.flexbox:flexbox:$flexbox_version"

    // Leanback for player aar
    implementation "androidx.leanback:leanback:$leanback_version"

    //gson
    implementation "com.google.android.gms:play-services-auth:20.1.0"

    // Cast
    implementation "androidx.mediarouter:mediarouter:${media_router}"
    implementation "com.google.android.gms:play-services-cast-framework:${cast_framework}"

    // Socket IO
    implementation('io.socket:socket.io-client:1.0.1') {
        exclude group: 'org.json', module: 'json'
    }

    //Retrofit
    implementation "com.squareup.retrofit2:retrofit:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-gson:$retrofit_version"
    implementation "com.squareup.okhttp3:logging-interceptor:$okhttp_logging_version"

    //Firebase
    implementation "com.google.firebase:firebase-core:$firebase_core_version"
    implementation "com.google.firebase:firebase-firestore:$firestore"

    //Clevertap
    implementation "com.clevertap.android:push-templates:$clevertap_templates_version"
    implementation "com.clevertap.android:clevertap-android-sdk:$clevertap_version"
    implementation "com.android.installreferrer:installreferrer:$install_referrer_version"
    //AppFlyer
    implementation "com.appsflyer:af-android-sdk:$app_flyer_version"

    // Import the BoM for the Firebase platform
    implementation platform("com.google.firebase:firebase-bom:$firebase_bom")

    // Declare the dependencies for the Crashlytics and Analytics libraries
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-messaging'
    implementation 'com.google.firebase:firebase-dynamic-links-ktx'
    implementation 'com.google.firebase:firebase-firestore-ktx'

    // Ads
    implementation "com.google.ads.interactivemedia.v3:interactivemedia:3.31.0"

    // Zendesk
    implementation group: 'com.zendesk', name: 'support', version: "$zendesk_version"

    // Lottie
    implementation "com.airbnb.android:lottie:$lottieVersion"

    // Number picker
    implementation "io.github.ShawnLin013:number-picker:$numberPickerVersion"


    //foxpay
    implementation "com.ftel.foxpay:foxsdkv2:1.2.9"

    implementation 'com.google.android.gms:play-services-auth:20.4.0'
    implementation 'com.google.android.gms:play-services-auth-api-phone:18.0.1'
    implementation 'com.google.android.gms:play-services-maps:18.1.0'

    //FileUtils
    implementation 'org.apache.directory.studio:org.apache.commons.io:2.4'

    // CameraX core library using camera2 implementation
    implementation "androidx.camera:camera-camera2:$camerax_version"

    // CameraX Lifecycle Library
    implementation "androidx.camera:camera-lifecycle:$camerax_version"

    // CameraX View class
    implementation "androidx.camera:camera-view:1.0.0-alpha27"

    //app auth
    implementation 'net.openid:appauth:0.11.1'

    // shimmer placeholder
    implementation 'com.facebook.shimmer:shimmer:0.5.0@aar'

    implementation ('com.github.danganhhao:Connect-SDK:1.0.0'){
        exclude group: "javax.jmdns", module: "jmdns"
    }

    //Adjust
    implementation 'com.adjust.sdk:adjust-android:5.2.0'
    implementation 'com.android.installreferrer:installreferrer:2.2'
    // Add the following if you are using the Adjust SDK inside web views on your app
    implementation 'com.adjust.sdk:adjust-android-webbridge:5.2.0'

    implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'

    //end Adjust

    implementation "com.google.android.play:integrity:$play_integrity_version"

    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.5'

}

apply plugin: 'com.google.gms.google-services'

task buildAllApp(dependsOn: ['assembleNormalRelease', 'assembleDevRelease', 'assembleDevDebug', 'assembleBillingDebug']) { task ->
    doLast {
        ext.apk_dev = file("build/outputs/apk/dev/debug/${MetaData.apk_name}-dev-debug.apk")
        ext.apk_dev_release = file("build/outputs/apk/dev/release/${MetaData.apk_name}-dev-release.apk")
        ext.apk_product = file("build/outputs/apk/normal/release/${MetaData.apk_name}-normal-release.apk")
        ext.apk_billing = file("build/outputs/apk/billing/debug/${MetaData.apk_name}-billing-debug.apk")
        def destination = System.getProperty("user.home") + "/Desktop/${MetaData.apk_name}"

        if (ext.apk_dev.exists()) {
            copy {
                from ext.apk_dev.absolutePath
                into destination
            }
        }

        if (ext.apk_dev_release.exists()) {
            copy {
                from ext.apk_dev_release.absolutePath
                into destination
            }
        }

        if (ext.apk_product.exists()) {
            copy {
                from ext.apk_product.absolutePath
                into destination
            }
        }

        if (ext.apk_billing.exists()) {
            copy {
                from ext.apk_billing.absolutePath
                into destination
            }
        }

        //Telegram is down in Vietnam
//        def destinationFolder = file(destination)
//        if(destinationFolder.exists()) {
//            def osName = System.getProperty('os.name').toLowerCase(Locale.ROOT)
//            def gradleCommand = osName.contains('windows') ? 'gradlew.bat' : './gradlew'
//            exec {
//                workingDir "$projectDir/.." // Ensure the correct working directory
//                commandLine gradleCommand, 'runShellScript'
//            }
//        }
    }
}

task runShellScript(type: Exec) {
    description = 'Runs a shell script'
    group = 'Custom Tasks'

    // Path to your shell script
    def scriptPath = System.getProperty("user.home") + "/Desktop/bot/telegram/send_telegram.sh"
    def messageToSend = "Build success app version: $MetaData.apk_name"

    // Ensure the script is executable
    doFirst {
        file(scriptPath).setExecutable(true)
    }

    // Command to execute
    commandLine 'bash', scriptPath, messageToSend
}