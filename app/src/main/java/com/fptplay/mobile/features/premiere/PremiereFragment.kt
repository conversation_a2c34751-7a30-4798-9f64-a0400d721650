package com.fptplay.mobile.features.premiere

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.widget.Toast
import androidx.annotation.UiThread
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.android.billingclient.api.BillingClient
import com.fptplay.dial.connection.models.transfer_model.ActionEventType
import com.fptplay.dial.model.FAndroidTVDeviceInfo
import com.fptplay.dial.model.FAndroidTVDeviceInfoExternal
import com.fptplay.dial.model.FBoxDeviceInfoV2
import com.fptplay.dial.model.FSamsungTVDeviceInfo
import com.fptplay.dial.model.FSamsungTVDeviceInfoExternal
import com.fptplay.mobile.HomeActivity
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.NavPremiereDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.onClick
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.extensions.showSnackBar
import com.fptplay.mobile.common.global.SourceRemoveObject
import com.fptplay.mobile.common.ui.bases.BaseActivity
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.ui.view.GlobalSnackbarManager
import com.fptplay.mobile.common.ui.view.SnackbarManager
import com.fptplay.mobile.common.ui.view.TooltipsCallback
import com.fptplay.mobile.common.ui.view.TooltipsView
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.DateTimeUtils
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.StringUtils
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.common.utils.firestore.EventIdFirestore
import com.fptplay.mobile.databinding.OnOffVotingViewLayoutBinding
import com.fptplay.mobile.databinding.PremiereFragmentBinding
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.adjust.Source
import com.fptplay.mobile.features.adjust.SourcePage
import com.fptplay.mobile.features.adjust.WatchType
import com.fptplay.mobile.features.game_30s.view.OnOffVotingView
import com.fptplay.mobile.features.game_30s.vote.entites.VoteEntities
import com.fptplay.mobile.features.livetv_detail.LiveTVDetailFragment
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.features.pairing_control.PairingControlConnectionHelper
import com.fptplay.mobile.features.pairing_control.model.RemotePlayerState
import com.fptplay.mobile.features.payment.PaymentViewModel
import com.fptplay.mobile.features.payment.google_billing.BillingClientLifecycleV6
import com.fptplay.mobile.features.payment.google_billing.BillingUtils
import com.fptplay.mobile.features.payment.util.PaymentTrackingUtil
import com.fptplay.mobile.features.premiere.data.PremiereLiveState
import com.fptplay.mobile.features.premiere.data.PremiereTabItem
import com.fptplay.mobile.features.premiere.data.PremiereTabRequest
import com.fptplay.mobile.features.premiere.live_chat.LiveChatV2ViewModel
import com.fptplay.mobile.features.sport_interactive.utils.SportInteractiveUtils.checkShowSportInteractive
import com.fptplay.mobile.player.PlayerUtils
import com.fptplay.mobile.player.PlayerView
import com.fptplay.mobile.features.tracking_ga4.TrackingGA4Proxy
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.services.FirestoreDatabaseService
import com.fptplay.mobile.services.IEventListenerFirestore
import com.tear.modules.player.util.IPlayer
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.RequiredVip
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItem
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.StructureItemContainer
import com.xhbadxx.projects.module.domain.entity.fplay.payment.PackagePlan
import com.xhbadxx.projects.module.domain.entity.fplay.premier.Details
import com.xhbadxx.projects.module.util.common.Util
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class PremiereFragment : BaseFragment<PremiereViewModel.PremiereState, PremiereViewModel.PremiereIntent>() {
    private var _binding: PremiereFragmentBinding? = null
    private val binding get() = _binding!!

    override val viewModel: PremiereViewModel by activityViewModels()

    private val paymentViewModel by activityViewModels<PaymentViewModel>()
    private val eventIdFirestore by lazy { EventIdFirestore() }

    private val liveChatV2ViewModel by activityViewModels<LiveChatV2ViewModel>()
    private val safeArgs: PremiereFragmentArgs by navArgs()

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    // Google Billing
    private var alertDialog: AlertDialog? = null

    private var billingClientLifecycle: BillingClientLifecycleV6? = null

    private var selectedPlan: PackagePlan? = null
    //

    //
    private var isOpenTabChat = false
    //

    private var countDownTimerEvent : CountDownTimer?= null
    private var handlerCheckCountDownTimer : Handler?= null
    private var runnableCheckCountDownTimer = Runnable {
        when (getEventType()) {
            PremiereViewModel.EventType.EVENT -> {
                updateCountDownTimer()
            }
            PremiereViewModel.EventType.EVENT_TV -> {
                updateCountDownTimerEventTV()
            }
        }
    }

    //region Event State
    private var currentEventState = -1
    private var liveState : PremiereLiveState = PremiereLiveState.ComingSoon
    //endregion

    //region game
    private val fireStoreServices: FirestoreDatabaseService by lazy { FirestoreDatabaseService() }
    private var votePopUpViewBinding: OnOffVotingViewLayoutBinding? = null
    private var voteRankingViewBinding: OnOffVotingViewLayoutBinding? = null

    private var voteEntities: VoteEntities? = null
    private var isShowVoting:Boolean = false
    private var isShowRanking:Boolean = false
    private val tooltipsView by lazy { TooltipsView(requireContext()) }

    // endregion game

    //region preview
    private var buttonTypeBelowPlayer: String = ""
    private var isShowRequiredLogin: Boolean = true
    //endregion preview

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.savePremiereId(premiereId = "")
        viewModel.saveDataDetail(data = null)
        viewModel.saveFollow(isFollow = false)
        viewModel.saveFirstTimePlay(isFirstTimePlay = true)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = PremiereFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        eventIdFirestore.stopListenEvent()
        hideVotingPopUpView(destroy = true)
        hideRankingPopUpView(destroy = true)
        fireStoreServices.stopListenFireStore()
        votePopUpViewBinding = null
        voteRankingViewBinding = null
        TrackingUtil.idRelated = ""
        _binding = null

        // Pairing Control
        MainApplication.INSTANCE.pairingConnectionHelper.removePlayerStateListener(listener = remotePlayerState)

        // Tooltips
        viewLifecycleOwner.lifecycle.removeObserver(tooltipsView)
        MainApplication.INSTANCE.sharedPreferences.setShouldShowTooltip(Constants.TOOLTIP_SPORT_INTERACTIVE, false)
        MainApplication.INSTANCE.sharedPreferences.setShouldShowTooltip(Constants.TOOLTIP_SETTING_PLAYER, shouldShow = false)
        setHideToolTipSportInteractive()
        //

        // Chat MiniApp view
        liveChatV2ViewModel.clearMiniApp()

        super.onDestroyView()
    }

    override fun onDestroy() {
        removeAllCountDown()
        super.onDestroy()
    }
    override fun initData() {
        val premiereId = viewModel.getPremiereId().ifBlank { safeArgs.idToPlay }
        lifecycleScope.launch(Dispatchers.Main) {
            viewModel.saveClickTimeToPlay(System.currentTimeMillis())
            viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.GetDetail(premiereId = premiereId))
            viewModel.savePremiereId(premiereId = premiereId)
        }
        viewModel.triggerInitPlayer()
    }
    override fun bindComponent() {
        viewModel.saveEventEndTime(endTime = 0L)
        initGoogleBilling()

        // Game
        fireStoreServices.openPanelVoting()
        fireStoreServices.callbackEventFirestore(object : IEventListenerFirestore {
            override fun onPlayerEvent(dataJson: String) {
                if (dataJson.isNotBlank()) {
                    viewModel.dispatchIntent(
                        PremiereViewModel.PremiereIntent.TriggerOpenVotingPopUp(
                            dataJson, voteEntities
                        )
                    )
                }
            }
            override fun openPanelVoting(vote: VoteEntities?) {
                voteEntities = vote
               // if(vote?.openPanelVoting==true){
                   if(vote?.openPanelVoting==true && vote.idEvent == viewModel.getPremiereId().ifBlank { safeArgs.idToPlay }){
                    fireStoreServices.getPlayer30sList(vote.dataCollection)
                }
            }
        })
        // Game

        binding.ivChat.isEnabled = false

    }

    override fun bindEvent() {
        observeData()
        bindEventForBuyPackage()
        // Tablet
        if (context.isTablet()) {
            binding.tlTitle.onClickDelay { openPremiereDescription() }
            binding.vDownTitle.onClickDelay { openPremiereDescription() }
            binding.llFollow.onClickDelay {
                getDetails()?.let {
                    val state = Utils.getTimeLiveState(beginTime = Utils.convertStringToLong(it.beginTime, 0L), endTime = Utils.convertStringToLong(it.endTime, 0L))
                    if (state == 1) {
                        if (viewModel.isFollow()) {
                            viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.DeleteFollow(isPremiere = isPremiere(), id = getDetails()?.id ?:""))
                        } else {
                            viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.AddFollow(isPremiere = isPremiere(), id = getDetails()?.id ?:""))
                        }
                    }
                }
            }
            binding.llShare.onClick(delayBetweenClick = if(context.isTablet()) 500L else 200L){ onShareLink(getDetails()?.websiteUrl?:"") }
            binding.llChat.onClickDelay {
                openLiveChat()
            }

            //region game
             binding.llRankGameTablet.onClickDelay {
                 if(isDashboard()){
                     viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.GetViewRankingCommon(gameId = gameId()))
                 }
             }
            // endregion game
            binding.buttonSport.llMatchData.onClickDelay {
                if (viewModel.getDataDetail()?.isInteractiveSports == true) {
                    if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.premiere_content_fragment) {
                        binding.navHostFragment.findNavController().navigateSafe(PremiereContentFragmentDirections.actionPremiereContentFragmentToSportInteractiveV2Fragment(idToPlay = viewModel.getPremiereId()))
                    } else {
                        if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.premiere_chat_fragment) {
                            binding.navHostFragment.findNavController().popBackStack()
                            binding.navHostFragment.findNavController().navigateSafe(PremiereContentFragmentDirections.actionPremiereContentFragmentToSportInteractiveV2Fragment(idToPlay = viewModel.getPremiereId()))
                        }else if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.sport_interactive_v2_fragment) {
                            binding.navHostFragment.findNavController().popBackStack()
                        }
                    }
                }
                setHideToolTipSportInteractive()
            }
            updateFollowButton()
        }
        setFragmentResultListener(Constants.LOGIN_SUCCESS) { _, bundle ->
            val isSuccess = bundle.getBoolean(Constants.LOGIN_SUCCESS_KEY, false)
            if (isSuccess) {
                currentEventState = -1
                // Trigger Login Success
                triggerIntent(PremiereViewModel.PremiereIntent.TriggerLoginSuccess)
                //

                if (viewModel.getPremiereId().isNotBlank()) {
                    viewModel.saveClickTimeToPlay(System.currentTimeMillis())
                    triggerIntent(PremiereViewModel.PremiereIntent.GetDetail(premiereId = viewModel.getPremiereId()))
                }

                val extensionBundle = bundle.getBundle(Constants.EXTENDS_ARG_NAVIGATE_LOGIN_KEY)?:Bundle()
                // case : check follow after login success
                val isHandleFollow = extensionBundle.getBoolean(Constants.PREMIERE_FOLLOW_NAVIGATE_LOGIN_REQUEST_KEY,false)
                val valueFollow = extensionBundle.getBoolean(Constants.PREMIERE_FOLLOW_NAVIGATE_LOGIN_VALUE,false)
                if (isHandleFollow) {
                    actionEventFollowNext = ActionEventNext(type = ACTION_FOLLOW, eventId = viewModel.getId(), value = valueFollow)
                }
                //
                handleActionAfterLogin(extensionBundle)
            }
        }
        //

        // Pairing Control
        MainApplication.INSTANCE.pairingConnectionHelper.addPlayerStateListener(listener = remotePlayerState)
        //

        setFragmentResultListener(Constants.SPORT_INTERACTIVE_OPEN_REQUEST) {_, requestBundle ->
            Timber.d("--------Open sport interactive")
            findNavController().navigateSafe(
                PremiereFragmentDirections.actionPremiereFragmentToSportInteractiveFullScreenV2DialogFragment(
                    idToPlay = requestBundle.getString(Constants.SPORT_INTERACTIVE_OPEN_REQUEST_VALUE) ?: "",
                    paddingStart = requestBundle.getInt(Constants.SPORT_INTERACTIVE_OPEN_REQUEST_PADDING_START, 0),
                    paddingEnd = requestBundle.getInt(Constants.SPORT_INTERACTIVE_OPEN_REQUEST_PADDING_END, 0),
                    rotation = requestBundle.getInt(Constants.SPORT_INTERACTIVE_OPEN_REQUEST_ROTATION, 0)
                )
            )
        }

        Logger.d("trangtest setFragmentResultListener(Constants.SPORT_INTERACTIVE_LOG_KIBANA) ")
        setFragmentResultListener(Constants.SPORT_INTERACTIVE_LOG_KIBANA) { _, bundle ->
            val screen = bundle.getString(Constants.SPORT_INTERACTIVE_LOG_SCREEN, "")
            val event = bundle.getString(Constants.SPORT_INTERACTIVE_LOG_EVENT, "")
            val itemName = bundle.getString(Constants.SPORT_INTERACTIVE_LOG_ITEM_NAME, null)
            logKibana601(screen, event, itemName)
        }
        setFragmentResultListener(Constants.SPORT_INTERACTIVE_NAVIGATE_REQUEST_KEY) { _, bundle ->
            Timber.tag("tam-sport").d("SPORT_INTERACTIVE_NAVIGATE_REQUEST ${this.javaClass.simpleName}")
            val contentId = bundle.getString(Constants.SPORT_INTERACTIVE_NAVIGATE_REQUEST_CONTENT_ID, "")
            val itemTypeString = bundle.getString(Constants.SPORT_INTERACTIVE_NAVIGATE_REQUEST_ITEM_TYPE, "")
            val itemType = ItemType.valueOf(itemTypeString)
            Timber.tag("tam-sport").d("$contentId $itemTypeString $itemType")
            if (itemType == ItemType.Event || itemType == ItemType.EventTV) {
                viewModel.triggerPlayPremiere(id = contentId)
            } else {
                checkBeforePlayUtil.navigateToSelectedContent(
                    data = StructureItem(
                        id = contentId,
                        highlightId = contentId,
                        itype = itemType
                    )

                )
            }
        }

        setFragmentResultListener(Utils.DEEPLINK_NOT_SUPPORTED_CONFIRM_EVENT) { _, _ ->
            val navController = (activity as? HomeActivity)?.navHostFragment?.navController
            val navigation = if(navController?.graph?.id == R.id.nav_home_main) { navController } else { null }
            navigation?.navigate(NavHomeMainDirections.actionGlobalToHomeMainFragment())

        }
    }

    private fun openLiveChat() {
        if (binding.ivChat.isEnabled) {
            if (sharedPreferences.userLogin()) {
                if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.premiere_content_fragment) {
                    navigateToLiveChatV2()
                } else {
                    if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.sport_interactive_v2_fragment) {
                        binding.navHostFragment.findNavController().popBackStack()
                        navigateToLiveChatV2()
                    } else if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.premiere_chat_fragment) {
                        binding.navHostFragment.findNavController().popBackStack()
                    }
                }
            } else {
                val extras = Bundle()
                extras.putString(Constants.ACTION_AFTER_LOGIN_KEY, Constants.ACTION_OPEN_CHAT)
                parentFragment?.navigateToLoginWithParams(extendsArgs = extras)
            }
        }
    }

    private fun handleActionAfterLogin(extensionBundle: Bundle) {
        val actionAfterLogin = extensionBundle.getString(Constants.ACTION_AFTER_LOGIN_KEY)
        when(actionAfterLogin) {
            Constants.ACTION_OPEN_CHAT -> {
                openLiveChat()
            }
        }
    }

    private fun bindEventForBuyPackage() {
        binding.layoutBuyPackageDetail.btnBuyPackage.onClickDelay {
            when(buttonTypeBelowPlayer) {
                LiveTVDetailFragment.LOGIN_TYPE -> {
                    parentFragment?.navigateToLoginWithParams(isDirect = true)
                }
                LiveTVDetailFragment.BUY_PACKAGE_TYPE -> {
                    viewModel.getVipRequired()?.let {
                        if (it.second?.isTvod == true) {
                            paymentViewModel.dispatchIntent(PaymentViewModel.PaymentViewIntent.GetPackagePlan(it.second?.requireVipPlan ?:"", BillingUtils.isEnableGoogleBilling(), PaymentViewModel.FromSource.PLAY.rawValue))
                        } else {
                            if (it.second?.enablePreview == true) {
                                viewModel.triggerBuyPackageForPreview(showRequirePackage = false)
                            } else {
                                parentFragment?.findNavController()?.navigate(
                                    NavHomeMainDirections.actionGlobalToPaymentDetail(packageType = it.second?.requireVipPlan ?:"", continueWatch = true))
                                AdjustAllEvent.sendPackageRecommendClickEvent(packageId = it.second?.requireVipPlan ?:"", packageName = it.second?.requireVipName ?:"")
                                TrackingGA4Proxy.saveTrackingTypeClickAndContentName(TrackingGA4Proxy.TypeCallDetail.button, binding.tvTitle.text.toString())
                            }
                        }
                    }
                }
            }
        }
    }

    private fun sendTrackingLogAlarm(status:Boolean){
        val event = if(status) Constants.EVENT_LOG_ADD_ALARM else Constants.EVENT_LOG_REMOVE_ALARM
        viewModel.getDataDetail()?.let {
            val info = InforMobile(
                infor = trackingInfo,
                logId = "174",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = event,
                event = event,
                itemId = it.id,
                chapterId = it.episodeId,
                EpisodeID = viewModel.getPremiereId(),
                itemName = it.title,
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                isLive = TrackingUtil.isLive,
                price = "0",
                subMenuId = TrackingUtil.blockId,
                businessPlan = getDetails()?.payment?.requireVipPlan ?: "",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                isRecommend = TrackingUtil.isRecommend
            )
            trackingProxy.sendEvent(info)
            Logger.d("trangtest === sendevent === $info")
        }
    }
    private fun logKibana601(screen:String, event:String, itemName:String? = null){
        viewModel.getDataDetail()?.let {
            val info = InforMobile(
                infor = trackingInfo,
                logId = "601",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = screen,
                event = event,
                chapterId = it.episodeId,
                EpisodeID = viewModel.getPremiereId(),
                itemId = it.id,
                itemName = itemName?: it.title,
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                startTime = TrackingUtil.startTime,
                isLive = TrackingUtil.isLive,
                blocKPosition = TrackingUtil.blockIndex,
                price = "0",
                subMenuId = TrackingUtil.blockId,
                videoQuality = viewModel.getBitrateId(),
                businessPlan = it.payment.requireVipPlan,
                isLinkDRM = it.isDrm.toString(),
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                isRecommend = TrackingUtil.isRecommend
            )
            trackingProxy.sendEvent(info)
            Logger.d("trangtest === sendevent === $info")
        }


    }

    //region actionNext
    private var actionEventFollowNext:ActionEventNext? = null

    data class ActionEventNext(val type:String= "", val eventId:String="", val value:Boolean=false)
    private fun handleClickActionNext(actionNext :ActionEventNext?){
        actionNext?.let {
            if(actionNext.eventId == viewModel.getId()){
                when(actionNext.type){
                    ACTION_FOLLOW ->{
                        actionEventFollowNext = null
                        handleFollowNavigateLogin(actionNext.value)
                    }
                }
            }
        }
    }
    //endregion actionNext
    private fun handleFollowNavigateLogin(shouldFollow:Boolean){
        getDetails()?.let {
            val state = Utils.getTimeLiveState(beginTime = Utils.convertStringToLong(it.beginTime, 0L), endTime = Utils.convertStringToLong(it.endTime, 0L))
            if (state == 1) {
                if (shouldFollow) {
                    viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.AddFollow(isPremiere = isPremiere(), id = getDetails()?.id ?:""))
                } else {
                    viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.DeleteFollow(isPremiere = isPremiere(), id = getDetails()?.id ?:""))
                }
            }
        }
    }
    private fun observeData() {
        viewModel.playPremiere.observe(viewLifecycleOwner) {
            it?.run {
                lifecycleScope.launch(Dispatchers.Main) {
                    viewModel.saveClickTimeToPlay(System.currentTimeMillis())
                    viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.GetDetail(premiereId = it))
                    viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.TriggerStopSportInteractive)
                }
            }
        }
        viewModel.isFullScreen.observe(viewLifecycleOwner) {
            it?.run {
                // isFullscreen, isLandscape, isPlayerScaled
                if (context.isTablet()) {
                    Logger.d("trangtest === viewModel.isFullScreen.observe isLandscapeMode = ${it.second}")
                    handleTabletLayout(isFullscreen = it.first, isLandscapeMode = it.second)
                    binding.navHostFragment.isVisible = !it.first || it.third
                    if (!it.second) {
                        adjustPlayerConstraint(isScale = false)
                    }
                    // game
                    if(votePopUpViewBinding?.ctlVotingPopupView?.visibility == View.VISIBLE){
                        adjustVotingViewConstraintTabletLayout(
                            it.second,
                            voteEntities?.isScale ?: false
                        )
                    }
                    if(voteRankingViewBinding?.ctlVotingPopupView?.visibility == View.VISIBLE){
                        adjustRankingViewConstraintTabletLayout(
                            it.second,
                            voteEntities?.isScale ?: false
                        )
                    }
                    // endgame
                    checkShowToolTip(it.second)
                } else {
                    binding.navHostFragment.isVisible = !it.first || it.third
                    if (!it.first) {
                        isOpenTabChat = false
                        adjustPlayerConstraint(isScale = false)
                        if (viewModel.getDataDetail()?.commentType == Constants.CHAT_EVENT_REAL_TIME_TYPE) {
                            adjustPremiereContentConstraint(isScale = false)
                        }
                    }
                    if (it.first && getDetails()?.commentType == Constants.CHAT_EVENT_REAL_TIME_TYPE) {
                        viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.TriggerOpenLiveChatFullScreen(isOpen = isOpenTabChat))
                    }
                    // game
                    if (votePopUpViewBinding?.ctlVotingPopupView?.visibility == View.VISIBLE) {
                        /**
                         *  for case: clicke icon comment and showing votingPopUpView
                         */
                        adjustVotingViewConstraint(voteEntities?.isScale ?: true)
                    }
                    if (voteRankingViewBinding?.ctlVotingPopupView?.visibility == View.VISIBLE) {
                        /**
                         *  for case: clicke icon comment and showing votingPopUpView
                         */
                        adjustRankingViewConstraint(voteEntities?.isScale ?: true)
                    }
                    // end game
                }
                handlePositionSnackBar(isFullscreen = it.first, isLandscape = it.second)
            }
        }
//        viewModel.playPremiere.observe(viewLifecycleOwner) {
//            it?.run {
//                removeAllCountDown()
//            }
//        }
        handlePaymentState()
    }
    // region Snackbar
    private fun handlePositionSnackBar(isFullscreen: Boolean,isLandscape: Boolean){
        GlobalSnackbarManager.withCurrent()?.position =  if (isFullscreen) SnackbarManager.Position.TOP_CENTER else SnackbarManager.Position.BOTTOM_CENTER
    }

    private fun removeAllCountDown() {
        when (getEventType()) {
            PremiereViewModel.EventType.EVENT -> {
                removeCountDownTask()
                removeHandlerCheckCountDownTimer()
            }
            PremiereViewModel.EventType.EVENT_TV -> {
                removeCountDownTaskEventTV()
                removeHandlerCheckCountDownTimerEventTV()
            }
        }
    }

    private fun bindFirestore(eventId: String, isOverrideTime: Boolean = true) {
        if(eventIdFirestore.getEventId() != eventId) {
            eventIdFirestore.setListener(
                lifecycleOwner = viewLifecycleOwner,
                eventId = eventId,
            ) {
                it?.let {
                    try {
                        if(isOverrideTime) {
                            viewModel.saveEventEndTime(it.endTime.toLong())
                            if(it.endTime != "0" && it.endTime.isNotBlank())
                                getDetails()?.endTime = it.endTime
                            if(it.startTime != "0" && it.startTime.isNotBlank())
                                getDetails()?.beginTime = it.startTime
                        }

                        val newState = getDetails()?.let { premiere ->
                            Utils.getTimeLiveState(beginTime = Utils.convertStringToLong(premiere.beginTime, 0L), endTime = Utils.convertStringToLong(premiere.endTime, 0L))
                        } ?: kotlin.run { -1 }

                        if (currentEventState == 2 && newState == 2 && viewModel.getVipRequired()?.first == false) {
                            triggerIntent(PremiereViewModel.PremiereIntent.TriggerUpdateCheckEndTime)
                        } else {
                            when (getEventType()) {
                                PremiereViewModel.EventType.EVENT -> {
                                    updateCountDownTimer()
                                }
                                PremiereViewModel.EventType.EVENT_TV -> {
                                    updateCountDownTimerEventTV()
                                }
                            }
                        }

                        Timber.d("*****DataChange: $it")
                    } catch (ex: Exception) {
                        ex.printStackTrace()
                        when (getEventType()) {
                            PremiereViewModel.EventType.EVENT -> {
                                updateCountDownTimer()
                            }
                            PremiereViewModel.EventType.EVENT_TV -> {
                                updateCountDownTimerEventTV()
                            }
                        }
                    }
                } ?: kotlin.run {
                    when (getEventType()) {
                        PremiereViewModel.EventType.EVENT -> {
                            updateCountDownTimer()
                        }
                        PremiereViewModel.EventType.EVENT_TV -> {
                            updateCountDownTimerEventTV()
                        }
                    }
                }
            }
        } else {
            when (getEventType()) {
                PremiereViewModel.EventType.EVENT -> {
                    getDetails()?.let { it ->
                        val state = Utils.getTimeLiveState(beginTime = Utils.convertStringToLong(it.beginTime, 0L), endTime = Utils.convertStringToLong(it.endTime, 0L))
                        if (state == 2) {
                            // Notify
                            triggerIntent(PremiereViewModel.PremiereIntent.TriggerUpdatePlayState)
                            //
                            // Get stream for play
                            triggerIntent(PremiereViewModel.PremiereIntent.TriggerGetStream(requireCheckPackage = false))
                        } else {
                            updateCountDownTimer()
                        }
                    }
                }
                PremiereViewModel.EventType.EVENT_TV -> {
                    getDetails()?.let {
                        updateCountDownTimerEventTV()
                    }
                }
            }
        }
    }

    override fun PremiereViewModel.PremiereState.toUI() {
        when (this) {
            is PremiereViewModel.PremiereState.OpenLiveChat -> {
                navigateToLiveChatV2()
            }
            is PremiereViewModel.PremiereState.ResultTriggerPlayerLayout -> {
                adjustPlayerConstraint(isScale = this.isScale)
            }
            is PremiereViewModel.PremiereState.ResultTriggerOpenLiveChatFullScreen -> {
                if (viewModel.getDataDetail()?.commentType == Constants.CHAT_EVENT_REAL_TIME_TYPE) {
                    adjustPremiereContentConstraint(isScale = this.isOpen)
                    isOpenTabChat = this.isOpen
                    viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.TriggerPlayerLayout(isScale = isOpen))

                    //
                    if (binding.ivChat.isEnabled) {
                        if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.premiere_content_fragment) {
                            triggerIntent(PremiereViewModel.PremiereIntent.ReflectionOpenLiveChat(isOpen = this.isOpen))
                        } else {
                            if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.sport_interactive_v2_fragment) {
                                binding.navHostFragment.findNavController().popBackStack()
                                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionOpenLiveChat(isOpen = this.isOpen))
                            }
                        }
                    }
                }
            }

            is PremiereViewModel.PremiereState.ResultTriggerMulticam -> {
                if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.premiere_chat_fragment) {
                    binding.navHostFragment.findNavController().popBackStack()
                }
                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionMulticamUI)
            }
            is PremiereViewModel.PremiereState.ResultTriggerEndTime -> {
                val text = <EMAIL>(R.string.premiere_end)
                liveState = PremiereLiveState.End
                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionUpdateLiveState(text = text, PremiereLiveState.End))
                SourceRemoveObject.setIdToRemove(viewModel.getPremiereId())

                // Tablet
                if (context.isTablet()) {
                    binding.tvStartTime.text = text
                }
            }
            is PremiereViewModel.PremiereState.ResultDetail -> {
                Timber.d("*****Save result detail: ${this.data}")
                //region save data for log Adjust
                data?.let {
                    AdjustAllEvent.dataCur.contentId = data.id
                    AdjustAllEvent.dataCur.contentName = data.title
                    AdjustAllEvent.dataCur.contentType = "event"
                    AdjustAllEvent.dataCur.genre = ""
                    AdjustAllEvent.dataCur.watchType = if (data.feeType == "1") WatchType.paid else WatchType.free
                    AdjustAllEvent.dataCur.sourcePage = SourcePage.detail_module
                    AdjustAllEvent.dataCur.source = Source.package_recommend
                }
                //endregion
                removeAllCountDown()
                if (intent is PremiereViewModel.PremiereIntent.GetDetail) {
                    if (viewModel.getPremiereId() != intent.premiereId) {
                        viewModel.saveFirstTimePlay(isFirstTimePlay = true)
                        triggerIntent(PremiereViewModel.PremiereIntent.TriggerStopPlayer)
                        triggerIntent(PremiereViewModel.PremiereIntent.TriggerUnlockPlayerControl)
                        currentEventState = -1
                        isShowRequiredLogin = true
                    }
                    viewModel.savePremiereId(premiereId = intent.premiereId)
                }

                viewModel.saveId(id = data?.id ?: "")
                //viewModel.saveDataDetail(data = this.data)
                viewModel.saveEventEndTime(endTime = Utils.convertStringToLong(this.data?.endTime, 0L))
                viewModel.saveVipRequired(false)
                viewModel.saveEventType(value = data?.type?: "")
                getBlockItems(data = this.data)
                checkFollow()


                if(MultiProfileUtils.isCurrentProfileKid(sharedPreferences) && data?.isKid != true) {
                    val navController = (activity as? HomeActivity)?.navHostFragment?.navController
                    val navigation = if(navController?.graph?.id == R.id.nav_home_main) { navController } else { null }
                    navigation?.navigate(NavHomeMainDirections.actionGlobalToDeeplinkNotSupportedDialog(
                        titlePositive = getString(R.string.multi_profile_kid_not_supported_content_title_confirm)
                    ))
                    return

                }
                // Firestore
                bindFirestore(eventId = viewModel.getPremiereId().ifBlank { safeArgs.idToPlay }, isOverrideTime = data?.isWhitelist == false) // Firestore
                //
                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionDetailUI)
                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionFollowButtonUI)

                // Tablet
                updateDetailUI(premiere = data)
                updateFollowButton()

                // Logic -> EventTV
                if (getEventType() == PremiereViewModel.EventType.EVENT_TV) {
                    updateEventTrackingInfo(Utils.convertStringToLong(this.data?.beginTime, 0L))

                    if (this.data?.id.isNullOrEmpty()) {
                        retryGetDetail(message = getString(R.string.not_have_data), textNegative = getString(R.string.all_exit), textPositive = getString(R.string.all_retry))
                    }

                    // Get stream for play
                    triggerIntent(PremiereViewModel.PremiereIntent.TriggerGetStream(requireCheckPackage = false))
                }
                //
            }

            is PremiereViewModel.PremiereState.ResultAllBlockItems -> {
                val tabData = arrayListOf<PremiereTabItem>()
                this.data?.forEach { item ->
                    if (item is PremiereViewModel.PremiereState.ResultBlockItems) {
                        item.data?.let {
                            if(it.listStructureItem.isNotEmpty()){
                                val listStructureItem = mutableListOf<StructureItem>()
                                it.listStructureItem.forEach { item -> if (item.highlightId != viewModel.getPremiereId()) { listStructureItem.add(item) } }
                                // case : add tab to list when list structure item not empty
                                if(listStructureItem.isNotEmpty()){
                                    tabData.add(PremiereTabItem(data = StructureItemContainer(meta = it.meta, listStructureItem = listStructureItem)))
                                }
                            }
                        }
                    }
                }
                viewModel.saveTabData(tabData = tabData)
                //
                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionTabsUI)
            }

            is PremiereViewModel.PremiereState.ResultCheckFollow -> {
                viewModel.saveFollow(isFollow = data.status == 1)
                //
                if (actionEventFollowNext != null) {
                    handleClickActionNext(actionEventFollowNext)
                } else {
                    triggerIntent(PremiereViewModel.PremiereIntent.ReflectionFollowButtonUI)
                    updateFollowButton()
                }
            }
            is PremiereViewModel.PremiereState.ResultAddFollow -> {
                val isFollow = data.status == 1
                viewModel.saveFollow(isFollow = isFollow)
                //
                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionFollowButtonUI)

                updateFollowButton()
                sendTrackingLogAlarm(isFollow)
                // Pairing control
                if (PlayerUtils.getPlayingType() == PlayerView.PlayingType.Cast) {
                    val pairingConnection = MainApplication.INSTANCE.pairingConnectionHelper
                    if (pairingConnection.isSessionRunning) {
                        pairingConnection.sendEventActionEvent(type = if (isFollow) ActionEventType.ADD_FOLLOW else ActionEventType.REMOVE_FOLLOW)
                    }
                }
                //
            }

            is PremiereViewModel.PremiereState.ResultDeleteFollow -> {
                val isFollow = data.status != 1
                viewModel.saveFollow(isFollow = isFollow)
                //
                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionFollowButtonUI)

                updateFollowButton()
                sendTrackingLogAlarm(isFollow)
                // Pairing control
                if (PlayerUtils.getPlayingType() == PlayerView.PlayingType.Cast) {
                    val pairingConnection = MainApplication.INSTANCE.pairingConnectionHelper
                    if (pairingConnection.isSessionRunning) {
                        pairingConnection.sendEventActionEvent(type = if (isFollow) ActionEventType.ADD_FOLLOW else ActionEventType.REMOVE_FOLLOW)
                    }
                }
                //
            }
            is PremiereViewModel.PremiereState.ErrorRequiredLogin -> {
                when (intent) {
                    is PremiereViewModel.PremiereIntent.GetDetail,
                    is PremiereViewModel.PremiereIntent.GetStream -> {
                        requiredLogin?.let {
                            if (isShowRequiredLogin || !it.enablePreview) {
                                parentFragment?.navigateToLoginWithParams(title = this.message)
                            }
                            when (getEventType()) {
                                PremiereViewModel.EventType.EVENT -> {
                                    isShowRequiredLogin = true
                                }
                                PremiereViewModel.EventType.EVENT_TV -> {
                                    if (it.enablePreview) {
                                        showViewRequestLogin(
                                            message = getTextRequestLoginChannel(binding.root.context),
                                            buttonTitle = binding.root.context.getString(R.string.login_title)
                                        )
                                        isShowRequiredLogin = false
                                    } else {
                                        isShowRequiredLogin = true
                                        hideViewBelowPlayer()
                                    }
                                    viewModel.triggerPlayRequiredVipTrailer(it.trailerUrl)
                                }
                            }
                        } ?: run {
                            hideViewBelowPlayer()
                        }
                    }
                    is PremiereViewModel.PremiereIntent.AddFollow,
                    is PremiereViewModel.PremiereIntent.DeleteFollow-> {
                        val extendsArgs:Bundle = bundleOf(Constants.PREMIERE_FOLLOW_NAVIGATE_LOGIN_REQUEST_KEY to true, Constants.PREMIERE_FOLLOW_NAVIGATE_LOGIN_VALUE to !viewModel.isFollow())
                        parentFragment?.navigateToLoginWithParams(title = this.message, extendsArgs = extendsArgs)
                    }
                    else -> {}
                }
            }
            is PremiereViewModel.PremiereState.ErrorRequiredVip -> {
                if (intent is PremiereViewModel.PremiereIntent.GetStream) {
                    viewModel.saveVipRequired(isRequired = true, requiredVip = requiredVip)
                    viewModel.getVipRequired()?.let {
                        if (it.first) {
                            it.second?.let { requiredVip ->
                                handleRequiredVipForStream(requiredVip)
                            }
                        } else hideViewBelowPlayer()
                    } ?: run {
                        hideViewBelowPlayer()
                    }
                }

            }
            is PremiereViewModel.PremiereState.ErrorItemNotFound -> {
                when (intent) {
                    is PremiereViewModel.PremiereIntent.GetDetail -> {
                        setFragmentResult(Constants.EVENT_END_TIME_REMOVE_ITEM_KEY, bundleOf(
                            Constants.EVENT_END_TIME_REMOVE_ITEM_VALUE to safeArgs.idToPlay
                        ))
                        //
                        showDialog(message = this.message) {
                            findNavController().popBackStack()
                        }
                        
                        hideLoading()
                    }

                    is PremiereViewModel.PremiereIntent.GetStream -> {

                        if (Utils.isEventStart(viewModel.getDataDetail()?.beginTime)) {
                            setFragmentResult(Constants.EVENT_END_TIME_REMOVE_ITEM_KEY, bundleOf(
                                Constants.EVENT_END_TIME_REMOVE_ITEM_VALUE to safeArgs.idToPlay
                            ))

                            showDialog(message = this.message) {
                                findNavController().popBackStack()
                            }
                        }

                        hideLoading()
                    }
                    else -> {}
                }
            }
            is PremiereViewModel.PremiereState.Error -> {
                when (intent) {
                    is PremiereViewModel.PremiereIntent.GetDetail -> {
                        when (getEventType()) {
                            PremiereViewModel.EventType.EVENT -> {
                                showDialog(message = this.message)
                            }
                            PremiereViewModel.EventType.EVENT_TV -> {
                                retryGetDetail(message = this.message, textNegative = getString(R.string.all_exit), textPositive = getString(R.string.all_retry))
                            }
                        }
                        hideLoading()
                    }
                    else -> {}
                }
            }
            is PremiereViewModel.PremiereState.ErrorNoInternet -> {
                showWarningDialog(message = this.message, textConfirm = <EMAIL>(R.string.understood))
            }
            is PremiereViewModel.PremiereState.ResultStream -> {
            }
            is PremiereViewModel.PremiereState.Done -> {
                when (intent) {
                    is PremiereViewModel.PremiereIntent.GetDetail -> hideLoading()
                    else -> {}
                }
            }
            // region game
            is PremiereViewModel.PremiereState.OpenVotingPopUp -> {
                if (vote != null) {
                    if (context.isTablet()) {
                        adjustVotingViewConstraintTabletLayout(
                            viewModel.isFullScreen.value?.second ?: false, vote.isScale
                        )
                    } else {
                        adjustVotingViewConstraint(vote.isScale)
                    }
                    startVotingView(jsonData = jsonData, vote = vote)
                }
            }
            is PremiereViewModel.PremiereState.ResultViewRankingCommon -> {
                //loadingWebView(binding,this.data.data.leaderBoardLink)
                if (context.isTablet()) {
                    adjustRankingViewConstraintTabletLayout(
                        viewModel.isFullScreen.value?.second ?: false, voteEntities?.isScale ?:false
                    )
                } else {
                    adjustRankingViewConstraint(voteEntities?.isScale ?:false)
                }
                startRankingView(this.data.data.leaderBoardLink)
            }
            // endregion game
            // region sport interactive
            is PremiereViewModel.PremiereState.ResultTriggerOpenTabDataFeelExistFullScreen -> {
                exitFullScreenAndShowSportInteractive(isFullscreen = viewModel.isFullScreen.value?.first?:false, isLandscapeMode = viewModel.isFullScreen.value?.second?:false)
            }
            is PremiereViewModel.PremiereState.StopSportInteractive->{
                triggerCloseSportInteractive()
            }
            // endregion sport interactive

            //region Report Player
            is PremiereViewModel.PremiereState.ResultTriggerMsgUserReport ->{
                if (context.isTablet()) {
                    showSnackbar(this.message)
                } else {
                    binding.clRoot.showSnackBar(this.message)
                }
            }
            //endregion
            is PremiereViewModel.PremiereState.ResultReflectionUpdateLiveState -> {
                if (_binding != null) {
                    updateLiveChatWhenLiveState(state)
                }
            }
            else -> {}
        }
    }

    private fun updateLiveChatWhenLiveState(state: PremiereLiveState) {
        when(state) {
            PremiereLiveState.ComingSoon -> {
                binding.ivChat.isEnabled = false
            }
            PremiereLiveState.CountDown,
            PremiereLiveState.Live,
            PremiereLiveState.End-> {
                binding.ivChat.isEnabled = true
            }
        }
    }

    //region Common
    private fun isPremiere() = viewModel.getDataDetail()?.isPremier == "1"

    private fun getDetails() = viewModel.getDataDetail()

    private fun getPlayerRequest() : IPlayer.Request? {
        return (childFragmentManager.findFragmentById(R.id.f_player) as? PremierePlayerFragment)?.request()
    }

    private fun isDashboard() = viewModel.getDataDetail()?.isDashboard == "1"
    private fun gameId() = viewModel.getPremiereId().ifBlank { safeArgs.idToPlay }
     // private fun gameId() = "639847761fd9fd001d7b453c"  // data test

    private fun getEventType() = viewModel.getEventType()
    //endregion

    //
    private fun handleAutoOpenLiveChat() {
        // If live chat enable -> redirect to live chat
        if(sharedPreferences.userLogin()) {
            if (viewModel.isFirstTimePlay()) {
                if (getDetails()?.commentType == Constants.CHAT_EVENT_REAL_TIME_TYPE  && (liveState == PremiereLiveState.CountDown ||  liveState == PremiereLiveState.Live)) {
                    viewModel.saveFirstTimePlay(isFirstTimePlay = false)
                    if(viewModel.isFullScreen.value?.first == false) {
                        viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.AutoOpenLiveChat)
                    }
                }
            }
        }
    }
    //

    //region CountDownTask
    private fun updateCountDownTimer() {
        getDetails()?.let {
            // Notify
            triggerIntent(PremiereViewModel.PremiereIntent.TriggerUpdatePlayState)
            //

            val state = Utils.getTimeLiveState(beginTime = Utils.convertStringToLong(it.beginTime, 0L), endTime = Utils.convertStringToLong(it.endTime, 0L))
            currentEventState = state
            if (state == 1) { // isComing
                // Get stream for package
                triggerIntent(PremiereViewModel.PremiereIntent.TriggerGetStream(requireCheckPackage = true))

                val timeToStart = Utils.getTimeToStartEvent(startTime = Utils.convertStringToLong(it.beginTime, 0L))
                if (timeToStart < 1000 * 60 * 60) {
                    startCountDownTask(timeToStart = timeToStart, it.beginTime)
                } else {
                    removeCountDownTask()
                    runHandlerCheckCountDownTimer(timeToHandlerCheckCountDownTimer = timeToStart - 1000 * 60 * 60)
                    val text = Util.toTimeShowInLabel(beginTime = it.beginTime, endTime = it.endTime, context = requireContext())
                    //
                    liveState = PremiereLiveState.ComingSoon
                    triggerIntent(PremiereViewModel.PremiereIntent.ReflectionUpdateLiveState(text = text, PremiereLiveState.ComingSoon))

                    // Tablet
                    if (context.isTablet()) {
                        binding.tvStartTime.text = text
                    }
                }
            } else if(state == 2) {
                val text = if (isPremiere()) getDetails()?.labelEvent ?: "" else this.getString(R.string.premiere_live)
                //timer update UI when end event
                runHandlerCheckCountDownTimer(timeToHandlerCheckCountDownTimer = Utils.getTimeToEndEvent(Utils.convertStringToLong(it.endTime, 0L)))
                removeAllCountDown()
                if (viewModel.getPremiereId().isNotBlank()) {
                    viewModel.saveClickTimeToPlay(System.currentTimeMillis())
                    triggerIntent(PremiereViewModel.PremiereIntent.GetDetail(premiereId = viewModel.getPremiereId()))
                }
                //
                liveState = PremiereLiveState.Live
                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionUpdateLiveState(text = text, PremiereLiveState.Live))
                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionFollowButtonUI)

                // Tablet
                if (context.isTablet()) {
                    binding.tvStartTime.text = text
                    // Update
                    updateFollowButton()
                }

                handleAutoOpenLiveChat()
            } else {
                removeAllCountDown()
                // Get stream for package
                triggerIntent(PremiereViewModel.PremiereIntent.TriggerGetStream(requireCheckPackage = true))

                val text = <EMAIL>(R.string.premiere_end)
                liveState = PremiereLiveState.End
                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionUpdateLiveState(text = text, PremiereLiveState.End))
                SourceRemoveObject.setIdToRemove(viewModel.getPremiereId())

                // Tablet
                if (context.isTablet()) {
                    binding.tvStartTime.text = text
                }
            }
        }
    }

    private fun startCountDownTask(timeToStart : Long, beginTime: String) {
        removeCountDownTask()
        countDownTimerEvent = object : CountDownTimer(timeToStart, 1_000L) {
            override fun onTick(millisUntilFinished: Long) {
                //
                val text = if(beginTime.toLongOrNull() == null || context == null) {
                    Util.getTimeCountDown(millisUntilFinished)
                } else {
                    Util.getTimeShowInLabel(beginTime, requireContext())
                }
                liveState = PremiereLiveState.CountDown
                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionUpdateLiveState(text = text, PremiereLiveState.CountDown))

                // Tablet
                if (context.isTablet()) {
                    if (_binding != null) {
                        binding.tvStartTime.text = text
                    }
                }
                handleAutoOpenLiveChat()
            }

            override fun onFinish() {
                if (_binding != null) {
                    updateCountDownTimer()
                }
            }
        }.start()
    }

    private fun removeCountDownTask() {
        countDownTimerEvent?.cancel()
        countDownTimerEvent = null
    }

    private fun removeHandlerCheckCountDownTimer() {
        handlerCheckCountDownTimer?.removeCallbacks(runnableCheckCountDownTimer)
        handlerCheckCountDownTimer = null
    }

    private fun runHandlerCheckCountDownTimer(timeToHandlerCheckCountDownTimer: Long) {
        removeHandlerCheckCountDownTimer()
        if (handlerCheckCountDownTimer == null) {
            Looper.getMainLooper()?.run { handlerCheckCountDownTimer = Handler(this) }
        }
        handlerCheckCountDownTimer?.run {
            postDelayed(runnableCheckCountDownTimer, timeToHandlerCheckCountDownTimer)
        }
    }
    //endregion

    //region Update Count Down
    private fun updateCountDownTimerEventTV() {
        getDetails()?.let {
            val state = Utils.getTimeLiveState(beginTime = Utils.convertStringToLong(it.beginTime, 0L), endTime = Utils.convertStringToLong(it.endTime, 0L))
            currentEventState = state
            if (state == 1) { // isComing
                val timeToStart = Utils.getTimeToStartEvent(startTime = Utils.convertStringToLong(it.beginTime, 0L))
                if (timeToStart < 1000 * 60 * 60) {
                    startCountDownTaskEventTV(timeToStart = timeToStart, it.beginTime)
                } else {
                    removeCountDownTaskEventTV()
                    runHandlerCheckCountDownTimerEventTV(timeToHandlerCheckCountDownTimer = timeToStart - 1000 * 60 * 60)
                    val text = Util.toTimeShowInLabel(beginTime = it.beginTime, endTime = it.endTime, context = requireContext())
                    //
                    liveState = PremiereLiveState.ComingSoon
                    triggerIntent(PremiereViewModel.PremiereIntent.ReflectionUpdateLiveState(text = text, PremiereLiveState.ComingSoon))

                    // Tablet
                    if (context.isTablet()) {
                        binding.tvStartTime.text = text
                    }
                }
            } else if(state == 2) {
                val text = if (isPremiere()) getDetails()?.labelEvent ?: "" else this.getString(R.string.premiere_live)
                removeAllCountDown()
                //timer update UI when end event
                runHandlerCheckCountDownTimerEventTV(timeToHandlerCheckCountDownTimer = Utils.getTimeToEndEvent(Utils.convertStringToLong(it.endTime, 0L)))
                //
                liveState = PremiereLiveState.Live
                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionUpdateLiveState(text = text, PremiereLiveState.Live))
                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionFollowButtonUI)

                // Tablet
                if (context.isTablet()) {
                    binding.tvStartTime.text = text
                    // Update
                    updateFollowButton()
                }

                handleAutoOpenLiveChat()
            } else {
                removeAllCountDown()

                val text = <EMAIL>(R.string.premiere_end)
                liveState = PremiereLiveState.End
                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionUpdateLiveState(text = text, PremiereLiveState.End))
                SourceRemoveObject.setIdToRemove(viewModel.getPremiereId())

                // Tablet
                if (context.isTablet()) {
                    binding.tvStartTime.text = text
                }
            }
        }
    }

    private fun startCountDownTaskEventTV(timeToStart : Long, beginTime: String) {
        removeCountDownTaskEventTV()
        countDownTimerEvent = object : CountDownTimer(timeToStart, 1_000L) {
            override fun onTick(millisUntilFinished: Long) {
                //
                val text = if(beginTime.toLongOrNull() == null || context == null) {
                    Util.getTimeCountDown(millisUntilFinished)
                } else {
                    Util.getTimeShowInLabel(beginTime, requireContext())
                }
                liveState = PremiereLiveState.CountDown
                triggerIntent(PremiereViewModel.PremiereIntent.ReflectionUpdateLiveState(text = text, PremiereLiveState.CountDown))

                // Tablet
                if (context.isTablet()) {
                    if (_binding != null) {
                        binding.tvStartTime.text = text
                    }
                }
                handleAutoOpenLiveChat()
            }

            override fun onFinish() {
                if (_binding != null) {
                    updateCountDownTimerEventTV()
                }
            }
        }.start()
    }

    private fun removeCountDownTaskEventTV() {
        countDownTimerEvent?.cancel()
        countDownTimerEvent = null
    }

    private fun removeHandlerCheckCountDownTimerEventTV() {
        handlerCheckCountDownTimer?.removeCallbacks(runnableCheckCountDownTimer)
        handlerCheckCountDownTimer = null
    }

    private fun runHandlerCheckCountDownTimerEventTV(timeToHandlerCheckCountDownTimer: Long) {
        removeHandlerCheckCountDownTimerEventTV()
        if (handlerCheckCountDownTimer == null) {
            Looper.getMainLooper()?.run { handlerCheckCountDownTimer = Handler(this) }
        }
        handlerCheckCountDownTimer?.run {
            postDelayed(runnableCheckCountDownTimer, timeToHandlerCheckCountDownTimer)
        }
    }
    //endregion


    //region UI reflection
    private fun triggerIntent(intent: PremiereViewModel.PremiereIntent) {
        lifecycleScope.launch(Dispatchers.Main) { viewModel.dispatchIntent(intent) }
    }

    private fun checkFollow() {
        viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.CheckFollow(isPremiere = isPremiere(), id = getDetails()?.id ?:""))
    }

    private fun getBlockItems(data: Details?) {
        data?.let {
            val requests = it.blocks.map { item -> PremiereTabRequest(type = item.type, blockId = item.id, pageIndex = 1, pageSize = 100, drm = "1") }
            viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.GetBlockItems(requests = requests))
        }
    }

    private fun showDialog(message: String, onConfirm: (() -> Unit)? = null) {
        alertDialog?.dismiss()
        alertDialog = AlertDialog().apply {
            setMessage(message)
            setShowTitle(value = true)
            setTextTitle(<EMAIL>(R.string.notification))
            setTextConfirm(<EMAIL>(R.string.text_rating_accept))
            setOnlyConfirmButton(true)
            setHandleBackPress(true)
            setListener(object : AlertDialogListener {
                override fun onConfirm() {
                    onConfirm?.invoke()
                }
            })
        }
        alertDialog?.show(childFragmentManager, "AlertDialog")
    }
    //endregion

    // region Google billing
    private fun handlePaymentState() {
        if (paymentViewModel.state.hasObservers()) paymentViewModel.resetState()
        paymentViewModel.state.observe(viewLifecycleOwner) { state ->
            when (state) {
                is PaymentViewModel.PaymentViewState.Loading -> showLoading()

                is PaymentViewModel.PaymentViewState.ResultPackagePlan -> {
                    if (state.data.plans.isNotEmpty()) {
                        if (BillingUtils.isEnableGoogleBilling()) {
                            selectedPlan = state.data.plans[0]
                            val utmData = Utils.getLocalDataUtm(sharedPreferences, trackingInfo)
                            paymentViewModel.dispatchIntent(
                                PaymentViewModel.PaymentViewIntent.CreateGoogleBillingTransaction(
                                    planId = state.data.plans[0].id,
                                    affiliateSource = utmData.first,
                                    trafficId = utmData.second)
                            )
                            AdjustAllEvent.sendPackageRecommendClickEvent(packageId = selectedPlan?.planType?: "", packageName = selectedPlan?.name?: "")
                            PaymentTrackingUtil.sendTrackingClickBuyTVodMovie(
                                trackingProxy = trackingProxy,
                                trackingInfo = trackingInfo,
                                monthPrepaid = selectedPlan?.id?: "",
                                idPackage = selectedPlan?.planType?: "",
                                price = selectedPlan?.amountStr?: ""
                            )
                        }
                    }
                }

                is PaymentViewModel.PaymentViewState.ResultCreateGoogleBillingTransaction -> {
                    AdjustAllEvent.dataCur.transactionType = state.data.ggSkuType
                    if (state.data.status == 1) {
                        if (state.data.isPurchase) {
                            billingClientLifecycle?.queryProductDetail(listOf(state.planId), state.data.ggSkuType)
                        } else {
                            showDialog(state.data.message) {
                                when (state.data.redirectLink) {
                                    BillingClientLifecycleV6.GOOGLE_BILLING_REDIRECT_SUBSCRIPTIONS -> {
                                        activity?.let {
                                            billingClientLifecycle?.openAccountSubscription("", it.packageName, it)
                                        }
                                    }
                                    BillingClientLifecycleV6.GOOGLE_BILLING_REDIRECT_DETAIL -> {
                                        activity?.let {
                                            billingClientLifecycle?.openAccountSubscription(state.data.latestPlanId, it.packageName, it)
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        showDialog(state.data.message)
                    }
                }

                is PaymentViewModel.PaymentViewState.ResultVerifyGoogleBillingTransaction -> {
                    if (state.data.status == 1) {
                        selectedPlan?.let {
                            AdjustAllEvent.sendPaymentSuccessEvent(transactionId = state.orderId)
                            TrackingGA4Proxy.sendTrackingPaymentSuccess(it, TrackingGA4Proxy.PaymentMethod.direct)
                            PaymentTrackingUtil.sendTrackingRentMovieShow(trackingProxy, trackingInfo,
                                PaymentTrackingUtil.rentSuccess, promoCode = "",
                                monthPrepaid = it.id, idMovie = viewModel.getId(), idService = it.planType, price = it.amountStr ?: "")
                        }
                        viewModel.saveVipRequired(isRequired = false)
                        viewModel.triggerRestartPlayer()
                        //
                        hideViewBelowPlayer()
                        //
                    } else {
                        selectedPlan?.let {
                            AdjustAllEvent.sendPaymentFailEvent(transactionId = state.orderId)
                            TrackingGA4Proxy.sendTrackingPaymentFail(it, TrackingGA4Proxy.PaymentMethod.direct)
                            PaymentTrackingUtil.sendTrackingRentMovieShow(trackingProxy, trackingInfo,
                                PaymentTrackingUtil.rentFail,  promoCode = "",
                                monthPrepaid = it.id, idMovie = viewModel.getId(), idService = it.planType, price = it.amountStr ?: "")
                        }
                        showDialog(state.data.message)
                    }
                }

                is PaymentViewModel.PaymentViewState.ResultRetryVerifyGoogleBillingTransaction -> {}

                is PaymentViewModel.PaymentViewState.RequiredLogin -> {
                    val extendsArgs:Bundle = bundleOf(Constants.PREMIERE_FOLLOW_NAVIGATE_LOGIN_REQUEST_KEY to true)
                    parentFragment?.navigateToLoginWithParams(title = state.message, extendsArgs = extendsArgs)
                }

                is PaymentViewModel.PaymentViewState.Done -> hideLoading()

                else -> {}
            }
        }
    }

    private fun initGoogleBilling() {
        if (BillingUtils.isEnableGoogleBilling() && activity != null) {
            billingClientLifecycle = BillingClientLifecycleV6.getInstance(requireActivity().applicationContext)
            billingClientLifecycle?.let { clientLifecycle ->
                viewLifecycleOwner.lifecycle.addObserver(clientLifecycle)

                clientLifecycle.purchasesInApp.observe(viewLifecycleOwner) { data ->
                    clientLifecycle.handlerPurchase(data, BillingClientLifecycleV6.PurchaseType.ResponseInAppPurchase,
                        onPurchaseSuccess = object : BillingClientLifecycleV6.OnPurchaseSuccess {
                            override fun onPurchaseSuccess(orderId: String, planId: String, googlePurchaseToken: String) {
                                val utmData = Utils.getLocalDataUtm(sharedPreferences, trackingInfo)
                                paymentViewModel.dispatchIntent(
                                    PaymentViewModel.PaymentViewIntent.RetryVerifyGoogleBillingTransaction(
                                        planId = planId,
                                        googlePurchaseToken = googlePurchaseToken,
                                        affiliateSource = utmData.first,
                                        trafficId = utmData.second)
                                )
                            }
                        },
                        onPurchasePending = null,
                        onPurchaseFail = null
                    )
                }
                clientLifecycle.purchasesSubs.observe(viewLifecycleOwner) { data ->
                    clientLifecycle.handlerPurchase(data, BillingClientLifecycleV6.PurchaseType.ResponseSubsPurchase,
                        onPurchaseSuccess = object : BillingClientLifecycleV6.OnPurchaseSuccess {
                            override fun onPurchaseSuccess(orderId: String, planId: String, googlePurchaseToken: String) {
                                val utmData = Utils.getLocalDataUtm(sharedPreferences, trackingInfo)
                                paymentViewModel.dispatchIntent(
                                    PaymentViewModel.PaymentViewIntent.RetryVerifyGoogleBillingTransaction(
                                        planId = planId,
                                        googlePurchaseToken = googlePurchaseToken,
                                        isRetry = 1,
                                        affiliateSource = utmData.first,
                                        trafficId = utmData.second)
                                )
                            }
                        },
                        onPurchasePending = null,
                        onPurchaseFail = null
                    )
                }
                clientLifecycle.purchaseUpdateEvent.observe(viewLifecycleOwner) { data ->
                    clientLifecycle.handlerPurchase(data, BillingClientLifecycleV6.PurchaseType.CurrentPurchase,
                        onPurchaseSuccess = object : BillingClientLifecycleV6.OnPurchaseSuccess {
                            override fun onPurchaseSuccess(orderId: String, planId: String, googlePurchaseToken: String) {
                                activity?.runOnUiThread {
                                    hideLoading()
                                    val utmData = Utils.getLocalDataUtm(sharedPreferences, trackingInfo)
                                    paymentViewModel.dispatchIntent(
                                        PaymentViewModel.PaymentViewIntent.VerifyGoogleBillingTransaction(
                                            orderId = orderId,
                                            planId = planId,
                                            googlePurchaseToken = googlePurchaseToken,
                                            affiliateSource = utmData.first,
                                            trafficId = utmData.second)
                                    )
                                }
                            }
                        },
                        onPurchasePending = object : BillingClientLifecycleV6.OnPurchasePending {
                            override fun onPurchasePending() {
                                activity?.runOnUiThread {
                                    showLoading()
                                }
                            }
                        },
                        onPurchaseFail = object : BillingClientLifecycleV6.OnPurchaseFail {
                            override fun onPurchaseFail(errorMessage: String) {
                                activity?.runOnUiThread {
                                    hideLoading()
                                    showDialog(errorMessage)
                                }
                            }
                        })
                }
                clientLifecycle.productDetail.observe(viewLifecycleOwner) { data ->
                    if (data != null && data.productId.isNotBlank() && sharedPreferences.userId().isNotBlank()) {
                        activity?.let {
                            clientLifecycle.launchBillingFlowV6(it, data, 0, sharedPreferences.userId())
                        }
                    }
                }
                clientLifecycle.errorResponse.observe(viewLifecycleOwner) { data ->
                    if (data.second.isNotBlank()) {
                        Toast.makeText(context, data.second, Toast.LENGTH_SHORT).show()
                    }
                    when (data.first) {
                        BillingClient.BillingResponseCode.USER_CANCELED -> {
                            selectedPlan?.let {
                                AdjustAllEvent.sendPaymentUserCancelEvent()
                                TrackingGA4Proxy.sendTrackingPaymentUserCancel(it.id, it.name?:"")
                                PaymentTrackingUtil.cancelBuyPackageOrRent(trackingProxy, trackingInfo,  promoCode = "",
                                    monthPrepaid = it.id, idMovie = viewModel.getId(), idService = it.planType, price = it.amountStr ?: "")
                            }
                        }
                        else -> {
                            selectedPlan?.let {
                                AdjustAllEvent.sendPaymentFailEvent(transactionId = "")
                                TrackingGA4Proxy.sendTrackingPaymentFail(it, TrackingGA4Proxy.PaymentMethod.direct)
                                PaymentTrackingUtil.sendTrackingRentMovieShow(trackingProxy, trackingInfo,
                                    PaymentTrackingUtil.rentFail,  promoCode = "",
                                    monthPrepaid = it.id, idMovie = viewModel.getId(), idService = it.planType, price = it.amountStr ?: "")
                            }
                        }
                    }
                }
            }
        }
    }


    //endregion

    //region Handle -> Player Layout
    private fun adjustPlayerConstraint(isScale: Boolean) {
        if (_binding == null) return
        try {
            if (isScale) {
                val condition = if (context.isTablet()) {
                    viewModel.isFullScreen.value?.first == true && viewModel.isFullScreen.value?.second == true
                } else {
                    MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
                }
                if (condition) {
                    ConstraintSet().apply {
                        // Set constraint for player
                        clone(binding.clRoot)
                        connect(R.id.f_player, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
                        connect(R.id.f_player, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clRoot)
                }
            } else {
                ConstraintSet().apply {
                    // Set constraint for player
                    clone(binding.clRoot)
                    connect(R.id.f_player, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
                    connect(R.id.f_player, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
                }.applyTo(binding.clRoot)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun adjustPremiereContentConstraint(isScale: Boolean) {
        if (_binding == null) return
        if (isScale) {
            if (MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                ConstraintSet().apply {
                    // Set constraint for live content
                    connect(R.id.nav_host_fragment, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
                    connect(R.id.nav_host_fragment, ConstraintSet.START, R.id.f_player, ConstraintSet.END)
                    connect(R.id.nav_host_fragment, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                    connect(R.id.nav_host_fragment, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                }.applyTo(binding.clRoot)
            }
        } else {
            ConstraintSet().apply {
                // Set constraint for live content
                connect(R.id.nav_host_fragment, ConstraintSet.TOP, R.id.layout_buy_package_detail, ConstraintSet.BOTTOM)
                connect(R.id.nav_host_fragment, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
                connect(R.id.nav_host_fragment, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                connect(R.id.nav_host_fragment, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
            }.applyTo(binding.clRoot)
        }
    }
    //endregion

    //region Tablet
    private fun updateDetailUI(premiere: Details ?= null) {
        if (context.isTablet()) {
            val data = premiere ?: viewModel.getDataDetail()
            data?.let { premiere ->
                binding.tvTitle.text = premiere.title
                //
                val textMeta = StringUtils.getMetaText(context = binding.root.context, priorityTag = premiere.priorityTag, metaData = premiere.metaData)
                binding.tvVideoInfo.apply {
                    isVisible = !textMeta.isNullOrBlank()
                    text = textMeta
                }
                //
                if (premiere.maturityRating.advisories.isBlank()) {
                    binding.tvAgeRestrictionTablet.gone()
                } else {
                    binding.tvAgeRestrictionTablet.text = premiere.maturityRating.advisories
                    binding.tvAgeRestrictionTablet.show()
                }
                //
                val isLiveChat = premiere.commentType == Constants.CHAT_EVENT_REAL_TIME_TYPE
                binding.llChat.isVisible = isLiveChat
                //
                binding.tvStartTime.show()
                //
                // region game
                binding.llRankGameTablet.isVisible = isDashboard()
                initSportInteractive()

                // endregion game
            }
        }
        //
        viewModel.getVipRequired()?.let {
            if (it.first) {
                it.second?.let { requiredVip ->
                    showViewBuyPackage()
                }
            } else hideViewBelowPlayer()
        } ?: run {hideViewBelowPlayer()}
    }
    private fun initSportInteractive(){
        binding.buttonSport.llMatchData.checkShowSportInteractive(viewModel.getDataDetail()?.isInteractiveSports?: false)
    }
    private fun checkShowToolTip(isLandscape: Boolean){
        if(viewModel.getDataDetail()?.isInteractiveSports == true) {
            if (isLandscape && MainApplication.INSTANCE.sharedPreferences.shouldShowTooltip(Constants.TOOLTIP_SPORT_INTERACTIVE)) {
                if(MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    viewLifecycleOwner.lifecycle.addObserver(tooltipsView)
                    tooltipsView.showToolTips(binding.buttonSport.llMatchData,
                        binding.clPremiereInfo,
                        getString(R.string.interactive_sport_tooltip),
                        TooltipsView.PositionTooltips.TopRight,
                        tooltipsCallback = object : TooltipsCallback {
                            override fun onClickTooltips() {
                                MainApplication.INSTANCE.sharedPreferences.setShouldShowTooltip(Constants.TOOLTIP_SPORT_INTERACTIVE, false)
                            }
                        })
                }
            }
        }
    }
    private fun setHideToolTipSportInteractive(){
        MainApplication.INSTANCE.sharedPreferences.setShouldShowTooltip(
            Constants.TOOLTIP_SPORT_INTERACTIVE,
            shouldShow = false
        )
    }
    private fun openPremiereDescription() {
        if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.premiere_chat_fragment) {
            binding.navHostFragment.findNavController().popBackStack()
            binding.root.postDelayed({
                viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.TriggerClickPremiereDescription(isOpen = true))
            }, 200)
        } else {
            if(binding.navHostFragment.findNavController().currentDestination?.id == R.id.sport_interactive_v2_fragment) {
                binding.navHostFragment.findNavController().popBackStack()
                binding.root.postDelayed({
                    viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.TriggerClickPremiereDescription(isOpen = true))
                }, 200)
            } else {
                viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.TriggerClickPremiereDescription())
            }
        }
    }

    private fun updateFollowButton() {
        if (context.isTablet()) {
            val isFollow = viewModel.isFollow()
            binding.ivFollow.isSelected = isFollow
            getDetails()?.let {
                val state = Utils.getTimeLiveState(beginTime = Utils.convertStringToLong(it.beginTime, 0L), endTime = Utils.convertStringToLong(it.endTime, 0L))
                binding.ivFollow.isActivated = state == 1
            }
            binding.tvFollow.text = if (isFollow) { getString(R.string.premiere_scheduled) } else getString(R.string.premiere_schedule)
        }
    }

    private fun onShareLink(url: String) {
        sendTrackingShare()
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, url)
            type = "text/plain"
        }
        val shareIntent = Intent.createChooser(sendIntent, getString(R.string.share))
        context?.startActivity(shareIntent)
    }
    private fun sendTrackingShare() {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "516",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = TrackingUtil.screen,
                event = "Share",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                itemId = viewModel.getId(),
                itemName = getDetails()?.title ?: "",
                EpisodeID = viewModel.getPremiereId(),
                chapterId = "0",
                businessPlan = getDetails()?.payment?.requireVipPlan ?: "",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                isRecommend = TrackingUtil.isRecommend
            )
        )
    }

    private fun handleTabletLayout(isFullscreen: Boolean, isLandscapeMode: Boolean) {
        if (_binding == null) return
        try {
            if (isLandscapeMode) {
                if (isFullscreen) {
                    ConstraintSet().apply {
                        connect(R.id.nav_host_fragment, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
                        connect(R.id.nav_host_fragment, ConstraintSet.START, R.id.f_player, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clRoot)
                } else {
                    ConstraintSet().apply {
                        connect(R.id.nav_host_fragment, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
                        connect(R.id.nav_host_fragment, ConstraintSet.START, R.id.f_player, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clRoot)
                }
            } else {
                if (isFullscreen) {
                    ConstraintSet().apply {
                        connect(R.id.nav_host_fragment, ConstraintSet.TOP, R.id.scrollViewPremiereInfo, ConstraintSet.BOTTOM)
                        connect(R.id.nav_host_fragment, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
                        connect(R.id.nav_host_fragment, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clRoot)
                } else {
                    ConstraintSet().apply {
                        connect(R.id.nav_host_fragment, ConstraintSet.TOP, R.id.scrollViewPremiereInfo, ConstraintSet.BOTTOM)
                        connect(R.id.nav_host_fragment, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
                        connect(R.id.nav_host_fragment, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clRoot)
                }
            }
            handleSubDetailLayout(isFullscreen, isLandscapeMode)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun handleSubDetailLayout(isFullscreen: Boolean, isLandscapeMode: Boolean) {
        if (isLandscapeMode) {
            //
            ConstraintSet().apply {
                clone(binding.clRoot)
                connect(R.id.scrollViewPremiereInfo, ConstraintSet.TOP, R.id.f_player, ConstraintSet.BOTTOM)
                connect(R.id.scrollViewPremiereInfo, ConstraintSet.START, R.id.f_player, ConstraintSet.START)
                connect(R.id.scrollViewPremiereInfo, ConstraintSet.END, R.id.f_player, ConstraintSet.END)
                connect(R.id.scrollViewPremiereInfo, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
            }.applyTo(binding.clRoot)
            //
            binding.clPremiereContainer.show()
            //
            val params = binding.scrollViewPremiereInfo.layoutParams
            params.width = 0
            params.height = 0
            binding.scrollViewPremiereInfo.layoutParams = params
        } else {

            //
            ConstraintSet().apply {
                clone(binding.clRoot)
                connect(R.id.scrollViewPremiereInfo, ConstraintSet.TOP, R.id.f_player, ConstraintSet.BOTTOM)
                connect(R.id.scrollViewPremiereInfo, ConstraintSet.START, R.id.f_player, ConstraintSet.START)
                connect(R.id.scrollViewPremiereInfo, ConstraintSet.END, R.id.f_player, ConstraintSet.END)
                connect(R.id.scrollViewPremiereInfo, ConstraintSet.BOTTOM, R.id.nav_host_fragment, ConstraintSet.TOP)
            }.applyTo(binding.clRoot)
            //
            binding.clPremiereContainer.hide()
            //
            val params = binding.scrollViewPremiereInfo.layoutParams
            params.width = ConstraintLayout.LayoutParams.MATCH_PARENT
            params.height = if (isFullscreen) 0 else ConstraintLayout.LayoutParams.WRAP_CONTENT
            binding.scrollViewPremiereInfo.layoutParams = params
        }
    }
    //endregion

    // region game
      // vote view
    private fun inflateVotingView() {
        if (binding.vsVotingPopUpView.parent  != null) {
            votePopUpViewBinding =
                OnOffVotingViewLayoutBinding.bind(binding.vsVotingPopUpView.inflate())
            votePopUpViewBinding?.apply {
                votePopUpViewBinding?.votingPopUpView?.votingPopupListener =
                    object : OnOffVotingView.VotingPopupListener {
                        @UiThread
                        override fun closePopup(reason: String?) {
                            hideVotingPopUpView()
                        }

                        @UiThread
                        override fun showWarningDialog(message: String?) {
                            // not show warning, just close popup
                            hideVotingPopUpView()
                        }
                        @UiThread
                        override fun onLoaded() {
                            /**
                             * @case if the mode of screen is fullscreen and need to show vote pop up -> exit mode full screen
                             * */
                            toggleEnableOnGame(false)
                            showVotingPopUpView()
                        }
                    }
                btnClose.setOnClickListener {
                    hideVotingPopUpView()
                }
            }
        }
    }
    private fun startVotingView(jsonData: String, vote: VoteEntities) {
        inflateVotingView()
        votePopUpViewBinding?.apply {
            votePopUpViewBinding?.votingPopUpView?.start(
                jsonData = jsonData,
                vote = vote,
                sharedPreferences.userId(),
                sharedPreferences.userPhone(),
                sharedPreferences.accessToken()
            )
        }
    }
    private fun showVotingPopUpView() {
        votePopUpViewBinding?.apply {
            votePopUpViewBinding?.votingPopUpView?.show()
            if (ctlVotingPopupView.visibility != View.VISIBLE) {
                val animSlideUp = AnimationUtils.loadAnimation(
                    context, R.anim.slide_up
                )
                animSlideUp.duration = 600
                ctlVotingPopupView.startAnimation(animSlideUp)
                ctlVotingPopupView.visibility = View.VISIBLE
            }
            isShowVoting = true
        }
    }
    private fun hideVotingPopUpView(destroy: Boolean = false) {
        votePopUpViewBinding?.votingPopUpView?.close(destroy = destroy)
        votePopUpViewBinding?.ctlVotingPopupView?.hide()
        isShowVoting = false
        toggleEnableOnGame(true)
        if(context.isTablet()){
            if(voteEntities?.isScale == true){
                viewModel.dispatchIntent(
                    PremiereViewModel.PremiereIntent.TriggerPlayerLayout(
                        isScale = false
                    )
                )
            }
        }else{
            if (!isOpenTabChat && voteEntities?.isScale == true) {
                viewModel.dispatchIntent(
                    PremiereViewModel.PremiereIntent.TriggerPlayerLayout(
                        isScale = false
                    )
                )
            }
        }
    }
    private fun adjustVotingViewConstraintTabletLayout(isLandscapeMode: Boolean,isScale: Boolean) {
        if (binding.vsVotingPopUpView.parent != null) {
            val lp = ConstraintLayout.LayoutParams(0, 0)
            if (isLandscapeMode) {
                if (viewModel.isFullScreen.value?.first ==true) {
                    viewModel.dispatchIntent(
                        PremiereViewModel.PremiereIntent.TriggerPlayerLayout(
                            isScale = isScale
                        )
                    )
                }
                lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                lp.matchConstraintPercentWidth = 1f - Constants.PLAYER_SCALED_RATIO
                if (votePopUpViewBinding != null) {
                    votePopUpViewBinding?.ctlVotingPopupView?.layoutParams = lp
                } else {
                    binding.vsVotingPopUpView.layoutParams = lp
                }
            } else {
                lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                lp.topToBottom = R.id.f_player
                lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                if (votePopUpViewBinding != null) {
                    votePopUpViewBinding?.ctlVotingPopupView?.layoutParams = lp
                } else {
                    binding.vsVotingPopUpView.layoutParams = lp
                }
            }
        } else {
            votePopUpViewBinding?.ctlVotingPopupView?.apply {
                val lp = ConstraintLayout.LayoutParams(
                    0,
                    0
                )
                if (isLandscapeMode) {
                    if (isScale){
                        viewModel.dispatchIntent(
                            PremiereViewModel.PremiereIntent.TriggerPlayerLayout(
                                isScale = if(viewModel.isFullScreen.value?.first ==true) isScale else false
                            )
                        )
                    }
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.matchConstraintPercentWidth = 1f - Constants.PLAYER_SCALED_RATIO
                } else {
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToBottom = R.id.f_player
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                }
                layoutParams = lp
            }
        }
    }
    private fun adjustVotingViewConstraint(isScale: Boolean) {
        if (binding.vsVotingPopUpView.parent != null) {
            binding.vsVotingPopUpView.apply {
                val lp = ConstraintLayout.LayoutParams(0, 0)
                if (MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    if (isScale) {
                        viewModel.dispatchIntent(
                            PremiereViewModel.PremiereIntent.TriggerPlayerLayout(
                                isScale = isScale
                            )
                        )
                    }
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.matchConstraintPercentWidth = 1f - Constants.PLAYER_SCALED_RATIO
                } else {
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToBottom = R.id.f_player
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID

                }
                layoutParams = lp
            }
        } else {
            votePopUpViewBinding?.ctlVotingPopupView?.apply {
                val lp = ConstraintLayout.LayoutParams(
                    0,
                    0
                )
                if (MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    if (isScale) {
                        viewModel.dispatchIntent(
                            PremiereViewModel.PremiereIntent.TriggerPlayerLayout(
                                isScale = isScale
                            )
                        )
                    }
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.matchConstraintPercentWidth = 1f - Constants.PLAYER_SCALED_RATIO

                } else {
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToBottom = R.id.f_player
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                }
                layoutParams = lp
            }
        }
    }
        // ranking view
    private fun inflateRankingView() {
            if (binding.vsRankingPopUpView.parent  != null) {
                voteRankingViewBinding =
                    OnOffVotingViewLayoutBinding.bind(binding.vsRankingPopUpView.inflate())
                voteRankingViewBinding?.apply {
                    voteRankingViewBinding?.votingPopUpView?.votingPopupListener =
                        object : OnOffVotingView.VotingPopupListener {
                            @UiThread
                            override fun closePopup(reason: String?) {
                                hideRankingPopUpView()
                            }
                            @UiThread
                            override fun showWarningDialog(message: String?) {
                                // not show warning, just close popup
                                hideRankingPopUpView()
                            }
                            @UiThread
                            override fun onLoaded() {
                                /**
                                 * @case if the mode of screen is fullscreen and need to show vote pop up -> exit mode full screen
                                 * */
                                showRankingPopUpView()
                            }
                        }
                    btnClose.setOnClickListener {
                        hideRankingPopUpView()
                    }
                }
            }
        }
    private fun startRankingView(urlRanking:String?) {
        if(!isShowRanking &&!isShowVoting){
            inflateRankingView()
            voteRankingViewBinding?.apply {
                voteRankingViewBinding?.votingPopUpView?.startRanking(
                    urlRanking = urlRanking
                )
            }
        }
    }
    private fun showRankingPopUpView() {
        voteRankingViewBinding?.apply {
            voteRankingViewBinding?.votingPopUpView?.show()
            if (ctlVotingPopupView.visibility != View.VISIBLE) {
                val animSlideUp = AnimationUtils.loadAnimation(
                    context, R.anim.slide_up
                )
                animSlideUp.duration = 600
                ctlVotingPopupView.startAnimation(animSlideUp)
                ctlVotingPopupView.visibility = View.VISIBLE
            }
            isShowRanking = true
            viewModel.saveIsShowRanking(isShowRanking)
        }
    }
    override fun onResume() {
        super.onResume()
        currentEventState = -1
        if(context.isTablet() && isDashboard() && viewModel.getIsShowRanking()){
            viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.GetViewRankingCommon(gameId = gameId()))
        }

        when (getEventType()) {
            PremiereViewModel.EventType.EVENT -> {
                sharedPreferences.saveCurrentContentType(Constants.FIREBASE_NOTIFICATION_FILED_TYPE_EVENT)
                sharedPreferences.saveCurrentContentId(safeArgs.idToPlay)
//                Timber.v("*** resume ${Constants.FIREBASE_NOTIFICATION_FILED_TYPE_EVENT}  ${safeArgs.idToPlay}")
            }
            PremiereViewModel.EventType.EVENT_TV -> {
                sharedPreferences.saveCurrentContentType(Constants.FIREBASE_NOTIFICATION_FILED_TYPE_EVENT_TV)
                sharedPreferences.saveCurrentContentId(safeArgs.idToPlay)
//                Timber.v("*** resume ${Constants.FIREBASE_NOTIFICATION_FILED_TYPE_EVENT_TV}  ${safeArgs.idToPlay}")
            }
        }
    }

    override fun onPause() {
        super.onPause()

        when (getEventType()) {
            PremiereViewModel.EventType.EVENT -> {
                sharedPreferences.removeCurrentContentId()
                sharedPreferences.removeCurrentContentType()
//                Timber.w("*** pause ${Constants.FIREBASE_NOTIFICATION_FILED_TYPE_EVENT}")
            }
            PremiereViewModel.EventType.EVENT_TV -> {
                sharedPreferences.removeCurrentContentId()
                sharedPreferences.removeCurrentContentType()
//                Timber.w("*** pause ${Constants.FIREBASE_NOTIFICATION_FILED_TYPE_EVENT_TV}")
            }
        }
    }

    private fun hideRankingPopUpView(destroy: Boolean = false) {
        voteRankingViewBinding?.votingPopUpView?.close(destroy = destroy)
        voteRankingViewBinding?.ctlVotingPopupView?.hide()
        isShowRanking = false
        if(destroy) viewModel.deleteIsShowRanking() else viewModel.saveIsShowRanking(false)
        if(context.isTablet()){
            if(voteEntities?.isScale == true){
                viewModel.dispatchIntent(
                    PremiereViewModel.PremiereIntent.TriggerPlayerLayout(
                        isScale = false
                    )
                )
            }
        }else{
            if (!isOpenTabChat && voteEntities?.isScale == true) {
                viewModel.dispatchIntent(
                    PremiereViewModel.PremiereIntent.TriggerPlayerLayout(
                        isScale = false
                    )
                )
            }
        }
    }
    private fun adjustRankingViewConstraintTabletLayout(isLandscapeMode: Boolean,isScale: Boolean) {
        if (binding.vsRankingPopUpView.parent != null) {
            val lp = ConstraintLayout.LayoutParams(0, 0)
            if (isLandscapeMode) {
                if (viewModel.isFullScreen.value?.first ==true) {
                    viewModel.dispatchIntent(
                        PremiereViewModel.PremiereIntent.TriggerPlayerLayout(
                            isScale = isScale
                        )
                    )
                }
                lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                lp.matchConstraintPercentWidth = 1f - Constants.PLAYER_SCALED_RATIO
                if (voteRankingViewBinding != null) {
                    voteRankingViewBinding?.ctlVotingPopupView?.layoutParams = lp
                } else {
                    binding.vsRankingPopUpView.layoutParams = lp
                }
            } else {
                lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                lp.topToBottom = R.id.f_player
                lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                if (voteRankingViewBinding != null) {
                    voteRankingViewBinding?.ctlVotingPopupView?.layoutParams = lp
                } else {
                    binding.vsRankingPopUpView.layoutParams = lp
                }
            }
        } else {
            voteRankingViewBinding?.ctlVotingPopupView?.apply {
                val lp = ConstraintLayout.LayoutParams(
                    0,
                    0
                )
                if (isLandscapeMode) {
                    if (isScale){
                        viewModel.dispatchIntent(
                            PremiereViewModel.PremiereIntent.TriggerPlayerLayout(
                                isScale = if(viewModel.isFullScreen.value?.first ==true) isScale else false
                            )
                        )
                    }
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.matchConstraintPercentWidth = 1f - Constants.PLAYER_SCALED_RATIO
                } else {
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToBottom = R.id.f_player
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                }
                layoutParams = lp
            }
        }
    }

    private fun adjustRankingViewConstraint(isScale: Boolean) {
        if (binding.vsRankingPopUpView.parent != null) {
            binding.vsRankingPopUpView.apply {
                val lp = ConstraintLayout.LayoutParams(0, 0)
                if (MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    if (isScale) {
                        viewModel.dispatchIntent(
                            PremiereViewModel.PremiereIntent.TriggerPlayerLayout(
                                isScale = isScale
                            )
                        )
                    }
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.matchConstraintPercentWidth = 1f - Constants.PLAYER_SCALED_RATIO
                } else {
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToBottom = R.id.f_player
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID

                }
                layoutParams = lp
            }
        } else {
            voteRankingViewBinding?.ctlVotingPopupView?.apply {
                val lp = ConstraintLayout.LayoutParams(
                    0,
                    0
                )
                if (MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    if (isScale) {
                        viewModel.dispatchIntent(
                            PremiereViewModel.PremiereIntent.TriggerPlayerLayout(
                                isScale = isScale
                            )
                        )
                    }
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.matchConstraintPercentWidth = 1f - Constants.PLAYER_SCALED_RATIO

                } else {
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToBottom = R.id.f_player
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                }
                layoutParams = lp
            }
        }
    }

    private fun toggleEnableOnGame(enabled: Boolean) {
        binding.flPremiereInfoOverlay?.apply {
            if(enabled) {
                hide()
            } else {
                show()
            }
        }
    }

    //endregion game


    //region Pairing Control
    private val remotePlayerState = object : PairingControlConnectionHelper.RemotePlayerStateListener {
        override fun onStateChanged(state: RemotePlayerState) {}
        override fun onActionEvent(type: String) {
            when (PlayerUtils.getPlayingType()) {
                PlayerView.PlayingType.Cast -> {
                    when (MainApplication.INSTANCE.pairingConnectionHelper.getCurrentConnection()) {
                        is FBoxDeviceInfoV2 -> {
                            if (type == ActionEventType.ADD_FOLLOW || type == ActionEventType.REMOVE_FOLLOW) {
                                getDetails()?.let {
                                    val state = Utils.getTimeLiveState(beginTime = Utils.convertStringToLong(it.beginTime, 0L), endTime = Utils.convertStringToLong(it.endTime, 0L))
                                    if (state == 1) {
                                        when (type) {
                                            ActionEventType.ADD_FOLLOW -> { viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.AddFollow(isPremiere = isPremiere(), id = getDetails()?.id ?:"")) }
                                            ActionEventType.REMOVE_FOLLOW -> { viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.DeleteFollow(isPremiere = isPremiere(), id = getDetails()?.id ?:"")) }
                                        }
                                    }
                                }
                            }
                        }
                        is FSamsungTVDeviceInfo,
                        is FSamsungTVDeviceInfoExternal -> {
                            when (type) {
                                ActionEventType.ADD_FOLLOW -> {
                                    viewModel.saveFollow(true)
                                    //
                                    triggerIntent(PremiereViewModel.PremiereIntent.ReflectionFollowButtonUI)
                                    updateFollowButton()
                                }
                                ActionEventType.REMOVE_FOLLOW -> {
                                    viewModel.saveFollow(false)
                                    //
                                    triggerIntent(PremiereViewModel.PremiereIntent.ReflectionFollowButtonUI)
                                    updateFollowButton()
                                }
                            }
                        }
                        is FAndroidTVDeviceInfo -> {}
                        is FAndroidTVDeviceInfoExternal -> {
                            when (type) {
                                ActionEventType.ADD_FOLLOW -> {
                                    viewModel.saveFollow(true)
                                    //
                                    triggerIntent(PremiereViewModel.PremiereIntent.ReflectionFollowButtonUI)
                                    updateFollowButton()
                                }
                                ActionEventType.REMOVE_FOLLOW -> {
                                    viewModel.saveFollow(false)
                                    //
                                    triggerIntent(PremiereViewModel.PremiereIntent.ReflectionFollowButtonUI)
                                    updateFollowButton()
                                }
                            }
                        }
                    }
                }
                else -> {}
            }
        }

        override fun onCastSessionChanged() {}
    }
    //endregion

    // region Event TV -> Handle all logic
    private fun updateEventTrackingInfo(startTime: Long) {
        val start = startTime * 1000
        if ((start) > System.currentTimeMillis()) { //su kien sap dien ra
            TrackingUtil.setEventTracking(DateTimeUtils.getLogSessionAtCurrentTime(start), false)
        } else {
            TrackingUtil.setEventTracking(DateTimeUtils.getLogSessionAtCurrentTime(start), true)
        }
    }

    private fun retryGetDetail(
        message: String,
        textNegative: String? = null,
        textPositive: String? = null
    ) {
        AlertDialog().apply {
            setMessage(message)
            if (!textNegative.isNullOrEmpty()) setTextExit(textNegative)
            if (!textPositive.isNullOrEmpty()) setTextConfirm(textPositive)
            setListener(object : AlertDialogListener {
                override fun onExit() {}
                override fun onConfirm() {
                    if (viewModel.getPremiereId().isNotBlank()) {
                        viewModel.saveClickTimeToPlay(System.currentTimeMillis())
                        triggerIntent(PremiereViewModel.PremiereIntent.GetDetail(premiereId = viewModel.getPremiereId()))
                    }
                }
            })
        }.show(childFragmentManager, "WarningDialog")
    }
    // endregion

    //region sport Interactive
    private fun exitFullScreenAndShowSportInteractive(isFullscreen : Boolean, isLandscapeMode :Boolean){
        if(context.isTablet()){
            if(isFullscreen && !isLandscapeMode){
                viewModel.dispatchIntent(PremiereViewModel.PremiereIntent.SwitchPlayerMode(modeFullscreen = false))
                if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.premiere_content_fragment) {
                    binding.navHostFragment.findNavController().navigateSafe(PremiereContentFragmentDirections.actionPremiereContentFragmentToSportInteractiveV2Fragment(idToPlay = viewModel.getPremiereId()))
                } else {
                    if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.premiere_chat_fragment){
                        binding.navHostFragment.findNavController().popBackStack()
                        binding.navHostFragment.findNavController().navigateSafe(PremiereContentFragmentDirections.actionPremiereContentFragmentToSportInteractiveV2Fragment(idToPlay = viewModel.getPremiereId()))
                    }
                }
            }
        }
    }
    private fun triggerCloseSportInteractive(){
        if(binding.navHostFragment.findNavController().currentDestination?.id == R.id.sport_interactive_v2_fragment){
            binding.navHostFragment.findNavController().popBackStack()
        }
    }
    //endregion

    //region handle preview
    private fun handleRequiredVipForStream(requiredVip: RequiredVip) {
        when (getEventType()) {
            PremiereViewModel.EventType.EVENT -> {
                viewModel.triggerPlayRequiredVipTrailer(url = requiredVip.trailerUrl)
                showViewBuyPackage()
            }
            PremiereViewModel.EventType.EVENT_TV -> {
                if (viewModel.canPreview) {
                    viewModel.triggerPlayPreview(requiredVip.livePreviewInfo)
                    showViewBuyPackageForPreview()
                    return
                }
                if (!viewModel.hasPreview) {
                    viewModel.triggerPlayRequiredVipTrailer(url = requiredVip.trailerUrl)
                    showViewBuyPackage()
                    return
                }
                viewModel.triggerPlayRequiredVipTrailer(url = requiredVip.livePreviewInfo?.trailerUrl ?: "")
                showViewBuyPackageForPreview()
                viewModel.triggerBuyPackageForPreview(showRequirePackage = true)
            }
        }
    }

    private fun showViewBuyPackageForPreview() {
        buttonTypeBelowPlayer = BUY_PACKAGE_TYPE
        AdjustAllEvent.sendPackageRecommendDisplayEvent(true)
        showViewBelowPlayer(
            message = viewModel.getTextDescriptionBellowPlayer(binding.root.context),
            buttonTitle = viewModel.getTextButtonBellowPlayer(binding.root.context)
        )
    }

    private fun showViewBuyPackage() {
        buttonTypeBelowPlayer = BUY_PACKAGE_TYPE
        AdjustAllEvent.sendPackageRecommendDisplayEvent(true)
        showViewBelowPlayer(
            message = viewModel.getTextDescriptionBellowPlayer(binding.root.context),
            buttonTitle = viewModel.getTextButtonBellowPlayer(binding.root.context),
        )
    }

    private fun showViewRequestLogin(message: String, buttonTitle: String) {
        buttonTypeBelowPlayer = LOGIN_TYPE
        showViewBelowPlayer(
            message = message,
            buttonTitle = buttonTitle
        )
    }

    private fun showViewBelowPlayer(message: String, buttonTitle: String) {
        if (_binding != null) {
            binding.layoutBuyPackageDetail.tvBuyPackage.text = buttonTitle
            binding.layoutBuyPackageDetail.tvBuyPackageDescription.text = message
            binding.layoutBuyPackageDetail.root.show()
        }
    }

    private fun hideViewBelowPlayer() {
        buttonTypeBelowPlayer = ""
        if (_binding != null) {
            binding.layoutBuyPackageDetail.root.hide()
        }
    }

    private fun getTextRequestLoginChannel(context: Context): String {
        return MainApplication.INSTANCE.sharedPreferences.getMsgPreviewRequestLogin().ifBlank {
            context.getString(R.string.description_request_login_for_live_preview)
        }
    }

    private fun navigateToLiveChatV2() {
        if(_binding != null) {
            binding.navHostFragment.findNavController().navigateSafe(
                PremiereContentFragmentDirections.actionPremiereContentFragmentToLiveChatFragmentV2(
                    id = viewModel.getPremiereId(),
                    reportAction = NavPremiereDirections.actionGlobalToReportLiveChatDialog().actionId,
                    optionAction = NavPremiereDirections.actionGlobalToChatOptionBottomSheet().actionId
                )
            )
        }
    }

    //endregion preview
    companion object {
        const val ACTION_FOLLOW = "follow"
        private val LOGIN_TYPE = "login_type"
        private val BUY_PACKAGE_TYPE = "buy_package_type"
    }
}