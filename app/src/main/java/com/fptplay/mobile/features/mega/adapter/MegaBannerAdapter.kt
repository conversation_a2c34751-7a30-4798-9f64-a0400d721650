package com.fptplay.mobile.features.mega.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.extensions.getDisplayWidth
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.databinding.MegaBannerItemBinding
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenuItem
import com.xhbadxx.projects.module.util.image.ImageProxy

class MegaBannerAdapter(private val context: Context) :
    BaseAdapter<MegaMenuItem, MegaBannerAdapter.MegaBannerViewHolder>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MegaBannerViewHolder {
        return MegaBannerViewHolder(
            MegaBannerItemBinding.inflate(
                LayoutInflater.from(context),
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: MegaBannerViewHolder, position: Int) {
        holder.bind(differ.currentList[position])
    }


    inner class MegaBannerViewHolder(private val binding: MegaBannerItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.setOnClickListener {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        it
                    )
                }
            }
        }

        fun bind(data: MegaMenuItem) {

            val url = if (context.isTablet()) {
                data.imgTablet
            } else {
                data.img
            }
            binding.apply {
                ImageProxy.load(
                    context = context,
                    width = context.getDisplayWidth(),
                    height = 0,
                    url = url,
                    target = binding.ivBanner,
                    errorDrawableId = R.drawable.placeholder_mega_banner,
                    placeHolderId = R.drawable.placeholder_mega_banner,
                )
            }
        }
    }

}