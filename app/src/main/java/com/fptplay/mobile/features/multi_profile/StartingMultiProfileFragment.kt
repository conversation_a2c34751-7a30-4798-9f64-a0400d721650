package com.fptplay.mobile.features.multi_profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.common.ui.bases.BaseDialogFragment
import com.fptplay.mobile.databinding.StartingFragmentBinding
import com.fptplay.mobile.features.multi_profile.utils.StartingTargetScreenType
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class StartingMultiProfileFragment :
    BaseDialogFragment<MultiProfileViewModel.MultiProfileState, MultiProfileViewModel.MultiProfileIntent>() {

    override val viewModel by activityViewModels<MultiProfileViewModel>()
    private var _binding: StartingFragmentBinding? = null
    private val binding get() = _binding!!
    private val safeArgs: StartingMultiProfileFragmentArgs by navArgs()

    @Inject
    lateinit var sharedPreferences: SharedPreferences
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = StartingFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun MultiProfileViewModel.MultiProfileState.toUI() {

    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun bindComponent() {
        when (safeArgs.targetScreen) {
            StartingTargetScreenType.SelectProfile -> {
                findNavController().navigate(
                    StartingMultiProfileFragmentDirections.actionStartingMultiProfileToMultiProfileFragment(
                        navigationId = safeArgs.navigationId,
                        requestRestartApp = safeArgs.requestRestartApp,
                        isSelectionOnBoarding = safeArgs.isSelectionOnBoarding,
                        previousBackStackEntryId = safeArgs.previousBackStackEntryId,
                        isFromLoginScreen = safeArgs.isFromLoginScreen,
                    )
                )
            }

            StartingTargetScreenType.ManageProfile -> {
                findNavController().navigate(
                    StartingMultiProfileFragmentDirections.actionStartingMultiProfileToManageProfileFragment()
                )
            }
        }
        dismissAllowingStateLoss()
    }
}