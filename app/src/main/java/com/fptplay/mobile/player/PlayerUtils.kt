package com.fptplay.mobile.player

import android.content.Context
import android.util.Log
import android.util.TypedValue
import android.view.View
import android.widget.TextView
import androidx.annotation.OptIn
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.updatePadding
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModel
import androidx.media3.common.util.UnstableApi
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.features.livetv_detail.LiveTVDetailViewModel
import com.fptplay.mobile.features.premiere.PremiereViewModel
import com.fptplay.mobile.player.model.PlayerBottomControlData
import com.fptplay.mobile.player.utils.PlayerConstants
import com.fptplay.mobile.player.utils.afterMeasuredAutoRemove
import com.fptplay.mobile.player.utils.getPhysicalScreenHeightWithOrientation
import com.fptplay.mobile.player.utils.getPhysicalScreenWidthWithOrientation
import com.fptplay.mobile.player.utils.PlayerPiPHelper
import com.fptplay.mobile.vod.VodDetailViewModel
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.media3.ui.SubtitleView
import com.tear.modules.player.exo.ExoPlayerView
import com.tear.modules.player.util.IPlayer
import com.xhbadxx.projects.module.domain.entity.fplay.common.Stream
import com.xhbadxx.projects.module.domain.entity.fplay.drm.DrmKey
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.xhbadxx.projects.module.util.common.Util.toBase64Default
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import kotlin.math.max

@OptIn(UnstableApi::class)
object PlayerUtils {

    private const val PLAYER_SUBTITLE_FRACTIONAL_TEXT_SIZE_DEFAULT = 0.075f
    private const val PLAYER_SUBTITLE_FRACTIONAL_TEXT_SIZE_TABLET_NORMAL = 0.055f
    private const val PLAYER_SUBTITLE_FRACTIONAL_TEXT_SIZE_TABLET_FULL_LAND = 0.045f
    private const val PLAYER_SUBTITLE_FRACTIONAL_TEXT_SIZE_MOBILE_LANDSCAPE = 0.06f
    private const val PLAYER_SUBTITLE_FRACTIONAL_TEXT_SIZE_MOBILE_PORTRAIT = 0.075f

    private const val PLAYER_SUBTITLE_PADDING_BOTTOM_FRACTION_DEFAULT_MARGIN_MOBILE_PORTRAIT = 0.028f
    private const val PLAYER_SUBTITLE_PADDING_BOTTOM_FRACTION_DEFAULT_MARGIN_MOBILE_LANDSCAPE = 0.01f

    private const val PLAYER_SUBTITLE_PADDING_BOTTOM_FRACTION_DEFAULT_MARGIN_TABLET_NORMAL = 0.05f
    private const val PLAYER_SUBTITLE_PADDING_BOTTOM_FRACTION_DEFAULT_MARGIN_TABLET_FULL_LAND = 0.02f

    private val pairingConnection by lazy { MainApplication.INSTANCE.pairingConnectionHelper }

    fun getPlayingType() : PlayerView.PlayingType {
        return if (pairingConnection.isConnected) {
            PlayerView.PlayingType.Cast
        } else {
            PlayerView.PlayingType.Local
        }
    }


    fun List<Details.Episode>.getIndexOf(episode: Details.Episode?) : Int {
        if (episode != null) {
            if (episode.isItemOfPlaylist) {
                this.forEachIndexed { index, item ->
                    if (item.id == episode.id && item.vodId == episode.vodId) {
                        return index
                    }
                }
            } else {
                this.forEachIndexed { index, item ->
                    if (item.id == episode.id && item.vodId == episode.vodId) {
                        return index
                    }
                }
            }
        }
        return -1
    }

    // region Subtitle
    private fun ExoPlayerView.getSubtitleView(): SubtitleView? {
        val exoSubtitleView = this.findViewById<View>(R.id.v_exo_subtitles)
        if (exoSubtitleView is SubtitleView) {
            return exoSubtitleView
        }
        return null
    }

    private fun ExoPlayerView.getSubtitleFractionalTextSize(
        fragmentActivity: FragmentActivity?,
        player: ExoPlayer,
        isFullscreen: Boolean,
        isLandscapeMode: Boolean,
        videoResizeMode: Int
    ): Float {
        val subtitleHeight = getSubtitleView()?.height?.toFloat()
        return if (subtitleHeight != null) {
            val constVideoScaleMode = if (videoResizeMode == AspectRatioFrameLayout.RESIZE_MODE_FIT) 0 else 1
            if (context.isTablet()) {
                if (!isLandscapeMode) {
                    if (isFullscreen) {
                        if (constVideoScaleMode == 1) {
                            PLAYER_SUBTITLE_FRACTIONAL_TEXT_SIZE_TABLET_NORMAL / (subtitleHeight / calculateOriginalSubtitleHeightFullscreen(
                                fragmentActivity,
                                player,
                                isLandscapeMode,
                                isFullscreen,
                                subtitleHeight
                            ))
                        } else {
                            PLAYER_SUBTITLE_FRACTIONAL_TEXT_SIZE_TABLET_NORMAL
                        }
                    } else {
                        PLAYER_SUBTITLE_FRACTIONAL_TEXT_SIZE_TABLET_NORMAL
                    }
                } else {
                    PLAYER_SUBTITLE_FRACTIONAL_TEXT_SIZE_TABLET_FULL_LAND
                }
            } else {
                if (isFullscreen) {
                    if (constVideoScaleMode == 1) {
                        PLAYER_SUBTITLE_FRACTIONAL_TEXT_SIZE_MOBILE_LANDSCAPE / (subtitleHeight / calculateOriginalSubtitleHeightFullscreen(
                            fragmentActivity,
                            player,
                            isLandscapeMode,
                            isFullscreen,
                            subtitleHeight
                        ))
                    } else {
                        PLAYER_SUBTITLE_FRACTIONAL_TEXT_SIZE_MOBILE_LANDSCAPE
                    }
                } else PLAYER_SUBTITLE_FRACTIONAL_TEXT_SIZE_MOBILE_PORTRAIT
            }
        } else PLAYER_SUBTITLE_FRACTIONAL_TEXT_SIZE_DEFAULT
    }

    fun ExoPlayerView.updateSubtitleLayoutStyle(
        player: IPlayer?,
        fragmentActivity: FragmentActivity?,
        isFullscreen: Boolean,
        isLandscape: Boolean,
        resizeMode: Int,
        isSituationWarningShown: Boolean
    ) {
        try {
            player?.internalPlayer().let {
                if (it is ExoPlayer) {
                    val defaultPaddingSub = if(context.isTablet()) {
                        if (isFullscreen && isLandscape) context.resources.getDimensionPixelSize(R.dimen._15sdp) else context.resources.getDimensionPixelSize(R.dimen._3sdp)
                    } else {
                        if (isFullscreen) context.resources.getDimensionPixelSize(R.dimen._18sdp) else context.resources.getDimensionPixelSize(R.dimen._7sdp)
                    }
                    val w = getPhysicalScreenWidthWithOrientation(MainApplication.INSTANCE.applicationContext.applicationContext, MainApplication.INSTANCE.applicationContext.resources.configuration.orientation)
                    val h = getPhysicalScreenHeightWithOrientation(MainApplication.INSTANCE.applicationContext.applicationContext, MainApplication.INSTANCE.applicationContext.resources.configuration.orientation)
                    val deviceRatio = w.toFloat() / h.toFloat()
                    val videoRatio =
                        (it.videoSize.width.toFloat() / it.videoSize.height.toFloat())
                    val constFullScreen = if (isFullscreen) 1 else 0
                    val constLandscape = if (isLandscape) 1 else 0
                    val constVideoScaleMode =
                        if (resizeMode == AspectRatioFrameLayout.RESIZE_MODE_FIT) 0 else 1
                    val epsilon = if (context.isTablet()) {
                        if (isFullscreen) context.resources.getDimensionPixelSize(R.dimen._61sdp)else context.resources.getDimensionPixelSize(R.dimen._48sdp)
                    } else {
                        if (isFullscreen) context.resources.getDimensionPixelSize(R.dimen._105sdp) else context.resources.getDimensionPixelSize(R.dimen._83sdp)
                    }
                    val newSubtitlePaddingBottom = (
                            max(0f, (deviceRatio - videoRatio))
                                    * epsilon
                                    * constFullScreen
                                    * constLandscape
                                    * constVideoScaleMode
                            ).toInt()

                    getSubtitleView()?.afterMeasuredAutoRemove {
                        val situationWarningView = currentViewSituationalWarning()
                        val subtitleHeight = getSubtitleView()?.height?.toFloat()

                        if (subtitleHeight != null) {
                            // Update Subtitle Bottom Padding Fraction
                            if (situationWarningView != null && isSituationWarningShown) {
                                val situationHeight = situationWarningView.height.toFloat()
                                val situationPaddingFraction = situationHeight / subtitleHeight
                                Logger.d("PlayerUtils -> Subtitle -> paddingBottom = $situationPaddingFraction | subtitleHeight = $subtitleHeight")
                                setSubtitleBottomPaddingFraction(bottomPaddingFraction = situationPaddingFraction)
                            } else {
                                val paddingBottomFraction = ((max(newSubtitlePaddingBottom, defaultPaddingSub)) * 1f) / subtitleHeight
                                Logger.d("PlayerUtils -> Subtitle -> paddingBottom = $paddingBottomFraction | subtitleHeight = $subtitleHeight")
                                setSubtitleBottomPaddingFraction(bottomPaddingFraction = paddingBottomFraction + getDefaultPadding(context, isFullscreen, isLandscape))
                            }

                            // Update Subtitle Fractional Text Size
                            val fractionalTextSize = getSubtitleFractionalTextSize(fragmentActivity, it, isFullscreen, isLandscape, resizeMode)
                            setFractionalTextSize(fractionalTextSize)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun getDefaultPadding(context: Context, isFullscreen: Boolean, isLandscape: Boolean): Float {
        return if (context.isTablet()) {
            if (isFullscreen && isLandscape) PLAYER_SUBTITLE_PADDING_BOTTOM_FRACTION_DEFAULT_MARGIN_TABLET_FULL_LAND else PLAYER_SUBTITLE_PADDING_BOTTOM_FRACTION_DEFAULT_MARGIN_TABLET_NORMAL
        } else {
            if (isFullscreen) PLAYER_SUBTITLE_PADDING_BOTTOM_FRACTION_DEFAULT_MARGIN_MOBILE_LANDSCAPE else PLAYER_SUBTITLE_PADDING_BOTTOM_FRACTION_DEFAULT_MARGIN_MOBILE_PORTRAIT
        }
    }


    private fun ExoPlayerView.calculateOriginalSubtitleHeightFullscreen(
        fragmentActivity: FragmentActivity?,
        player: ExoPlayer,
        isLandscapeMode: Boolean,
        isFullscreen: Boolean,
        defaultValue: Float
    ): Float {
        if (isFullscreen) {
            if (isLandscapeMode) {
                /**
                 * Landscape fullscreen -> Origin subtitle height always equal to device height
                 * So, originalSubtitleHeight is device height
                 */
                val deviceHeight = getPhysicalScreenHeightWithOrientation(MainApplication.INSTANCE.applicationContext.applicationContext, MainApplication.INSTANCE.applicationContext.resources.configuration.orientation)
                return deviceHeight.toFloat()
            } else {
                /**
                 * Portrait fullscreen -> Origin subtitle width always equal to device width
                 * So, calculate originalSubtitleHeight is correct
                 */
                val subtitleRatio = player.videoSize.height.toFloat() / player.videoSize.width.toFloat()
                val deviceWidth = getPhysicalScreenWidthWithOrientation(MainApplication.INSTANCE.applicationContext.applicationContext, MainApplication.INSTANCE.applicationContext.resources.configuration.orientation)
                val originalSubtitleHeight = deviceWidth * subtitleRatio
                Logger.d("PlayerUtils -> Subtitle -> SubtitleRatio = $subtitleRatio | deviceWidth = $deviceWidth videoSizeWidth = ${player.videoSize.width} | videosSizeHeight = ${player.videoSize.height} | -> originalSubtitleHeight = $originalSubtitleHeight")
                return originalSubtitleHeight
            }
        } else {
            return defaultValue
        }
    }

    // endregion

    // region Situation Warning
    fun View.updateSituationWarningView(
        player: IPlayer?,
        fragmentActivity: FragmentActivity?,
        situationView: View?,
        isFullscreen: Boolean,
        isLandscape: Boolean,
        resizeMode: Int
    ) {
        try {
            when (situationView) {
                is TextView -> {
                    // Update Padding Bottom
                    player?.internalPlayer().let {
                        if (it is ExoPlayer) {
                            val defaultPaddingSituation = if(context.isTablet()) {
                                if (isFullscreen && isLandscape) context.resources.getDimensionPixelSize(R.dimen._9sdp) else context.resources.getDimensionPixelSize(R.dimen._5sdp)
                            } else {
                                if (isFullscreen) context.resources.getDimensionPixelSize(R.dimen._9sdp) else context.resources.getDimensionPixelSize(R.dimen._3sdp)
                            }
                            val w = getPhysicalScreenWidthWithOrientation(MainApplication.INSTANCE.applicationContext.applicationContext, MainApplication.INSTANCE.applicationContext.resources.configuration.orientation)
                            val h = getPhysicalScreenHeightWithOrientation(MainApplication.INSTANCE.applicationContext.applicationContext, MainApplication.INSTANCE.applicationContext.resources.configuration.orientation)
                            val deviceRatio = w.toFloat() / h.toFloat()
                            val videoRatio = (it.videoSize.width.toFloat() / it.videoSize.height.toFloat())
                            val constFullScreen = if (isFullscreen) 1 else 0
                            val constLandscape = if (isLandscape) 1 else 0
                            val constVideoScaleMode =
                                if (resizeMode == AspectRatioFrameLayout.RESIZE_MODE_FIT || resizeMode == AspectRatioFrameLayout.RESIZE_MODE_FILL) 0 else 1
                            val epsilon = if (context.isTablet()) {
                                if (isFullscreen) context.resources.getDimensionPixelSize(R.dimen._65sdp) else context.resources.getDimensionPixelSize(R.dimen._44sdp)
                            } else {
                                if (isFullscreen) context.resources.getDimensionPixelSize(R.dimen._100sdp) else context.resources.getDimensionPixelSize(R.dimen._75sdp)
                            }

                            val newSituationPaddingBottom = (
                                    max(0f, (deviceRatio - videoRatio))
                                            * epsilon
                                            * constFullScreen
                                            * constLandscape
                                            * constVideoScaleMode
                                    ).toInt()
                            situationView.updatePadding(top = context.resources.getDimensionPixelSize(R.dimen._2sdp), bottom = max(newSituationPaddingBottom, defaultPaddingSituation))
                        }
                    }

                    //
                    val textSize = if (context.isTablet()) {
                        if (isFullscreen && isLandscape) context.resources.getDimensionPixelSize(R.dimen.player_situation_warning_size_fullscreen) else context.resources.getDimensionPixelSize(R.dimen.player_situation_warning_size)
                    } else {
                        if (isFullscreen) context.resources.getDimensionPixelSize(R.dimen.player_situation_warning_size_fullscreen) else context.resources.getDimensionPixelSize(R.dimen.player_situation_warning_size)
                    }
                    situationView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize.toFloat())
                    //

                    // Update margins
                    val parentView = situationView.parent as? ConstraintLayout
                    parentView?.let {
                        val margin = if (context.isTablet()) {
                            if (isFullscreen && isLandscape) context.resources.getDimensionPixelSize(R.dimen.player_situation_warning_margin_horizontal_landscape) else context.resources.getDimensionPixelSize(R.dimen.player_situation_warning_margin_horizontal_portrait)
                        } else {
                            if (isFullscreen) context.resources.getDimensionPixelSize(R.dimen.player_situation_warning_margin_horizontal_landscape) else context.resources.getDimensionPixelSize(R.dimen.player_situation_warning_margin_horizontal_portrait)
                        }
                        ConstraintSet().apply {
                            clone(it)
                            connect(situationView.id, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START, margin)
                            connect(situationView.id, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END, margin)
                            connect(situationView.id, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, 0)
                        }.applyTo(it)
                    }
                    //
                    //

                    // Update Max Width
//                    situationView.maxWidth = 40// binding.root.context.getDisplayWidth() / 2 - 100

                }
                else -> {}
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    // endregion


    // region DRM & H265

    fun buildPlayerDrmCallback(viewModel: ViewModel): IPlayer.DrmCallback {
        Log.d("DRMCallback", "Build DRM Callback")
        return object : IPlayer.DrmCallback {
            override fun onDrmCallback(keyOffId: ByteArray?) {
                when(viewModel) {
                    is LiveTVDetailViewModel -> {
                        val uid = getLiveDrmUid(
                            viewModel.getChannelId()
                        )
                        Log.d("DRMCallback Live", "Save DRM Key: $uid: ${keyOffId?.toBase64Default()}")
                        viewModel.dispatchIntent(
                            LiveTVDetailViewModel.LiveTVDetailIntent.SaveDRMKey(
                            drmKey = DrmKey(
                                uid = uid,
                                drmKeyBase64 = keyOffId?.toBase64Default() ?: "",
                            )
                        ))
                    }
                    is VodDetailViewModel -> {
                        val uid = getVodDrmUid(
                            viewModel.getId(),
                            viewModel.currentEpisode()?.id ?: ""
                        )
                        Log.d("DRMCallback VOD", "Save DRM Key: $uid: ${keyOffId?.toBase64Default()}")
                        viewModel.dispatchIntent(
                            VodDetailViewModel.VodDetailIntent.SaveDrmKeyOffline(
                                drmKey = DrmKey(
                                    uid = uid,
                                    drmKeyBase64 = keyOffId?.toBase64Default() ?: "",
                                )
                            )
                        )
                    }
                    is PremiereViewModel -> {
                        val uid = getEventDrmUid(viewModel.getDataDetail())
                        Log.d("DRMCallback Event", "Save DRM Key: $uid: ${keyOffId?.toBase64Default()}")
                        viewModel.dispatchIntent(
                            PremiereViewModel.PremiereIntent.SaveDRMKey(
                                drmKey = DrmKey(
                                    uid = uid,
                                    drmKeyBase64 = keyOffId?.toBase64Default() ?: "",
                                )
                            )
                        )
                    }
                    else -> {

                    }

                }
            }
        }
    }

    private val userId : String get() = MainApplication.INSTANCE.sharedPreferences.userId()


    fun getVodDrmUid(vodId: String?, episodeId: String?) : String {
        return "${userId}_${vodId ?: ""}_${episodeId ?: ""}"
    }

    fun getLiveDrmUid(channelId: String?) : String {
        return "${userId}_${channelId ?: ""}"
    }

    fun getEventDrmUid(eventDetail: com.xhbadxx.projects.module.domain.entity.fplay.premier.Details?): String {
        return eventDetail?.let { data ->
            getEventDrmUid(data.isPremier == "1", data.id, data.episodeId, data.id)
        } ?: ""
    }

    fun getEventDrmUid(isPremiere: Boolean, vodId: String = "", episodeId: String = "", channelId: String = "") : String {
        return if (isPremiere) {
            getVodDrmUid(vodId, episodeId)
        } else {
            getLiveDrmUid(channelId)
        }
    }

    // H265
    fun isPreventH265DrmNeedToReset(sharedPreferences: SharedPreferences) : Boolean {
        val lifetimePreventH265Ms = sharedPreferences.lifeTimeCachePreventH265InHour() * 60L * 60L * 1000L
        return isPreventH265Expired(
            lastSaveTime = sharedPreferences.lastTimeCachePreventH265DRM(),
            lifetimePreventH265Ms = lifetimePreventH265Ms
        )
    }

    fun isPreventH265NoDrmNeedToReset(sharedPreferences: SharedPreferences): Boolean {
        val lifetimePreventH265Ms =
            sharedPreferences.lifeTimeCachePreventH265InHour() * 60L * 60L * 1000L
        return isPreventH265Expired(
            lastSaveTime = sharedPreferences.lastTimeCachePreventH265NoDRM(),
            lifetimePreventH265Ms = lifetimePreventH265Ms
        )
    }

    fun resetPreventH265Drm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventH265DRM(false)
    }

    fun resetPreventH265NoDrm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventH265NoDRM(false)
    }

    fun preventH265Drm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventH265DRM(true)
        sharedPreferences.saveLastTimeCachePreventH265DRM(System.currentTimeMillis())
    }

    fun preventH265NoDrm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventH265NoDRM(true)
        sharedPreferences.saveLastTimeCachePreventH265NoDRM(System.currentTimeMillis())
    }

    private fun isPreventH265Expired(lastSaveTime: Long, lifetimePreventH265Ms: Long) : Boolean {
        return if(lifetimePreventH265Ms < 0) false
        else {
            val currentTimeMs = System.currentTimeMillis()
            currentTimeMs - lastSaveTime > lifetimePreventH265Ms
        }
    }

    /**
     * Get H265 local config for logging
     * @param sharedPreferences
     * @return
     * h265_drm: pl: true - tl: 1634025600000 - lc: 24
     * pl: prevent h265 drm
     * tl: last time cache prevent h265 in ms
     * lc: life time cache prevent h265 in hour
     */
    fun getH265LocalConfig(sharedPreferences: SharedPreferences) : String {
        return "(H265_L:${sharedPreferences.isPreventH265NoDRM()}, H265_S:${sharedPreferences.isPreventH265ByBlackList()}, " +
                "H265_DRM_L:${sharedPreferences.isPreventH265DRM()}, H265_DRM_S:${sharedPreferences.isPreventH265ByBlackListDRM()}, " +
                "H265_TL:${sharedPreferences.lastTimeCachePreventH265NoDRM()}, H265_DRM_TL:${sharedPreferences.lastTimeCachePreventH265DRM()}, " +
                "H265_LC:${sharedPreferences.lifeTimeCachePreventH265InHour()})"
    }

    // H265 (HDR, HDR 10Plus)
    fun isPreventH265HdrDrmNeedToReset(sharedPreferences: SharedPreferences) : Boolean {
        val lifetimePreventH265HdrMs = sharedPreferences.lifeTimeCachePreventH265HdrInHour() * 60L * 60L * 1000L
        return isPreventH265HdrExpired(
            lastSaveTime = sharedPreferences.lastTimeCachePreventH265HdrDRM(),
            lifetimePreventH265HdrMs = lifetimePreventH265HdrMs
        )
    }

    fun isPreventH265HdrNoDrmNeedToReset(sharedPreferences: SharedPreferences): Boolean {
        val lifetimePreventH265HdrMs =
            sharedPreferences.lifeTimeCachePreventH265HdrInHour() * 60L * 60L * 1000L
        return isPreventH265HdrExpired(
            lastSaveTime = sharedPreferences.lastTimeCachePreventH265HdrNoDRM(),
            lifetimePreventH265HdrMs = lifetimePreventH265HdrMs
        )
    }

    fun resetPreventH265HdrDrm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventH265HdrDRM(false)
    }

    fun resetPreventH265HdrNoDrm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventH265HdrNoDRM(false)
    }

    fun preventH265HdrDrm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventH265HdrDRM(true)
        sharedPreferences.saveLastTimeCachePreventH265HdrDRM(System.currentTimeMillis())
    }

    fun preventH265HdrNoDrm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventH265HdrNoDRM(true)
        sharedPreferences.saveLastTimeCachePreventH265HdrNoDRM(System.currentTimeMillis())
    }

    private fun isPreventH265HdrExpired(lastSaveTime: Long, lifetimePreventH265HdrMs: Long) : Boolean {
        return if(lifetimePreventH265HdrMs < 0) false
        else {
            val currentTimeMs = System.currentTimeMillis()
            currentTimeMs - lastSaveTime > lifetimePreventH265HdrMs
        }
    }

    fun getH265HdrLocalConfig(sharedPreferences: SharedPreferences) : String {
        return "(H265_HDR_L:${sharedPreferences.isPreventH265HdrNoDRM()}, H265_HDR_S:${sharedPreferences.isPreventH265HdrByBlackList()}, " +
                "H265_HDR_DRM_L:${sharedPreferences.isPreventH265HdrDRM()}, H265_HDR_DRM_S:${sharedPreferences.isPreventH265HdrByBlackListDRM()}, " +
                "H265_HDR_TL:${sharedPreferences.lastTimeCachePreventH265HdrNoDRM()}, H265_HDR_DRM_TL:${sharedPreferences.lastTimeCachePreventH265HdrDRM()}, " +
                "H265_HDR_LC:${sharedPreferences.lifeTimeCachePreventH265HdrInHour()})"
    }


    // Dolby Vision
    fun isPreventDolbyVisionDrmNeedToReset(sharedPreferences: SharedPreferences) : Boolean {
        val lifetimePreventDolbyVisionMs = sharedPreferences.lifeTimeCachePreventDolbyVisionInHour() * 60L * 60L * 1000L
        return isPreventDolbyVisionExpired(
            lastSaveTime = sharedPreferences.lastTimeCachePreventDolbyVisionDRM(),
            lifetimePreventDolbyVisionMs = lifetimePreventDolbyVisionMs
        )
    }

    fun isPreventDolbyVisionNoDrmNeedToReset(sharedPreferences: SharedPreferences): Boolean {
        val lifetimePreventDolbyVisionMs =
            sharedPreferences.lifeTimeCachePreventDolbyVisionInHour() * 60L * 60L * 1000L
        return isPreventDolbyVisionExpired(
            lastSaveTime = sharedPreferences.lastTimeCachePreventDolbyVisionNoDRM(),
            lifetimePreventDolbyVisionMs = lifetimePreventDolbyVisionMs
        )
    }

    fun resetPreventDolbyVisionDrm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventDolbyVisionDRM(false)
    }

    fun resetPreventDolbyVisionNoDrm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventDolbyVisionNoDRM(false)
    }

    fun preventDolbyVisionDrm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventDolbyVisionDRM(true)
        sharedPreferences.saveLastTimeCachePreventDolbyVisionDRM(System.currentTimeMillis())
    }

    fun preventDolbyVisionNoDrm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventDolbyVisionNoDRM(true)
        sharedPreferences.saveLastTimeCachePreventDolbyVisionNoDRM(System.currentTimeMillis())
    }

    private fun isPreventDolbyVisionExpired(lastSaveTime: Long, lifetimePreventDolbyVisionMs: Long) : Boolean {
        return if(lifetimePreventDolbyVisionMs < 0) false
        else {
            val currentTimeMs = System.currentTimeMillis()
            currentTimeMs - lastSaveTime > lifetimePreventDolbyVisionMs
        }
    }

    fun getDolbyVisionLocalConfig(sharedPreferences: SharedPreferences) : String {
        return "(DV_L:${sharedPreferences.isPreventDolbyVisionNoDRM()}, DV_S:${sharedPreferences.isPreventDolbyVisionByBlackList()}, " +
                "DV_DRM_L:${sharedPreferences.isPreventDolbyVisionDRM()}, DV_DRM_S:${sharedPreferences.isPreventDolbyVisionByBlackListDRM()}, " +
                "DV_TL:${sharedPreferences.lastTimeCachePreventDolbyVisionNoDRM()}, DV_DRM_TL:${sharedPreferences.lastTimeCachePreventDolbyVisionDRM()}, " +
                "DV_LC:${sharedPreferences.lifeTimeCachePreventDolbyVisionInHour()})"
    }


    // AV1
    fun isPreventAV1DrmNeedToReset(sharedPreferences: SharedPreferences) : Boolean {
        val lifetimePreventAV1Ms = sharedPreferences.lifeTimeCachePreventAV1InHour() * 60L * 60L * 1000L
        return isPreventAV1Expired(
            lastSaveTime = sharedPreferences.lastTimeCachePreventAV1DRM(),
            lifetimePreventAV1Ms = lifetimePreventAV1Ms
        )
    }

    fun isPreventAV1NoDrmNeedToReset(sharedPreferences: SharedPreferences): Boolean {
        val lifetimePreventAV1Ms =
            sharedPreferences.lifeTimeCachePreventAV1InHour() * 60L * 60L * 1000L
        return isPreventAV1Expired(
            lastSaveTime = sharedPreferences.lastTimeCachePreventAV1NoDRM(),
            lifetimePreventAV1Ms = lifetimePreventAV1Ms
        )
    }

    fun resetPreventAV1Drm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventAV1DRM(false)
    }

    fun resetPreventAV1NoDrm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventAV1NoDRM(false)
    }

    fun preventAV1Drm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventAV1DRM(true)
        sharedPreferences.saveLastTimeCachePreventAV1DRM(System.currentTimeMillis())
    }

    fun preventAV1NoDrm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventAV1NoDRM(true)
        sharedPreferences.saveLastTimeCachePreventAV1NoDRM(System.currentTimeMillis())
    }

    private fun isPreventAV1Expired(lastSaveTime: Long, lifetimePreventAV1Ms: Long) : Boolean {
        return if(lifetimePreventAV1Ms < 0) false
        else {
            val currentTimeMs = System.currentTimeMillis()
            currentTimeMs - lastSaveTime > lifetimePreventAV1Ms
        }
    }

    fun getAV1LocalConfig(sharedPreferences: SharedPreferences) : String {
        return "(AV1_L:${sharedPreferences.isPreventAV1NoDRM()}, AV1_S:${sharedPreferences.isPreventAV1ByBlackList()}, " +
                "AV1_DRM_L:${sharedPreferences.isPreventAV1DRM()}, AV1_DRM_S:${sharedPreferences.isPreventAV1ByBlackListDRM()}, " +
                "AV1_TL:${sharedPreferences.lastTimeCachePreventAV1NoDRM()}, AV1_DRM_TL:${sharedPreferences.lastTimeCachePreventAV1DRM()}, " +
                "AV1_LC:${sharedPreferences.lifeTimeCachePreventAV1InHour()})"
    }

    // VP9
    fun isPreventVP9DrmNeedToReset(sharedPreferences: SharedPreferences) : Boolean {
        val lifetimePreventVP9Ms = sharedPreferences.lifeTimeCachePreventVP9InHour() * 60L * 60L * 1000L
        return isPreventVP9Expired(
            lastSaveTime = sharedPreferences.lastTimeCachePreventVP9DRM(),
            lifetimePreventVP9Ms = lifetimePreventVP9Ms
        )
    }

    fun isPreventVP9NoDrmNeedToReset(sharedPreferences: SharedPreferences): Boolean {
        val lifetimePreventVP9Ms =
            sharedPreferences.lifeTimeCachePreventVP9InHour() * 60L * 60L * 1000L
        return isPreventVP9Expired(
            lastSaveTime = sharedPreferences.lastTimeCachePreventVP9NoDRM(),
            lifetimePreventVP9Ms = lifetimePreventVP9Ms
        )
    }

    fun resetPreventVP9Drm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventVP9DRM(false)
    }

    fun resetPreventVP9NoDrm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventVP9NoDRM(false)
    }

    fun preventVP9Drm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventVP9DRM(true)
        sharedPreferences.saveLastTimeCachePreventVP9DRM(System.currentTimeMillis())
    }

    fun preventVP9NoDrm(sharedPreferences: SharedPreferences) {
        sharedPreferences.saveIsPreventVP9NoDRM(true)
        sharedPreferences.saveLastTimeCachePreventVP9NoDRM(System.currentTimeMillis())
    }

    private fun isPreventVP9Expired(lastSaveTime: Long, lifetimePreventVP9Ms: Long) : Boolean {
        return if(lifetimePreventVP9Ms < 0) false
        else {
            val currentTimeMs = System.currentTimeMillis()
            currentTimeMs - lastSaveTime > lifetimePreventVP9Ms
        }
    }

    fun getVP9LocalConfig(sharedPreferences: SharedPreferences) : String {
        return "(VP9_L:${sharedPreferences.isPreventVP9NoDRM()}, VP9_S:${sharedPreferences.isPreventVP9ByBlackList()}, " +
                "VP9_DRM_L:${sharedPreferences.isPreventVP9DRM()}, VP9_DRM_S:${sharedPreferences.isPreventVP9ByBlackListDRM()}, " +
                "VP9_TL:${sharedPreferences.lastTimeCachePreventVP9NoDRM()}, VP9_DRM_TL:${sharedPreferences.lastTimeCachePreventVP9DRM()}, " +
                "VP9_LC:${sharedPreferences.lifeTimeCachePreventVP9InHour()})"
    }

    // endregion

    //region Background Audio, PiP
    fun checkRulePlayBAAndBAForPiP(isApiEnableBackgroundAudio: Boolean, sharedPreferences: SharedPreferences): Boolean {
        return isApiEnableBackgroundAudio || PlayerPiPHelper.checkRulePlayBackgroundAudioPiP(sharedPreferences)
    }

    fun checkRulePlayPiP(sharedPreferences: SharedPreferences): Boolean {
        return PlayerPiPHelper.userPackagePlanSupportPictureInPicture(sharedPreferences) && PlayerPiPHelper.checkOsVersionSupportPictureInPicture()
    }
    //endregion

    //region Player Build Request
    fun IPlayer.Request.isTheSameUrlRequest(isDrm: Boolean, stream: Stream) : Boolean {
        return if (isDrm) {
            url.dolbyVisionUrl == stream.urlDashDrmDolbyVision
                    && url.h265HDR10PlusUrl == stream.urlDashDrmH265Hdr10Plus
                    && url.h265HDR10Url == stream.urlDashDrmH265Hdr
                    && url.h265HlgUrl == stream.urlDashDrmH265Hlg
                    && url.av1Url == stream.urlDashDrmAv1
                    && url.vp9Url == stream.urlDashDrmVp9
                    && (url.url == stream.urlDash || url.url == if (stream.urlDashNoDrm.isNotEmpty())
                            stream.urlDashNoDrm
                        else if (stream.urlSub.isNotEmpty())
                            stream.urlSub
                        else
                            stream.url)
        } else {
            url.dolbyVisionUrl == stream.urlDashDolbyVision
                    && url.h265HDR10PlusUrl == stream.urlDashH265Hdr10Plus
                    && url.h265HDR10Url == stream.urlDashH265Hdr
                    && url.h265HlgUrl == stream.urlDashH265Hlg
                    && url.av1Url == stream.urlDashAv1
                    && url.vp9Url == stream.urlDashVp9
                    && (url.url == if (stream.urlDashNoDrm.isNotEmpty())
                        stream.urlDashNoDrm
                    else if (stream.urlSub.isNotEmpty())
                        stream.urlSub
                    else
                        stream.url)
        }
    }

    fun IPlayer.Request.Url.isTheSame(other: IPlayer.Request.Url) : Boolean {
        return this.h265Url == other.h265Url
                && this.h265HlgUrl == other.h265HlgUrl
                && this.h265HDR10Url == other.h265HDR10Url
                && this.h265HDR10PlusUrl == other.h265HDR10PlusUrl
                && this.url == other.url
                && this.av1Url == other.av1Url
                && this.vp9Url == other.vp9Url
                && this.dolbyVisionUrl == other.dolbyVisionUrl
    }

    fun String?.playerAudioModeName(): IPlayer.AudioTrackMappingNameType {
        return this?.let {
            when(it){
                "0" -> IPlayer.AudioTrackMappingNameType.API
                "1" -> IPlayer.AudioTrackMappingNameType.TRACK_LABEL
                "2" -> IPlayer.AudioTrackMappingNameType.MANIFEST
                else -> IPlayer.AudioTrackMappingNameType.DEFAULT
            }
        } ?: IPlayer.AudioTrackMappingNameType.DEFAULT
    }

    fun getExtraForUrlMode(sharedPreferences: SharedPreferences): String {
        return "${getH265LocalConfig(sharedPreferences)}${getH265HdrLocalConfig(sharedPreferences)}${
            getAV1LocalConfig(
                sharedPreferences
            )
        }${getVP9LocalConfig(sharedPreferences)}${getDolbyVisionLocalConfig(sharedPreferences)}"
    }
    //endregion

    fun generateDefaultPlayerBottomControlSettingList(context: Context): List<PlayerBottomControlData> {
        val list = mutableListOf<PlayerBottomControlData>()
        list.apply {
            add(
                PlayerBottomControlData(
                    _id = PlayerConstants.PlayerBottomControlType.ID_SUBTITLE_SETTING,
                    iconRes = R.drawable.player_ic_subtitle,
                    value = "Subtitle",
                    isEnable = false,
                    title = context.getString(R.string.player_subtitle),
                )
            )
            add(
                PlayerBottomControlData(
                    _id = PlayerConstants.PlayerBottomControlType.ID_AUDIO_SETTING,
                    iconRes = R.drawable.player_ic_audio_control,
                    value = "Audio",
                    isEnable = false,
                    title = context.getString(R.string.player_track),
                )
            )
            add(
                PlayerBottomControlData(
                    _id = PlayerConstants.PlayerBottomControlType.ID_VIDEO_SETTING,
                    iconRes = R.drawable.player_ic_video_quality,
                    value = "1080p",
                    isEnable = false,
                    title = context.getString(R.string.quality),
                )
            )
            add(
                PlayerBottomControlData(
                    _id = PlayerConstants.PlayerBottomControlType.ID_PLAY_SPEED_SETTING,
                    iconRes = R.drawable.player_ic_speed,
                    value = "1.0x",
                    isEnable = false,
                    title = context.getString(R.string.player_speed),
                )
            )

            add(
                PlayerBottomControlData(
                    _id = PlayerConstants.PlayerBottomControlType.ID_PLAYLIST_SETTING,
                    iconRes = R.drawable.player_ic_playlist,
                    value = context.getString(R.string.player_playlist),
                    isEnable = false,
                    title = context.getString(R.string.player_playlist),
                )
            )

            add(
                PlayerBottomControlData(
                    _id = PlayerConstants.PlayerBottomControlType.ID_CHANNEL_LIST_SETTING,
                    iconRes = R.drawable.player_ic_channel_list,
                    value = context.getString(R.string.player_channel_list),
                    isEnable = false,
                    title = context.getString(R.string.player_channel_list),
                )
            )

            add(
                PlayerBottomControlData(
                    _id = PlayerConstants.PlayerBottomControlType.ID_EPG_SETTING,
                    iconRes = R.drawable.player_ic_epg,
                    value = context.getString(R.string.player_epg),
                    isEnable = false,
                    title = context.getString(R.string.player_epg),
                )
            )
        }
        return list
    }
}

/**
private fun codecLevel(mimeType: String): Map<Int, String> =
    when(mimeType){
        MimeTypes.VIDEO_H264 -> avcLevels
        MimeTypes.VIDEO_H265 -> hevcLevels
        MimeTypes.VIDEO_DOLBY_VISION -> dolbyVisionLevels
        else -> emptyMap()
    }

private fun codecProfile(mimeType: String): Map<Int, String> =
    when(mimeType){
        MimeTypes.VIDEO_H264 -> avcProfiles
        MimeTypes.VIDEO_H265 -> hevcProfiles
        MimeTypes.VIDEO_DOLBY_VISION -> dolbyVisionProfiles
        else -> emptyMap()
    }

private fun Map<Int, String>.getSafeValue(key: Int): String {
    return try {
        this.getValue(key)
    }catch (_: Exception){
        ""
    }
}

private fun informationCodec(mimeType: String, levelNumber: Int, profileNumber: Int) =
    "${codecLevel(mimeType = mimeType).getSafeValue(key = levelNumber)}-${codecProfile(mimeType = mimeType).getSafeValue(key = profileNumber)}"
//endregion  Methods Private

//region variables
private val avcLevels = mapOf(
    1 to "AVCLevel1",
    4 to "AVCLevel11",
    8 to "AVCLevel12",
    16 to "AVCLevel13",
    2 to "AVCLevel1b",
    32 to "AVCLevel2",
    64 to "AVCLevel21",
    128 to "AVCLevel22",
    256 to "AVCLevel3",
    512 to "AVCLevel31",
    1024 to "AVCLevel32",
    2048 to "AVCLevel4",
    4096 to "AVCLevel41",
    8192 to "AVCLevel42",
    16384 to "AVCLevel5",
    32768 to "AVCLevel51",
    65536 to "AVCLevel52",
    131072 to "AVCLevel6",
    262144 to "AVCLevel61",
    524288 to "AVCLevel62"
)

private val avcProfiles = mapOf(
    1 to "AVCProfileBaseline",
    65536 to "AVCProfileConstrainedBaseline",
    524288 to "AVCProfileConstrainedHigh",
    4 to "AVCProfileExtended",
    8 to "AVCProfileHigh",
    16 to "AVCProfileHigh10",
    32 to "AVCProfileHigh422",
    64 to "AVCProfileHigh444",
    2 to "AVCProfileMain"
)

private val dolbyVisionLevels = mapOf(
    4 to "DolbyVisionLevelFhd24",
    8 to "DolbyVisionLevelFhd30",
    16 to "DolbyVisionLevelFhd60",
    1 to "DolbyVisionLevelHd24",
    2 to "DolbyVisionLevelHd30",
    32 to "DolbyVisionLevelUhd24",
    64 to "DolbyVisionLevelUhd30",
    128 to "DolbyVisionLevelUhd48",
    256 to "DolbyVisionLevelUhd60"
)

private val dolbyVisionProfiles = mapOf(
    1024 to "DolbyVisionProfileDvav110",
    2 to "DolbyVisionProfileDvavPen",
    1 to "DolbyVisionProfileDvavPer",
    512 to "DolbyVisionProfileDvavSe",
    8 to "DolbyVisionProfileDvheDen",
    4 to "DolbyVisionProfileDvheDer",
    128 to "DolbyVisionProfileDvheDtb",
    64 to "DolbyVisionProfileDvheDth",
    16 to "DolbyVisionProfileDvheDtr",
    256 to "DolbyVisionProfileDvheSt",
    32 to "DolbyVisionProfileDvheStn"
)

private val hevcLevels = mapOf(
    1 to "HEVCMainTierLevel1",
    16 to "HEVCMainTierLevel21",
    4 to "HEVCMainTierLevel2",
    64 to "HEVCMainTierLevel3",
    256 to "HEVCMainTierLevel31",
    1024 to "HEVCMainTierLevel4",
    4096 to "HEVCMainTierLevel41",
    16384 to "HEVCMainTierLevel5",
    65536 to "HEVCMainTierLevel51",
    262144 to "HEVCMainTierLevel52",
    1048576 to "HEVCMainTierLevel6",
    4194304 to "HEVCMainTierLevel61",
    16777216 to "HEVCMainTierLevel62",
    2 to "HEVCHighTierLevel1",
    32 to "HEVCHighTierLevel21",
    8 to "HEVCHighTierLevel2",
    128 to "HEVCHighTierLevel3",
    512 to "HEVCHighTierLevel31",
    2048 to "HEVCHighTierLevel4",
    8192 to "HEVCHighTierLevel41",
    32768 to "HEVCHighTierLevel5",
    131072 to "HEVCHighTierLevel51",
    524288 to "HEVCHighTierLevel52",
    2097152 to "HEVCHighTierLevel6",
    8388608 to "HEVCHighTierLevel61",
    33554432 to "HEVCHighTierLevel62"
)

private val hevcProfiles = mapOf(
    1 to "HEVCProfileMain",
    2 to "HEVCProfileMain10",
    4096 to "HEVCProfileMain10HDR10",
    8192 to "HEVCProfileMain10HDR10Plus",
    4 to "HEVCProfileMainStill"
)

 */