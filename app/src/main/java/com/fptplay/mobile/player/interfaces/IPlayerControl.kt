package com.fptplay.mobile.player.interfaces

import com.fptplay.mobile.player.model.PlayerBottomControlData
import com.fptplay.mobile.player.utils.PlayerConstants
import com.fptplay.mobile.player.utils.PlayerDebugViewData
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details

interface IPlayerControl {
    fun onBack()
    fun onPlayToggle()
    fun onNext()
    fun onPrevious()
    fun onPlayItem(episode: Details.Episode)
    fun onSeekNext()
    fun onSeekPrevious()
    fun onLockToggle()
    fun onMulticam()
    fun onCast()
    fun onShare()
    fun onExpand()
    fun onMore(moreData: List<PlayerBottomControlData>)
    fun onLiveChatClick()
    fun onSportInteractiveClick()
    fun onFullScreenToggle()
    fun onAudioAndSubtitle()
    fun onOpenAudioSelection()
    fun onOpenSubtitleSelection()
    fun onPlayerSpeed()
    fun onSetting()
    fun onSeek(position: Long)
    fun onSkipIntro()
    fun onWatchCredit(isSendEventToRemote: Boolean = true)
    fun onSkipCredit()
    fun onRecommendClose()
    fun onRecommendWatchNow(related: Details.RelatedVod?)
    fun onRecommendPlayTrailer(related: Details.RelatedVod?)
    fun onDebugViewStateChange()
    fun onReport()
    fun onClickBuyPackage()
    fun hideBuyPackageGuide()
    fun onOpenFrameOverlay(type: PlayerConstants.PlayerFrameOverlayType?)
    fun onCloseFrameOverlay(type: PlayerConstants.PlayerFrameOverlayType?)
}