<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cv_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/margin_landing_page_button"
        app:cardCornerRadius="@dimen/landing_page_button_radius"
        app:cardElevation="@dimen/landing_page_button_elevation"
        app:cardBackgroundColor="#00000000">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_btn"
            android:layout_width="@dimen/landing_page__button_item__width"
            android:layout_height="@dimen/landing_page__button_item__height"
            android:background="@drawable/landing_page_button_background">
            <TextView
                android:id="@+id/tv_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                tools:text="@string/account"
                android:layout_gravity="center"
                android:enabled="true"
                android:includeFontPadding="false"
                android:background="@color/transparent"
                android:fontFamily="@font/sf_pro_display_bold"
                android:textColor="@color/landing_page_button_text_selector"
                android:textSize="@dimen/landing_page__button_item__text_size"
                android:letterSpacing="0.02"
                android:textAllCaps="false"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>
</FrameLayout>
