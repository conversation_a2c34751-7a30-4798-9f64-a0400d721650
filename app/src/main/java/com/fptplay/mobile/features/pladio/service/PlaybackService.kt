package com.fptplay.mobile.features.pladio.service

import android.annotation.SuppressLint
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences.OnSharedPreferenceChangeListener
import android.content.pm.ServiceInfo
import android.os.Binder
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.HandlerThread
import android.os.IBinder
import android.os.Looper
import androidx.annotation.OptIn
import androidx.core.app.ServiceCompat
import androidx.core.content.getSystemService
import androidx.core.os.bundleOf
import androidx.lifecycle.Observer
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.media3.common.Player
import androidx.media3.common.Player.COMMAND_CHANGE_MEDIA_ITEMS
import androidx.media3.common.Player.COMMAND_GET_CURRENT_MEDIA_ITEM
import androidx.media3.common.Player.COMMAND_GET_METADATA
import androidx.media3.common.Player.COMMAND_GET_TIMELINE
import androidx.media3.common.Player.COMMAND_PLAY_PAUSE
import androidx.media3.common.Player.COMMAND_SEEK_BACK
import androidx.media3.common.Player.COMMAND_SEEK_FORWARD
import androidx.media3.common.Player.COMMAND_SEEK_IN_CURRENT_MEDIA_ITEM
import androidx.media3.common.Player.COMMAND_SEEK_TO_DEFAULT_POSITION
import androidx.media3.common.Player.COMMAND_SEEK_TO_MEDIA_ITEM
import androidx.media3.common.Player.COMMAND_SEEK_TO_NEXT
import androidx.media3.common.Player.COMMAND_SEEK_TO_NEXT_MEDIA_ITEM
import androidx.media3.common.Player.COMMAND_SEEK_TO_PREVIOUS
import androidx.media3.common.Player.COMMAND_SEEK_TO_PREVIOUS_MEDIA_ITEM
import androidx.media3.common.Player.COMMAND_STOP
import androidx.media3.common.Player.Commands
import androidx.media3.common.SimpleBasePlayer
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.session.MediaLibraryService
import androidx.media3.session.MediaSession
import com.fptplay.mobile.BuildConfig
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.application.FptPlayLifecycleObserver
import com.fptplay.mobile.common.utils.NetworkUtils
import com.fptplay.mobile.common.utils.TrackingConstants
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.features.mqtt.MqttConnectManager
import com.fptplay.mobile.features.mqtt.MqttUtil
import com.fptplay.mobile.features.mqtt.MqttUtil.DEFAULT_MODE
import com.fptplay.mobile.features.mqtt.MqttUtil.getCurrentTimeInSeconds
import com.fptplay.mobile.features.mqtt.model.MqttContentType
import com.fptplay.mobile.features.mqtt.model.Publisher
import com.fptplay.mobile.features.pladio.data.PladioActionSeekType
import com.fptplay.mobile.features.pladio.data.Song
import com.fptplay.mobile.features.pladio.data.Song.Companion.emptySong
import com.fptplay.mobile.features.pladio.data.Song.PladioType
import com.fptplay.mobile.features.pladio.playback.OpenPanel
import com.fptplay.mobile.features.pladio.playback.PlaybackPlayerRemote
import com.fptplay.mobile.features.pladio.service.notification.PlayingNotification
import com.fptplay.mobile.features.pladio.service.notification.PlayingNotificationClassic
import com.fptplay.mobile.features.pladio.service.notification.PlayingNotificationImpl24
import com.fptplay.mobile.features.pladio.util.PladioConstants
import com.fptplay.mobile.features.pladio.util.PladioConstants.ACTION_WITH_EXTRA_DATA
import com.fptplay.mobile.features.pladio.util.PladioConstants.BEGIN_TIME_WITH_EXTRA_DATA
import com.fptplay.mobile.features.pladio.util.PladioConstants.ID_WITH_EXTRA_DATA
import com.fptplay.mobile.features.pladio.util.PladioConstants.MESSAGE_WITH_EXTRA_DATA
import com.fptplay.mobile.features.pladio.util.PladioConstants.TITLE_WITH_EXTRA_DATA
import com.fptplay.mobile.features.pladio.util.PladioTrackingHandler
import com.fptplay.mobile.features.pladio.util.PladioUtil
import com.fptplay.mobile.features.pladio.util.PladioUtil.isTheSame
import com.fptplay.mobile.features.pladio.util.PladioUtil.makeShuffleList
import com.fptplay.mobile.features.user_realtime_playing.UserRealtimePlayingTracker
import com.fptplay.mobile.player.PlayerUtils
import com.fptplay.mobile.player.PlayerView
import com.fptplay.mobile.player.retry.PlayerGetStreamRetryHandler
import com.fptplay.mobile.player.retry.PlayerPiPRetryHandler
import com.fptplay.mobile.player.retry.PlayerRetryHandler
import com.fptplay.mobile.services.player.CustomForwardingSimpleBasePlayer
import com.tear.modules.player.exo.ExoPlayerProxy
import com.tear.modules.player.util.IPlayer
import com.tear.modules.player.util.PlayerControlView
import com.tear.modules.player.util.UnValidResponseCode
import com.xhbadxx.projects.module.domain.entity.fplay.common.AlarmTimeConfig
import com.xhbadxx.projects.module.domain.entity.fplay.common.AlarmType
import com.xhbadxx.projects.module.domain.entity.fplay.common.Stream
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.domain.repository.fplay.PladioRepository
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.Dispatchers.Main
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Objects
import java.util.Timer
import java.util.TimerTask
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@OptIn(UnstableApi::class)
@SuppressLint("RestrictedApi")
@AndroidEntryPoint
class PlaybackService : MediaLibraryService(), IPlayer.IPlayerCallback,
    OnSharedPreferenceChangeListener {

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var pladioRepository: PladioRepository

    @Inject
    lateinit var userRealtimePlayingTracker: UserRealtimePlayingTracker

    @Inject
    lateinit var pladioTrackingHandler: PladioTrackingHandler

    @Inject
    lateinit var playerRetryHandler: PlayerRetryHandler

    @Inject
    lateinit var playerGetStreamRetryHandler: PlayerGetStreamRetryHandler

    @Inject
    lateinit var playerPiPRetryHandler: PlayerPiPRetryHandler

    private val playbackStreamServiceHandler by lazy { PlaybackStreamServiceHandler(pladioRepository) }

    private val playbackBind: IBinder = PlaybackBinder()

    private lateinit var storage: PersistentStorage

    private var originalPlayingQueue = ArrayList<Song>()

    private var isDestroy = false

    @JvmField
    var playingQueue = ArrayList<Song>()

    var tempRestoreSong: RestoreSongDate? = null
    private var queuesRestored = false

    // Player
    private val serviceScope = CoroutineScope(Job() + Main)
    private var player: IPlayer ?= null
    private val basePlayerCallback: IPlayer.IPlayerCallback by lazy { PlayerEvents() }
    private var mediaSession: MediaLibrarySession? = null
    //
    private var notificationManager: NotificationManager? = null
    private var playingNotification: PlayingNotification? = null
    private var isForeground = false

    // Alarm
    private val alarmConfig = mutableListOf<AlarmTimeConfig>()
    private var alarmTimer: Timer? = null
    //
    @JvmField
    var position = -1
    @JvmField
    var shuffleMode = 0
    @JvmField
    var pendingQuit = false

    private var isStreamRetry = false

    private var musicPlayerHandlerThread: HandlerThread? = null
    private var playbackPlayerHandler: Handler? = null
    //
    private var uiThreadHandler: Handler? = null

    //
    private var countDownTimerRetry: CountDownTimer? = null

    private var isPlayerErrorByInternet = false

    var playbackBarState: OpenPanel = OpenPanel.MAIN


    var repeatMode = 0
        private set(value) {
            when (value) {
                REPEAT_MODE_NONE, REPEAT_MODE_ALL, REPEAT_MODE_THIS -> {
                    field = value
                    sharedPreferences.savePladioPlaybackRepeatMode(SAVED_REPEAT_MODE, value)
                    handleAndSendChangeInternal(REPEAT_MODE_CHANGED)
                }
            }
        }

    var playbackSpeed
        get() = sharedPreferences
            .getPladioPlaybackSpeed(PLAYBACK_SPEED)
        set(value) {
            sharedPreferences.savePladioPlaybackSpeed(PLAYBACK_SPEED, value)
            handleAndSendChangeInternal(PLAYBACK_SPEED_CHANGED)
        }

    private var _playbackTimer = getAlarmConfig().find { it.typeAlarm == AlarmType.Off } ?: AlarmTimeConfig(typeAlarm = AlarmType.Off)
    var playbackTimer
        get() = _playbackTimer
        set(value) {
            _playbackTimer = value

            // Handle logic
            when (_playbackTimer.typeAlarm) {
                AlarmType.Off -> stopScheduleTimer()
                AlarmType.End -> {
                    pendingQuit = true
                    stopScheduleTimer()
                }
                AlarmType.Normal -> {
                    val alarmTime = Utils.convertStringToLong(_playbackTimer.duration, -1L)
                    if (alarmTime > 0L) {
                        startScheduleTimer(scheduleTimeMs = TimeUnit.SECONDS.toMillis(alarmTime))
                    } else {
                        stopScheduleTimer()
                    }
                }
                else -> {}
            }
            // Notify
            handleAndSendChangeInternal(PLAYBACK_TIMER_CHANGED)
        }

    fun setPlaybackTimer(timeConfig: AlarmTimeConfig, manualSetting: Boolean = false) {
        playbackTimer = timeConfig
        if(manualSetting) {
            pladioTrackingHandler.sendTrackingAlarmTimeOffSelected(timeConfig.time)
        }
    }

    fun resetService() {
        queuesRestored = false
        playingQueue.clear()
        stop(force = true)
    }

    private var playbackTimerStartTime = 0L
    private var playbackTimerTargetTime = 0L
    val playbackTimerProcessing
        get() = Pair(playbackTimerStartTime, playbackTimerTargetTime)

    val exoPlayer
        get() = player

    //MQTT
    private var mqttPublisher : Publisher? = null

    private fun Song.getPladioId(): String {
        return when (this.songType) {
            Song.PladioType.Event -> {
                this.eventDetail?.eventId ?: ""
            }
            else -> {
                this.id
            }
        }
    }

    // region Service Implement
    override fun onCreate() {
        super.onCreate()
        Logger.d("$TAG -> onCreate")
        isDestroy = false
        musicPlayerHandlerThread = HandlerThread("PlaybackHandler")
        musicPlayerHandlerThread?.start()
        playbackPlayerHandler = Handler(musicPlayerHandlerThread!!.looper)
        //
        player = createExoplayerProxy(context = this).apply {
            addPlayerCallback(basePlayerCallback)
        }
        // Setup Media Session
        setupMediaSession()
        //
        uiThreadHandler = Handler(Looper.getMainLooper())
        notificationManager = getSystemService()
        initNotification()
        sharedPreferences.registerOnSharedPreferenceChangeListener(this)
        sendBroadcast(Intent("$PLADIO_MUSIC_PACKAGE_NAME.PLADIO_MUSIC_SERVICE_CREATED"))
        storage = PersistentStorage.getInstance(sharedPreferences)
        observerStreamState()
        //
        // User realtime playing
        userRealtimePlayingTracker.addRealtimePlayingTrackingCallback(listener = userRealtimePlayingTrackerListener)
        // Player Retry Handler
        setPlayerRetryHandlerListener()
        // Player get stream retry
        setPlayerGetStreamRetryListener()
        //
        // PladioTrackingHandler
        pladioTrackingHandler.setupPladioTrackingHandler(pladioTrackingCallback)
        // Network listener
        setupNetworkListener()

    }

    private fun createExoplayerProxy(context: Context): ExoPlayerProxy {
        Logger.d("$TAG -> createExoplayerProxy")
        return ExoPlayerProxy(
            context = context,
            useCronetForNetworking = true,
            requireMinimumResolutionH265 = sharedPreferences.requiredMinimumResolutionH265(),
            requireMinimumResolutionH265HDR = sharedPreferences.requiredMinimumResolutionH265Hdr(),
            requireMinimumResolutionAV1 = sharedPreferences.requiredMinimumResolutionAV1(),
            requireMinimumResolutionVP9 = sharedPreferences.requiredMinimumResolutionVP9(),
            requireMinimumResolutionDolbyVision = sharedPreferences.requiredMinimumResolutionDolbyVision(),
        )
    }

    override fun onBind(intent: Intent?): IBinder? {
        // For Android auto, need to call super, or onGetRoot won't be called.
        return if ("android.media.browse.MediaBrowserService" == intent?.action) {
            super.onBind(intent)
        } else playbackBind
    }

    override fun onUnbind(intent: Intent): Boolean {
        Logger.d("$TAG -> onUnbind")
        stopSelf()
        return true
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        Logger.d("$TAG -> onTaskRemoved")
        super.onTaskRemoved(rootIntent)
        onDestroy()
    }

    override fun onGetSession(controllerInfo: MediaSession.ControllerInfo): MediaLibrarySession? {
        return mediaSession
    }

    override fun onDestroy() {
        Logger.d("$TAG -> onDestroy")
        isDestroy = true
        resetUserSettingsAfterDestroyIfNecessary()
        mediaSession?.release()
        quit()
        releaseResources()
        serviceScope.cancel()
        sharedPreferences.unregisterOnSharedPreferenceChangeListener(this)
        sendBroadcast(Intent("$PLADIO_MUSIC_PACKAGE_NAME.PLADIO_MUSIC_SERVICE_DESTROYED"))
        // User Realtime Playing
        userRealtimePlayingTracker.removeRealtimePlayingTrackingCallback(listener = userRealtimePlayingTrackerListener)
        // Player Retry Handler
        removePlayerRetryHandlerListener()
        // Player get stream error handler
        removePlayerGetStreamRetryListener()
        // PladioTrackingHandler
        pladioTrackingHandler.sendTrackingStopPladio()
        pladioTrackingHandler.destroy()
        // Network listener
        removeNetworkListener()
        //
        //MQTT
        publishEndToTopic()

        super.onDestroy()
    }
    // endregion

    // region Media Session
    private fun setupMediaSession() {
        Logger.d("$TAG -> setupMediaSession")
        (player?.internalPlayer() as? Player)?.let { player ->
            Logger.d("$TAG -> setupMediaSession -> mediaSession: $mediaSession | player: $player")
            if (mediaSession == null) {
                val wrapperPlayer = CustomForwardingSimpleBasePlayer(player, onForwardingSimpleBasePlayerListener)
                val mediaLibrarySessionCallback = MediaLibrarySessionCallback(this)
                mediaSession = MediaLibrarySession.Builder(this, wrapperPlayer, mediaLibrarySessionCallback)
                    .setId("PladioMediaSessionPlayerService")
                    .build()
            }
            initNotification()
            notifyChange(META_CHANGED)
        }
    }

    private val onForwardingSimpleBasePlayerListener = object : CustomForwardingSimpleBasePlayer.OnForwardingSimpleBasePlayerListener {
        override fun getState(state: SimpleBasePlayer.State): SimpleBasePlayer.State {
            return state
                .buildUpon()
                .setAvailableCommands(getCommand())
                .build()
        }

        override fun onSkipToNext(player: Player) {
            Logger.d("$TAG -> OnForwardingSimpleBasePlayerListener -> onSkipToNext")
            playNextSong(true)
        }

        override fun onSkipToPrevious(player: Player) {
            Logger.d("$TAG -> OnForwardingSimpleBasePlayerListener -> onSkipToPrevious")
            playPreviousSong(true)
        }

        override fun onPause(player: Player): Boolean {
            pause(force = true)
            return true
        }

        override fun onPlay(player: Player): Boolean {
            play(force = true)
            return true
        }
    }
    // endregion

    // region Notification
    private fun initNotification() {
        Logger.d("$TAG -> initNotification -> playingNotification: ${playingNotification}")
        try {
            if (playingNotification == null) {
                if (mediaSession != null) {
                    playingNotification = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        PlayingNotificationImpl24.from(this, notificationManager!!, mediaSession!!)
                    } else {
                        PlayingNotificationClassic.from(this, notificationManager!!)
                    }
                }
            }
        } catch (_: Exception) {}
    }
    // endregion

    // region MediaBrowserServiceCompat Implement

    override fun onSharedPreferenceChanged(
        sharedPreferences: android.content.SharedPreferences?,
        key: String?
    ) {
        when (key) {
            PLAYBACK_SPEED -> {
                updateMediaSessionPlaybackState()
                player?.setPlaybackSpeed(playbackSpeed = playbackSpeed)
            }
            SAVED_REPEAT_MODE -> {
                updateMediaSessionPlaybackState()
            }
//            ALBUM_ART_ON_LOCK_SCREEN, BLURRED_ALBUM_ART -> updateMediaSessionMetaData(::updateMediaSessionPlaybackState)
//            COLORED_NOTIFICATION -> {
//                playingNotification?.updateMetadata(currentSong) {
//                    playingNotification?.setPlaying(isPlaying)
//                    startForegroundOrNotify()
//                }
//            }
//
//            CLASSIC_NOTIFICATION -> {
//                updateNotification()
//                playingNotification?.updateMetadata(currentSong) {
//                    playingNotification?.setPlaying(isPlaying)
//                    startForegroundOrNotify()
//                }
//            }
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        if (intent != null) {
            serviceScope.launch {
                val shouldRestoreRecentItem = intent.getBooleanExtra(ACTION_SHOULD_RESTORE_ITEM, true)
                Logger.d("$TAG -> onStartCommand -> shouldRestoreRecentItem: $shouldRestoreRecentItem - queue restore status: $queuesRestored")
                if (shouldRestoreRecentItem) {
                    restoreQueuesAndPositionIfNecessary()
                }
                when (intent.action) {
                    ACTION_TOGGLE_PAUSE -> if (isPlaying) {
                        pause()
                    } else {
                        play()
                    }

                    ACTION_PAUSE -> pause()
                    ACTION_PLAY -> play()
                    ACTION_REWIND -> back(true)
                    ACTION_SKIP -> playNextSong(true)
                    ACTION_STOP, ACTION_QUIT -> {
                        pendingQuit = false
                        quit()
                    }

                    ACTION_PENDING_QUIT -> {
                        pendingQuit = true
                    }
                    else -> {}
                }
            }
        }
        return START_NOT_STICKY
    }
    // endregion

    // region IPlayer Listener
    inner class PlayerEvents : IPlayer.IPlayerCallback {
        override fun onBandwidth(message: String) {
            Logger.d("$TAG -> Playback -> onBandwidth")
            super.onBandwidth(message)
            userRealtimePlayingTracker.playerEvents.onBandwidth(message = message)
            playerRetryHandler.playerEvents.onBandwidth(message = message)
            pladioTrackingHandler.playerEvents.onBandwidth(message = message)
        }
        override fun onBuffering() {
            Logger.d("$TAG -> Playback -> onBuffering")
            super.onBuffering()
            userRealtimePlayingTracker.playerEvents.onBuffering()
            playerRetryHandler.playerEvents.onBuffering()
            pladioTrackingHandler.playerEvents.onBuffering()
        }
        override fun onPrepare() {
            super.onPrepare()
            Logger.d("$TAG -> Playback -> onPrepare")
            userRealtimePlayingTracker.playerEvents.onPrepare()
            playerRetryHandler.playerEvents.onPrepare()
            pladioTrackingHandler.playerEvents.onPrepare()
            (player?.internalPlayer() as? ExoPlayer)?.run {
                removeListener(pladioTrackingHandler.playerTrackChangedEvents)
                addListener(pladioTrackingHandler.playerTrackChangedEvents)

                removeAnalyticsListener(pladioTrackingHandler.playerAnalyticEvents)
                addAnalyticsListener(pladioTrackingHandler.playerAnalyticEvents)
            }
        }

        override fun onReady() {
            super.onReady()
            Logger.d("$TAG -> Playback -> onReady")
            //
            setupUserRealtimePlayingTracker()
            userRealtimePlayingTracker.playerEvents.onReady()

            // Case: If autoPlay=false, don't calculate real time playing
            if (player?.request?.autoPlay == false) {
                userRealtimePlayingTracker.playerEvents.onPause()
            }
            //
            playerRetryHandler.playerEvents.onReady()
            //
            notifyChange(PLAY_STATE_CHANGED)
            notifyChange(PLAYBACK_READY)
            player?.let {
                pladioTrackingHandler.setCurrentDuration(it.currentDuration())
                pladioTrackingHandler.setTotalDuration(it.totalDuration())

            }
            pladioTrackingHandler.playerEvents.onReady()
        }

        override fun onStart() {
            super.onStart()
            Logger.d("$TAG -> Playback -> onStart")
            userRealtimePlayingTracker.playerEvents.onStart()
            playerRetryHandler.playerEvents.onStart()
            pladioTrackingHandler.playerEvents.onStart()
            notifyChange(PLAY_STATE_CHANGED)
        }

        override fun onPlay() {
            super.onPlay()
            Logger.d("$TAG -> Playback -> onPlay")
            userRealtimePlayingTracker.playerEvents.onPlay()
            playerRetryHandler.playerEvents.onPlay()
            pladioTrackingHandler.playerEvents.onPlay()
            notifyChange(PLAY_STATE_CHANGED)
        }

        override fun onPause() {
            super.onPause()
            Logger.d("$TAG -> Playback -> onPause")
            userRealtimePlayingTracker.playerEvents.onPause()
            playerRetryHandler.playerEvents.onPause()
            pladioTrackingHandler.playerEvents.onPause()
            notifyChange(PLAY_STATE_CHANGED)
        }

        override fun onSeek() {
            super.onSeek()
            Logger.d("$TAG -> Playback -> onSeek")
            userRealtimePlayingTracker.playerEvents.onSeek()
            playerRetryHandler.playerEvents.onSeek()
            pladioTrackingHandler.playerEvents.onSeek()
            notifyChange(PLAY_STATE_CHANGED)
        }

        override fun onResume() {
            super.onResume()
            Logger.d("$TAG -> Playback -> onResume")
            userRealtimePlayingTracker.playerEvents.onResume()
            playerRetryHandler.playerEvents.onResume()
            pladioTrackingHandler.playerEvents.onResume()
        }

        override fun onEnd() {
            super.onEnd()
            Logger.d("$TAG -> Playback -> onEnd")
            userRealtimePlayingTracker.playerEvents.onEnd()
            playerRetryHandler.playerEvents.onEnd()
            pladioTrackingHandler.playerEvents.onEnd()
            onTrackEnded()
            notifyChange(PLAY_STATE_CHANGED)
        }

        override fun onStop() {
            super.onStop()
            Logger.d("$TAG -> Playback -> onStop")
            userRealtimePlayingTracker.playerEvents.onStop()
            playerRetryHandler.playerEvents.onStop()
            notifyChange(PLAY_STATE_CHANGED)
            pladioTrackingHandler.playerEvents.onStop()
        }

        override fun onRelease() {
            super.onRelease()
            Logger.d("$TAG -> Playback -> onRelease")
            userRealtimePlayingTracker.playerEvents.onRelease()
            playerRetryHandler.playerEvents.onRelease()
            notifyChange(PLAY_STATE_CHANGED)
            pladioTrackingHandler.playerEvents.onRelease()
        }

        override fun onAudioChange(bitrate: String) {
            super.onAudioChange(bitrate)
            Logger.d("$TAG -> Playback -> onAudioChange: $bitrate")
            userRealtimePlayingTracker.playerEvents.onAudioChange(bitrate = bitrate)
            playerRetryHandler.playerEvents.onAudioChange(bitrate = bitrate)
            pladioTrackingHandler.playerEvents.onAudioChange(bitrate = bitrate)
        }

        override fun onFetchBitrateSuccess(bitrates: ArrayList<IPlayer.Bitrate>) {
            super.onFetchBitrateSuccess(bitrates)
            Logger.d("$TAG -> Playback -> onFetchBitrateSuccess: $bitrates")
            userRealtimePlayingTracker.playerEvents.onFetchBitrateSuccess(bitrates = bitrates)
            playerRetryHandler.playerEvents.onFetchBitrateSuccess(bitrates = bitrates)
            pladioTrackingHandler.playerEvents.onFetchBitrateSuccess(bitrates = bitrates)
        }

        override fun onFetchBitrateAll(
            bitrates: ArrayList<IPlayer.Bitrate>,
            audioTracks: List<PlayerControlView.Data.Track>?
        ) {
            super.onFetchBitrateAll(bitrates, audioTracks)
            Logger.d("$TAG -> Playback -> onFetchBitrateAll: $bitrates")
            userRealtimePlayingTracker.playerEvents.onFetchBitrateAll(bitrates = bitrates, audioTracks = audioTracks)
            playerRetryHandler.playerEvents.onFetchBitrateAll(bitrates = bitrates, audioTracks = audioTracks)
            pladioTrackingHandler.playerEvents.onFetchBitrateAll(bitrates = bitrates, audioTracks = audioTracks)
        }

        override fun onTimelineChanged(any: Any?) {
            super.onTimelineChanged(any)
            Logger.d("$TAG -> Playback -> onTimelineChanged")
            userRealtimePlayingTracker.playerEvents.onTimelineChanged(any = any)
            playerRetryHandler.playerEvents.onTimelineChanged(any = any)
            pladioTrackingHandler.playerEvents.onTimelineChanged(any = any)
        }

        override fun onVideoChange(bitrate: String) {
            super.onVideoChange(bitrate)
            Logger.d("$TAG -> Playback -> onVideoChange")
            userRealtimePlayingTracker.playerEvents.onVideoChange(bitrate = bitrate)
            playerRetryHandler.playerEvents.onVideoChange(bitrate = bitrate)
            pladioTrackingHandler.playerEvents.onVideoChange(bitrate = bitrate)
        }

        override fun onVideoSizeChange(videoSize: String) {
            super.onVideoSizeChange(videoSize)
            Logger.d("$TAG -> Playback -> onVideoSizeChange")
            userRealtimePlayingTracker.playerEvents.onVideoSizeChange(videoSize = videoSize)
            playerRetryHandler.playerEvents.onVideoSizeChange(videoSize = videoSize)
            pladioTrackingHandler.playerEvents.onVideoSizeChange(videoSize = videoSize)
        }

        override fun onDrmKeysLoaded() {
            super.onDrmKeysLoaded()
            Logger.d("$TAG -> Playback -> onDrmKeysLoaded")
            userRealtimePlayingTracker.playerEvents.onDrmKeysLoaded()
            playerRetryHandler.playerEvents.onDrmKeysLoaded()
            pladioTrackingHandler.playerEvents.onDrmKeysLoaded()
        }

        override fun onRotationKey() {
            super.onRotationKey()
            Logger.d("$TAG -> Playback -> onRotationKey")
            userRealtimePlayingTracker.playerEvents.onRotationKey()
            playerRetryHandler.playerEvents.onRotationKey()
            pladioTrackingHandler.playerEvents.onRotationKey()
        }

        override fun onError(
            code: Int,
            name: String,
            detail: String,
            error403: Boolean,
            responseCode: Int
        ) {
            super.onError(code, name, detail, error403, responseCode)
            Logger.d("$TAG -> Playback -> onError -> code: $code | name: $name | detail: $detail | error403: $error403 | responseCode: $responseCode")
            isPlayerErrorByInternet = code == 2001
            userRealtimePlayingTracker.playerEvents.onError(code = code, name = name, detail = detail, error403 = error403, responseCode = responseCode)
            playerRetryHandler.playerEvents.onError(code = code, name = name, detail = detail, error403 = error403, responseCode = responseCode)
            pladioTrackingHandler.playerEvents.onError(code = code, name = name, detail = detail, error403 = error403, responseCode = responseCode)
        }

        override fun onErrorCodec(
            code: Int,
            name: String,
            detail: String,
            responseCode: Int,
            isDrm: Boolean,
            codec: IPlayer.CodecType
        ) {
            super.onErrorCodec(code, name, detail, responseCode, isDrm, codec)
            Logger.d("$TAG -> Playback -> onErrorCodec -> code: $code | name: $name | detail: $detail | responseCode: $responseCode | isDrm: $isDrm | codec: $codec")
            userRealtimePlayingTracker.playerEvents.onErrorCodec(code = code, name = name, detail = detail, responseCode = responseCode, isDrm = isDrm, codec = codec)
            playerRetryHandler.playerEvents.onErrorCodec(code = code, name = name, detail = detail, responseCode = responseCode, isDrm = isDrm, codec = codec)
            pladioTrackingHandler.playerEvents.onErrorCodec(code = code, name = name, detail = detail, responseCode = responseCode, isDrm = isDrm, codec = codec)
        }

        override fun onError6006(code: Int, name: String, detail: String) {
            super.onError6006(code, name, detail)
            Logger.d("$TAG -> Playback -> onError6006 -> code: $code | name: $name | detail: $detail")
            userRealtimePlayingTracker.playerEvents.onError6006(code = code, name = name, detail = detail)
            playerRetryHandler.playerEvents.onError6006(code = code, name = name, detail = detail)
            pladioTrackingHandler.playerEvents.onError6006(code = code, name = name, detail = detail)
        }

        override fun onError6006WhenPreview(
            code: Int,
            name: String,
            detail: String,
            responseCode: Int
        ) {
            super.onError6006WhenPreview(code, name, detail, responseCode)
            Logger.d("$TAG -> Playback -> onError6006WhenPreview -> code: $code | name: $name | detail: $detail | responseCode: $responseCode")
            userRealtimePlayingTracker.playerEvents.onError6006WhenPreview(code = code, name = name, detail = detail, responseCode = responseCode)
            playerRetryHandler.playerEvents.onError6006WhenPreview(code = code, name = name, detail = detail, responseCode = responseCode)
            pladioTrackingHandler.playerEvents.onError6006WhenPreview(code = code, name = name, detail = detail, responseCode = responseCode)
        }

        override fun onErrorBehindInLive(code: Int, name: String, detail: String) {
            super.onErrorBehindInLive(code, name, detail)
            Logger.d("$TAG -> Playback -> onErrorBehindInLive -> code: $code | name: $name | detail: $detail")
            userRealtimePlayingTracker.playerEvents.onErrorBehindInLive(code = code, name = name, detail = detail)
            playerRetryHandler.playerEvents.onErrorBehindInLive(code = code, name = name, detail = detail)
            pladioTrackingHandler.playerEvents.onErrorBehindInLive(code = code, name = name, detail = detail)
        }

        override fun startBuffering() {
            super.startBuffering()
            pladioTrackingHandler.playerEvents.startBuffering()
        }

        override fun endBuffering() {
            super.endBuffering()
            pladioTrackingHandler.playerEvents.endBuffering()
        }
    }
    // endregion

    // region Logic Playback Change
    private fun onTrackEnded() {
        // if there is a timer finished, don't continue
        if (pendingQuit
            || repeatMode == REPEAT_MODE_NONE && isLastTrack
        ) {
            pause(force = true)
            seek(0)
            if (pendingQuit) {
                pendingQuit = false
                resetPlaybackTimerDefault()
                //
                prepareNextSong(force = true)
            }
        } else {
            seek(0)
            playNextSong(false)
        }
    }
    // endregion

    // region Playback Control
    fun setQueue(songs: List<Song>?) {
        Logger.d("$TAG -> setQueue -> song: $songs")
        clearQueue()
        playingQueue.addAll(songs!!)
        originalPlayingQueue.addAll(songs)
        notifyChange(QUEUE_CHANGED)
    }

    fun addSongs(position: Int, songs: List<Song>) {
        Logger.d("$TAG -> addSongs -> song: $songs")
        playingQueue.addAll(position, songs)
        originalPlayingQueue.addAll(position, songs)
        notifyChange(QUEUE_CHANGED)
    }

    fun addSongs(songs: List<Song>) {
        Logger.d("$TAG -> addSongs -> song: $songs")
        playingQueue.addAll(songs)
        originalPlayingQueue.addAll(songs)
        notifyChange(QUEUE_CHANGED)
    }

    fun clearQueue() {
        Logger.d("$TAG -> clearQueue")
        playingQueue.clear()
        originalPlayingQueue.clear()
        notifyChange(QUEUE_CHANGED)
    }

    fun clearQueueAndResetPlaying() {
        Logger.d("$TAG -> clearQueueAndResetPlaying")
        stop(force = true)
        this.position = -1
        playingQueue.clear()
        originalPlayingQueue.clear()
        notifyChange(QUEUE_CHANGED)
        notifyChange(META_CHANGED)
    }

    fun back(force: Boolean) {
        Logger.d("$TAG -> back -> force: $force")
        if (songProgressMillis > 5000) {
            seek(0)
        } else {
            playPreviousSong(force)
        }
    }

    fun cycleRepeatMode() {
        Logger.d("$TAG -> cycleRepeatMode")
        repeatMode = when (repeatMode) {
            REPEAT_MODE_NONE -> REPEAT_MODE_ALL
            REPEAT_MODE_ALL -> REPEAT_MODE_THIS
            else -> REPEAT_MODE_NONE
        }
        pladioTrackingHandler.pladioUserInteractEvents.onRepeatModeSelected()
    }

    private val isLastTrack: Boolean
        get() = getPosition() == playingQueue.size - 1

    val isPlaying: Boolean
        get() = player?.isPlaying() == true

    val currentSong: Song
        get() = getSongAt(getPosition())

    val nextSong: Song?
        get() = if (isLastTrack && repeatMode == REPEAT_MODE_NONE) {
            null
        } else {
            getSongAt(getNextPosition(false))
        }

    val songDurationMillis: Long
        get() = player?.totalDuration() ?: 0L

    val songBufferMillis: Long
        get() = player?.bufferDuration() ?: 0L

    val songProgressMillis: Long
        get() = player?.currentDuration() ?: 0L

    private fun getSongAt(position: Int): Song {
        return if ((position >= 0) && (position < playingQueue.size)) {
            playingQueue[position]
        } else {
            emptySong
        }
    }

    fun toggleShuffle() {
        if (getShuffleMode() == SHUFFLE_MODE_NONE) {
            setShuffleMode(SHUFFLE_MODE_SHUFFLE)
        } else {
            setShuffleMode(SHUFFLE_MODE_NONE)
        }
        pladioTrackingHandler.pladioUserInteractEvents.onShuffleModeSelected()
    }

    private fun getShuffleMode(): Int {
        return shuffleMode
    }

    fun setShuffleMode(shuffleMode: Int) {
        sharedPreferences.savePladioPlaybackShuffleMode(SAVED_SHUFFLE_MODE, shuffleMode)
        when (shuffleMode) {
            SHUFFLE_MODE_SHUFFLE -> {
                this.shuffleMode = shuffleMode
                makeShuffleList(playingQueue, getPosition())
                position = 0
            }

            SHUFFLE_MODE_NONE -> {
                this.shuffleMode = shuffleMode
                val currentSong = Objects.requireNonNull(currentSong)
                playingQueue = ArrayList(originalPlayingQueue)
                var newPosition = 0
                for (song in playingQueue) {
                    if (song.id == currentSong.id && song.playlistId == currentSong.playlistId && song.streamRequestDataExtra?.episodeId == currentSong.streamRequestDataExtra?.episodeId) {
                        newPosition = playingQueue.indexOf(song)
                    }
                }
                position = newPosition
            }
        }
        handleAndSendChangeInternal(SHUFFLE_MODE_CHANGED)
        notifyChange(QUEUE_CHANGED)
    }


    private fun getPosition(): Int {
        return position
    }

    private fun getNextPosition(force: Boolean): Int {
        Logger.d("$TAG -> getNextPosition")
        var position = getPosition() + 1
        when (repeatMode) {
            REPEAT_MODE_ALL -> if (isLastTrack) {
                position = 0
            }

            REPEAT_MODE_THIS -> if (force) {
                if (isLastTrack) {
                    position = 0
                }
            } else {
                position -= 1
            }

            REPEAT_MODE_NONE -> if (isLastTrack) {
                position -= 1
            }

            else -> if (isLastTrack) {
                position -= 1
            }
        }
        return position
    }

    private fun getPreviousPosition(force: Boolean): Int {
        var newPosition = getPosition() - 1
        when (repeatMode) {
            REPEAT_MODE_ALL -> if (newPosition < 0) {
                newPosition = playingQueue.size - 1
            }

            REPEAT_MODE_THIS -> if (force) {
                if (newPosition < 0) {
                    newPosition = playingQueue.size - 1
                }
            } else {
                newPosition = getPosition()
            }

            REPEAT_MODE_NONE -> if (newPosition < 0) {
                newPosition = 0
            }

            else -> if (newPosition < 0) {
                newPosition = 0
            }
        }
        return newPosition
    }

    fun handlePlayPause(force: Boolean = true) {
        Logger.d("$TAG -> handlePlayPause")
        if (isPlaying) {
            player?.pause(force)
            pladioTrackingHandler.pladioUserInteractEvents.onPause()
        } else {
            player?.play(force)
            pladioTrackingHandler.pladioUserInteractEvents.onPlay()
        }
        notifyChange(PLAY_STATE_CHANGED)
    }

    fun play(force: Boolean = false) {
        Logger.d("$TAG -> Play")
        player?.play(force)
        pladioTrackingHandler.pladioUserInteractEvents.onPlay()
        notifyChange(PLAY_STATE_CHANGED)
    }

    fun pause(force: Boolean = false) {
        Logger.d("$TAG -> Pause")
        player?.pause(force)
        pladioTrackingHandler.pladioUserInteractEvents.onPause()
        notifyChange(PLAY_STATE_CHANGED)
    }

    @Synchronized
    fun seek(duration: Long, pladioActionSeekType: PladioActionSeekType = PladioActionSeekType.Seek): Long {
        Logger.d("$TAG -> Seek")
        player?.seek(duration = duration)
        pladioTrackingHandler.pladioUserInteractEvents.onSeek(duration = duration, pladioActionSeekType = pladioActionSeekType)
        notifyChange(PLAY_STATE_CHANGED)
        return duration
    }

    fun stop(force: Boolean = false) {
        Logger.d("$TAG -> Stop")
        player?.stop(force)
        notifyChange(PLAY_STATE_CHANGED)
    }

    fun playNextSong(force: Boolean) {
        Logger.d("$TAG -> playNextSong")
        TrackingUtil.setDataTracking(
            screenValue = if (PlaybackPlayerRemote.currentSong.contentType == Song.PladioContentType.Single) {
                TrackingUtil.screenRelated
            } else TrackingUtil.screen,
            idRelated = PlaybackPlayerRemote.currentSong.id,
            position = ""
        )
        playSongAt(getNextPosition(force))
        if(getNextPosition(force) != getPosition()) {
            pladioTrackingHandler.pladioUserInteractEvents.onNext()
        }
    }

    fun playPreviousSong(force: Boolean) {
        Logger.d("$TAG -> playPreviousSong")
        TrackingUtil.setDataTracking(
            screenValue = if (PlaybackPlayerRemote.currentSong.contentType == Song.PladioContentType.Single) {
                TrackingUtil.screenRelated
            } else TrackingUtil.screen,
            idRelated = PlaybackPlayerRemote.currentSong.id,
            position = ""
        )
        playSongAt(getPreviousPosition(force))
        if(getPreviousPosition(force) != getPosition()) {
            pladioTrackingHandler.pladioUserInteractEvents.onPrev()
        }
    }

    fun prepareNextSong(force: Boolean) {
        Logger.d("$TAG -> prepareNextSong")
        TrackingUtil.setDataTracking(
            screenValue = if (PlaybackPlayerRemote.currentSong.contentType == Song.PladioContentType.Single) {
                TrackingUtil.screenRelated
            } else TrackingUtil.screen,
            idRelated = PlaybackPlayerRemote.currentSong.id,
            position = ""
        )
        playSongAt(getNextPosition(force), isAutoPlay = false)
    }

    @Synchronized
    fun playSongAt(position: Int, force: Boolean = true, startPositionMs: Long = 0L, isAutoPlay: Boolean = true) {
        Logger.d("$TAG -> playSongAt: $position | startPositionMs: $startPositionMs | isAutoPlay: $isAutoPlay")
        if (force || position != this.position) {
            this.position = position
            notifyChange(META_CHANGED)
            seek(0)
            stop(force = true)
            // trigger reset timer end type
            triggerResetTimerDefaultForEndType()
            // get stream here
            val currentSong = getSongAt(position)
            if (currentSong.stream == null) {
                playbackStreamServiceHandler.fetchPlayLink(
                    currentSong = currentSong,
                    pladioId = currentSong.getPladioId(),
                    episodeId = currentSong.streamRequestDataExtra?.episodeId?:"",
                    streamType = currentSong.streamRequestDataExtra?.autoProfile?:"",
                    startPositionMs = startPositionMs,
                    isAutoPlay = isAutoPlay
                )
            } else {
                processPlay(startPositionMs, isAutoPlay)
            }
            if(position != this.position) {
                pladioTrackingHandler.sendTrackingStopPladio()
            }
            pladioTrackingHandler.pladioUserInteractEvents.onPladioSelected()
//            if(currentSong.contentType == Song.PladioContentType.Single) {
//                pladioTrackingHandler.sendTrackingEnterDetail(eventName = TrackingConstants.EVENT_LOG_NAME_ENTER_DETAIL_VOD)
//            }
        }
    }


    @Synchronized
    fun bindEventAndPlayAt(position: Int, force: Boolean = true, fromCheckPackage: Boolean, startPositionMs: Long = 0L, isAutoPlay: Boolean = true, hasFetchPlayLink: Boolean = true) {
        Logger.d("$TAG -> playSongAt: $position | startPositionMs: $startPositionMs | isAutoPlay: $isAutoPlay")
        if (force || position != this.position) {
            val oldPosition = this.position
            this.position = position
            notifyChange(META_CHANGED)
            seek(0)
            stop(force = true)
            // trigger reset timer end type
            triggerResetTimerDefaultForEndType()
            //
            when (currentSong.songType) {
                Song.PladioType.Event -> {
                    if (oldPosition != this.position) {
                        notifyChange(EVENT_CHANGED)
                    }
                    //
                    if (hasFetchPlayLink) {
                        playbackStreamServiceHandler.fetchPlayLink(
                            currentSong = currentSong,
                            pladioId = currentSong.getPladioId(),
                            episodeId = currentSong.streamRequestDataExtra?.episodeId?:"",
                            streamType = currentSong.streamRequestDataExtra?.autoProfile?:"",
                            startPositionMs = startPositionMs,
                            isAutoPlay = isAutoPlay,
                            fromCheckPackage = fromCheckPackage
                        )
                    }
                }
                else -> {}
            }
        }
    }

    fun openQueue(
        playingQueue: List<Song>?,
        startPosition: Int,
        isAutoOpenPlayer: Boolean = false,
        startPlayPositionMs: Long = 0L,
        isAutoPlay: Boolean = true
    ) {
        Logger.d("$TAG -> openQueue -> startPosition: $startPosition | isAutoOpenPlayer: $isAutoOpenPlayer | startPlayPositionMs: $startPlayPositionMs | isAutoPlay: $isAutoPlay")
        if (!playingQueue.isNullOrEmpty()
            && startPosition >= 0 && startPosition < playingQueue.size
        ) {
            val prevFirstQueue = originalPlayingQueue.firstOrNull()
            val curFirstQueue = playingQueue.firstOrNull()
            if (prevFirstQueue != null && curFirstQueue != null) {
                val isTheSame = prevFirstQueue.isTheSame(curFirstQueue)
                updateUserSettingsWhenOpenNewQueue(song = playingQueue.first(), isTheSameList = isTheSame)
            }

            tempRestoreSong = null

            // it is important to copy the playing queue here first as we might add/remove songs later
            originalPlayingQueue = ArrayList(playingQueue)
            this.playingQueue = ArrayList(originalPlayingQueue)
            var position = startPosition
            if (shuffleMode == SHUFFLE_MODE_SHUFFLE) {
                makeShuffleList(this.playingQueue, startPosition)
                position = 0
            }
            playSongAt(position, force = true, startPositionMs = startPlayPositionMs, isAutoPlay = isAutoPlay)
            notifyChange(QUEUE_CHANGED)
            //
            if (isAutoOpenPlayer) {
                handleAutoOpenPlaybackPanel()
            }
//            pladioTrackingHandler.sendTrackingEnterDetail(eventName = TrackingConstants.EVENT_LOG_NAME_ENTER_DETAIL_VOD)
            if(currentSong.contentType == Song.PladioContentType.Single) {
                pladioTrackingHandler.sendTrackingEnterDetail(eventName = TrackingConstants.EVENT_LOG_NAME_ENTER_DETAIL_VOD)
            }


        }
    }

    fun openRestoreQueue(
        playingQueue: List<Song>?,
        startPosition: Int,
        startPlayPositionMs: Long,
        isAutoPlay: Boolean
    ) {
        Logger.d("$TAG -> openRestoreQueue")
        if (!playingQueue.isNullOrEmpty()
            && startPosition >= 0 && startPosition < playingQueue.size
        ) {
            val prevFirstQueue = originalPlayingQueue.firstOrNull()
            val curFirstQueue = playingQueue.firstOrNull()
            if (prevFirstQueue != null && curFirstQueue != null) {
                val isTheSame = prevFirstQueue.isTheSame(curFirstQueue)
                updateUserSettingsWhenOpenNewQueue(song = playingQueue.first(), isTheSameList = isTheSame)
            }

            tempRestoreSong = null

            // it is important to copy the playing queue here first as we might add/remove songs later
            originalPlayingQueue = ArrayList(playingQueue)
            this.playingQueue = ArrayList(originalPlayingQueue)
            var position = startPosition
            if (shuffleMode == SHUFFLE_MODE_SHUFFLE) {
                makeShuffleList(this.playingQueue, startPosition)
                position = 0
            }
            playSongAt(position, force = true, startPositionMs = startPlayPositionMs, isAutoPlay = isAutoPlay)
            notifyChange(QUEUE_CHANGED)
        }
    }


    fun openEventQueue(
        playingQueue: List<Song>?,
        startPosition: Int,
        isAutoOpenPlayer: Boolean = false,
        startPlayPositionMs: Long = 0L,
        isAutoPlay: Boolean = true
    ) {
        Logger.d("$TAG -> openEventQueue -> startPosition: $startPosition | isAutoOpenPlayer: $isAutoOpenPlayer | startPlayPositionMs: $startPlayPositionMs | isAutoPlay: $isAutoPlay")
        if (!playingQueue.isNullOrEmpty()
            && startPosition >= 0 && startPosition < playingQueue.size
        ) {
            val prevFirstQueue = originalPlayingQueue.firstOrNull()
            val curFirstQueue = playingQueue.firstOrNull()
            if (prevFirstQueue != null && curFirstQueue != null) {
                val isTheSame = prevFirstQueue.isTheSame(curFirstQueue)
                updateUserSettingsWhenOpenNewQueue(song = playingQueue.first(), isTheSameList = isTheSame)
            }

            tempRestoreSong = null

            // make sure event changed post
            this.position = -1

            // it is important to copy the playing queue here first as we might add/remove songs later
            originalPlayingQueue = ArrayList(playingQueue)
            this.playingQueue = ArrayList(originalPlayingQueue)
            var position = startPosition
            if (shuffleMode == SHUFFLE_MODE_SHUFFLE) {
                makeShuffleList(this.playingQueue, startPosition)
                position = 0
            }
            bindEventAndPlayAt(position, force = true, startPositionMs = startPlayPositionMs, isAutoPlay = isAutoPlay, fromCheckPackage = false, hasFetchPlayLink = false)
            notifyChange(QUEUE_CHANGED)
            //
            if (isAutoOpenPlayer) {
                handleAutoOpenPlaybackPanel()
            }
        }
    }

//    fun restoreState(completion: () -> Unit = {}) {
//        serviceScope.launch {
//            restoreQueuesAndPositionIfNecessary()
//            completion()
//        }
//    }

    private fun handleAutoOpenPlaybackPanel() {
        LocalBroadcastManager.getInstance(this).sendBroadcast(Intent(
            PladioConstants.PLADIO_MAIN_OPEN_PANEL).apply {
            putExtra(PladioConstants.PLADIO_MAIN_OPEN_PANEL_DATA, OpenPanel.PLAYBACK.rawValue)
        })
    }

    private fun savePosition() {
        sharedPreferences.savePladioSavedPosition(SAVED_POSITION, getPosition())
    }

    fun savePositionInTrack() {
        sharedPreferences.savePladioSavedPositionInTrack(SAVED_POSITION_IN_TRACK, songProgressMillis.toInt())
    }

    private fun notifyChange(what: String) {
        handleAndSendChangeInternal(what)
        sendPublicIntent(what)
    }

    fun handleAndSendChangeInternal(what: String) {
        handleChangeInternal(what)
        sendChangeInternal(what)
    }

    // to let other apps know whats playing. i.e. last.fm (scrobbling) or musixmatch
    fun sendPublicIntent(what: String) {
        val intent = Intent(what.replace(PLADIO_MUSIC_PACKAGE_NAME, MUSIC_PACKAGE_NAME))
        val song = currentSong
        intent.putExtra("id", song.id)
        intent.putExtra("artist", song.artist)
//        intent.putExtra("album", song.albumName)
        intent.putExtra("track", song.title)
        intent.putExtra("duration", songDurationMillis)
        intent.putExtra("position", songProgressMillis)
        intent.putExtra("playing", isPlaying)
        intent.putExtra("scrobbling_source", PLADIO_MUSIC_PACKAGE_NAME)
        @Suppress("Deprecation")
        sendStickyBroadcast(intent)
    }

    fun runOnUiThread(runnable: Runnable?) {
        uiThreadHandler?.post(runnable!!)
    }

    private fun pausePlaybackByAlarm() {
        pause(force = true)
    }

    fun quit() {
        pause()
        ServiceCompat.stopForeground(this, ServiceCompat.STOP_FOREGROUND_REMOVE)
        isForeground = false
        notificationManager?.cancel(PlayingNotification.NOTIFICATION_ID)
        playingNotification = null

        stopSelf()
    }

    private suspend fun restoreQueuesAndPositionIfNecessary() {
        Logger.d("$TAG > restoreQueuesAndPositionIfNecessary -> queuesRestored: $queuesRestored - playingQueue.isEmpty(): ${playingQueue.isEmpty()}")
        if (!queuesRestored && playingQueue.isEmpty()) {
            withContext(IO) {
//                shuffleMode = sharedPreferences.getPladioPlaybackShuffleMode(SAVED_SHUFFLE_MODE)
//                repeatMode = sharedPreferences.getPladioPlaybackRepeatMode(SAVED_REPEAT_MODE)
                handleAndSendChangeInternal(SHUFFLE_MODE_CHANGED)
                handleAndSendChangeInternal(REPEAT_MODE_CHANGED)
                //
                val restoreSong = storage.recentSong().second
                val restoredPosition = sharedPreferences.getPladioSavedPosition(SAVED_POSITION)
                val restoredPositionInTrack = sharedPreferences.getPladioSavedPositionInTrack(SAVED_POSITION_IN_TRACK)
                if (restoreSong != null && restoredPosition != -1) {
                    if (restoreSong.songType == Song.PladioType.Event) {
                        handleAndSendChangeInternal(ON_PROCESS_RESTORE_LAST_ITEM_DONE)
                        // Don't restore event
//                        tempRestoreSong = RestoreSongDate(song = restoreSong, startPlayPositionMs = restoredPositionInTrack.toLong(), isAutoPlay = false)
//                        handleAndSendChangeInternal(GET_EVENT_DETAIL_AFTER_RESTORE)
                    } else {
                        when (restoreSong.contentType) {
                            Song.PladioContentType.Single -> {
                                openRestoreQueue(playingQueue = listOf(restoreSong), startPosition = 0, startPlayPositionMs = restoredPositionInTrack.toLong(), isAutoPlay = false)
                                handleAndSendChangeInternal(GET_RECOMMEND_AFTER_RESTORE)
                                handleAndSendChangeInternal(ON_PROCESS_RESTORE_LAST_ITEM_DONE) // Restore done, get recommend (optional)
                            }
                            Song.PladioContentType.Series -> {
                                tempRestoreSong = RestoreSongDate(song = restoreSong, startPlayPositionMs = restoredPositionInTrack.toLong(), isAutoPlay = false)
                                handleAndSendChangeInternal(GET_SERIES_AFTER_RESTORE)
                            }
                            Song.PladioContentType.Playlist -> {
                                tempRestoreSong = RestoreSongDate(song = restoreSong, startPlayPositionMs = restoredPositionInTrack.toLong(), isAutoPlay = false)
                                handleAndSendChangeInternal(GET_PLAYLIST_AFTER_RESTORE)
                            }
                            else -> {}
                        }
                    }

                    //
//                    sendChangeInternal(META_CHANGED)
                    sendChangeInternal(QUEUE_CHANGED)
//                    mediaSession?.setQueueTitle(getString(R.string.pladio_player_control_now_playing_queue))
//                    mediaSession?.setQueue(playingQueue.toMediaSessionQueue())
                } else {
                    handleAndSendChangeInternal(ON_PROCESS_RESTORE_LAST_ITEM_DONE)
                }

            }
            queuesRestored = true
        }
    }

    private fun releaseResources() {
        playbackPlayerHandler?.removeCallbacksAndMessages(null)
        musicPlayerHandlerThread?.quitSafely()
        player?.release()
        player = null
        mediaSession?.release()
        stopScheduleTimer()
    }


    private fun handleChangeInternal(what: String) {
        Logger.d("$TAG -> handleChangeInternal -> what: $what")
        when (what) {
            PLAY_STATE_CHANGED -> {
                updateMediaSessionPlaybackState()
                val isPlaying = isPlaying
                if (!isPlaying && songProgressMillis > 0) {
                    savePositionInTrack()
                }
                playingNotification?.setPlaying(isPlaying)
                startForegroundOrNotify()
            }

            PLAYBACK_READY,
            META_CHANGED -> {
                Logger.d("$TAG -> handleChangeInternal -> META_CHANGED -> playingNotification: $playingNotification")
                playingNotification?.updateMetadata(currentSong) { startForegroundOrNotify() }

                // We must call updateMediaSessionPlaybackState after the load of album art is completed
                // if we are loading it or it won't be updated in the notification
                updateMediaSessionMetaData(::updateMediaSessionPlaybackState)
                savePosition()
                savePositionInTrack()
                pladioTrackingHandler.setCurrentSong(currentSong)
                serviceScope.launch(IO) {
                    val currentSong = currentSong
//                    if (sharedPreferences.userId().isNotBlank()) {
                        storage.saveSong(currentSong)
//                    }
                }
            }

            QUEUE_CHANGED -> {
//                mediaSession?.setQueueTitle(getString(R.string.pladio_player_control_now_playing_queue))
//                mediaSession?.setQueue(playingQueue.toMediaSessionQueue())
                updateMediaSessionMetaData(::updateMediaSessionPlaybackState) // because playing queue size might have changed
                if (playingQueue.size > 0) {
//                    prepareAndPlay(playingQueue.first())
                } else {
                    stopForegroundAndNotification()
                }
            }
        }
    }

    private fun sendChangeInternal(what: String) {
        LocalBroadcastManager.getInstance(this).sendBroadcast(Intent(what))
//        appWidgetBig.notifyChange(this, what)
//        appWidgetClassic.notifyChange(this, what)
//        appWidgetSmall.notifyChange(this, what)
//        appWidgetCard.notifyChange(this, what)
//        appWidgetText.notifyChange(this, what)
//        appWidgetMd3.notifyChange(this, what)
//        appWidgetCircle.notifyChange(this, what)
    }

    private fun sendChangeInteralWithExtraString(what: String, extraData: String) {
        LocalBroadcastManager.getInstance(this).sendBroadcast(Intent(what).apply {
            putExtra(SHOW_POPUP_ERROR_EXTRA_DATA, extraData)
        })
    }

    private fun sendActionWithExtraData(what: String, extraData: Bundle) {
        LocalBroadcastManager.getInstance(this).sendBroadcast(Intent(what).apply {
            putExtra(ACTION_WITH_EXTRA_DATA, extraData)
        })
    }
    // endregion

    // region Notification & MediaSession
    fun updateMediaSessionPlaybackState() {
        Logger.d("$TAG -> updateMediaSessionPlaybackState -> isPlaying: ${isPlaying} | songProgressMillis: $songProgressMillis | songDurationMillis: ${songDurationMillis} | playbackSpeed: $playbackSpeed")

//        val stateBuilder = PlaybackStateCompat.Builder()
//            .setActions(getCurrentMediaSessionAction())
//            .setState(
//                if (isPlaying) PlaybackStateCompat.STATE_PLAYING else PlaybackStateCompat.STATE_PAUSED,
//                songProgressMillis.toLong(),
//                playbackSpeed
//            )
//        mediaSession?.setPlaybackState(stateBuilder.build())
    }


    fun getCommand(): Player.Commands {
        return when (currentSong.songType) {
            Song.PladioType.Event -> {
                PLAY_PAUSE_COMMANDS
            }
            Song.PladioType.Music -> {
                if (repeatMode == REPEAT_MODE_ALL) {
                    ALL_COMMANDS
                } else {
                    if (playingQueue.size > 1) {
                        if (isLastTrack) {
                            PLAY_PAUSE_PREVIOUS_COMMANDS
                        } else if (position == 0) {
                            PLAY_PAUSE_NEXT_COMMANDS
                        } else {
                            ALL_COMMANDS
                        }
                    } else {
                        PLAY_PAUSE_COMMANDS
                    }
                }
            }
            Song.PladioType.Podcast -> {
                if (playingQueue.size > 1) {
                    if (isLastTrack) {
                        PLAY_PAUSE_PREVIOUS_COMMANDS
                    } else if (position == 0) {
                        PLAY_PAUSE_NEXT_COMMANDS
                    } else {
                        ALL_COMMANDS
                    }
                } else {
                    PLAY_PAUSE_COMMANDS
                }
            }
            else -> ALL_COMMANDS
        }
    }


    @SuppressLint("CheckResult")
    fun updateMediaSessionMetaData(onCompletion: () -> Unit) {
//        val song = currentSong
//        if (song.id == "") {
//            mediaSession?.setMetadata(null)
//            return
//        }
//        val metaData = MediaMetadataCompat.Builder()
//            .putString(MediaMetadataCompat.METADATA_KEY_TITLE, song.title)
//            .putString(MediaMetadataCompat.METADATA_KEY_ARTIST, song.artist)
//            .putLong(MediaMetadataCompat.METADATA_KEY_DURATION, songDurationMillis)
//            .putLong(
//                MediaMetadataCompat.METADATA_KEY_TRACK_NUMBER,
//                (getPosition() + 1).toLong()
//            )
//            .putBitmap(MediaMetadataCompat.METADATA_KEY_ALBUM_ART, null)
//            .putLong(MediaMetadataCompat.METADATA_KEY_NUM_TRACKS, playingQueue.size.toLong())
////            .putLong(MediaMetadataCompat.METADATA_KEY_YEAR, song.year.toLong())
////            .putString(MediaMetadataCompat.METADATA_KEY_ALBUM_ARTIST, song.albumArtist)
////            .putString(MediaMetadataCompat.METADATA_KEY_ALBUM, song.albumName)
//
//        // We must send the album art in METADATA_KEY_ALBUM_ART key on A13+ or
//        // else album art is blurry in notification
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
//            this.loadBitmapPalette(
//                url = song.posterUrl ?: "",
//                onCompleted = { _, bitmap ->
//                    metaData.putBitmap(
//                        MediaMetadataCompat.METADATA_KEY_ALBUM_ART,
//                        bitmap
//                    )
//                    mediaSession?.setMetadata(metaData.build())
//                    onCompletion()
//                },
//                onLoadCleared = { _ ->
//                    onCompletion()
//                },
//                onLoadFailed = { _ ->
//                    metaData.putBitmap(
//                        MediaMetadataCompat.METADATA_KEY_ALBUM_ART,
//                        BitmapFactory.decodeResource(
//                            resources,
//                            R.drawable.pladio_bottom_navigation_pladio_icon_focus
//                        )
//                    )
//                    mediaSession?.setMetadata(metaData.build())
//                    onCompletion()
//                }
//            )
//        } else {
//            mediaSession?.setMetadata(metaData.build())
//            onCompletion()
//        }
    }

    private fun startForegroundOrNotify() {
        Logger.d("$TAG -> startForegroundOrNotify -> playingNotification: $playingNotification")
        if (!isDestroy) {
            if (playingNotification != null && currentSong.id != "") {
                if (isForeground && !isPlaying) {
                    // This makes the notification dismissible
                    // We can't call stopForeground(false) on A12 though, which may result in crashes
                    // when we call startForeground after that e.g. when Alarm goes off,
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
                        ServiceCompat.stopForeground(this, ServiceCompat.STOP_FOREGROUND_DETACH)
                        isForeground = false
                    }
                }
                if (!isForeground && isPlaying) {
                    // Specify that this is a media service, if supported.
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        startForeground(
                            PlayingNotification.NOTIFICATION_ID, playingNotification!!.build(),
                            ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK
                        )
                    } else {
                        startForeground(
                            PlayingNotification.NOTIFICATION_ID,
                            playingNotification!!.build()
                        )
                    }
                    isForeground = true
                } else {
                    // If we are already in foreground just update the notification
                    notificationManager?.notify(
                        PlayingNotification.NOTIFICATION_ID, playingNotification!!.build()
                    )
                }
            }
        }
    }

    private fun stopForegroundAndNotification() {
        ServiceCompat.stopForeground(this, ServiceCompat.STOP_FOREGROUND_REMOVE)
        notificationManager?.cancel(PlayingNotification.NOTIFICATION_ID)
        isForeground = false
    }
    // endregion


    // region Exoplayer Build Request
    private fun observerStreamState() {
        serviceScope.launch {
            playbackStreamServiceHandler.state.collect { state ->
                Logger.d("$TAG -> observerStreamState -> state : $state")
                when (state) {
                    is PlaybackStreamServiceHandler.PlaybackApiState.Idle -> {
                    }
                    is PlaybackStreamServiceHandler.PlaybackApiState.Loading -> {
                    }
                    is PlaybackStreamServiceHandler.PlaybackApiState.SuccessAndPlay -> {
                        isStreamRetry = state.isRetry
                        resetGetStreamRetryStep()
                        pladioTrackingHandler.pladioUserInteractEvents.onFetchStreamSuccess()
                        updateStreamToObject(updateObject = state.currentSong, stream = state.stream)
                        processPlay(startPositionMs = state.startPositionMs, isAutoPlay = state.isAutoPlay)

                        //MQTT
                        publishStartToTopic(
                            contentType = MqttContentType.Pladio,
                            iType = ItemType.VOD,
                            pingStart = state.stream.pingMqtt,
                            mqttMode = state.stream.mqttMode
                        )

                    }
                    is PlaybackStreamServiceHandler.PlaybackApiState.SuccessEventStream -> {
                        isStreamRetry = state.isRetry
                        resetGetStreamRetryStep()
                        pladioTrackingHandler.pladioUserInteractEvents.onFetchStreamSuccess()
                        updateStreamToObject(updateObject = state.currentSong, stream = state.stream)
                        if (state.fromCheckPackage) {
                            // Check package, don't play
                        } else {
                            when(state.currentSong.songType) {
                                Song.PladioType.Event -> {
                                    when(state.currentSong.eventDetail?.type) {
                                        Song.PladioEventType.EventTV -> {
                                            processPlay(startPositionMs = state.startPositionMs, isAutoPlay = state.isAutoPlay)
                                            
                                            //MQTT
                                            publishStartToTopic(
                                                contentType = MqttContentType.Pladio,
                                                iType = ItemType.EventTV,
                                                pingStart = state.stream.pingMqtt,
                                                mqttMode = state.stream.mqttMode
                                            )
                                        }
                                        Song.PladioEventType.Event,
                                        Song.PladioEventType.Premiere -> {
                                            val eventState = Utils.getTimeLiveState(
                                                beginTime = Utils.convertStringToLong(
                                                    state.currentSong.eventDetail.beginTime,
                                                    0L
                                                ), endTime = Utils.convertStringToLong(state.currentSong.eventDetail.endTime, 0L)
                                            )
                                            if (eventState == 2) { // Live
                                                processPlay(startPositionMs = state.startPositionMs, isAutoPlay = state.isAutoPlay)
                                            }
                                            //MQTT
                                            publishStartToTopic(
                                                contentType = MqttContentType.Pladio,
                                                iType = ItemType.Event,
                                                pingStart = state.stream.pingMqtt,
                                                mqttMode = state.stream.mqttMode
                                            )
                                        }
                                        else -> {}
                                    }
                                }
                                else -> {}
                            }
                        }
                    }
                    is PlaybackStreamServiceHandler.PlaybackApiState.SuccessAndUpdateStream -> {
                        isStreamRetry = state.isRetry
                        resetGetStreamRetryStep()
                        pladioTrackingHandler.pladioUserInteractEvents.onFetchStreamSuccess()
                        updateStreamToObject(updateObject = state.currentSong, stream = state.stream)
                        //MQTT
                        publishStartToTopic(
                            contentType = MqttContentType.Pladio,
                            iType = getItemTypeFromCurrentSong(currentSong),
                            pingStart = state.stream.pingMqtt,
                            mqttMode = state.stream.mqttMode
                        )
                    }
                    is PlaybackStreamServiceHandler.PlaybackApiState.Error -> {
                        notifyGetStreamError(errorMessage = state.message)
                        val extraData = bundleOf(
                            ID_WITH_EXTRA_DATA to state.currentSong.id,
                            MESSAGE_WITH_EXTRA_DATA to state.message,
                        )
                        sendActionWithExtraData(ERROR_FOR_STREAM, extraData = extraData)
                    }
                    is PlaybackStreamServiceHandler.PlaybackApiState.ErrorByInternet -> {
                        isPlayerErrorByInternet = true
                        sendChangeInteralWithExtraString(SHOW_GENERAL_ERROR, extraData = state.message)
                    }
                    is PlaybackStreamServiceHandler.PlaybackApiState.ErrorRequiredLogin -> {
                        handleAndSendChangeInternal(ERROR_REQUIRED_LOGIN)
                    }
                    is PlaybackStreamServiceHandler.PlaybackApiState.ErrorRequiredVip -> {
                        handleAndSendChangeInternal(ERROR_REQUIRED_VIP)
                    }
                    is PlaybackStreamServiceHandler.PlaybackApiState.ErrorItemNotFound -> {
                        val extraData = bundleOf(
                            ID_WITH_EXTRA_DATA to state.currentSong.id,
                            TITLE_WITH_EXTRA_DATA to state.title,
                            MESSAGE_WITH_EXTRA_DATA to state.message,
                            BEGIN_TIME_WITH_EXTRA_DATA to state.currentSong.eventDetail?.beginTime
                        )
                        sendActionWithExtraData(ERROR_ITEM_NOT_FOUND_FOR_STREAM, extraData = extraData)
                    }
                    else -> {}
                }
            }

        }
    }

    private fun getItemTypeFromCurrentSong(currentSong: Song): ItemType {
        return when(currentSong.songType) {
            PladioType.Event -> {
                getItemTypeToEventType(currentSong.eventDetail?.type)
            }
            PladioType.Music,
            PladioType.Podcast -> {
                ItemType.VOD
            }
            else -> {
                ItemType.Unknown
            }
        }
    }

    private fun getItemTypeToEventType(type: Song.PladioEventType?): ItemType {
        return when(type) {
            Song.PladioEventType.EventTV -> {
                ItemType.EventTV
            }
            Song.PladioEventType.Event,
            Song.PladioEventType.Premiere -> {
                ItemType.Event
            }
            else -> {
                ItemType.Unknown
            }
        }
    }

    private fun updateStreamToObject(updateObject: Song, stream: Stream) {
        Logger.d("$TAG -> updateStreamToObject -> updateObject: $updateObject | stream: $stream")
        playingQueue.forEach { song ->
            if (song.id == updateObject.id && song.playlistId == updateObject.playlistId && song.streamRequestDataExtra?.episodeId == updateObject.streamRequestDataExtra?.episodeId) {
                song.stream = stream
                song.audioOnly = stream.isAudio

                /**
                 * Logic:
                 *  - Event: Get link share from api detail
                 *  - Else: Get link share from api stream
                 */
                if (song.songType != Song.PladioType.Event) {
                    song.shareUrl = stream.deepLink
                }
            }
        }
    }

    private fun processPlay(startPositionMs: Long = 0L, isAutoPlay: Boolean = true) {
        prepareAndPlay(getSongAt(position), startPositionMs = startPositionMs, isAutoPlay = isAutoPlay)
        notifyChange(META_CHANGED)
    }

    private fun prepareAndPlay(song: Song, startPositionMs: Long = 0L, isAutoPlay: Boolean = true) {
        Logger.d("$TAG -> prepareAndPlay -> song : $song | startPositionMs: $startPositionMs | isAutoPlay: $isAutoPlay")
        setupMediaSession()

        setupPlayerRetryHandler(channelId = song.id, streamId =  song.streamRequestDataExtra?.autoProfile ?: "")

        val request = buildPlayerRequest(
            id = song.id,
            streamId = song.streamRequestDataExtra?.autoProfile ?: "",
            isDrm = false,
            isAutoPlay = isAutoPlay,
            startPositionMs = startPositionMs,
            stream = song.stream ?: Stream(),
            isLive = song.songType == Song.PladioType.Event
        )
        player?.prepare(request)
    }

    private fun buildPlayerRequest(
        id: String,
        streamId: String,
        isDrm: Boolean,
        stream: Stream,
        isAutoPlay: Boolean,
        startPositionMs: Long,
        isLive: Boolean
    ): IPlayer.Request {
        fun buildUrlRequest(isDrm: Boolean): IPlayer.Request.Url {
            return if (isDrm)
                IPlayer.Request.Url(
                    dolbyVisionUrl = stream.urlDashDrmDolbyVision,
                    h265HDR10PlusUrl = stream.urlDashDrmH265Hdr10Plus,
                    h265HDR10Url = stream.urlDashDrmH265Hdr,
                    h265HlgUrl = stream.urlDashDrmH265Hlg,
                    h265Url = stream.urlDashDrmH265,
                    av1Url = stream.urlDashDrmAv1,
                    vp9Url = stream.urlDashDrmVp9,
                    url = stream.urlDash
                )
            else
                IPlayer.Request.Url(
                    dolbyVisionUrl = stream.urlDashDolbyVision,
                    h265HDR10PlusUrl = stream.urlDashH265Hdr10Plus,
                    h265HDR10Url = stream.urlDashH265Hdr,
                    h265HlgUrl = stream.urlDashH265Hlg,
                    h265Url = stream.urlDashH265,
                    av1Url = stream.urlDashAv1,
                    vp9Url = stream.urlDashVp9,
                    url = stream.urlDashNoDrm.ifEmpty { stream.urlSub.ifEmpty { stream.url } }
                )
        }

        val result = IPlayer.Request(
            id = id,
            streamId = streamId,
            url = buildUrlRequest(isDrm),
            forceUsingStartPosition = false,
            delayToPlay = false,
            clearRequestWhenOnStop = false,
            autoPlay = isAutoPlay,
            audioMode = true,
            isLive = isLive,
            headerRequestProperties = Utils.getHeaderForPlayerRequest(stream.streamSession)
        )
        return if (!isLive) result.copy(startPosition = startPositionMs) else result
    }
    // endregion

    // region Alarm Logic
    fun setupAlarmTimeConfig(data: List<AlarmTimeConfig>) {
        alarmConfig.clear()
        alarmConfig.addAll(data)
    }

    fun getAlarmConfig(): List<AlarmTimeConfig> {
        return alarmConfig.ifEmpty {
            PladioUtil.getDefaultAlarmTimeConfig()
        }
    }

    private fun startScheduleTimer(scheduleTimeMs: Long) {
        stopScheduleTimer()

        playbackTimerStartTime = System.currentTimeMillis()
        playbackTimerTargetTime = playbackTimerStartTime + scheduleTimeMs

        alarmTimer = Timer()
        alarmTimer?.schedule(object : TimerTask() {
            override fun run() {
                runOnUiThread {
                    letPausePlaybackByAlarm()
                    resetPlaybackTimerDefault()
                }
            }
        }, scheduleTimeMs)
    }

    private fun stopScheduleTimer() {
        playbackTimerStartTime = 0L
        playbackTimerTargetTime = 0L
        alarmTimer?.cancel()
        alarmTimer = null
    }

    private fun letPausePlaybackByAlarm() {
        pendingQuit = false
        pausePlaybackByAlarm()
    }

    private fun resetPlaybackTimerDefault() {
        playbackTimer = getAlarmConfig().find { it.typeAlarm == AlarmType.Off } ?: AlarmTimeConfig(typeAlarm = AlarmType.Off)
    }

    private fun triggerResetTimerDefaultForEndType() {
        if (playbackTimer.typeAlarm == AlarmType.End) {
            resetPlaybackTimerDefault()
        }
    }

    // endregion

    // region User Settings

    private fun updateUserSettingsWhenOpenNewQueue(song: Song, isTheSameList: Boolean) {
        when (song.songType) {
            Song.PladioType.Podcast -> {
                // Speed Settings
                player?.setPlaybackSpeed(playbackSpeed)

                // Shuffle Settings
                if (!isTheSameList) {
                    setShuffleMode(SHUFFLE_MODE_NONE)
                }

                // Repeat Settings
                if (!isTheSameList) {
                    repeatMode = REPEAT_MODE_NONE
                }
            }
            Song.PladioType.Music -> {
                // Speed Settings
                player?.setPlaybackSpeed(1f)

                // Shuffle Settings
                if (!isTheSameList) {
                    setShuffleMode(SHUFFLE_MODE_NONE)
                }

                // Repeat Settings
                if (!isTheSameList) {
                    repeatMode = REPEAT_MODE_NONE
                }
            }
            Song.PladioType.Event -> {
                // Speed Settings
                player?.setPlaybackSpeed(1f)

                // Shuffle Settings
                if (!isTheSameList) {
                    setShuffleMode(SHUFFLE_MODE_NONE)
                }

                // Repeat Settings
                if (!isTheSameList) {
                    repeatMode = REPEAT_MODE_NONE
                }

                // Reset timer alarm
                resetPlaybackTimerDefault()

            }
            else -> {
            }
        }
    }

    private fun resetUserSettingsAfterDestroyIfNecessary() {
        // Playback Speed
        playbackSpeed = 1f
        player?.setPlaybackSpeed(playbackSpeed = playbackSpeed)

        // Playback Timer
        resetPlaybackTimerDefault()

        // Reset repeat mode
        repeatMode = REPEAT_MODE_NONE

    }
    // endregion

    //region Handle User Realtime Playing
    private fun setupUserRealtimePlayingTracker() {
        userRealtimePlayingTracker.setData(contentId = currentSong.id, extraId = currentSong.streamRequestDataExtra?.episodeId ?: "")
    }

    private val userRealtimePlayingTrackerListener = object : UserRealtimePlayingTracker.UserRealtimePlayingTrackerListener {
        override fun onRealtimePlayingChanged(timeWatched: Long) {
//            Logger.d("$TAG onRealtimePlayingChanged => Time watched : $timeWatched")
            player?.let {
                pladioTrackingHandler.setCurrentDuration(it.currentDuration())
                pladioTrackingHandler.setTotalDuration(it.totalDuration())

            }
        }

        override fun onRealtimePlayingAtCheckpoint(timeWatched: Long, checkPoint: Long) {}
        override fun getCheckpointForRealtimeTracking(): List<Long> {
            return listOf()
        }
    }
    //endregion


    //region Handle Get Stream Error
    private fun setPlayerGetStreamRetryListener() {
        playerGetStreamRetryHandler.setListener(listener = playerGetStreamRetryListener)
    }

    private fun removePlayerGetStreamRetryListener() {
        playerGetStreamRetryHandler.setListener(listener = null)
    }

    private fun resetGetStreamRetryStep() {
        playerGetStreamRetryHandler.resetGetStreamRetryStep()
    }

    private fun notifyGetStreamError(errorMessage: String) {
        playerGetStreamRetryHandler.notifyGetStreamError(errorMessage = errorMessage)
    }

    private val playerGetStreamRetryListener = object : PlayerGetStreamRetryHandler.PlayerGetStreamRetryListener {
        override fun onRetryGetStream() {
            val restoredPositionInTrack = sharedPreferences.getPladioSavedPositionInTrack(SAVED_POSITION_IN_TRACK)
            tryRetry(delay = PlayerGetStreamRetryHandler.FIRST_TIME_DELAY_TIMES_MS, startPositionMs = restoredPositionInTrack.toLong())
        }

        override fun onShowGetStreamError(
            shouldCountDown: Boolean,
            countdownTimeMs: Long,
            errorMessage: String
        ) {
            if (FptPlayLifecycleObserver.appInBackground) {
                playerPiPRetryHandler.startRetryFlow(
                    processData = PlayerPiPRetryHandler.PlayerGetStreamRetryData(
                        shouldCountDown = shouldCountDown,
                        countdownTimeMs = countdownTimeMs,
                        errorMessage = errorMessage
                    ),
                    onCompleted = {
                        val restoredPositionInTrack = sharedPreferences.getPladioSavedPositionInTrack(SAVED_POSITION_IN_TRACK)
                        tryRetry(startPositionMs = restoredPositionInTrack.toLong())
                    }
                )
            } else {
                if (shouldCountDown) {
                    sendChangeInteralWithExtraString(SHOW_POPUP_ERROR_WITH_RETRY, extraData = errorMessage)
                    stopCountDownTimerRetry()
                    startCountDownTimerRetry(timeCountDown = countdownTimeMs / 1000L)
                } else {
                    sendChangeInteralWithExtraString(SHOW_POPUP_ERROR, extraData = errorMessage)
                }
            }
        }

        override fun onSendTrackingGetStreamError(
            isAutoRetry: Boolean,
            screen: String,
            errorMessage: String
        ) {
            pladioTrackingHandler.sendTrackingGetStreamError(
                isAutoRetry = isAutoRetry,
                screen = screen,
                errorMessage = errorMessage
            )
        }

    }

    //endregion


    //region Handle Player Error
    private fun setupPlayerRetryHandler(channelId: String, streamId: String) {
        playerRetryHandler.setupData(channelId = channelId, streamId = streamId)
    }

    private fun setPlayerRetryHandlerListener() {
        playerRetryHandler.setListener(listener = playerRetryHandlerListener)
    }

    private fun removePlayerRetryHandlerListener() {
        playerRetryHandler.removeListener()
    }

    private val playerRetryHandlerListener = object : PlayerRetryHandler.PlayerRetryHandlerListener {
        override fun getRealtimePlayingMs(): Long {
            return userRealtimePlayingTracker.getRealtimePlaying() * 1000L
        }

        override fun onRetryGetStream() {
            val restoredPositionInTrack = sharedPreferences.getPladioSavedPositionInTrack(SAVED_POSITION_IN_TRACK)
            tryRetry(startPositionMs = restoredPositionInTrack.toLong())
        }

        override fun onShowPlayerError(shouldCountDown: Boolean, countdownTimeMs: Long, code: Int, responseCode: Int) {
            showPlayerError(shouldCountDown = shouldCountDown, countdownTimeMs = countdownTimeMs, code = code, responseCode = responseCode)
        }

        override fun onSendTrackingPlayerError(
            isAutoRetry: Boolean,
            screen: String,
            errorCode: String,
            errorMessage: String
        ) {
            sendTrackingPlayerError(isAutoRetry = isAutoRetry, screen = screen, errorCode = errorCode, errorMessage = errorMessage)
        }

        override fun onClearAudioUserHistory() {
//            sharedPreferences.saveAudioVod("")
        }
    }

    private fun showPlayerError(shouldCountDown: Boolean, countdownTimeMs: Long, code: Int, responseCode: Int) {
        if (FptPlayLifecycleObserver.appInBackground) {
            playerPiPRetryHandler.startRetryFlow(
                processData = PlayerPiPRetryHandler.PlayerRetryData(
                    shouldCountDown = shouldCountDown,
                    countdownTimeMs = countdownTimeMs,
                    code = code,
                    responseCode = responseCode
                ),
                onCompleted = {
                    val restoredPositionInTrack = sharedPreferences.getPladioSavedPositionInTrack(SAVED_POSITION_IN_TRACK)
                    tryRetry(startPositionMs = restoredPositionInTrack.toLong())
                }
            )
        } else {
            if (shouldCountDown) {
                sendChangeInteralWithExtraString(SHOW_POPUP_ERROR_WITH_RETRY, getErrorMessage(code, responseCode))
                stopCountDownTimerRetry()
                startCountDownTimerRetry(timeCountDown = countdownTimeMs / 1000L)
            } else {
                sendChangeInteralWithExtraString(SHOW_POPUP_ERROR, getErrorMessage(code, responseCode))
            }
        }
    }

    // endregion

    //region Retry Task
    fun tryRetry(delay: Long = 0L, startPositionMs: Long = 0L) {
        stop(true)
        // todo
        playbackStreamServiceHandler.fetchPlayLink(
            currentSong = currentSong,
            pladioId = currentSong.getPladioId(),
            episodeId = currentSong.streamRequestDataExtra?.episodeId?:"",
            streamType = currentSong.streamRequestDataExtra?.autoProfile?:"",
            delay = delay,
            startPositionMs = startPositionMs,
            isRetry = true
        )
    }

    private fun getErrorMessage(code: Int, responseCode: Int): String {
        val message =
            MainApplication.INSTANCE.appConfig.msgPlayerError.ifBlank {
                this.getString(R.string.msg_player_error)
            }
        return if(responseCode != UnValidResponseCode) {
            "$message (Mã lỗi ${code}-${responseCode})"

        } else {
            "$message (Mã lỗi $code)"
        }
    }

    private fun startCountDownTimerRetry(timeCountDown: Long) {
        if (countDownTimerRetry == null) {
            countDownTimerRetry = object : CountDownTimer(timeCountDown * 1000 + 1000, 1000) {
                override fun onTick(millis: Long) {
                    sendChangeInteralWithExtraString(UPDATE_POPUP_ERROR_WITH_RETRY, extraData = "${getString(R.string.all_retry)} (${millis / 1000})")
                }

                override fun onFinish() {
                    stopCountDownTimerRetry()
                    sendChangeInteralWithExtraString(DISMISS_POPUP_ERROR_WITH_RETRY, extraData = "")

                    val restoredPositionInTrack = sharedPreferences.getPladioSavedPositionInTrack(SAVED_POSITION_IN_TRACK)
                    tryRetry(startPositionMs = restoredPositionInTrack.toLong())
                }
            }
            countDownTimerRetry?.start()
        }
    }

    fun stopCountDownTimerRetry() {
        if (countDownTimerRetry != null) {
            countDownTimerRetry?.cancel()
            countDownTimerRetry = null
        }
    }
    //endregion

    //region Tracking player retry
    private fun sendTrackingPlayerError(
        isAutoRetry: Boolean,
        screen: String,
        errorCode: String,
        errorMessage: String
    ) {
        pladioTrackingHandler.sendTrackingPlayerError(
            isAutoRetry = isAutoRetry,
            screen = screen,
            errorCode = errorCode,
            errorMessage = errorMessage,
            urlError = ( player as? ExoPlayerProxy)?.tracking?.urlError ?: "",
            responseHeaderWhenError = (player as? ExoPlayerProxy)?.tracking?.responseHeaderWhenError ?: "",
        )
        if (!isAutoRetry) {
            pladioTrackingHandler.sendTrackingShowPopupRetry(screen, errorCode, errorMessage)
        }
    }

    //endregion


    // region PladioTrackingHandler

    private val pladioTrackingCallback by lazy {
        object: PladioTrackingHandler.PladioTrackingCallback {
            override fun getRealTimePlaying(): Long {
                return userRealtimePlayingTracker.getRealtimePlaying()
            }

            override fun getRealTimePlaying(contentId: String?, extraId: String?): Long {
                return userRealtimePlayingTracker.getRealTimePlaying(contentId = contentId, extraId = extraId)
            }

            override fun getElapsedTimePlaying(): String? {
                return player?.let {
                    val position = it.currentDuration()
                    if (position != 0L) (position / 1000L).toString() else null
                }
            }

            override fun getVideoQuality(): String {
                return currentSong.streamRequestDataExtra?.autoProfile ?: ""
            }

            override fun getStreamUrl(): String {
                return (player as? ExoPlayerProxy)?.url() ?: ""
            }

            override fun getBandwidth(): String {
                return (player as? ExoPlayerProxy)?.getTrackingBandwidth() ?: ""
            }


            override fun getBitrate(): String {
                return (player as? ExoPlayerProxy)?.getTrackingBitrate() ?: ""
            }

            override fun getUrlMode(): String {
                return "${player?.urlMode()}${PlayerUtils.getH265LocalConfig(sharedPreferences)}${PlayerUtils.getH265HdrLocalConfig(sharedPreferences)}${PlayerUtils.getAV1LocalConfig(sharedPreferences)}${PlayerUtils.getVP9LocalConfig(sharedPreferences)}${PlayerUtils.getDolbyVisionLocalConfig(sharedPreferences)}"
            }

            override fun getTotalByteLoaded(): String {
                return player?.currentByteLoaded()?.toString() ?: ""
            }

            override fun getStreamHeaderSession(): String {
                return player?.request?.headerRequestProperties?.get(Utils.STREAM_SESSION_KEY) ?: ""
            }

            override fun isPlayerPlaying(): Boolean {
                return player?.isPlaying() ?: false
            }

            override fun getPlayerState(): PladioTrackingHandler.LogPladioPlayerInfo.LogPladioPlayerState {
                return if(FptPlayLifecycleObserver.appInBackground) {
                    PladioTrackingHandler.LogPladioPlayerInfo.LogPladioPlayerState.Hide
                } else if(playbackBarState == OpenPanel.PLAYBACK) {
                    PladioTrackingHandler.LogPladioPlayerInfo.LogPladioPlayerState.Maximize
                } else {
                    PladioTrackingHandler.LogPladioPlayerInfo.LogPladioPlayerState.Minimize
                }
            }


            override fun getRepeatType(): PladioTrackingHandler.LogPladioPlayerInfo.LogPladioPlayerRepeatMode {
                return when (repeatMode) {
                    REPEAT_MODE_NONE -> PladioTrackingHandler.LogPladioPlayerInfo.LogPladioPlayerRepeatMode.NoRepeat
                    REPEAT_MODE_ALL -> PladioTrackingHandler.LogPladioPlayerInfo.LogPladioPlayerRepeatMode.RepeatAll
                    REPEAT_MODE_THIS -> PladioTrackingHandler.LogPladioPlayerInfo.LogPladioPlayerRepeatMode.RepeatOne
                    else -> PladioTrackingHandler.LogPladioPlayerInfo.LogPladioPlayerRepeatMode.NoRepeat
                }
            }

            override fun getShuffleType(): PladioTrackingHandler.LogPladioPlayerInfo.LogPladioShuffleType {
                return if(shuffleMode == SHUFFLE_MODE_SHUFFLE) {
                    PladioTrackingHandler.LogPladioPlayerInfo.LogPladioShuffleType .Shuffle
                } else {
                    PladioTrackingHandler.LogPladioPlayerInfo.LogPladioShuffleType.NoShuffle
                }
            }

            override fun getIsStreamRetry(): Boolean = isStreamRetry
        }
    }
    // endregion PladioTrackingHandler

    // region Handle Network

    private val networkObserver = Observer<Boolean?> { value ->
        value?.let { hasInternet ->
            if (hasInternet) {
                onNetworkAvailable()
            } else {
                onNetworkLost()
            }
        }
    }

    private fun setupNetworkListener() {
        // Check internet status
        try {
            MainApplication.INSTANCE.networkDetector.observeForever(networkObserver)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun removeNetworkListener() {
        try {
            MainApplication.INSTANCE.networkDetector.removeObserver(networkObserver)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun onNetworkLost() {

    }

    private fun onNetworkAvailable() {
        when (PlayerUtils.getPlayingType()) {
            PlayerView.PlayingType.Local -> {
                if (isPlayerErrorByInternet) {
                    val restoredPositionInTrack = sharedPreferences.getPladioSavedPositionInTrack(SAVED_POSITION_IN_TRACK)
                    runOnUiThread {
                        tryRetry(startPositionMs = restoredPositionInTrack.toLong())
                    }
                }
            }
            PlayerView.PlayingType.Cast -> {}
        }
        isPlayerErrorByInternet = false
    }
    // endregion

    // region Handle Button Event
    fun checkHandlePlayPause(): Boolean {
        return when (currentSong.songType) {
            Song.PladioType.Music,
            Song.PladioType.Podcast -> false
            Song.PladioType.Event -> true
            else -> false
        }
    }
    // endregion

    //MQTT
    private fun publishStartToTopic(contentType: MqttContentType, iType : ItemType, pingStart: Boolean = false, mqttMode: Int = DEFAULT_MODE) {
        if (mqttPublisher != null) {
            publishEndToTopic()
        }
        if (pingStart) {
            mqttPublisher = Publisher(
                action = MqttUtil.ACTION_START,
                createdTime = getCurrentTimeInSeconds(),
                isRetry = 0,
                uid = sharedPreferences.userId(),
                contract = "",
                netMode = NetworkUtils.getNetworkMode(),
                appVer = BuildConfig.VERSION_NAME,
                profileId = sharedPreferences.profileId(),
                contentType = contentType.value.toString(),
                playlistId = if (currentSong.contentType != Song.PladioContentType.Series) currentSong.playlistId ?: "" else "",
                chapterId = currentSong.streamRequestDataExtra?.episodeId ?: "",
                episodeId = currentSong.streamRequestDataExtra?.realEpisodeId ?: "",
                itemId = currentSong.id,
                refPlaylistId = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refEpisodeId =TrackingUtil.contentPlayingInfo.refEpisodeId,
                appSource = "",
                isLinkDrm = "0",
                mode = mqttMode.toString(),
                sourceProvider = "",
                drmPartner = currentSong.stream?.merchant ?: "",
                businessPlan = "",
                isFree = "",
            ).apply {
                itemType = iType
                MqttConnectManager.INSTANCE.publishToTopic(
                    publisher = this,
                    type = itemType.id,
                    typeId = itemId
                )
            }
        }
    }
    private fun publishEndToTopic() {
        mqttPublisher?.let { publisher ->
            MqttConnectManager.INSTANCE.publishToTopic(
                publisher = publisher.copy(action = MqttUtil.ACTION_END, createdTime = getCurrentTimeInSeconds()),
                type = publisher.itemType.id,
                typeId = publisher.itemId
            )
            mqttPublisher = null
        } ?: kotlin.run { return }
    }
    
    inner class PlaybackBinder : Binder() {
        val service: PlaybackService
            get() = this@PlaybackService
    }

    data class RestoreSongDate(
        val song: Song?,
        val startPlayPositionMs: Long,
        val isAutoPlay: Boolean
    )

    companion object {
        val TAG: String = PlaybackService::class.java.simpleName
        const val PLADIO_MUSIC_PACKAGE_NAME = "com.fplay.activity.pladio"
        const val MUSIC_PACKAGE_NAME = "com.android.music"

        const val ACTION_SHOULD_RESTORE_ITEM = "$PLADIO_MUSIC_PACKAGE_NAME.shouldrestoreitem"
        const val ON_PROCESS_RESTORE_LAST_ITEM_DONE = "$PLADIO_MUSIC_PACKAGE_NAME.onprocessrestorelastitemdone"

        const val ACTION_TOGGLE_PAUSE = "$PLADIO_MUSIC_PACKAGE_NAME.togglepause"
        const val ACTION_PLAY = "$PLADIO_MUSIC_PACKAGE_NAME.play"
        const val ACTION_PAUSE = "$PLADIO_MUSIC_PACKAGE_NAME.pause"
        const val ACTION_STOP = "$PLADIO_MUSIC_PACKAGE_NAME.stop"
        const val ACTION_QUIT = "$PLADIO_MUSIC_PACKAGE_NAME.quitservice"
        const val ACTION_SKIP = "$PLADIO_MUSIC_PACKAGE_NAME.skip"
        const val ACTION_REWIND = "$PLADIO_MUSIC_PACKAGE_NAME.rewind"
        const val ACTION_PENDING_QUIT = "$PLADIO_MUSIC_PACKAGE_NAME.pendingquitservice"

        const val PLAYBACK_READY = "$PLADIO_MUSIC_PACKAGE_NAME.playbackready"
        const val META_CHANGED = "$PLADIO_MUSIC_PACKAGE_NAME.metachanged"
        const val QUEUE_CHANGED = "$PLADIO_MUSIC_PACKAGE_NAME.queuechanged"
        const val PLAY_STATE_CHANGED = "$PLADIO_MUSIC_PACKAGE_NAME.playstatechanged"
        const val REPEAT_MODE_CHANGED = "$PLADIO_MUSIC_PACKAGE_NAME.repeatmodechanged"
        const val SHUFFLE_MODE_CHANGED = "$PLADIO_MUSIC_PACKAGE_NAME.shufflemodechanged"
        const val PLAYBACK_SPEED_CHANGED = "$PLADIO_MUSIC_PACKAGE_NAME.playbackspeedchanged"
        const val PLAYBACK_TIMER_CHANGED = "$PLADIO_MUSIC_PACKAGE_NAME.playbacktimberchanged"
        const val EVENT_CHANGED = "$PLADIO_MUSIC_PACKAGE_NAME.evntchanged"

        const val PLAYBACK_SPEED = "PLADIO_PLAYBACK_SPEED"
        const val SAVED_POSITION = "POSITION"
        const val SAVED_POSITION_IN_TRACK = "POSITION_IN_TRACK"
        const val SAVED_SHUFFLE_MODE = "SHUFFLE_MODE"
        const val SAVED_REPEAT_MODE = "REPEAT_MODE"
        const val SHUFFLE_MODE_NONE = 0
        const val SHUFFLE_MODE_SHUFFLE = 1
        const val REPEAT_MODE_NONE = 0
        const val REPEAT_MODE_ALL = 1
        const val REPEAT_MODE_THIS = 2

        const val ERROR_REQUIRED_LOGIN = "$PLADIO_MUSIC_PACKAGE_NAME.errorrequiredlogin"
        const val ERROR_REQUIRED_VIP = "$PLADIO_MUSIC_PACKAGE_NAME.errorrequiredvip"

        const val GET_RECOMMEND_AFTER_RESTORE = "$PLADIO_MUSIC_PACKAGE_NAME.getrecommendafterrestore"
        const val GET_SERIES_AFTER_RESTORE = "$PLADIO_MUSIC_PACKAGE_NAME.getseriesafterrestore"
        const val GET_PLAYLIST_AFTER_RESTORE = "$PLADIO_MUSIC_PACKAGE_NAME.getplaylistafterrestore"
        const val GET_EVENT_DETAIL_AFTER_RESTORE = "$PLADIO_MUSIC_PACKAGE_NAME.geteventdetailafterrestore"

        const val SHOW_GENERAL_ERROR = "$PLADIO_MUSIC_PACKAGE_NAME.showgeneralerror"
        const val ERROR_ITEM_NOT_FOUND_FOR_STREAM = "$PLADIO_MUSIC_PACKAGE_NAME.erroritemnotfound"
        const val ERROR_FOR_STREAM = "$PLADIO_MUSIC_PACKAGE_NAME.errorforstream"
        const val SHOW_POPUP_ERROR_WITH_RETRY = "$PLADIO_MUSIC_PACKAGE_NAME.showpopuperrorwithretry"
        const val SHOW_POPUP_ERROR_EXTRA_DATA = "$PLADIO_MUSIC_PACKAGE_NAME.showpopuperrorwithretryextradata"
        const val UPDATE_POPUP_ERROR_WITH_RETRY = "$PLADIO_MUSIC_PACKAGE_NAME.updatepopuperrorwithretry"
        const val DISMISS_POPUP_ERROR_WITH_RETRY = "$PLADIO_MUSIC_PACKAGE_NAME.dismisspopuperrorwithretry"
        const val SHOW_POPUP_ERROR = "$PLADIO_MUSIC_PACKAGE_NAME.showpopuperror"


        private val DEFAULT_COMMANDS = Commands.Builder()
            .add(COMMAND_PLAY_PAUSE)
            .add(COMMAND_STOP)
            .add(COMMAND_GET_METADATA)
            .add(COMMAND_CHANGE_MEDIA_ITEMS)
            .add(COMMAND_GET_CURRENT_MEDIA_ITEM)
            .add(COMMAND_GET_TIMELINE)
            .add(COMMAND_SEEK_TO_DEFAULT_POSITION)
            .add(COMMAND_SEEK_TO_MEDIA_ITEM)
            .add(COMMAND_SEEK_IN_CURRENT_MEDIA_ITEM)
            .build()

        private val PLAY_PAUSE_COMMANDS = Commands.Builder()
            .addAll(DEFAULT_COMMANDS)
            .build()

        private val PLAY_PAUSE_NEXT_COMMANDS = Commands.Builder()
            .addAll(DEFAULT_COMMANDS)
            .add(COMMAND_SEEK_TO_NEXT)
            .add(COMMAND_SEEK_TO_NEXT_MEDIA_ITEM)
            .build()

        private val PLAY_PAUSE_PREVIOUS_COMMANDS = Commands.Builder()
            .addAll(DEFAULT_COMMANDS)
            .add(COMMAND_SEEK_TO_PREVIOUS)
            .add(COMMAND_SEEK_TO_PREVIOUS_MEDIA_ITEM)
            .build()

        private val ALL_COMMANDS = Commands.Builder()
            .addAll(DEFAULT_COMMANDS)
            .add(COMMAND_SEEK_TO_NEXT)
            .add(COMMAND_SEEK_TO_NEXT_MEDIA_ITEM)
            .add(COMMAND_SEEK_TO_PREVIOUS)
            .add(COMMAND_SEEK_TO_PREVIOUS_MEDIA_ITEM)
            .add(COMMAND_SEEK_BACK)
            .add(COMMAND_SEEK_FORWARD)
            .build()


    }
}
