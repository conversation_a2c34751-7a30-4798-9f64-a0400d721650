package com.fptplay.mobile.features.mqtt.model

import com.google.gson.annotations.SerializedName

class MessageArrived(
    val action: String, // "limit_ccu"
    val code: String,   // "406"
    val data: LimitCcuData,
    @SerializedName("item_id")
    val itemId: String,
    @SerializedName("episode_id")
    val episodeId: String,
    @SerializedName("chapter_id")
    val chapterId: String,
    @SerializedName("playlist_id")
    val playlistId: String,
    @SerializedName("created_time")
    val createdTime: Long
) {
    override fun toString(): String {
        return "MessageArrived(action='$action', code='$code', data=$data, itemId='$itemId', episodeId='$episodeId', chapterId='$chapterId', playlistId='$playlistId', createdTime=$createdTime)"
    }
}

data class LimitCcuData(
    val action: ActionDetails
) {
    override fun toString(): String {
        return "LimitCcuData(action=$action)"
    }
}

data class ActionDetails(
    val title: String,
    val desc: String,
    @SerializedName("title_en")
    val titleEn: String,
    @SerializedName("desc_en")
    val descEn: String
) {
    override fun toString(): String {
        return "ActionDetails(title='$title', desc='$desc', titleEn='$titleEn', descEn='$descEn')"
    }

}