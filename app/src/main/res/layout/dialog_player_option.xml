<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/ll_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/pairing_bottom_sheet_rounded_background"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/player_menu_options_item_height"
            android:layout_marginTop="@dimen/app_margin"
            android:gravity="start|center_vertical"
            android:padding="@dimen/app_margin_small"
            android:textColor="@color/white_87"
            android:textSize="@dimen/player_playlist_overlay_sub_title_text_size"
            android:textStyle="bold"
            tools:text="Tat phu de" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_options"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginBottom="@dimen/margin_common_double"
            android:layout_weight="1"
            tools:itemCount="2"
            tools:listitem="@layout/dialog_player_option_item" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>