package com.fptplay.mobile.common.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.net.wifi.WifiManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import timber.log.Timber
import java.net.InetAddress
import java.net.NetworkInterface
import java.util.*

object NetworkUtils {
    const val NETWORK_TIMEOUT = 1000L
    const val NETWORK_TIMEOUT_DELAY = 200L

    private val connectivityManager by lazy {
        MainApplication.INSTANCE.applicationContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    private val wifiManager by lazy {
        MainApplication.INSTANCE.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
    }

    /**
     * Returns MAC address of the given interface name.
     * @param interfaceName eth0, wlan0 or NULL=use first interface
     * @return  mac address or empty string
     */
    fun getMACAddress(interfaceName: String?): String {
        try {
            val interfaces: List<NetworkInterface> =
                Collections.list(NetworkInterface.getNetworkInterfaces())
            for (info in interfaces) {
                if (interfaceName != null) {
                    if (!info.name.equals(interfaceName, true)) continue
                }
                val mac: ByteArray = info.hardwareAddress ?: return ""
                val buf = StringBuilder()
                for (aMac in mac) buf.append(String.format("%02X:", aMac))
                if (buf.isNotEmpty()) buf.deleteCharAt(buf.length - 1)
                return buf.toString()
            }
        } catch (ignored: Exception) {
        }
        return ""
    }

    /**
     * Get IP address from first non-localhost interface
     * @param useIPv4   true=return ipv4, false=return ipv6
     * @return  address or empty string
     */
    fun getIPAddress(useIPv4: Boolean): String {
        try {
            val interfaces: List<NetworkInterface> =
                Collections.list(NetworkInterface.getNetworkInterfaces())
            for (info in interfaces) {
                val addressList: List<InetAddress> = Collections.list(info.inetAddresses)
                for (address in addressList) {
                    if (!address.isLoopbackAddress) {
                        val sAddress: String = address.hostAddress
                        val isIPv4 = sAddress.indexOf(':') < 0
                        if (useIPv4) {
                            if (isIPv4) return sAddress
                        } else {
                            if (!isIPv4) {
                                val delim = sAddress.indexOf('%')
                                return if (delim < 0) sAddress.toUpperCase(Locale.getDefault())
                                else sAddress.substring(0, delim).toUpperCase(Locale.getDefault())
                            }
                        }
                    }
                }
            }
        } catch (ignored: Exception) {
        }
        return ""
    }

    fun isWifiEnabled(): Boolean {
        return wifiManager.isWifiEnabled
    }

    fun isConnectedWifi(): Boolean {
        val nw = connectivityManager.activeNetwork
        val actNw = connectivityManager.getNetworkCapabilities(nw)
        return actNw?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ?: false
        //connectivityManager.activeNetworkInfo.type == ConnectivityManager.TYPE_WIFI
    }

    fun isConnectedEthernet(): Boolean {
        val nw = connectivityManager.activeNetwork
        val actNw = connectivityManager.getNetworkCapabilities(nw)
        return actNw?.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) ?: false
        //connectivityManager.activeNetworkInfo.type == ConnectivityManager.TYPE_ETHERNET
    }

    fun getWifiName(): String {
        return wifiManager.connectionInfo.ssid
    }

    fun getNetworkName(): String {
        return if (isWifiEnabled() && isConnectedWifi())
            getWifiName()
        else MainApplication.INSTANCE.applicationContext.getString(R.string.ethernet)
    }

    fun isNetworkAvailable(): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val nw      = connectivityManager.activeNetwork ?: return false
            val actNw = connectivityManager.getNetworkCapabilities(nw) ?: return false
            return when {
                actNw.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> true
                actNw.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> true
                else -> false
            }
        } else {
            val nwInfo = connectivityManager.activeNetworkInfo ?: return false
            return nwInfo.isConnected
        }
    }
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            val networkAvailable = isNetworkAvailable()
            MainApplication.INSTANCE.networkDetector.postValue(networkAvailable)
        }

        override fun onLost(network: Network) {
            try {
                Timber.d("*****onLost")
                Handler(Looper.getMainLooper()).postDelayed({
                    val networkAvailable = isNetworkAvailable()
                    MainApplication.INSTANCE.networkDetector.postValue(networkAvailable)
                }, NETWORK_TIMEOUT_DELAY)
            }
            catch (e: Exception) {
                val networkAvailable =  isNetworkAvailable()
                MainApplication.INSTANCE.networkDetector.postValue(networkAvailable)
            }
        }
    }

    fun registerNetworkCallback() {
        Timber.d("*****registerNetworkCallback")
        connectivityManager.registerNetworkCallback(NetworkRequest.Builder().build(), networkCallback)
    }

    fun unregisterNetworkCallback() {
        Timber.d("*****unregisterNetworkCallback")
        connectivityManager.unregisterNetworkCallback(networkCallback)
    }

    fun getNetworkMode(): String {
        return if (isWifiEnabled() && isConnectedWifi())
            MainApplication.INSTANCE.applicationContext.getString(R.string.wifi_mode)
        else MainApplication.INSTANCE.applicationContext.getString(R.string.ethernet_mode)
    }

    fun isConnectedCellular(): Boolean{
        val nw = connectivityManager.activeNetwork
        val actNw = connectivityManager.getNetworkCapabilities(nw)
        return actNw?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ?: false
    }
}