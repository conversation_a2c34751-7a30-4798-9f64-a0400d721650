package com.fptplay.mobile.player.views

import android.content.Context
import android.media.session.PlaybackState
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.annotation.*
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.widget.TextViewCompat
import com.fptplay.mobile.R
import com.fptplay.mobile.databinding.PlayerDoubleSeekOverlayBinding
import com.fptplay.mobile.player.PlayerUIView
import com.fptplay.mobile.player.PlayerView
import com.fptplay.mobile.player.handler.PlayerHandler
import com.fptplay.mobile.player.utils.visible
import androidx.media3.common.Player

class DoubleSeekOverlay @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, style: Int = 0) : FrameLayout(context, attrs, style),  PlayerUIView.PlayerDoubleTapListener {

    private val TAG = this::class.java.simpleName

    private var seekSeconds = 10

    private var _binding: PlayerDoubleSeekOverlayBinding? = null
    private val binding get() = _binding!!

    private var player: PlayerView?= null
    private var playerLayoutTouch: View ?= null
    private var doubleTapGestureListener: PlayerUIView.DoubleTapGestureListener?= null
    private var performListener: PerformListener? = null
    private var screenType: PlayerHandler.ScreenType ?= null
    private var isPlayingTimeShift = false
    private var playerControllerLocked = false
    private var isThumbShown = false
    private var playerUICanVisible = true


    /**
     * Size of the arc which will be clipped from the background circle.
     * The greater the value the more roundish the shape becomes
     */
    var arcSize: Float
        get() = binding.circleClipTapView.arcSize
        internal set(value) {
            binding.circleClipTapView.arcSize = value
        }

    /**
     * Color of the scaling circle on touch feedback.
     */
    var tapCircleColor: Int
        get() = binding.circleClipTapView.circleColor
        private set(value) {
            binding.circleClipTapView.circleColor = value
        }

    /**
     * Color of the clipped background circle
     */
    var circleBackgroundColor: Int
        get() = binding.circleClipTapView.circleBackgroundColor
        private set(value) {
            binding.circleClipTapView.circleBackgroundColor = value
        }

    /**
     * Duration of the circle scaling animation / speed in milliseconds.
     * The overlay keeps visible until the animation finishes.
     */
    var animationDuration: Long
        get() = binding.circleClipTapView.animationDuration
        private set(value) {
            binding.circleClipTapView.animationDuration = value
        }

    /**
     * Duration the icon animation (fade in + fade out) for a full cycle in milliseconds.
     */
    var iconAnimationDuration: Long = 750
        get() = binding.secondsView.cycleDuration
        private set(value) {
            binding.secondsView.cycleDuration = value
            field = value
        }

    /**
     * Text appearance of the *xx seconds* text.
     */
    @StyleRes
    var textAppearance: Int = 0
        private set(value) {
            TextViewCompat.setTextAppearance(binding.secondsView.textView, value)
            field = value
        }


    init {
        _binding = PlayerDoubleSeekOverlayBinding.inflate(LayoutInflater.from(context), this, true)

        // Initialize UI components
        initializeAttributes()
        binding.secondsView.isForward = true
        changeConstraints(true)

        // This code snippet is executed when the circle scale animation is finished
        binding.circleClipTapView.performAtEnd = {
            performListener?.onAnimationEnd()
            binding.secondsView.visibility = View.INVISIBLE
            binding.secondsView.seconds = 0
            binding.secondsView.stop()
        }
    }
    
    private fun initializeAttributes() {
        // Set defaults
        arcSize = context.resources.getDimensionPixelSize(R.dimen.player_yt_arc_size).toFloat()
        tapCircleColor = ContextCompat.getColor(context, R.color.player_yt_tap_circle_color)
        circleBackgroundColor = ContextCompat.getColor(context, R.color.player_yt_background_circle_color)
        animationDuration = 650
        iconAnimationDuration = 750
        seekSeconds = 10
        textAppearance = R.style.YTOSecondsTextAppearance
    }

    /**
     * Sets a listener to execute some code before and after the animation
     * (for example UI changes (hide and show views etc.))
     */
    fun performListener(listener: PerformListener) = apply {
        performListener = listener
    }


    fun setPlayer(player: PlayerView) {
        this.player = player
    }

    fun setPlayerTouch(playerLayoutTouch: View) {
        this.playerLayoutTouch = playerLayoutTouch
    }

    fun setDoubleTapGestureListener(doubleTapGestureListener: PlayerUIView.DoubleTapGestureListener) {
        this.doubleTapGestureListener = doubleTapGestureListener
    }

    fun setPlayerUIViewVisible(isVisible: Boolean) {
        this.playerUICanVisible = isVisible
    }

    fun updateScreenType(screenType: PlayerHandler.ScreenType) {
        this.screenType = screenType
    }

    fun updateIsPlayingTimeShift(isPlayingTimeShift: Boolean) {
        this.isPlayingTimeShift = isPlayingTimeShift
    }

    fun updatePlayerControllerLocked(isLock: Boolean) {
        this.playerControllerLocked = isLock
    }

    fun updateThumbShown(isThumbShown: Boolean) {
        this.isThumbShown = isThumbShown
    }

    override fun onDoubleTapStarted(posX: Float, posY: Float) {
        if (!enableSeek()) return

        if (player == null)
            return

        if (performListener?.shouldForward(player, doubleTapGestureListener, playerLayoutTouch, posX) == null)
            return
    }

    override fun onDoubleTapProgressUp(posX: Float, posY: Float) {
        if (!enableSeek()) return

        // Check first whether forwarding/rewinding is "valid"
        if (player == null ) return

        val shouldForward = performListener?.shouldForward(player, doubleTapGestureListener, playerLayoutTouch, posX)

        // YouTube behavior: show overlay on MOTION_UP
        // But check whether the first double tap is in invalid area
        if (this.visibility != View.VISIBLE) {
            if (shouldForward != null) {
                performListener?.onAnimationStart()
                binding.secondsView.visible()
                binding.secondsView.start()
            } else
                return
        }

        when (shouldForward) {
            false -> {
                // First time tap or switched
                if (binding.secondsView.isForward) {
                    changeConstraints(false)
                    binding.secondsView.apply {
                        isForward = false
                        seconds = 0
                    }
                }
                // Cancel ripple and start new without triggering overlay disappearance
                // (resetting instead of ending)
                binding.circleClipTapView.resetAnimation {
                    binding.circleClipTapView.updatePosition(posX, posY)
                }
                rewinding()
            }
            true -> {

                // First time tap or switched
                if (!binding.secondsView.isForward) {
                    changeConstraints(true)
                    binding.secondsView.apply {
                        isForward = true
                        seconds = 0
                    }
                }
                // Cancel ripple and start new without triggering overlay disappearance
                // (resetting instead of ending)
                binding.circleClipTapView.resetAnimation {
                    binding.circleClipTapView.updatePosition(posX, posY)
                }
                forwarding()
            }
            else -> {
                // Middle area tapped: do nothing
                //
                // playerView?.cancelInDoubleTapMode()
                // circle_clip_tap_view.endAnimation()
                // triangle_seconds_view.stop()
            }
        }
    }

    override fun onDoubleTapFinished() {
//        if (!enableSeek()) return
//        currentSeek = 0
    }
    /**
     * Seeks the video to desired position.
     * Calls interface functions when start reached ([SeekListener.onVideoStartReached])
     * or when end reached ([SeekListener.onVideoEndReached])
     *
     * @param newPosition desired position
     */
    private fun seekToPosition(newPosition: Long?) {
        if (newPosition == null) return

        // Start of the video reached
        if (newPosition <= 0) {
            player?.run {
                seek(0)
                play(true)
            }
            return
        }

        // End of the video reached
        player?.totalDuration()?.let { total ->
            if (newPosition >= total) {
                player?.run {
                    seek(total)
                    play(true)
                }
                return
            }
        }

        // Otherwise
        doubleTapGestureListener?.keepInDoubleTapMode()
        player?.run {
            seek(newPosition)
            play(true)
        }
    }

    private fun forwarding() {
        binding.secondsView.seconds += seekSeconds
        seekToPosition(player?.currentDuration()?.plus(seekSeconds * 1000))
    }

    private fun rewinding() {
        binding.secondsView.seconds += seekSeconds
        seekToPosition(player?.currentDuration()?.minus(seekSeconds * 1000))
    }

    private fun changeConstraints(forward: Boolean) {
        val constraintSet = ConstraintSet()
        with(constraintSet) {
            clone(binding.rootConstraintLayout)
            if (forward) {
                clear(binding.secondsView.id, ConstraintSet.START)
                connect(binding.secondsView.id, ConstraintSet.END,
                    ConstraintSet.PARENT_ID, ConstraintSet.END)
            } else {
                clear(binding.secondsView.id, ConstraintSet.END)
                connect(binding.secondsView.id, ConstraintSet.START,
                    ConstraintSet.PARENT_ID, ConstraintSet.START)
            }
            binding.secondsView.start()
            applyTo(binding.rootConstraintLayout)
        }
    }

    interface PerformListener {
        /**
         * Called when the overlay is not visible and onDoubleTapProgressUp event occurred.
         * Visibility of the overlay should be set to VISIBLE within this interface method.
         */
        fun onAnimationStart()

        /**
         * Called when the circle animation is finished.
         * Visibility of the overlay should be set to GONE within this interface method.
         */
        fun onAnimationEnd()

        /**
         * Determines whether the player should forward, rewind or skip this tap by doing
         * nothing / ignoring. Is called for each tap.
         *
         * By overriding this method you can check for self-defined conditions whether showing the
         * overlay and rewinding/forwarding (e.g. if the media source valid) or skip it.
         *
         * In the following you see the default conditions for each action (if there is no media
         * to play ([PlaybackState.STATE_NONE]), an error occurred ([PlaybackState.STATE_ERROR])
         * or the media is stopped ([PlaybackState.STATE_STOPPED]) the tap will be ignored in any
         * case):
         *
         *
         *      | Action  | Current position          | Screen width portion |
         *      |---------|---------------------------|----------------------|
         *      | rewind  | greater than 500 ms       | 0% to 45%            |
         *      | forward | less than total duration  | 55% to 100%          |
         *      | ignore  |       ------------        | between 35% and 65%  |
         *
         * @param player Current [Player]
         * @param playerView [PlayerView] which accepts the taps
         * @param posX Position of the tap on the x-axis
         *
         * @return `true` to forward, `false` to rewind or `null` to ignore.
         */
        fun shouldForward(player: PlayerView?, doubleTapGestureListener: PlayerUIView.DoubleTapGestureListener?, playerLayoutTouch: View ?, posX: Float): Boolean? {
            if (player == null) {
                doubleTapGestureListener?.cancelInDoubleTapMode()
                return null
            }

            playerLayoutTouch?.run {
                if (player.currentDuration()?: 0 > 200 && posX < width * 0.35f)
                    return false

                if (player.currentDuration()?: 0 < player.totalDuration()?:0 && posX > width * 0.55f)
                    return true
            }
            return null
        }

    }

    private fun enableSeek(): Boolean {
        if (!playerUICanVisible) return false
        if (playerControllerLocked) return false
        if (isThumbShown) return false
        if (screenType == null) return false
        if (screenType == PlayerHandler.ScreenType.Premiere) return false
        if (screenType == PlayerHandler.ScreenType.Vod) {
            return true
        }
        return if (screenType == PlayerHandler.ScreenType.Live) {
            isPlayingTimeShift
        } else {
            true
        }
    }
}