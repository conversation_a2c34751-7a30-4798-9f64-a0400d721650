<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/black"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.google.android.material.tabs.TabLayout
        android:layout_marginHorizontal="@dimen/_11sdp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1sdp"
        android:layout_marginTop="@dimen/_37sdp"
        app:tabIndicatorFullWidth="false"
        app:tabGravity="fill"
        app:tabMode="fixed"
        app:tabPaddingStart="@dimen/_2sdp"
        app:tabPaddingEnd="@dimen/_2sdp"
        app:tabIndicatorColor="@color/white"
        app:tabBackground="@color/transparent"
        app:tabIndicatorGravity="center"
        app:tabIndicatorHeight="@dimen/_1sdp" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/_37sdp"
        android:layout_height="@dimen/_37sdp"
        android:layout_marginEnd="@dimen/_1sdp"
        android:layout_marginTop="@dimen/_47sdp"
        android:padding="@dimen/_10sdp"
        android:scaleType="fitXY"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:srcCompat="@drawable/ic_x_close" />

</androidx.constraintlayout.widget.ConstraintLayout>
