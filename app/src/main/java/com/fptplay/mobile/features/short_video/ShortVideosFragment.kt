package com.fptplay.mobile.features.short_video

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.widget.ViewPager2
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.PageId
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.databinding.PageMomentErrorViewBinding
import com.fptplay.mobile.databinding.ShortVideoTabFragmentBinding
import com.fptplay.mobile.features.home.HomeMainFragment
import com.fptplay.mobile.features.moments.MomentsViewModel
import com.fptplay.mobile.features.short_video.adapter.ShortVideosViewPagerAdapter
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.invisible
import com.fptplay.mobile.player.utils.visible
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


@AndroidEntryPoint
class ShortVideosFragment() : BaseFragment<MomentsViewModel.MomentsState, MomentsViewModel.MomentsIntent>() {
    override val hasEdgeToEdge: Boolean = true
    override val viewModel: MomentsViewModel by activityViewModels()
    private var _binding: ShortVideoTabFragmentBinding? = null
    private val binding get() = _binding!!
    override val handleBackPressed: Boolean = true
    private var pagerAdapter: ShortVideosViewPagerAdapter? = null
    private var pageMomentErrorViewBinding: PageMomentErrorViewBinding? = null
    private var isSetFocusWhenStart = false


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = ShortVideoTabFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initData() {
        loadData()
    }
    private fun loadData(){
        TrackingUtil.resetDataScreenBlockAndRecommend()
        viewModel.isReloading = true
        (parentFragment as? HomeMainFragment)?.hideNoInternetView()
        (parentFragment as? HomeMainFragment)?.hidePageError()
        if (DataCacheObject.dataLikeCommentCache.dataActive || DataCacheObject.dataCache.needReload()) {
            DataCacheObject.clearDataCache() //clear list data of old profile
        }
        if (DataCacheObject.dataCache.getListTabData().isEmpty()) {
            fetchTabMenu()
        } else {
            initTabMenu()
        }
    }

    override fun MomentsViewModel.MomentsState.toUI() {
        when (this) {
            is MomentsViewModel.MomentsState.ResultListTabData -> {
                DataCacheObject.dataCache.saveListTabData(data)
                data.forEachIndexed { index, tab ->
                    if(tab.isFocus) {
                        isSetFocusWhenStart = false
                        DataCacheObject.dataCache.saveFocusTabWithIndex(index)
                    }
                }
                initTabMenu()
            }

            is MomentsViewModel.MomentsState.Error -> {
                if (this.intent is MomentsViewModel.MomentsIntent.GetListTab) {
                    hideLoading()
                    showPageMomentError(
                        errorMessage = getString(R.string.moment_error_api_des),
                        errorTitle = getString(R.string.moment_error_api_title)
                    )
                }
            }

            is MomentsViewModel.MomentsState.ErrorNoInternet -> {
                if (this.intent is MomentsViewModel.MomentsIntent.GetListTab) {
                    hideLoading()
                    showPageMomentError(
                        errorMessage = getString(R.string.moment_error_api_des),
                        errorTitle = getString(R.string.moment_error_api_title)
                    )
                }
            }

            is MomentsViewModel.MomentsState.ErrorRequiredLogin -> {
                //khong co case nay xay ra vi getlisttab ko y/c login
                if (this.intent is MomentsViewModel.MomentsIntent.GetListTab) {
                    hideLoading()
                    showPageMomentError(
                        errorMessage = getString(R.string.moment_error_api_des),
                        errorTitle = getString(R.string.moment_error_api_title)
                    )
                }
            }

            else -> {}
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        registerReloadBroadcast()
    }


    private fun registerReloadBroadcast(){
        val filter = IntentFilter()
        filter.addAction(Constants.NEED_RELOAD)
        LocalBroadcastManager.getInstance(MainApplication.INSTANCE).registerReceiver(
            reloadBroadcastReceiver, filter
        )
    }
    private fun unRegisterReloadBroadcast(){
        LocalBroadcastManager.getInstance(MainApplication.INSTANCE).unregisterReceiver(reloadBroadcastReceiver)
    }
    private val reloadBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if(intent?.action == Constants.NEED_RELOAD){
                if(intent.getStringExtra(Constants.TAB_ID_NEED_RELOAD) == PageId.ShortVideoPageId.id) {
                    if(!viewModel.isReloading) //neu dang reload thi ko lam gi ca
                        reloadAll()
                }
            }
        }
    }
    private fun reloadAll(){
        Logger.d("trangtest call reload shortvideo")
        DataCacheObject.clearDataCache() //clear list data of old profile
        loadData()
    }

    override fun onResume() {
        super.onResume()
        (parentFragment as? HomeMainFragment)?.setPaddingForSeekBarShortVideo(true)
    }

    override fun onDestroy() {
        unRegisterReloadBroadcast()
        super.onDestroy()
        (parentFragment as? HomeMainFragment)?.setPaddingForSeekBarShortVideo(false)
    }

    private fun fetchTabMenu() {
        viewModel.dispatchIntent(MomentsViewModel.MomentsIntent.GetListTab(PageId.ShortVideoPageId.id))
    }

    private fun initTabMenu() {
        if (DataCacheObject.dataLikeCommentCache.dataActive) {
            var isHaveTabCache = false
            DataCacheObject.dataCache.getListTabData().forEachIndexed { index, item ->
                if (item.id == DataCacheObject.dataLikeCommentCache.tabId) {
                    DataCacheObject.dataCache.saveFocusTabWithIndex(index)
                    isHaveTabCache = true
                }
            }
            if (!isHaveTabCache) {
                DataCacheObject.dataLikeCommentCache.resetData()
            }
        }
        binding.ivSearch.visible()
        pagerAdapter = ShortVideosViewPagerAdapter(childFragmentManager, lifecycle, DataCacheObject.dataCache.getListTabData().size)
        binding.tlMenu.invisible()
        binding.viewPager.apply {
            adapter = pagerAdapter
            TabLayoutMediator(binding.tlMenu, binding.viewPager) { tab, pos ->
                val item = DataCacheObject.dataCache.getListTabData().getOrNull(pos)
                tab.text = item?.title ?: ""
                tab.tag = item?.id ?: ""
            }.attach()

            lifecycleScope.launch {
                delay(100)
                isSetFocusWhenStart = true
                setCurrentItem(DataCacheObject.dataCache.getCurFocusTabIndex(), false)
                binding.tlMenu.postDelayed({
                    binding.tlMenu.visible()
                }, 200)
            }
        }
    }

    override fun bindEvent() {
        binding.tlMenu.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.let {
                    if(isSetFocusWhenStart) DataCacheObject.dataCache.saveFocusTabWithIndex(tab.position)
                }

            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {

            }

            override fun onTabReselected(tab: TabLayout.Tab?) {

            }
        })

        binding.ivSearch.setOnClickListener {
            findNavController().navigate(NavHomeMainDirections.actionGlobalToSearchFragmentDialog())
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        pageMomentErrorViewBinding = null
    }

    private fun destroyTabMenu(){
        pagerAdapter = ShortVideosViewPagerAdapter(childFragmentManager, lifecycle, 0)
        binding.viewPager.apply {
            adapter = pagerAdapter
        }
        binding.tlMenu.removeAllTabs()
        binding.ivSearch.invisible()
    }
    private fun showPageMomentError(errorMessage: String, errorTitle: String, btRetryText: String? = null) {
        viewModel.isReloading = false
        destroyTabMenu()
        if (pageMomentErrorViewBinding == null) {
            pageMomentErrorViewBinding = PageMomentErrorViewBinding.inflate(layoutInflater, binding.flError, false)
        }
        pageMomentErrorViewBinding?.let {
            binding.flError.removeAllViews()
            binding.flError.addView(it.root)
            it.ibBack.gone()
            it.tvDes.text = errorMessage
            it.tvTitle.text = errorTitle
            it.btRetry.text = btRetryText ?: getString(R.string.all_retry)
            it.btRetry.onClickDelay {
                binding.flError.removeView(it.root)
                reloadAll()
            }
        }
    }
    override fun backHandler() {
        checkExit()
    }

    private var backCount = 0
    private var lastBackTime = 0L
    private fun checkExit() {
        backCount += 1
        if (backCount == 1) {
            Toast.makeText(context, getString(R.string.press_back_2_times_to_exit_app), Toast.LENGTH_SHORT).show()
            lastBackTime = System.currentTimeMillis()
        } else if (backCount > 1) {
            if (System.currentTimeMillis() - lastBackTime < 1000L) {
                activity?.finish()
            } else {
                Toast.makeText(context, getString(R.string.press_back_2_times_to_exit_app), Toast.LENGTH_SHORT).show()
                lastBackTime = System.currentTimeMillis()
                backCount = 1
            }
        }
    }

}