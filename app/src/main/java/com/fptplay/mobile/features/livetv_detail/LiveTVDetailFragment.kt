package com.fptplay.mobile.features.livetv_detail

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.clearFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.onClick
import com.fptplay.mobile.common.extensions.showSnackBar
import com.fptplay.mobile.common.ui.bases.BaseActivity
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.view.GlobalSnackbarManager
import com.fptplay.mobile.common.ui.view.SnackbarManager
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.Navigation.navigateToLogin
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.Navigation.navigateToRequiredBuyPackage
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.databinding.LivetvDetailFragmentBinding
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.livetv_detail.LiveTVDetailViewModel.LiveTVDetailIntent.GetTvChannelStream
import com.fptplay.mobile.features.livetv_detail.LiveTVDetailViewModel.LiveTVDetailIntent.GetTvScheduleStream
import com.fptplay.mobile.features.livetv_detail.LiveTVDetailViewModel.LiveTVDetailState.ErrorByInternet
import com.fptplay.mobile.features.livetv_detail.LiveTVDetailViewModel.LiveTVDetailState.ErrorRequiredVip
import com.fptplay.mobile.features.livetv_detail.LiveTVDetailViewModel.LiveTVDetailState.ResultTvChannelStream
import com.fptplay.mobile.features.livetv_detail.LiveTVDetailViewModel.LiveTVDetailState.ResultTvScheduleStream
import com.fptplay.mobile.features.livetv_detail.LiveTVPlayerFragment.Companion.getCurrentChannelDetail
import com.fptplay.mobile.features.payment.PaymentViewModel
import com.fptplay.mobile.features.payment.google_billing.BillingUtils
import com.fptplay.mobile.vod.VodDetailViewModel
import com.xhbadxx.projects.module.domain.entity.fplay.live.TvChannel
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class LiveTVDetailFragment : BaseFragment<LiveTVDetailViewModel.LiveTVDetailState, LiveTVDetailViewModel.LiveTVDetailIntent>() {

    override val viewModel: LiveTVDetailViewModel by activityViewModels()

    private val safeArgs: LiveTVDetailFragmentArgs by navArgs()
    private var _binding: LivetvDetailFragmentBinding? = null
    private val binding get() = _binding!!
    private var buttonTypeBelowPlayer: String = ""
    companion object {
        val LOGIN_TYPE = "login_type"
        val BUY_PACKAGE_TYPE = "buy_package_type"
    }
    @Inject
    lateinit var sharedPreferences: SharedPreferences

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.saveChannelId("")
        viewModel.saveCurChannel(null)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = LivetvDetailFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        viewModel.triggerPreparePlayTrailer(null)
        viewModel.triggerPreparePlayPreview(null)
        TrackingUtil.idRelated = ""
        if (MainApplication.INSTANCE.sharedPreferences.shouldShowTooltip(Constants.TOOLTIP_SETTING_PLAYER)) {
            MainApplication.INSTANCE.sharedPreferences.setShouldShowTooltip(Constants.TOOLTIP_SETTING_PLAYER, shouldShow = false)
        }
    }

    override fun onResume() {
        super.onResume()
        sharedPreferences.saveCurrentContentType(Constants.FIREBASE_NOTIFICATION_FILED_TYPE_LIVE_TV)
        sharedPreferences.saveCurrentContentId(viewModel.getChannelId())
//        Timber.v("*** resume ${Constants.FIREBASE_NOTIFICATION_FILED_TYPE_LIVE_TV}  ${viewModel.getChannelId()}")
    }

    override fun onPause() {
        super.onPause()
        sharedPreferences.removeCurrentContentId()
        sharedPreferences.removeCurrentContentType()
//        Timber.w("*** pause ${Constants.FIREBASE_NOTIFICATION_FILED_TYPE_LIVE_TV} ")
    }

    override fun bindData() {
        viewModel.saveChannelId(channelId = viewModel.getChannelId().ifBlank { safeArgs.idToPlay })
        if (viewModel.getCurChannel() == null) {
            viewModel.saveCurChannel(channel = TvChannel(id = safeArgs.idToPlay, timeshiftLitmit = safeArgs.timeShiftLimit, timeshift = safeArgs.timeShift))
        }
        viewModel.saveGroupId(groupId = safeArgs.groupId)
        viewModel.triggerInitPlayer()
        hideViewBelowPlayer()
    }

    override fun bindEvent() {
        observeData()
        binding.layoutBuyPackageDetail.btnBuyPackage.onClick(200) {
            when(buttonTypeBelowPlayer) {
                LOGIN_TYPE -> {
                    parentFragment?.navigateToLoginWithParams(isDirect = true)
                }
                BUY_PACKAGE_TYPE -> {
                    viewModel.dispatchIntent(LiveTVDetailViewModel.LiveTVDetailIntent.TriggerBuyPackageForPreview)
                }
            }
        }
    }

    override fun LiveTVDetailViewModel.LiveTVDetailState.toUI() {
        when (this) {
            is LiveTVDetailViewModel.LiveTVDetailState.ResultTriggerShowSnackBar -> {
                if (context.isTablet()) {
                    showSnackbar(text = text)
                } else {
                    binding.clTvDetail.showSnackBar(text)
                }
            }
            is LiveTVDetailViewModel.LiveTVDetailState.ResultTriggerMsgUserReport->{
                if (context.isTablet()) {
                    showSnackbar(text = message)
                } else {
                    binding.clTvDetail.showSnackBar(message)
                }
            }
            is LiveTVDetailViewModel.LiveTVDetailState.ErrorRequiredLogin -> {
                when (intent) {
                    is GetTvScheduleStream,
                    is GetTvChannelStream -> {
                        requiredLogin?.let {
                            if (it.enablePreview) {
                                showViewRequestLogin(
                                    message = getTextRequestLoginChannel(binding.root.context),
                                    buttonTitle = binding.root.context.getString(R.string.login_title)
                                )
                            } else {
                                hideViewBelowPlayer()
                            }
                        }
                    }
                    else -> {
                        hideViewBelowPlayer()
                    }
                }
            }
            is ErrorRequiredVip -> {
                when (intent) {
                    is GetTvChannelStream -> {
                        requiredVip?.let {
                            if (it.enablePreview) {
                                showViewBuyPackage()
                            } else {
                                hideViewBelowPlayer()
                            }
                        }
                    }
                    else -> {
                        hideViewBelowPlayer()
                    }
                }
            }
            is LiveTVDetailViewModel.LiveTVDetailState.ResultTvChannelDetail -> {
                hideViewBelowPlayer()
            }
            is ErrorByInternet -> {
                hideViewBelowPlayer()
            }
            else -> {
            }
        }
    }

    private fun observeData() {
        viewModel.isFullScreen.observe(viewLifecycleOwner) {
            it?.let {
                if (context.isTablet()) {
                    handleTabletLayout(isFullscreen = it.first, isLandscapeMode = it.second)
                    if (it.second) { // isLandscapeMode
                        binding.navHostFragment.isVisible = !it.first
                    } else {
                        binding.navHostFragment.isVisible = !it.first
                    }
                } else {
                    binding.navHostFragment.isVisible = !it.first
                }
                handlePositionSnackBar(isFullscreen = it.first, isLandscape = it.second)
            }
        }
    }
    private fun handlePositionSnackBar(isFullscreen: Boolean,isLandscape: Boolean){
        GlobalSnackbarManager.withCurrent()?.position =  if (isFullscreen) SnackbarManager.Position.TOP_CENTER else SnackbarManager.Position.BOTTOM_CENTER
    }
    private fun showSnackBar(time: String, name: String) {
        val text = getString(R.string.live_tv_detail_snackbar_content1) + " " + name + " " + getString(R.string.live_tv_detail_snackbar_content2) + " " + time
        if (context.isTablet()) {
            showSnackbar(text = text)
        } else {
            binding.clTvDetail.showSnackBar(text)
        }
    }

    //region Tablet
    private fun handleTabletLayout(isFullscreen: Boolean, isLandscapeMode: Boolean) {
        if (_binding == null) return
        try {
            if (isLandscapeMode) {
                if (isFullscreen) {
                    ConstraintSet().apply {
                        // Set constraint for live tv content
                        connect(R.id.nav_host_fragment, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
                        connect(R.id.nav_host_fragment, ConstraintSet.START, R.id.f_player, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clTvDetail)
                } else {
                    ConstraintSet().apply {
                        // Set constraint for live tv content
                        connect(R.id.nav_host_fragment, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
                        connect(R.id.nav_host_fragment, ConstraintSet.START, R.id.f_player, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clTvDetail)
                }
            } else {
                if (isFullscreen) {
                    ConstraintSet().apply {
                        // Set constraint for live tv content
                        connect(R.id.nav_host_fragment, ConstraintSet.TOP, R.id.layout_buy_package_detail, ConstraintSet.BOTTOM)
                        connect(R.id.nav_host_fragment, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
                        connect(R.id.nav_host_fragment, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clTvDetail)
                } else {
                    ConstraintSet().apply {
                        // Set constraint for live tv content
                        connect(R.id.nav_host_fragment, ConstraintSet.TOP, R.id.layout_buy_package_detail, ConstraintSet.BOTTOM)
                        connect(R.id.nav_host_fragment, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
                        connect(R.id.nav_host_fragment, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clTvDetail)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    //endregion

    private fun showViewBuyPackage() {
        buttonTypeBelowPlayer = BUY_PACKAGE_TYPE
        AdjustAllEvent.sendPackageRecommendDisplayEvent(true)
        showViewBelowPlayer(
            message = BillingUtils.getTextDescriptionBuyPackageChannel(binding.root.context),
            buttonTitle = BillingUtils.getTextBuyPackageChannel(binding.root.context)
        )
    }

    private fun showViewRequestLogin(message: String, buttonTitle: String) {
        buttonTypeBelowPlayer = LOGIN_TYPE
        showViewBelowPlayer(
            message = message,
            buttonTitle = buttonTitle
        )
    }

    private fun showViewBelowPlayer(message: String, buttonTitle: String) {
        if (_binding != null) {
            binding.layoutBuyPackageDetail.tvBuyPackage.text = buttonTitle
            binding.layoutBuyPackageDetail.tvBuyPackageDescription.text = message
            binding.layoutBuyPackageDetail.root.show()
        }
    }

    private fun hideViewBelowPlayer() {
        buttonTypeBelowPlayer = ""
        if (_binding != null) {
            binding.layoutBuyPackageDetail.root.hide()
        }
    }

    private fun getTextRequestLoginChannel(context: Context): String {
        return MainApplication.INSTANCE.sharedPreferences.getMsgPreviewRequestLogin().ifBlank {
            context.getString(R.string.description_request_login_for_live_preview)
        }
    }
}