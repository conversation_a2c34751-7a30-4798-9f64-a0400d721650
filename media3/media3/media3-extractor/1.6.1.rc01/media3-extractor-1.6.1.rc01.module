{"formatVersion": "1.1", "component": {"group": "androidx.media3", "module": "media3-extractor", "version": "1.6.1.rc01", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.media3", "module": "media3-common", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-container", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-extractor-1.6.1.rc01.aar", "url": "media3-extractor-1.6.1.rc01.aar", "size": 771351, "sha512": "418cb6c38ab93e45f2c7311cee1fef0b896b768355c521fb3e7f0271d7bc2a5024ebaa783e941fe08162d4e1406cbb56d6547c242944f98f5cf77868631b91a2", "sha256": "cad7102585ed7ba076a122795ceae3b80ea9b01663f78ad4c60c39fa6e2454ee", "sha1": "656efe3867c5d09ba8732b3e64c38c0816c67659", "md5": "c88f41d7fc0b591a0b4cb037e4aac491"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.6.0"}}, {"group": "androidx.media3", "module": "media3-decoder", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-common", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-container", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-extractor-1.6.1.rc01.aar", "url": "media3-extractor-1.6.1.rc01.aar", "size": 771351, "sha512": "418cb6c38ab93e45f2c7311cee1fef0b896b768355c521fb3e7f0271d7bc2a5024ebaa783e941fe08162d4e1406cbb56d6547c242944f98f5cf77868631b91a2", "sha256": "cad7102585ed7ba076a122795ceae3b80ea9b01663f78ad4c60c39fa6e2454ee", "sha1": "656efe3867c5d09ba8732b3e64c38c0816c67659", "md5": "c88f41d7fc0b591a0b4cb037e4aac491"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "media3-extractor-1.6.1.rc01-sources.jar", "url": "media3-extractor-1.6.1.rc01-sources.jar", "size": 668572, "sha512": "8ce83a25cafc212d881a5d22b7d56501e719c9097e5dde7620552db863d3c65b634e556afe365fee804adbd4f803c8eefe8c94c210122398be5b838799327f10", "sha256": "8453813e8c64eaec0374aceacd0810c4cf7c3b2019961f2fee3609c4c4b0e3e1", "sha1": "168f5c757bc7dadf6d627cc56dac6b58ffbef729", "md5": "4a526c529793a2e77b6ae65a17c649e3"}]}]}