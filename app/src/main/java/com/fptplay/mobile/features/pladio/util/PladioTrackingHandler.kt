package com.fptplay.mobile.features.pladio.util

import android.os.Handler
import android.os.Looper
import androidx.annotation.OptIn
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.common.log.data.LogStreamInfo
import com.fptplay.mobile.common.log.data.LogUserTimeInfo
import com.fptplay.mobile.common.utils.AppErrorConstants
import com.fptplay.mobile.common.utils.DateTimeUtils
import com.fptplay.mobile.common.utils.TrackingConstants
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.features.pladio.data.PladioActionSeekType
import com.fptplay.mobile.features.pladio.data.Song
import com.fptplay.mobile.features.pladio.playback.PlaybackPlayerRemote
import com.fptplay.mobile.player.utils.PlayerDebugViewData
import androidx.media3.common.Format
import androidx.media3.common.Player
import androidx.media3.exoplayer.analytics.AnalyticsListener
import androidx.media3.exoplayer.DecoderReuseEvaluation
import androidx.media3.common.VideoSize
import androidx.media3.common.util.UnstableApi
import com.tear.modules.player.util.IPlayer
import com.tear.modules.player.util.PlayerUtils.getHdrType
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.pladio.PladioType
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import timber.log.Timber
import java.util.Locale
import javax.inject.Inject

@OptIn(UnstableApi::class)
class PladioTrackingHandler @Inject constructor(
    val trackingProxy: TrackingProxy,
    val trackingInfo: Infor,
    val sharedPreferences: SharedPreferences
) {

        val TAG = this::class.java.simpleName
    private var inforMobile =
        InforMobile() //save infor lại khi start 1 phim -> stop send log đúng infor này
    val playerEvents get() = playerCallback
    val playerAnalyticEvents get() = playerAnalyticsListener
    val playerTrackChangedEvents get() = playerTrackChangedListener
    val pladioUserInteractEvents get() = pladioUserInteractListener

    private var handlerTrackingHeartBeat: Handler? = null
    private var runnableTrackingHeartBeat = Runnable {
        sendTrackingHeartBeat()
    }
    private var isNeedUpdatePlayingSession = true
    private var pladioTrackingCallback: PladioTrackingCallback? = null
    private var currentSong = Song.emptySong
    private var currentDuration = 0L
    private var totalDuration = 0L
    private var timeExecuteActionSeek: Long? = null
    private var typeActionSeek: String? = null
    private var clickOnItemTimeInMs: Long = 0L

    // playerViewInfo
    private var playerVideoSize: VideoSize? = null

    companion object {
        const val LOG_PING_SCREEN_NAME = "PingPladio"
    }

    fun setupPladioTrackingHandler(pladioTrackingCallback: PladioTrackingCallback) {
        this.pladioTrackingCallback = pladioTrackingCallback
    }

    fun destroy() {
        pladioTrackingCallback = null
        removeTrackingHeartBeat()
        trackingInfo.updatePlayingSession(0)
    }

    // region send tracking
    private fun sendTrackingStartBuffering() {
        Timber.tag(TAG).d("sendTrackingStartBuffering")
        trackingProxy.sendEvent(
            infor = getCommonLogInfo(
                logId = "112",
                screen = "Buffering",
                event = "StartBuffering"
            ).mergeWithStreamInfo(getStreamInfoForLogger())
                .mergeWithUserTimeInfo(
                    getUserTimeInfoForLogger(
                        elapseTimePlaying = getElapsedTimePlayingForLog()
                    )
                )
        )

    }

    private fun sendTrackingEndBuffering(bufferLength: String) {
        Timber.tag(TAG).d("sendTrackingEndBuffering")
        trackingProxy.sendEvent(
            infor = getCommonLogInfo(
                logId = "112",
                screen = "Buffering",
                event = "EndBuffering"
            ).copy(
                bufferLength = bufferLength
            ).mergeWithStreamInfo(getStreamInfoForLogger())
                .mergeWithUserTimeInfo(getUserTimeInfoForLogger())
        )

    }

    private fun sendTrackingFirstFrame(timeFromClick: Long, timeFromPrepareSource: Long) {
        Timber.tag(TAG).d("sendTrackingFirstFrame")
        trackingProxy.sendEvent(
            infor = getCommonLogInfo(
                logId = TrackingConstants.EVENT_LOG_ID_FIRST_FRAME,
                screen = TrackingUtil.screen,
                event = if (pladioTrackingCallback?.getIsStreamRetry() == true) "Retry" else "Initial"
            ).copy(
                isPreAdv = "0",
                clickToPlayTime = timeFromClick.toString(),
                initPlayerTime = timeFromPrepareSource.toString(),
                adsTime = "0"
            ).mergeWithStreamInfo(getStreamInfoForLogger())
        )

    }

    private fun sendTrackingStartPladio() {
        Timber.tag(TAG).w("sendTrackingStartPladio")
        sendTrackingStopPladio() //stop phim trước đó nếu c

        if (isNeedUpdatePlayingSession) {
            val playingSession = System.currentTimeMillis()
            Timber.tag(TAG).i("updatePlayingSession on sendTrackingStartPladio $playingSession")
            trackingInfo.updatePlayingSession(playingSession)
//            vodPlaybackLogger.apply {
//                updatePlayingSession(playingSession)
//            }

            TrackingUtil.savePreviewProcessItem(vodId = "", episodeId = "")

        }
        isNeedUpdatePlayingSession = true
        initTrackingHeartBeat()
        inforMobile = getCommonLogInfo(
            logId = TrackingConstants.EVENT_LOG_ID_START_STREAM_VOD,
            screen = TrackingUtil.screen,
            event = TrackingConstants.EVENT_LOG_NAME_START_STREAM_VOD
        ).mergeWithStreamInfo(getStreamInfoForLogger())
            .mergeWithUserTimeInfo(getUserTimeInfoForLogger())

        trackingProxy.sendEvent(inforMobile)
        Timber.tag(TAG).w("sendTrackingStartPladio $inforMobile")
        sendHeartBeat()
    }

    private fun sendTrackingResumePladio() {
        if (inforMobile.logId != TrackingConstants.EVENT_LOG_ID_START_STREAM_VOD) return // ko có start phim trước đó hoặc đang cast thì ko gửi log
        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = "54",
                screen = inforMobile.screen,    // clone from log start
                event = "ResumeMovie",
                cloneCurrent = true
            ).mergeWithStreamInfo(getStreamInfoForLogger())
                .mergeWithUserTimeInfo(
                    getUserTimeInfoForLogger(
                        elapseTimePlaying = (currentDuration / 1000).toString(),
                        realtimePlaying = getRealTimePlayingForLog()
                    )
                )
        )
    }

    // region tracking Heartbeat
    private fun sendTrackingHeartBeat() {
        if (pladioTrackingCallback?.isPlayerPlaying() == true) {
            Logger.d("trangtest === sendTrackingHeartBeat runnable")
            sendHeartBeat()
        }

        handlerTrackingHeartBeat?.run {
            postDelayed(runnableTrackingHeartBeat, 60000)
        }
    }

    private fun sendHeartBeat(duration: Long = getTotalDuration()) {
        if (inforMobile.logId != TrackingConstants.EVENT_LOG_ID_START_STREAM_VOD) return // ko có start phim trước đó hoặc đang cast thì ko gửi log
        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = TrackingConstants.EVENT_LOG_ID_PING_PLADIO,
                screen = TrackingConstants.EVENT_LOG_PING_PLADIO_SCREEN_NAME,
                event = "Ping",
                cloneCurrent = true
            ).mergeWithStreamInfo(getStreamInfoForLogger(duration = duration))
                .mergeWithUserTimeInfo(
                    getUserTimeInfoForLogger(
                        elapseTimePlaying = getCurrentDurationString(),
                        realtimePlaying = pladioTrackingCallback?.getRealTimePlaying()?.toString()
                            ?: "",
                    )
                )
                .mergeWithLogPladioPlayerInfo(getPladioPlayerInfo())

        )
    }

    private fun initTrackingHeartBeat() {
        removeTrackingHeartBeat()
        if (handlerTrackingHeartBeat == null) {
            Looper.getMainLooper()?.run {
                handlerTrackingHeartBeat = Handler(this)
            }
        }
        handlerTrackingHeartBeat?.run {
            post(runnableTrackingHeartBeat)
        }
    }

    private fun removeTrackingHeartBeat() {
        handlerTrackingHeartBeat?.removeCallbacks(runnableTrackingHeartBeat)
        handlerTrackingHeartBeat = null
    }

    private fun sendHeartBeatStop(duration: Long = getTotalDuration()) {
        val canPingHeartBeat = true
        if (currentSong == Song.emptySong) return
        if (canPingHeartBeat) {
            trackingProxy.sendEvent(
                getCommonLogInfo(
                    logId = TrackingConstants.EVENT_LOG_ID_PING_PLADIO,
                    event = "Ping",
                    screen = TrackingConstants.EVENT_LOG_PING_PLADIO_SCREEN_NAME,
                    cloneCurrent = true
                ).mergeWithStreamInfo(getStreamInfoForLogger(duration = duration))
                    .mergeWithUserTimeInfo(
                        getUserTimeInfoForLogger(
                            elapseTimePlaying = getCurrentDurationString(),
                            realtimePlaying = pladioTrackingCallback?.getRealTimePlaying()
                                ?.toString() ?: "",
                        )
                    )
                    .mergeWithLogPladioPlayerInfo(getPladioPlayerInfo())

            )
        }
    }
    // endregion tracking Heartbeat

    private fun sendTrackingPausePladio() {
        if (inforMobile.logId != TrackingConstants.EVENT_LOG_ID_START_STREAM_VOD) return // ko có start phim trước đó hoặc đang cast thì ko gửi log
        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = "53",
                event = "PauseMovie",
                screen = inforMobile.screen,       // clone from log start
                cloneCurrent = true
            ).mergeWithStreamInfo(getStreamInfoForLogger())
                .mergeWithUserTimeInfo(
                    getUserTimeInfoForLogger(
                        elapseTimePlaying = getCurrentDurationString(),
                        realtimePlaying = getRealTimePlayingForLog(),
                    )
                )
        )
    }

    fun sendTrackingStopPladio() {
        Timber.tag(TAG).d("sendTrackingStopPladio stop ${inforMobile.logId}")
        if (inforMobile.logId != TrackingConstants.EVENT_LOG_ID_START_STREAM_VOD) return // ko có start phim trước đó hoặc đang cast thì ko gửi log
        sendHeartBeatStop()
        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = "52",
                event = "StopMovie",
                screen = inforMobile.screen,       // clone from log start
                cloneCurrent = true
            ).mergeWithStreamInfo(getStreamInfoForLogger())
                .mergeWithUserTimeInfo(
                    getUserTimeInfoForLogger(
                        elapseTimePlaying = getCurrentDurationString(),
                        realtimePlaying = pladioTrackingCallback?.getRealTimePlaying()
                            ?.toString() ?: "",
                    )
                )
        )
        TrackingUtil.resetIsRecommend() //reset when stop
        inforMobile = InforMobile()
    }

    fun sendTrackingPlayAttempt() {
        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = "521",
                screen = TrackingUtil.screen,
                event = "PlayAttemp"
            ).mergeWithStreamInfo(streamInfo = getStreamInfoForLogger())
        )
    }

    private fun sendTrackingChangeResolution() {
        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = "113",
                screen = "ChangeResolution",
                event = "ChangeResolution"
            ).copy(
                isManual = "0"
            )
                .mergeWithStreamInfo(streamInfo = getStreamInfoForLogger(duration = getTotalDuration()))
                .mergeWithUserTimeInfo(
                    getUserTimeInfoForLogger(
                        elapseTimePlaying = getElapsedTimePlayingForLog(),
                        realtimePlaying = getRealTimePlayingForLog(),
                    )
                )
        )
    }

    private fun sendTrackingNextPrev(logId: String, event: String) {
        sendTrackingStopPladio() //stop phim trước đó nếu có
        isNeedUpdatePlayingSession = false
        val playingSession = System.currentTimeMillis()
        Timber.tag(TAG).i("updatePlayingSession on sendTrackingNextPrev $playingSession")
        trackingInfo.updatePlayingSession(playingSession)
//        vodPlaybackLogger.apply {
//            updatePlayingSession(createPlayingSession())
//        }
        TrackingUtil.savePreviewProcessItem(vodId = "", episodeId = "")

        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = logId,
                event = event,
                screen = TrackingUtil.screen
            ).mergeWithStreamInfo(getStreamInfoForLogger(duration = getTotalDuration()))
        )
    }


    fun sendTrackingRepeatOrShuffleModeSelected(repeatModeSelected: Boolean, isManual: Boolean) {
        var screen: String? = null
        var event: String? = null
        if (repeatModeSelected) {
            screen = pladioTrackingCallback?.getRepeatType()?.rawValue
            event = "Repeat"
        } else {
            screen = pladioTrackingCallback?.getShuffleType()?.rawValue
            event = "Shuffle"
        }

        if (screen == null) return
        val infoMobile = getCommonLogInfo(
            logId = TrackingConstants.EVENT_LOG_ID_SHUFFLE_MODE_SELECTED,
            screen = screen,
            event = event
        )
        val isLandingPage = if (!repeatModeSelected) {
            if (isManual) "0" else "1"
        } else infoMobile.isLandingPage

        trackingProxy.sendEvent(
            infoMobile
                .copy(
                    isLandingPage = isLandingPage
                )
                .mergeWithStreamInfo(getStreamInfoForLogger())
                .mergeWithUserTimeInfo(getUserTimeInfoForLogger())
        )

    }

    fun sendTrackingSharePladio(song: Song) {
        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = TrackingConstants.EVENT_LOG_ID_SHARE_VOD,
                screen = TrackingUtil.screen,
                event = TrackingConstants.EVENT_LOG_EVENT_NAME_SHARE_VOD,
                song = song
            ).mergeWithStreamInfo(getStreamInfoForLogger())
                .mergeWithUserTimeInfo(getUserTimeInfoForLogger())
        )
    }

    fun sendTrackingAlarmTimeOffSelected(alarmName: String) {
        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = TrackingConstants.EVENT_LOG_ID_ALARM_TIME_OFF_SELECTED,
                screen = TrackingConstants.EVENT_SCREEN_NAME_ALARM_TIME_OFF_SELECTED,
                event = TrackingConstants.EVENT_EVENT_NAME_ALARM_TIME_OFF_SELECTED
            ).copy(
                itemName = alarmName
            ).mergeWithStreamInfo(getStreamInfoForLogger())
        )

    }


    fun sendTrackingSearchResult(haveResult: Boolean, searchKeyword: String, statusSearch: String) {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = TrackingConstants.EVENT_LOG_ID_SEARCH_RESULT,
                appId = TrackingConstants.EVENT_APP_ID_NAME_SEARCH_RESULT,
                appName = TrackingConstants.EVENT_APP_ID_NAME_SEARCH_RESULT,
                event = "Search",
                itemId = if (haveResult) "1" else "0",
                itemName = searchKeyword,
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                status = statusSearch
            )
        )

    }

    fun sendTrackingSharePladioPlaylist(playlistId: String, playlistTitle: String) {
        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = TrackingConstants.EVENT_LOG_ID_SHARE_VOD,
                screen = TrackingUtil.screen,
                event = TrackingConstants.EVENT_LOG_EVENT_NAME_SHARE_VOD
            ).copy(
                EpisodeID = "", // remove for share playlist
                chapterId = "", // remove for share playlist
                itemId = playlistId,
                itemName = playlistTitle,
                credit = "",    // remove for share playlist
                playlistID = playlistId,    // send playlistId = itemId, team data tự check if playlistID == itemId -> share playlist

            ).mergeWithStreamInfo(getStreamInfoForLogger())
                .mergeWithUserTimeInfo(getUserTimeInfoForLogger())
        )

    }

    fun sendTrackingEnterDetail(eventName: String) {
        Timber.tag(TAG).i("sendTrackingEnterDetail $eventName")
        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = TrackingConstants.EVENT_LOG_ID_ENTER_DETAIL_VOD,
                screen = TrackingUtil.screen,
                event = eventName,
            ).mergeWithStreamInfo(getStreamInfoForLogger())
                .mergeWithUserTimeInfo(
                    getUserTimeInfoForLogger(
                        realtimePlaying = "" //log 512 ko gửi realtimePlaying
                    )
                )
        )
    }

    fun sendTrackingEnterDetailPlaylist(playlistId: String, playlistTitle: String) {
        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = TrackingConstants.EVENT_LOG_ID_ENTER_DETAIL_VOD,
                screen = TrackingUtil.screen,
                event = TrackingConstants.EVENT_LOG_NAME_ENTER_DETAIL_PLAYLIST_VOD
            ).copy(
                status = "Playlist",
                itemId = playlistId,
                itemName = playlistTitle
            )
        )
    }


    // region tracking player error
    fun sendTrackingPlayerError(
        isAutoRetry: Boolean,
        screen: String,
        errorCode: String,
        errorMessage: String,
        urlError: String,
        responseHeaderWhenError: String
    ) {
        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = TrackingConstants.EVENT_LOG_ID_ERROR_STREAM_VOD,
                screen = screen,
                event = TrackingConstants.EVENT_LOG_NAME_ERROR_STREAM_VOD
            ).copy(
                errorCode = errorCode,
                errorMessage = errorMessage,
                errUrl = urlError,
                errHeader = responseHeaderWhenError
            ).mergeWithStreamInfo(getStreamInfoForLogger())
                .mergeWithUserTimeInfo(getUserTimeInfoForLogger())
        )
    }

    fun sendTrackingShowPopupRetry(
        screen: String,
        errorCode: String,
        errorMessage: String
    ) {
        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = "17",
                screen = screen,
                event = "Error"
            ).copy(
                errorCode = errorCode,
                errorMessage = errorMessage,
                issueId = TrackingUtil.createIssueId()
            ).mergeWithStreamInfo(getStreamInfoForLogger())
                .mergeWithUserTimeInfo(getUserTimeInfoForLogger())
        )

    }

    fun sendTrackingGetStreamError(
        isAutoRetry: Boolean,
        screen: String,
        errorMessage: String
    ) {
        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = "17",
                screen = screen,
                event = "Error"
            ).copy(
                errorCode = AppErrorConstants.GET_VOD_STREAM_CODE,
                errorMessage = errorMessage,
                issueId = TrackingUtil.createIssueId()
            ).mergeWithStreamInfo(getStreamInfoForLogger())
                .mergeWithUserTimeInfo(getUserTimeInfoForLogger())
        )

    }
    // endregion tracking player error

    private fun sendTrackingSeekComplete(
        seekType: String,
        seekTimeMs: String,
        seekToSec: String,
        durationSec: String
    ) {
        trackingProxy.sendEvent(
            getCommonLogInfo(
                logId = "514",
                screen = TrackingUtil.screen,
                event = seekType
            ).copy(
                realTimePlaying = seekTimeMs,
                elapsedTimePlaying = seekToSec,
                duration = durationSec
            )
        )
    }

    // endregion send tracking
    // region player callback
    val debugViewData: PlayerDebugViewData by lazy { PlayerDebugViewData() }

    private val playerCallback = object : IPlayer.IPlayerCallback {
        var startBufferTime = 0L
        var prepareSourceTimeInMs = 0L
        override fun onPrepare() {
            Timber.tag(TAG).d("$TAG => onPrepare")
            prepareSourceTimeInMs = System.currentTimeMillis()
            sendTrackingPlayAttempt()
        }

        override fun onBuffering() {
            Timber.tag(TAG).d("$TAG => onBuffering")
        }

        override fun startBuffering() {
            Timber.tag(TAG).d("$TAG => startBuffering")
            sendTrackingStartBuffering()
            startBufferTime = System.currentTimeMillis()
        }

        override fun endBuffering() {
            Timber.tag(TAG).d("$TAG => endBuffering")
            sendTrackingEndBuffering(bufferLength = (System.currentTimeMillis() - startBufferTime).toString())
        }

        override fun onReady() {
            Timber.tag(TAG).d("$TAG => onReady ${inforMobile.itemId} - ${currentSong.id}")
            val currentTime = System.currentTimeMillis()
            sendTrackingFirstFrame(
                timeFromClick = currentTime - clickOnItemTimeInMs,
                timeFromPrepareSource = currentTime - prepareSourceTimeInMs
            )

            val isStart = when (currentSong.contentType) {
                Song.PladioContentType.Single -> {
                    inforMobile.itemId != currentSong.id
                }

                Song.PladioContentType.Series -> {
                    inforMobile.itemId != currentSong.id || inforMobile.EpisodeID != currentSong.streamRequestDataExtra?.realEpisodeId
                }

                else -> {
                    inforMobile.itemId != currentSong.id
                }
            }

            if (isStart)
                sendTrackingStartPladio()
            else sendTrackingResumePladio()

        }

        override fun onStart() {
            Timber.tag(TAG).d("$TAG => onStart")
            timeExecuteActionSeek?.let { timeStartSeek ->
                sendTrackingSeekComplete(
                    seekType = typeActionSeek ?: "",
                    seekTimeMs = (System.currentTimeMillis() - timeStartSeek).toString(),
                    seekToSec = getElapsedTimePlayingForLog(),
                    durationSec = getTotalDuration().toString()
                )

                timeExecuteActionSeek = null
                typeActionSeek = null
            }
        }

        override fun onPause() {
            Timber.tag(TAG).d("$TAG => onPause")
        }

        override fun onPlay() {
            Timber.tag(TAG).d("$TAG => onPlay")
        }

        override fun onResume() {
            Timber.tag(TAG).d("$TAG => onResume")
        }


        override fun onFetchBitrateSuccess(bitrates: ArrayList<IPlayer.Bitrate>) {
            Timber.tag(TAG).d("$TAG => OnFetchBitrateSuccess: $bitrates")
        }

        override fun onBandwidth(message: String) {
            Timber.tag(TAG).d("$TAG => onBandwidth: $message")
        }

        override fun onRelease() {
            Timber.tag(TAG).d("$TAG => onRelease")
        }

        override fun onError(
            code: Int,
            name: String,
            detail: String,
            error403: Boolean,
            responseCode: Int
        ) {
            Timber.tag(TAG)
                .d("$TAG => onError: $code - $name - $detail - $error403 - $responseCode")
        }

        override fun onErrorBehindInLive(code: Int, name: String, detail: String) {
            Timber.tag(TAG).d("$TAG => onErrorBehindInLive: $code - $name - $detail")

        }

        override fun onErrorCodec(
            code: Int,
            name: String,
            detail: String,
            responseCode: Int,
            isDrm: Boolean,
            codec: IPlayer.CodecType
        ) {
            Timber.tag(TAG)
                .d("$TAG => onErrorCodec: $code - $name - $detail - $responseCode - $isDrm - $codec")
        }

        override fun onError6006WhenPreview(
            code: Int,
            name: String,
            detail: String,
            responseCode: Int
        ) {
            Timber.tag(TAG)
                .d("$TAG => onError6006WhenPreview: $code - $name - $detail - $responseCode")
        }

        override fun onError6006(code: Int, name: String, detail: String) {
            Timber.tag(TAG).d("$TAG => onError6006: $code - $name - $detail")
        }

        override fun onStop() {
            Timber.tag(TAG).d("$TAG => onStop")
        }

        override fun onEnd() {
            Timber.tag(TAG).d("$TAG => onEnd")
        }
    }
    private val playerTrackChangedListener = object : Player.Listener {
//        override fun onTimelineChanged(timeline: Timeline, reason: Int) {
//            (player?.internalPlayer() as? ExoPlayer)?.let { exoPlayer ->
//                if (exoPlayer.currentMediaItemIndex in 0 until  timeline.windowCount) {
//                    val windowCurrent =
//                        timeline.getWindow(exoPlayer.currentMediaItemIndex, Timeline.Window())
//                    if (windowCurrent.windowStartTimeMs != C.TIME_UNSET && windowCurrent.windowStartTimeMs > 0) {
//                        val localTime = windowCurrent.currentUnixTimeMs
//                        val nextTime = windowCurrent.windowStartTimeMs + exoPlayer.currentPosition
//                        debugViewData.latency =
//                            String.format("%.1fs", (localTime - nextTime) / 1000.0)
//
//                    } else {
//                        debugViewData.latency = ""
//                    }
//                } else {
//                    debugViewData.latency = ""
//                }
//
//                updateDebugView(debugViewData)
//            }
//        }
//
//        override fun onTracksInfoChanged(tracksInfo: TracksInfo) {
//            super.onTracksInfoChanged(tracksInfo)
//            Looper.getMainLooper()?.run { Handler(this).postDelayed({
//                playerUIListener?.onTracksInfoChanged()
//                updateTrackPlayerData()
//            }, 300) }
//        }

        override fun onVideoSizeChanged(videoSize: VideoSize) {
            super.onVideoSizeChanged(videoSize)
            if (playerVideoSize?.height != videoSize.height) {
//                playerUIListener?.onVideoSizeChanged(videoSize = videoSize)
                sendTrackingChangeResolution()

            }
            playerVideoSize = videoSize
        }
    }
    private val playerAnalyticsListener = object : AnalyticsListener {
        override fun onVideoInputFormatChanged(
            eventTime: AnalyticsListener.EventTime,
            format: Format,
            decoderReuseEvaluation: DecoderReuseEvaluation?
        ) {
            try {
                val fps =
                    if (format.frameRate != Format.NO_VALUE.toFloat()) "${format.frameRate}" else ""
                val codecs = format.codecs ?: ""
                val res = "${format.height}"
                val bitrate = format.bitrate
                val bitrateInMbps =
                    if (bitrate > 0) String.format("%.2f", (bitrate / 1_000_000.toFloat())) else ""

//                debugViewData.bitrateInMps = bitrateInMbps
                debugViewData.videoInfo.let { videoInfo ->
                    videoInfo.bitrateInMps = bitrateInMbps
                    videoInfo.bitrateInBitValue = if (bitrate >= 0) bitrate.toString() else ""
                    videoInfo.codec = codecs
                    videoInfo.fps = fps
                    videoInfo.res = res
                }

            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }

        override fun onAudioInputFormatChanged(
            eventTime: AnalyticsListener.EventTime,
            format: Format,
            decoderReuseEvaluation: DecoderReuseEvaluation?
        ) {
            try {
                val codecs = format.codecs ?: ""
                val bitrate = format.bitrate
                val bitrateInMbps =
                    if (bitrate > 0) String.format("%.2f", (bitrate / 1_000_000.toFloat())) else ""

                debugViewData.audioInfo.let { audioInfo ->
                    audioInfo.bitrateInMps = bitrateInMbps
                    audioInfo.bitrateInBitValue = if (bitrate >= 0) bitrate.toString() else ""
                    audioInfo.codec = codecs
                }

            } catch (ex: Exception) {
                ex.printStackTrace()
            }

        }

        override fun onBandwidthEstimate(
            eventTime: AnalyticsListener.EventTime,
            totalLoadTimeMs: Int,
            totalBytesLoaded: Long,
            bitrateEstimate: Long
        ) {
            debugViewData.bitrateInMps = String.format(
                Locale.getDefault(),
                "%.2f Mbps",
                bitrateEstimate / 1_000_000.toFloat()
            )
        }
    }
    private val pladioUserInteractListener = object : PladioUserInteractListener {
        override fun onPause() {
            sendTrackingPausePladio()
        }

        override fun onPlay() {
            sendTrackingResumePladio()
        }

        override fun onSeek(duration: Long, pladioActionSeekType: PladioActionSeekType) {
            timeExecuteActionSeek = System.currentTimeMillis()
            typeActionSeek = pladioActionSeekType.rawValue
        }

        override fun onNext() {
            sendTrackingNextPrev(logId = "55", event = "NextMovie")
        }

        override fun onPrev() {
            sendTrackingNextPrev(logId = "56", event = "PreviousMovie")
        }

        override fun onShuffleModeSelected() {
            sendTrackingRepeatOrShuffleModeSelected(repeatModeSelected = false, isManual = true)
        }

        override fun onRepeatModeSelected() {
            sendTrackingRepeatOrShuffleModeSelected(repeatModeSelected = true, isManual = true)
        }

        override fun onFetchStreamSuccess() {
            sendTrackingStopPladio()
            val playingSession = System.currentTimeMillis()
            trackingInfo.updatePlayingSession(playingSession)
            isNeedUpdatePlayingSession = false
        }

        override fun onPladioSelected() {
            clickOnItemTimeInMs = System.currentTimeMillis()
        }
    }

    // endregion player callback


    fun getRealTimePlayingForLog(song: Song = currentSong): String {
        val vodId = song.id
        return pladioTrackingCallback?.getRealTimePlaying(
            contentId = vodId,
            extraId = song.streamRequestDataExtra?.episodeId ?: ""
        ).toString() ?: "0"
    }


    fun getElapsedTimePlayingForLog(): String {
        return pladioTrackingCallback?.getElapsedTimePlaying()?.toString()
            ?: (currentDuration / 1000L).toString()
    }

    fun getVideoSize(): String {
        playerVideoSize?.let {
            return "${it.width}x${it.height}"
        } ?: kotlin.run {
            return ""
        }
    }

    // region getInfoForLog
    private fun getCommonLogInfo(
        logId: String,
        screen: String,
        event: String,
        song: Song = currentSong,
        cloneCurrent: Boolean = false
    ): InforMobile {
        return InforMobile(
            infor = trackingInfo,
            logId = logId,
//            appId = TrackingUtil.currentAppId,
            appId = TrackingConstants.EVENT_PLADIO_APP_ID,
            status = TrackingConstants.STATUS_NONE,
//            appName = TrackingUtil.currentAppName,
            appName = TrackingConstants.EVENT_PLADIO_APP_NAME,
            screen = screen,
            event = event,
            EpisodeID = if (cloneCurrent) inforMobile.EpisodeID else song.streamRequestDataExtra?.realEpisodeId
                ?: "",
            chapterId = if (cloneCurrent) inforMobile.chapterId else song.streamRequestDataExtra?.episodeId
                ?: "",
            itemId = if (cloneCurrent) inforMobile.itemId else song.id,
            itemName = if (cloneCurrent) inforMobile.itemName else song.title,
            blocKPosition = if (cloneCurrent) inforMobile.blocKPosition else TrackingUtil.blockIndex,
//            url = (binding.player.getPlayer() as? ExoPlayerProxy)?.url() ?: "",
//            directors = directors,        // not apply for pladio
//            publishCountry = publishCountry, // not apply for pladio
            boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
            dateStamp = DateTimeUtils.getLogSessionAtCurrentTime(),
            startTime = if (cloneCurrent) inforMobile.startTime else "0", // pladio have not logic bookmark
//            credit = if(cloneCurrent) inforMobile.credit else song.stream?.timeEndContent?.toString() ?: "",
            credit = "1", // hard code for pladio
            subMenuId = if (cloneCurrent) inforMobile.subMenuId else TrackingUtil.blockId,
//            duration = (totalDuration / 1000L).toString(),
//            elapsedTimePlaying = "",
//            realTimePlaying = getRealTimePlayingForLog(),
            keyword = if (cloneCurrent) inforMobile.keyword
            else if (TrackingUtil.screen == TrackingUtil.screenSearch) TrackingUtil.keyword
            else "",
            idRelated = if (cloneCurrent) inforMobile.idRelated else TrackingUtil.idRelated,
//            playlistID = song.playlistId ?: "",
            playlistID = if (cloneCurrent) inforMobile.playlistID
            else if (song.contentType != Song.PladioContentType.Series) song.playlistId ?: ""
            else "",   // if is pladio series not send playlistId
            isLastEpisode = "0", // hard code for pladio

            isRoot = sharedPreferences.isDeviceRooted(),
            widevineLevel = sharedPreferences.widevineLevel(),
            buildNumber = sharedPreferences.buildNumber(),
            refItemId = TrackingUtil.contentPlayingInfo.refId,
            refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
            refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
            dimension = TrackingUtil.getDimensionForTracking(
                width = sharedPreferences.getDisplayWidth(),
                height = sharedPreferences.getDisplayHeight()
            ),
            videoCodec = sharedPreferences.videoCodecInfo(),
            videoCodecDrm = sharedPreferences.videoDrmCodecInfo(),
            audioCodec = sharedPreferences.audioCodecInfo(),

            deviceFingerprint = sharedPreferences.deviceFingerprint(),
            urlMode = pladioTrackingCallback?.getUrlMode() ?: "",
            streamProfile = TrackingUtil.getStreamProfile(),
            totalByteLoaded = pladioTrackingCallback?.getTotalByteLoaded() ?: "",
            streamHeaderSession = pladioTrackingCallback?.getStreamHeaderSession() ?: "",
            hdrType = "${MainApplication.INSTANCE.applicationContext.getHdrType()}",
            isLive = if (cloneCurrent) inforMobile.isLive else if (currentSong.songType == Song.PladioType.Event) "2" else "0",
            position = if (cloneCurrent) inforMobile.position else TrackingUtil.position,
            isRecommend = TrackingUtil.isRecommend
        )
    }

    private fun getStreamInfoForLogger(duration: Long = getTotalDuration()): LogStreamInfo {
        return LogStreamInfo(
            bitrate = pladioTrackingCallback?.getBitrate() ?: "",
            bandwidth = pladioTrackingCallback?.getBandwidth() ?: "",
            streamBandwidth = debugViewData.videoInfo.getBitrateRawValue(),
            resolution = getVideoSize(),
            videoQuality = pladioTrackingCallback?.getVideoQuality() ?: "",

            audioName = "", // logic not apply for pladio
            audioBandwidth = debugViewData.audioInfo.getBitrateRawValue(),

            subtitle = "", // logic not apply for pladio

            durationInSecond = getTotalDuration(),

            url = pladioTrackingCallback?.getStreamUrl() ?: "",
            urlMode = pladioTrackingCallback?.getUrlMode() ?: "",
            status = LogStreamInfo.LogPlaybackStatus.None,
        )
    }

    private fun getUserTimeInfoForLogger(
        elapseTimePlaying: String = "",
        realtimePlaying: String = getRealTimePlayingForLog()
    ): LogUserTimeInfo {
        return LogUserTimeInfo(
            elapsedTimePlaying = elapseTimePlaying,
            realtimePlaying = realtimePlaying,
        )
    }

    private fun getPladioPlayerInfo(): LogPladioPlayerInfo {
        return LogPladioPlayerInfo(
            playerState = pladioTrackingCallback?.getPlayerState(),
            repeatMode = pladioTrackingCallback?.getRepeatType(),
            shuffleType = pladioTrackingCallback?.getShuffleType(),
        )
    }
    // endregion getInfoForLog

    //region util function
    private fun InforMobile.mergeWithStreamInfo(streamInfo: LogStreamInfo): InforMobile {
        return this.copy(
            Bitrate = streamInfo.bitrate,
            videoQuality = streamInfo.videoQuality,
            bandwidth = streamInfo.bandwidth,
            Resolution = streamInfo.resolution,
            streamBandwidth = streamInfo.streamBandwidth,

            audio = streamInfo.audioName,
            streamBandwidthAudio = streamInfo.audioBandwidth,

            subtitle = streamInfo.subtitle,

            duration = streamInfo.durationInSecond.toString(),

            url = streamInfo.url,
            multicast = streamInfo.url,
            urlMode = streamInfo.urlMode,
            status = streamInfo.status.rawValue
        )
    }

    private fun InforMobile.mergeWithUserTimeInfo(userTimeInfo: LogUserTimeInfo): InforMobile {
        return this.copy(
            realTimePlaying = userTimeInfo.realtimePlaying,
            elapsedTimePlaying = userTimeInfo.elapsedTimePlaying,
        )
    }


    private fun InforMobile.mergeWithLogPladioPlayerInfo(logPladioPlayerInfo: LogPladioPlayerInfo): InforMobile {
        return this.copy(
            playerState = logPladioPlayerInfo.playerState?.rawValue ?: "",
            repeatType = logPladioPlayerInfo.repeatMode?.rawValue ?: "",
            shuffleType = logPladioPlayerInfo.shuffleType?.rawValue ?: "",
        )
    }

    //endregion util function

    // region get & set
    fun setCurrentDuration(currentDuration: Long) {
        this.currentDuration = currentDuration
    }

    fun setTotalDuration(totalDuration: Long) {
        this.totalDuration = totalDuration
    }

    private fun getCurrentDurationString(): String {
        return (currentDuration / 1000L).toString()
    }

    private fun getTotalDuration(): Long {
        return (totalDuration / 1000L)
    }

    fun setCurrentSong(currentSong: Song) {
        Timber.tag(TAG).d("setCurrentSong: $currentSong")

        this.currentSong = currentSong
    }
    // endregion get & set

    data class LogPladioPlayerInfo(
        val playerState: LogPladioPlayerState? = LogPladioPlayerState.Minimize,
        val repeatMode: LogPladioPlayerRepeatMode? = LogPladioPlayerRepeatMode.NoRepeat,
        val shuffleType: LogPladioShuffleType? = LogPladioShuffleType.NoShuffle,


        ) {
        enum class LogPladioPlayerState(val rawValue: String) {
            Maximize("Maximize"),
            Minimize("Minimize"),
            Hide("Hide")
        }

        enum class LogPladioPlayerRepeatMode(val rawValue: String) {
            NoRepeat("NoRepeat"),
            RepeatAll("RepeatAll"),
            RepeatOne("RepeatOne")
        }

        enum class LogPladioShuffleType(val rawValue: String) {
            NoShuffle("NoShuffle"),
            Shuffle("Shuffle")
        }
    }

    interface PladioTrackingCallback {
        fun getRealTimePlaying(): Long
        fun getRealTimePlaying(contentId: String? = null, extraId: String? = null): Long
        fun getElapsedTimePlaying(): String?
        fun getVideoQuality(): String
        fun getStreamUrl(): String
        fun getBandwidth(): String
        fun getBitrate(): String
        fun getUrlMode(): String
        fun getTotalByteLoaded(): String
        fun getStreamHeaderSession(): String
        fun isPlayerPlaying(): Boolean
        fun getPlayerState(): LogPladioPlayerInfo.LogPladioPlayerState
        fun getRepeatType(): LogPladioPlayerInfo.LogPladioPlayerRepeatMode
        fun getShuffleType(): LogPladioPlayerInfo.LogPladioShuffleType
        fun getIsStreamRetry(): Boolean
    }

    interface PladioUserInteractListener {
        fun onPause()
        fun onPlay()
        fun onSeek(duration: Long, pladioActionSeekType: PladioActionSeekType)
        fun onNext()
        fun onPrev()
        fun onShuffleModeSelected()
        fun onRepeatModeSelected()
        fun onFetchStreamSuccess()
        fun onPladioSelected()
    }

}