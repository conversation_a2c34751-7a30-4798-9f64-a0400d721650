<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/black"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_background"
        android:scaleType="centerCrop"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/_37sdp"
        android:layout_height="@dimen/_37sdp"
        android:layout_marginEnd="@dimen/_1sdp"
        android:layout_marginTop="@dimen/_47sdp"
        android:padding="@dimen/_10sdp"
        android:scaleType="fitXY"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:srcCompat="@drawable/ic_x_close" />
    <TextView
        android:id="@+id/tv_head_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_close"
        android:layout_marginHorizontal="@dimen/_11sdp"
        tools:text="Gợi ý cho bạn"
        android:textColor="@color/white_38"
        android:textSize="@dimen/_11ssp"
        android:fontFamily="@font/sf_pro_display_semibold"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
    <TextView
        android:id="@+id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_head_title"
        android:layout_marginHorizontal="@dimen/_11sdp"
        tools:text="Liên kết số điện thoại ádadasdadadasda"
        android:textColor="@color/white_87"
        android:textSize="@dimen/_14ssp"
        android:fontFamily="@font/sf_pro_display_semibold"
        android:layout_marginTop="@dimen/_4sdp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/tv_des"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        android:layout_marginHorizontal="@dimen/_11sdp"
        tools:text="Hãy liên kết số điện thoại của bạn để có thể đăng nhập và sử dụng dịch vụ của FPT Play trên các thiết bị khác như: Thiết bị di động, SmartTV và trình duyệt máy tính."
        android:textColor="@color/white_60"
        android:textSize="@dimen/_11ssp"
        android:fontFamily="@font/sf_pro_display_regular"
        android:layout_marginTop="@dimen/_4sdp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_interaction"
        app:layout_constraintTop_toBottomOf="@id/tv_des"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/btn_go"
        android:layout_marginHorizontal="@dimen/_11sdp"
        android:scaleType="fitCenter"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/_11sdp"
        android:layout_marginBottom="@dimen/_11sdp"
        android:layout_height="0dp"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_go"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_34sdp"
        android:layout_marginBottom="@dimen/_23sdp"
        tools:text="@string/survey_start_survey"
        android:textAllCaps="false"
        android:textSize="@dimen/_13ssp"
        android:textColor="@color/white_87"
        android:fontFamily="@font/sf_pro_display_semibold"
        android:layout_marginHorizontal="@dimen/_11sdp"
        android:background="@drawable/login_button_rectangle_disable"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
