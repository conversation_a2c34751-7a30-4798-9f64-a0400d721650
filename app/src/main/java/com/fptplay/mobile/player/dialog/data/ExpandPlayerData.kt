package com.fptplay.mobile.player.dialog.data

import androidx.annotation.OptIn
import androidx.media3.common.util.UnstableApi
import androidx.media3.ui.AspectRatioFrameLayout
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject

data class ExpandPlayerData(
    override var id: String = "",
    var resizeModeId: Int,
    var title: String,
    var isVipRequired: Boolean = false,
    var vipImage: String = "",
    var isSelected: Boolean = false,
) : BaseObject() {

    companion object {
        @OptIn(UnstableApi::class)
        fun getDefaultData() : List<ExpandPlayerData> {
            val result = mutableListOf<ExpandPlayerData>()
            result.add(ExpandPlayerData(resizeModeId = AspectRatioFrameLayout.RESIZE_MODE_FIT, title = "Auto"))
            result.add(ExpandPlayerData(resizeModeId = AspectRatioFrameLayout.RESIZE_MODE_ZOOM, title = "Toàn màn hình (Resize)"))
            result.add(ExpandPlayerData(resizeModeId = AspectRatioFrameLayout.RESIZE_MODE_FIT, title = "Toàn màn hình (Fit)"))
            result.add(ExpandPlayerData(resizeModeId = AspectRatioFrameLayout.RESIZE_MODE_FILL, title = "Toàn màn hình (Fill)"))
//            result.add(ExpandPlayerData(resizeModeId = AspectRatioFrameLayout.RESIZE_MODE_FIXED_WIDTH, title = "Fixed Width"))
//            result.add(ExpandPlayerData(resizeModeId = AspectRatioFrameLayout.RESIZE_MODE_FIXED_HEIGHT, title = "Fixed Height"))
            return result
        }
    }
}