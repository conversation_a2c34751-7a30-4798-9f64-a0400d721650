package com.fptplay.mobile.player.dialog

import android.app.Dialog
import android.content.DialogInterface
import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.DialogFragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.databinding.DialogAlertInfoFragmentBinding
import com.fptplay.mobile.databinding.PlayerMenuMoreDialogFragmentBinding
import com.fptplay.mobile.player.adapter.PlayerMenuMoreAdapter
import com.fptplay.mobile.player.model.PlayerBottomControlData
import com.fptplay.mobile.player.utils.PlayerConstants
import com.xhbadxx.projects.module.util.common.IEventListener

class PlayerMenuMoreDialogFragment private constructor(private val menuData: List<PlayerBottomControlData>): DialogFragment() {

    companion object {
        fun newInstance(menu: List<PlayerBottomControlData>): PlayerMenuMoreDialogFragment {
            val fragment = PlayerMenuMoreDialogFragment(menu.filter {
                it._id in PlayerConstants.PlayerBottomControlType.ID_SUBTITLE_SETTING..PlayerConstants.PlayerBottomControlType.ID_PLAY_SPEED_SETTING && it.isEnable
            })
            return fragment
        }
    }

    private val menuAdapter by lazy { PlayerMenuMoreAdapter() }
    private var menuListener: PlayerMenuMoreItemSelectedListener? = null

    private var _binding: PlayerMenuMoreDialogFragmentBinding? = null
    private val binding get() = _binding!!


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialog)
    }

    override fun onStart() {
        super.onStart()
        dialog?.run {
            window?.setLayout(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = PlayerMenuMoreDialogFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = object : Dialog(requireContext(), theme) {}
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        return dialog
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initComponents()
        initEventListener()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        // Update container width immediately on orientation change
        if (context?.isTablet() == false) {
            menuListener?.onCancel()
            dismiss()
        }
    }

    fun setOnMenuClickListener(listener: PlayerMenuMoreItemSelectedListener) {
        menuListener = listener
    }

    private fun initComponents() {
        binding.rvOptions.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            adapter = menuAdapter
        }
        menuAdapter.bind(menuData)
        setContainerWidth()
    }

    private fun initEventListener() {
        binding.clContainer.onClickDelay {
            menuListener?.onCancel()
            dismiss()
        }

        menuAdapter.eventListener = object : IEventListener<PlayerBottomControlData> {
            override fun onClickedItem(position: Int, data: PlayerBottomControlData) {
                when (data._id) {
                    PlayerConstants.PlayerBottomControlType.ID_SUBTITLE_SETTING -> menuListener?.onSelectSubtitle()
                    PlayerConstants.PlayerBottomControlType.ID_AUDIO_SETTING -> menuListener?.onSelectAudio()
                    PlayerConstants.PlayerBottomControlType.ID_VIDEO_SETTING -> menuListener?.onSelectVideoQuality()
                    PlayerConstants.PlayerBottomControlType.ID_PLAY_SPEED_SETTING -> menuListener?.onSelectPlaySpeed()
                    else -> {}
                }
                dismiss()
            }
        }
    }

    private fun setContainerWidth() {
        val layoutParams = binding.llContainer.layoutParams
        val isTablet = context.isTablet()
        val isLandscape = MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE

        when {
            isTablet -> {
                // Tablet + portrait & Tablet + landscape: 640px
                layoutParams.width = resources.getDimensionPixelSize(R.dimen._266sdp)
            }
            isLandscape -> {
                // Phone + landscape: 420px
                layoutParams.width = resources.getDimensionPixelSize(R.dimen._300sdp)
            }
            else -> {
                // Phone + portrait: match parent
                layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
            }
        }

        binding.llContainer.layoutParams = layoutParams
    }
}

interface PlayerMenuMoreItemSelectedListener {
    fun onSelectSubtitle()
    fun onSelectAudio()
    fun onSelectVideoQuality()
    fun onSelectPlaySpeed()
    fun onCancel()
}