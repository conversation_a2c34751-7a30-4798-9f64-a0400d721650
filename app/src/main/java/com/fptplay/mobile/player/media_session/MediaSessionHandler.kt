package com.fptplay.mobile.player.media_session

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.os.Build
import android.view.KeyEvent
import androidx.media3.common.MediaMetadata
import androidx.media3.common.Player
import androidx.media3.common.Player.*
import androidx.media3.common.SimpleBasePlayer
import androidx.media3.common.util.UnstableApi
import androidx.media3.session.MediaSession
import com.fptplay.mobile.player.handler.PlayerHandler
import com.fptplay.mobile.services.player.CustomForwardingSimpleBasePlayer
import com.tear.modules.player.exo.ExoPlayerProxy
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.xhbadxx.projects.module.util.logger.Logger
import com.google.common.util.concurrent.Futures
import com.google.common.util.concurrent.ListenableFuture
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import javax.inject.Inject

@UnstableApi
class MediaSessionHandler @Inject constructor() {

    data class DataSource(
        val screenType: PlayerHandler.ScreenType? = null,
        val isPlayVipTrailer: Boolean = false,
        val isPlayTimeShift: Boolean = false,
    )

    companion object {
        private const val TAG = "MediaSessionHandler"

        private val DEFAULT_COMMANDS = Commands.Builder()
            .add(COMMAND_PLAY_PAUSE)
            .add(COMMAND_STOP)
            .add(COMMAND_GET_METADATA)
            .add(COMMAND_CHANGE_MEDIA_ITEMS)
            .add(COMMAND_GET_CURRENT_MEDIA_ITEM)
            .add(COMMAND_GET_TIMELINE)
            .add(COMMAND_SEEK_TO_DEFAULT_POSITION)
            .add(COMMAND_SEEK_TO_MEDIA_ITEM)
            .add(COMMAND_SEEK_IN_CURRENT_MEDIA_ITEM)
            .build()

        private val PLAY_PAUSE_COMMANDS = Commands.Builder()
            .addAll(DEFAULT_COMMANDS)
            .build()

        private val PLAY_PAUSE_NEXT_COMMANDS = Commands.Builder()
            .addAll(DEFAULT_COMMANDS)
            .add(COMMAND_SEEK_TO_NEXT)
            .add(COMMAND_SEEK_TO_NEXT_MEDIA_ITEM)
            .build()

        private val PLAY_PAUSE_PREVIOUS_COMMANDS = Commands.Builder()
            .addAll(DEFAULT_COMMANDS)
            .add(COMMAND_SEEK_TO_PREVIOUS)
            .add(COMMAND_SEEK_TO_PREVIOUS_MEDIA_ITEM)
            .build()

        private val ALL_COMMANDS = Commands.Builder()
            .addAll(DEFAULT_COMMANDS)
            .add(COMMAND_SEEK_TO_NEXT)
            .add(COMMAND_SEEK_TO_NEXT_MEDIA_ITEM)
            .add(COMMAND_SEEK_TO_PREVIOUS)
            .add(COMMAND_SEEK_TO_PREVIOUS_MEDIA_ITEM)
            .add(COMMAND_SEEK_BACK)
            .add(COMMAND_SEEK_FORWARD)
            .build()

    }

    private var context: Context? = null
    private var exoPlayerProxy: ExoPlayerProxy? = null
    private var dataSource: DataSource? = null
    private var mediaSession: MediaSession? = null

    private var listener: OnMediaSessionListener? = null

    fun initMediaSessionHandler(context: Context, listener: OnMediaSessionListener) {
        Logger.d("$TAG -> initMediaSessionHandler")
        this.context = context
        this.listener = listener
    }

    fun releaseMediaSession() {
        Logger.d("$TAG -> releaseMediaSession")
        unbindMediaSession()
        listener = null
        exoPlayerProxy = null
    }

    fun setupPlayerAndBindSession(exoPlayerProxy: ExoPlayerProxy?, dataSource: DataSource) {
        Logger.d("$TAG -> setupPlayerAndBindSession")
        this.dataSource = dataSource
        if (this.exoPlayerProxy == null || this.mediaSession == null) {
            this.exoPlayerProxy = exoPlayerProxy
            bindMediaSession()
        }
    }

    private fun bindMediaSession() {
        unbindMediaSession()
        context?.let { context ->
            (exoPlayerProxy?.internalPlayer() as? Player)?.let { player ->
                val wrapperPlayer = CustomForwardingSimpleBasePlayer(player, onForwardingSimpleBasePlayerListener)
                mediaSession = MediaSession.Builder(context, wrapperPlayer)
                    .setId("MediaSessionPlayerService")
                    .setCallback(mediaSessionCallback)
                    .build()

                updateMediaMetadata(exoPlayerProxy)
            }
        }
    }

    fun unbindMediaSession() {
        Logger.d("$TAG -> unbindMediaSession")
        mediaSession?.run {
            release()
        }
        mediaSession = null
    }

    private fun checkEnableAllActionEvent(dataSource: DataSource): Boolean {
        val isEnableAllActions = if (dataSource.isPlayVipTrailer) {
            false
        } else when (dataSource.screenType) {
            is PlayerHandler.ScreenType.Vod -> true
            is PlayerHandler.ScreenType.Live -> dataSource.isPlayTimeShift
            is PlayerHandler.ScreenType.Premiere -> false
            else -> false
        }
        Logger.d("$TAG -> checkEnableAllActionEvent -> isEnableAllActions: $isEnableAllActions")
        return isEnableAllActions
    }

    private fun checkHandlePlayPause() : Boolean {
        return checkEnableAllActionEvent(dataSource ?: DataSource())
    }

    private fun getCurrentPosition(): Int {
        return listener?.getCurrentIndexPosition() ?: 0
    }

    //region Media Session
    private val onForwardingSimpleBasePlayerListener = object : CustomForwardingSimpleBasePlayer.OnForwardingSimpleBasePlayerListener {
        override fun getState(state: SimpleBasePlayer.State): SimpleBasePlayer.State {
            return state
                .buildUpon()
                .setAvailableCommands(getCommand())
                .build()
        }

        override fun onSkipToNext(player: Player) {
            Logger.d("$TAG -> OnForwardingSimpleBasePlayerListener -> onSkipToNext")
            listener?.setSkipToNext()
        }

        override fun onSkipToPrevious(player: Player) {
            Logger.d("$TAG -> OnForwardingSimpleBasePlayerListener -> onSkipToPrevious")
            listener?.setSkipToPrevious()
        }
    }


    private val mediaSessionCallback = object : MediaSession.Callback {
        override fun onConnect(
            session: MediaSession,
            controller: MediaSession.ControllerInfo
        ): MediaSession.ConnectionResult {
            val availableSessionCommands = MediaSession.ConnectionResult.DEFAULT_SESSION_COMMANDS.buildUpon()
            val availablePlayerCommands = getCommand()

            return MediaSession.ConnectionResult.accept(
                availableSessionCommands.build(),
                availablePlayerCommands
            )
        }

        override fun onPostConnect(session: MediaSession, controller: MediaSession.ControllerInfo) {
            super.onPostConnect(session, controller)
            updateMediaMetadata(exoPlayerProxy)
        }

        override fun onMediaButtonEvent(
            session: MediaSession,
            controller: MediaSession.ControllerInfo,
            mediaButtonEvent: Intent
        ): Boolean {
            val keyEvent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                mediaButtonEvent.getParcelableExtra(Intent.EXTRA_KEY_EVENT, KeyEvent::class.java)
            } else {
                @Suppress("DEPRECATION")
                mediaButtonEvent.getParcelableExtra(Intent.EXTRA_KEY_EVENT)
            }

            Logger.d("$TAG -> MediaButtonEvent -> KeyEvent: $keyEvent -> keycode: ${keyEvent?.keyCode}")
            if (keyEvent != null && keyEvent.action == KeyEvent.ACTION_DOWN) {
                when (keyEvent.keyCode) {
                    KeyEvent.KEYCODE_MEDIA_PLAY,
                    KeyEvent.KEYCODE_MEDIA_PAUSE -> {
                        return !checkHandlePlayPause()
                    }
                }
            }
            return false
        }

        override fun onSetMediaItems(
            mediaSession: MediaSession,
            controller: MediaSession.ControllerInfo,
            mediaItems: List<androidx.media3.common.MediaItem>,
            startIndex: Int,
            startPositionMs: Long
        ): ListenableFuture<MediaSession.MediaItemsWithStartPosition> {
            return Futures.immediateFuture(
                MediaSession.MediaItemsWithStartPosition(
                    mediaItems,
                    startIndex,
                    startPositionMs
                )
            )
        }
    }


    private fun getCommand(): Player.Commands {
        dataSource?.let { dataSource ->
            val isEnableAllActions = checkEnableAllActionEvent(dataSource)
            return if (isEnableAllActions) {
                when (dataSource.screenType) {
                    is PlayerHandler.ScreenType.Vod -> {
                        val currentEpisodePos = getCurrentPosition()
                        val playlist = listener?.getPlaylistContent() ?: listOf()
                        val playbackActions = if (playlist.size == 1) {
                            PLAY_PAUSE_COMMANDS
                        } else if (currentEpisodePos > 0 && currentEpisodePos < playlist.size - 1) {
                            ALL_COMMANDS
                        } else if (currentEpisodePos == playlist.size - 1) {
                            PLAY_PAUSE_PREVIOUS_COMMANDS
                        } else if (currentEpisodePos == 0) {
                            PLAY_PAUSE_NEXT_COMMANDS
                        } else {
                            ALL_COMMANDS
                        }
                        playbackActions
                    }
                    is PlayerHandler.ScreenType.Live -> { // timeshift
                        PLAY_PAUSE_COMMANDS
                    }
                    else -> PLAY_PAUSE_COMMANDS
                }
            } else PLAY_PAUSE_COMMANDS
        }
        return PLAY_PAUSE_COMMANDS
    }

    private fun updateMediaMetadata(exoPlayerProxy: ExoPlayerProxy?, bitmap: Bitmap? = null) {
        Logger.d("$TAG -> updateMediaMetadata -> Bitmap = $bitmap")
        if (exoPlayerProxy == null) {
            Logger.d("$TAG -> updateMediaMetadata: ExoPlayerProxy is null")
            return
        }
        CoroutineScope(Dispatchers.IO).launch {
            kotlin.runCatching {
                val artworkData = bitmapToByteArray(bitmap)
                withContext(Dispatchers.Main) {
                    Logger.d("$TAG -> before updateMediaMetadata -> ${mediaSession?.player?.currentMediaItem}")

                    val item = mediaSession?.player?.currentMediaItem
                    item?.let {
                        val playerData = exoPlayerProxy.dataPlayerControl()
                        val checkEnableAllActions = checkEnableAllActionEvent(dataSource ?: DataSource())

                        val metadata = it.mediaMetadata.buildUpon().apply {
                            setTitle(playerData?.title.orEmpty())
                            setArtist(playerData?.des.orEmpty())
                            if (artworkData != null) {
                                setArtworkData(artworkData, MediaMetadata.PICTURE_TYPE_FRONT_COVER)
                            }
                            if (checkEnableAllActions) {
                                setDurationMs(exoPlayerProxy.totalDuration() )
                            }
                        }.build()
                        val mediaItem = it.buildUpon().setMediaMetadata(metadata).build()
                        mediaSession?.player?.currentMediaItemIndex?.let { index ->
                            mediaSession?.player?.replaceMediaItem(index, mediaItem)
                        }
                    }
                    Logger.d("$TAG -> updateMediaMetadata: Metadata updated successfully -> ${mediaSession?.player?.currentMediaItem}")
                }
            }.onFailure { error ->
                Logger.d("$TAG -> updateMediaMetadata error: ${error.message}")
            }
        }
    }

    private fun bitmapToByteArray(bitmap: Bitmap?): ByteArray? {
        if (bitmap == null) return null
        val stream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, stream)
        return stream.toByteArray()
    }

    //endregion

    interface OnMediaSessionListener {
        fun setSkipToNext()
        fun setSkipToPrevious()
        fun getPlaylistContent(): List<Details.Episode>?
        fun getCurrentIndexPosition(): Int
    }

}