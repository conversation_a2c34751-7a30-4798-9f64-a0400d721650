package com.fptplay.mobile.features.event_trigger.adapter

import android.annotation.SuppressLint
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.Outline
import android.graphics.RenderEffect
import android.graphics.Shader
import android.os.Build
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.isVisible
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.adapter.BaseViewHolder
import com.fptplay.mobile.databinding.LandingPageButtonItemBinding
import com.fptplay.mobile.features.event_trigger.data.EventButton
import com.fptplay.mobile.features.pladio.util.context
import com.google.android.material.shape.MaterialShapeDrawable
import com.xhbadxx.projects.module.domain.entity.fplay.common.LandingPage
import com.xhbadxx.projects.module.util.logger.Logger
import java.util.concurrent.TimeUnit

class LandingPageAdapter :
    BaseAdapter<EventButton, LandingPageAdapter.StructureViewHolder>() {

    //region Overrides
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): StructureViewHolder {
        return StructureViewHolder(
            LandingPageButtonItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: StructureViewHolder, position: Int) {
        holder.bind(differ.currentList[position])
    }
    //endregion

    //region View Holder
    @SuppressLint("NotifyDataSetChanged")
    inner class StructureViewHolder(private val binding: LandingPageButtonItemBinding) :
        BaseViewHolder(binding) {
        private var countDownTimer: CountDownTimer? = null
        private var time: Int = -1

        init {
            binding.clBtn.apply {
                setOnClickListener {
                    Logger.d("click $absoluteAdapterPosition")
                    if (absoluteAdapterPosition >= 0 && absoluteAdapterPosition < size()) {
                        eventListener?.onClickView(
                            absoluteAdapterPosition,
                            itemView,
                            differ.currentList[absoluteAdapterPosition]
                        )
                    }
                }
            }
        }

        @SuppressLint("SetTextI18n")
        fun bind(data: EventButton) {
            stopCountDown()
            binding.apply {
                if (time == -1) time = data.countdown ?: 0
                if (time > 0) {
                    tvBtn.text = "${data.text} (${time}s)"
                    startCountDown(time)
                } else {
                    tvBtn.text = data.text
                    setButtonEnabled(true)
                }

            }
        }

        private fun startCountDown(countDownTime: Int) {
            val startTimeValid = TimeUnit.SECONDS.toMillis(countDownTime.toLong())
            if (startTimeValid > 0) {
                countDownTimer = object : CountDownTimer(startTimeValid, 1000) {
                    @SuppressLint("SetTextI18n")
                    override fun onTick(millisUntilFinished: Long) {
                        val position = absoluteAdapterPosition
                        if (position != -1) {
                            val item = data()[position]
                            binding.tvBtn.text = "${item.text} (${time}s)"
                            setButtonEnabled(false)
                            time -= 1
                        }
                    }

                    override fun onFinish() {
                        val position = absoluteAdapterPosition
                        if (position != -1) {
                            time = 0
                            binding.apply {
                                tvBtn.text = data()[position].text
                                setButtonEnabled(true)
                            }
                        }
                    }
                }
                countDownTimer?.start()
            }
        }

        private fun stopCountDown() {
            countDownTimer?.cancel()
        }

        fun setButtonEnabled(enabled: Boolean) {
            binding.apply {
                if (enabled) {
                    cvBtn.isEnabled = true
                    clBtn.isEnabled = true
                    tvBtn.isEnabled = true
                    cvBtn.cardElevation = root.resources.getDimension(R.dimen.landing_page_button_elevation)
                    // gradient background
                } else {
                    cvBtn.isEnabled = false
                    clBtn.isEnabled = false
                    tvBtn.isEnabled = false
                    cvBtn.cardElevation = 0f
                    // solid color with alpha

                }
            }
        }
    }
    //endregion
}