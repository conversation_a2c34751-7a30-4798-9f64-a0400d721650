package com.fptplay.mobile.features.mega

import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.view.WarningDialogFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.TrackingConstants
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils.isSettingEnabled
import com.fptplay.mobile.databinding.MegaFragmentBinding
import com.fptplay.mobile.features.home.HomeMainFragment
import com.fptplay.mobile.features.mega.adapter.MegaMenuV2Adapter
import com.fptplay.mobile.features.mega.apps.airline.AirlineActivity
import com.fptplay.mobile.features.mega.apps.airline.model.AirlineBrand
import com.fptplay.mobile.features.mega.data.MegaApp
import com.fptplay.mobile.features.mega.util.CheckNavigateMegaUtils
import com.fptplay.mobile.features.mega.util.FoxpayUtils
import com.fptplay.mobile.features.mega.util.MegaMenuItemDecoration
import com.fptplay.mobile.features.multi_profile.model.ProfileItem
import com.ftel.foxpay.foxsdk.feature.FoxSdkManager
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.common.Config
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenu
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenuItem
import com.xhbadxx.projects.module.domain.entity.fplay.user.UserInfo
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class MegaFragmentV2 : BaseFragment<MegaViewModel.MegaState, MegaViewModel.MegaIntent>() {
    override val hasEdgeToEdge: Boolean = true

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    override val viewModel: MegaViewModel by activityViewModels()
    val megaNavigateViewModel: MegaNavigateViewModel by activityViewModels()
    private var _binding: MegaFragmentBinding? = null
    private val binding get() = _binding!!
    private var megaAppId = ""
    private var megaAppName = ""


    private val megaMenusV2 by lazy {
        val blockProfile = if (::sharedPreferences.isInitialized && sharedPreferences.userLogin()) {

            generateProfileMenu(
                userInfo = UserInfo(
                    name = sharedPreferences.displayName(),
                    phone = sharedPreferences.userPhone(),
                    avatar = sharedPreferences.userAvatar()
                ),
                profileItem = if(sharedPreferences.profileId().isNotBlank()) {
                    ProfileItem.Profile(
                        name = sharedPreferences.profileName(),
                        avatar = sharedPreferences.profileAvatar(),
                        id = sharedPreferences.profileId()
                    )
                } else null

            )

        } else {
            generateProfileMenu(null)

        }
        arrayListOf(blockProfile)

    }

    //    private val menuAdapter by lazy { MegaMenuAdapter(binding.root.context) }
    private val menuAdapter by lazy { MegaMenuV2Adapter(binding.root.context) }
    private val menuMarginTop by lazy {
        requireContext().resources.getDimensionPixelSize(R.dimen.mega_block_item_margin_top)
    }
    private var isFirstInit = true
    private var warningDialogFragment: WarningDialogFragment? = null

    private val sdkManager: FoxSdkManager.Companion by lazy {
        FoxpayUtils.initFoxpaySDK(sharedPreferences.linkingToken(), requireContext())
    }

    private val checkNavigateMegaUtils: CheckNavigateMegaUtils by lazy  {
        CheckNavigateMegaUtils(
            context = requireContext(),
            sharedPreferences = MainApplication.INSTANCE.sharedPreferences,
            megaViewModel = viewModel,
            megaNavigateViewModel = megaNavigateViewModel,
            navHostFragment = parentFragment?.parentFragment,
            checkNavigateLoyaltyUtils = checkNavigateLoyaltyUtils,
            foxPaySdkManager = sdkManager
        )
    }

    override fun onResume() {
        super.onResume()
        if (isFirstInit) {
            getAllInfoOnInit()
            menuAdapter.bind(megaMenusV2.copy())
        } else {
            val menuAccount = generateProfileMenu(
                UserInfo(
                    name = sharedPreferences.displayName(),
                    phone = sharedPreferences.userPhone(),
                    avatar = sharedPreferences.userAvatar()
                ),
                profileItem = if(sharedPreferences.profileId().isNotBlank()) {
                    ProfileItem.Profile(
                        name = sharedPreferences.profileName(),
                        avatar = sharedPreferences.profileAvatar(),
                        id = sharedPreferences.profileId()
                    )
                } else null

            )
            megaMenusV2.updateItem(newMenu = menuAccount)
            menuAdapter.bind(megaMenusV2.copy())
            getUserInfo()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        megaAppId = TrackingUtil.currentAppId
        megaAppName = TrackingUtil.currentAppName
        lifecycle.addObserver(checkNavigateLoyaltyUtils)
        lifecycle.addObserver(checkNavigateMegaUtils)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = MegaFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        warningDialogFragment?.dismissAllowingStateLoss()
    }

    override fun onDestroy() {
        super.onDestroy()
        lifecycle.removeObserver(checkNavigateLoyaltyUtils)
        lifecycle.removeObserver(checkNavigateMegaUtils)

    }

    //region Handle exit app
    override val handleBackPressed = true
    override fun backHandler() {
        checkExit()
    }

    private var backCount = 0
    private var lastBackTime = 0L
    private fun checkExit() {
        backCount += 1
        if (backCount == 1) {
            Toast.makeText(
                context,
                getString(R.string.press_back_2_times_to_exit_app),
                Toast.LENGTH_SHORT
            ).show()
            lastBackTime = System.currentTimeMillis()
        } else if (backCount > 1) {
            if (System.currentTimeMillis() - lastBackTime < 1000L) {
                activity?.finish()
            } else {
                Toast.makeText(
                    context,
                    getString(R.string.press_back_2_times_to_exit_app),
                    Toast.LENGTH_SHORT
                ).show()
                lastBackTime = System.currentTimeMillis()
                backCount = 1
            }
        }
    }
    //endregion Handle exit app

    override fun bindComponent() {
        //
        if (context.isTablet()) {
            activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        }
        //

//        sdkManager = FoxpayUtils.initFoxpaySDK(sharedPreferences.linkingToken(), requireContext())
//        checkNavigateMegaUtils =  CheckNavigateMegaUtils(
//            context = requireContext(),
//            sharedPreferences = MainApplication.INSTANCE.sharedPreferences,
//            megaViewModel = viewModel,
//            megaNavigateViewModel = megaNavigateViewModel,
//            navHostFragment = parentFragment?.parentFragment,
//            checkNavigateLoyaltyUtils = checkNavigateLoyaltyUtils,
//            foxPaySdkManager = sdkManager
//        )
        binding.rvMega.apply {
            adapter = menuAdapter
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            addItemDecoration(MegaMenuItemDecoration(menuMarginTop))
        }
//        viewModel.dispatchIntent(MegaViewModel.MegaIntent.GetListApp)
    }

    override fun bindData() {
        (parentFragment as? HomeMainFragment)?.hideNoInternetView()
        (parentFragment as? HomeMainFragment)?.hidePageError()
//        if (viewModel.userPhone().isBlank()) viewModel.dispatchIntent(MegaViewModel.MegaIntent.GetUserInfo)

    }

    override fun bindEvent() {
        menuAdapter.eventListener = object : IEventListener<MegaMenu.Block> {

            override fun onClickView(position: Int, view: View?, data: MegaMenu.Block) {
//                if (data is MegaMenu.MenuMegaApp && view?.id == R.id.tv_view_all) {
//                    navigateToViewAll(data)
//                    Timber.d("---Navigate to view more")
//                }
            }

            override fun onClickedItem(position: Int, data: MegaMenu.Block) {

                TrackingUtil.currentAppId = megaAppId
                TrackingUtil.currentAppName = megaAppName

                checkNavigateMegaUtils.navigateToSelectedContent(data)
//                when (data) {
//                    is MegaMenu.BlockProfileLogin -> {
//                        navigateToLogin()
//                    }
//                    is MegaMenu.BlockProfile -> {
//                        checkUserLoginBeforeNavigate(navigationId = R.id.action_global_to_account_info) {
//                            parentFragment?.parentFragment?.findNavController()?.navigate(
//                                NavHomeMainDirections.actionGlobalToAccountInfo()
//                            )
//                        }
//                    }
//                    else -> {
//                    }
//                }
            }
        }

        menuAdapter.megaMenuItemClickListener = object : IEventListener<MegaMenuItem> {
            override fun onClickedItem(position: Int, data: MegaMenuItem) {
                TrackingUtil.currentAppId = megaAppId
                TrackingUtil.currentAppName = megaAppName

                val eventLog = if (data.blockType == MegaMenu.Type.MegaApp) {
                    "AccessApp"
                } else {
                    "AccessItem"
                }
                trackingProxy.sendEvent(
                    infor = InforMobile(
                        infor = trackingInfo,
                        appId = TrackingUtil.currentAppId,
                        appName = TrackingUtil.currentAppName,
                        logId = TrackingConstants.EVENT_LOG_ID_MEGA_FUNCTION_CLICK,
                        screen = TrackingConstants.SCREEN_NAME_MEGA_FUNCTION_CLICK,
                        event = eventLog,
                        itemId = data.id,
                        itemName = data.title
                    )
                )
                //
                if(data.blockType == MegaMenu.Type.MegaApp){
                    TrackingUtil.currentAppId = data.id
                    TrackingUtil.currentAppName = data.title

                    megaNavigateViewModel.saveNavigateMenu(data)
                    parentFragment?.parentFragment?.findNavController()?.navigate(
                        NavHomeMainDirections.actionGlobalToNavMegaApp(
                            megaAppId = data.id,
                            fromDeeplink = false

                        )
                    )
                } else {
                    checkNavigateMegaUtils.navigateToSelectedContent(data)
                }

                Logger.d("trangtest logid = 108 $data")

            }
        }
        listenLoginAndRefreshData()
    }

    private fun listenLoginAndRefreshData(){
        // Tablet - tạm thời đối với dialog sẽ sử dụng listener này để refresh
        parentFragment?.setFragmentResultListener(Constants.LOGIN_SUCCESS) { _, bundle ->
            val refresh = bundle.getBoolean(Constants.HAVE_CHANGE_DATA, false)
            Logger.d("trangtest setFragmentResultListener LOGIN_SUCCESS = $refresh")
            if(refresh)
                getUserInfo()
            listenLoginAndRefreshData()
        }
        parentFragment?.setFragmentResultListener(Constants.REFRESH_DATA) { _, bundle ->
            val refresh = bundle.getBoolean(Constants.HAVE_CHANGE_DATA, false)
            Logger.d("trangtest setFragmentResultListener REFRESH_DATA = $refresh")
            if(refresh)
                getUserInfo()
            listenLoginAndRefreshData()
        }
    }

    override fun MegaViewModel.MegaState.toUI() {
        when (this) {
            is MegaViewModel.MegaState.ResultGetClusterInfoAtInit -> {
                handleClusterInfoResult(this.data)
            }
            is MegaViewModel.MegaState.ErrorRequiredLogin -> {
                if(this.intent is MegaViewModel.MegaIntent.GetUserInfo) {
                    viewModel.saveUserInfo(UserInfo())
                    val menuAccount = generateProfileMenu(null)
                    megaMenusV2.updateItem(newMenu = menuAccount)
                    menuAdapter.bind(megaMenusV2.copy())
                }
            }

            is MegaViewModel.MegaState.ResultUserInfo -> {
                viewModel.saveUserInfo(data)
                val menuAccount = generateProfileMenu(
                    userInfo = data,
                    profileItem = if(sharedPreferences.profileId().isNotBlank()) {
                        ProfileItem.Profile(
                            name = sharedPreferences.profileName(),
                            avatar = sharedPreferences.profileAvatar(),
                            id = sharedPreferences.profileId()
                        )
                    } else null

                )
                megaMenusV2.updateItem(newMenu = menuAccount)
                menuAdapter.bind(megaMenusV2.copy())
            }
            else -> {}
        }
    }

    // region Commons

    private fun checkUserLoginBeforeNavigate(
        showWarningDialog: Boolean = false,
        navigationId: Int? = null,
        navigateFun: () -> Unit
    ) {
        if (sharedPreferences.userLogin()) {
            navigateFun()
        } else {
            if (showWarningDialog) {
                warningDialogFragment?.dismiss()
                warningDialogFragment = WarningDialogFragment(
                    message = getString(R.string.mega_warning_dialog_required_login_message),
                    negativeButtonTitle = getString(R.string.mega_warning_dialog_required_login_negative_button),
                    positiveButtonTitle = getString(R.string.mega_warning_dialog_required_login_positive_button),
                    positiveClickListener = { navigateToLogin(navigationId = navigationId) }
                ).apply {
                    show(<EMAIL>, null)
                }
            } else {
                navigateToLogin(navigationId = navigationId)
            }
        }
    }


    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_LONG).show()
    }

    private fun navigateToLogin(navigationId: Int? = null) {
        navigationId?.let {
            parentFragment?.parentFragment?.navigateToLoginWithParams(
                isDirect = true,
                navigationId = it
            )
        } ?: run {
            parentFragment?.parentFragment?.navigateToLoginWithParams(isDirect = true)
        }
    }

    private fun navigateToViewAll() {
//        actionMegaFragmentToMegaAppViewAllFragment
        parentFragment?.parentFragment?.findNavController()?.navigate(
            NavHomeMainDirections.actionGlobalToMegaViewAll()
        )
    }

    private fun navigateToAirline(airlineBrand: AirlineBrand) {
        val intent = Intent(requireContext(), AirlineActivity::class.java)
        intent.putExtra(AirlineActivity.AIRLINE_BRAND_KEY, airlineBrand)
        startActivity(intent)
    }


    // region Handle Apis
    private fun handleClusterInfoResult(result: List<MegaViewModel.MegaState>) {
        isFirstInit = false
        result.forEach {
            when (it) {
                is MegaViewModel.MegaState.ResultGetUserInfo -> {
                    viewModel.saveUserInfo(it.data)
                    val menuAccount = generateProfileMenu(
                        userInfo = it.data,
                        profileItem = if(sharedPreferences.profileId().isNotBlank()) {
                            ProfileItem.Profile(
                                name = sharedPreferences.profileName(),
                                avatar = sharedPreferences.profileAvatar(),
                                id = sharedPreferences.profileId()
                            )
                        } else null

                    )
                    megaMenusV2.updateItem(newMenu = menuAccount)
                }
                is MegaViewModel.MegaState.ResultMegaMenu -> {
//                    Timber.tag("tam-mega").d("ResultMegaMenu ${it.data.blocks}")
                    for (block in it.data.blocks) {
                        megaMenusV2.updateItem(newMenu = block)

                    }
                }

                is MegaViewModel.MegaState.ErrorRequiredLogin -> {
                    if(it.intent is MegaViewModel.MegaIntent.GetUserInfo) {
                        viewModel.saveUserInfo(UserInfo())
                        val menuAccount = generateProfileMenu(null)
                        megaMenusV2.updateItem(newMenu = menuAccount)
                        menuAdapter.bind(megaMenusV2.copy())
                    }
                }
                else -> {
                }
            }
        }
//        Timber.tag("tam-mega").i("megaMenusV2 ${megaMenusV2}")
        menuAdapter.bind(megaMenusV2.copy())
    }

    private fun generateListMegaApp(appConfig: Config): List<MegaApp> {
        val megaApps = ArrayList<MegaApp>()
        if (appConfig.settingFPTPlayShop.isSettingEnabled()) {
            megaApps.add(MegaApp.FPTPlayShop)
        }
//        if (appConfig.settingMuaDayBanDinh.isSettingEnabled()) {
//            megaApps.add(MegaApp.MDBD)
//        }
        if (appConfig.settingOmnishop.isSettingEnabled()) {
            megaApps.add(MegaApp.OmniShop)
        }
        if (appConfig.settingStar30s.isSettingEnabled()) {
            megaApps.add(MegaApp.Game30s)
        }
        if (appConfig.settingVna.isSettingEnabled()) {
            megaApps.add(MegaApp.VietnamAirlines)
        }
        if (appConfig.settingBamboo.isSettingEnabled()) {
            megaApps.add(MegaApp.BambooAirways)
        }
        if (appConfig.settingVietjet.isSettingEnabled()) {
            megaApps.add(MegaApp.VietjetAir)
        }
        if (appConfig.settingHipFest.isSettingEnabled()) {
            megaApps.add(MegaApp.HipFest)
        }
        if (appConfig.settingChoiHayChia.isSettingEnabled()) {
            megaApps.add(MegaApp.PlayOrShare)
        }
        if (appConfig.settingFptPlay3G.isSettingEnabled()) {
            megaApps.add(MegaApp.Service3G)
        }
        if (appConfig.settingFptPlayReward.isSettingEnabled()) {
            megaApps.add(MegaApp.FptPlayRewards)
        }
        if (appConfig.settingFoxpayApp.isSettingEnabled()) {
            megaApps.add(MegaApp.Foxpay)
        }
        if (appConfig.settingFoxpayPhone.isSettingEnabled()) {
            megaApps.add(MegaApp.Recharge)
        }
//        if(appConfig.settingLoyalty.isSettingEnabled()) {
//            megaApps.add(MegaApp.Loyalty)
//        }

        return megaApps
    }
    // endregion Handle Apis

    // region generate Menu
    var index = -1
    private fun generateProfileMenu(userInfo: UserInfo?, profileItem: ProfileItem.Profile? = null): MegaMenu.Block {

        return if (userInfo == null) {
            MegaMenu.BlockProfileLogin()
        } else {
//            MegaMenu.BlockProfile(
//                title = "",
//                userName = userInfo.displayName(),
//                userAvatar = userInfo.avatar ?: "",
//            )
            val name = if(profileItem?.name?.isNotBlank() == true) {
                profileItem.name
            } else {
                getString(R.string.multi_profile_default_profile_name)
            }
            val avatar = if(profileItem?.avatar?.isNotBlank() == true) {
                profileItem.avatar
            } else {
                ""
            }
            MegaMenu.BlockMultiProfile(
                title = "",
                profileName = name,
                profileAvatar = avatar,
            )
        }
    }

    // endregion generate Menu

    private fun getUserInfo() {
        if (sharedPreferences.userLogin()) {
            viewModel.dispatchIntent(MegaViewModel.MegaIntent.GetUserInfo)
        } else {
            megaMenusV2.updateItem(generateProfileMenu(userInfo = null))
            menuAdapter.bind(megaMenusV2.copy())
        }
    }

    private fun getAllInfoOnInit() {
        val listApis = listOf(
//            MegaViewModel.MegaIntent.GetListApp,
            MegaViewModel.MegaIntent.GetUserInfo,
            MegaViewModel.MegaIntent.GetConfig,
            MegaViewModel.MegaIntent.GetMegaMenu(miniAppSdkVersion = Constants.MINI_APP_SDK_SUPPORTED)
        )
        viewModel.dispatchIntent(MegaViewModel.MegaIntent.GetClusterInfoAtInit(data = listApis))
    }

    private fun ArrayList<MegaMenu.Block>.copy(): List<MegaMenu.Block> {
        val cloneOfMegaMenus = ArrayList<MegaMenu.Block>()
        forEach { item -> cloneOfMegaMenus.add(item.clone()) }
        return cloneOfMegaMenus
    }

    private fun MutableList<MegaMenu.Block>.updateItem(
        newMenu: MegaMenu.Block,
        indexInserted: Int = -1
    ): Int {
        if (this.isEmpty()) {
            add(newMenu)
            return size - 1
        }
        var indexMenu = -1
        for ((i, megaMenu) in this.withIndex()) {
            if (megaMenu.blockType == newMenu.blockType) {
                this[i] = newMenu
                indexMenu = i
                break
            }
        }
        if (indexMenu == -1) {
            // Not in list menu more yet
            indexMenu = if (indexInserted in 0 until megaMenusV2.size) {
                add(indexInserted, newMenu)
                indexInserted
            } else {
                add(newMenu)
                size - 1
            }
        }
        return indexMenu
    }

    private fun UserInfo.displayName(): String {
        return if (name.isNullOrEmpty()) {
            phone ?: ""
        } else {
            name!!
        }
    }
    // endregion Commons
}