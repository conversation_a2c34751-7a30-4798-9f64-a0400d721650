package com.fptplay.mobile.features.moments.view.player_recycler_view

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.SeekBar
import androidx.appcompat.widget.AppCompatSeekBar
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.common.ui.popup.loading.LoadingView
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.features.moments.DataSourceUtils
import com.fptplay.mobile.features.moments.MomentAdapter
import com.fptplay.mobile.features.moments.ShortVideosLogKibana
import com.fptplay.mobile.features.moments.data.MomentStreamInfo
import com.fptplay.mobile.features.moments.data.MomentStreamLocalBookmarkInfo
import com.fptplay.mobile.features.moments.utils.LoadMorePreloadHandler
import com.fptplay.mobile.features.moments.utils.MomentAutoScrollCallback
import com.fptplay.mobile.features.moments.utils.MultipleLoadMorePreloadHandler
import com.fptplay.mobile.features.moments.view.MomentSeekbar
import com.fptplay.mobile.player.utils.PlayerDebugViewData
import androidx.media3.common.C
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.common.Format
import androidx.media3.common.Player
import androidx.media3.common.Timeline
import androidx.media3.exoplayer.analytics.AnalyticsListener
import androidx.media3.exoplayer.DecoderReuseEvaluation
import androidx.media3.exoplayer.source.MediaSourceFactory
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import androidx.media3.common.VideoSize
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DataSource
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.exoplayer.dash.DashMediaSource
import androidx.media3.exoplayer.hls.HlsMediaSource
import androidx.media3.ui.AspectRatioFrameLayout
import com.tear.modules.player.exo.ExoPlayerProxy
import com.tear.modules.player.exo.ExoPlayerView
import com.tear.modules.player.util.IPlayer
import com.xhbadxx.projects.module.domain.entity.fplay.moment.MomentDetail
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.logger.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.Timer
import java.util.TimerTask

@UnstableApi
class PlayerRecyclerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr), DefaultLifecycleObserver {

    // View config
    private var autoPlayOnSelect = true
    private var preloadStreamOffsetBottom = 8

    private var started = false
    private var isNextVideo = false
    private val pagerSnapHelper by lazy { PagerSnapHelper() }
    var onPageChangeCallback: OnPageChangeCallback? = null
    private var playerRvAdapter: PlayerRvAdapter<*, *>? = null

    private var currentPage = -1
    private var currentItemId: String? = null
    private var currentViewHolder: PlayerRvItemViewHolder? = null
    private var viewLifecycleOwner: LifecycleOwner? = null

    private var player: IPlayer? = null
    private var playerView: ExoPlayerView? = null
    private var cacheDataSourceFactory: CacheDataSource.Factory? = null

    private var playerSeekbar: AppCompatSeekBar? = null
    private var currentStreamInfo: MomentStreamInfo = MomentStreamInfo()
    private var momentStreamLocalBookmarkInfo: MomentStreamLocalBookmarkInfo? = null

    // flag
    private var isSeekBarDragging = false
    private var isPausingManual = false
    private var isRepeat = false

    //load more handler
    var loadMoreHandler: LoadMorePreloadHandler? = null

    var momentAutoScrollCallback: MomentAutoScrollCallback? = null

    // tracking
    var currentDuration = 0L
        private set
    private var handlerTrackingHeartBeat: Handler? = null
    private var runnableTrackingHeartBeat = Runnable {
        sendTrackingHeartBeat()
    }


    private val scrollListener by lazy {
        object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                isNextVideo = true
                Timber.tag("tam-moment").i("onScrollStateChanged")
                detectPositionAndStart(newState)
                checkLoadMore()
            }
        }
    }

    // region seekbar
    private val onSeekBarChangeListener by lazy {
        object : SeekBar.OnSeekBarChangeListener {
            private var currentProgress = 0
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                currentProgress = progress
                currentViewHolder?.onSeekbarProgressChanged(seekBar, progress, fromUser)
                momentAutoScrollCallback?.onSeekbarProgressChanged(seekBar, progress, fromUser)
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                isSeekBarDragging = true
                currentViewHolder?.onSeekbarStartTrackingTouch(seekBar)
                momentAutoScrollCallback?.onSeekbarStartTrackingTouch(seekBar)
                logKibana?.startSeekLog()
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                isSeekBarDragging = false
                player?.seek(currentProgress.toLong())
                currentViewHolder?.onSeekbarStopTrackingTouch(seekBar)
                momentAutoScrollCallback?.onSeekbarStopTrackingTouch(seekBar)
                updateProgress()
            }
        }
    }

    //endregion seekbar
    private val playerCallback by lazy {
        object : IPlayer.IPlayerCallback {
            override fun onEnd() {
                super.onEnd()
                Timber.tag("tam-moment").e("onEnd")
                currentViewHolder?.onPlayerEnd()

                // Tracking
                playerTrackingCallback.onEnd()
                momentAutoScrollCallback?.onMomentEnded(currPosition = currentPage)
                loopPlayer()
            }

            override fun onError(code: Int, name: String, detail: String, error403: Boolean, responseCode: Int) {
                super.onError(code, name, detail, error403, responseCode)
                Timber.tag("tam-moment").e("onError: $code - $name - $detail")
                isPlayerErrorByInternet = code == PLAYER_ERROR_CODE_INTERNET
                handlePlayerError(code, name, detail)
                showLoading()
                currentViewHolder?.onPlayerError(code, name, detail)

                // Tracking
                playerTrackingCallback.onError(code, name, detail)
            }

            override fun onErrorBehindInLive(code: Int, name: String, detail: String) {
                super.onErrorBehindInLive(code, name, detail)
                showLoading()
                Timber.tag("tam-moment").e("onErrorBehindInLive: $code - $name - $detail")
                currentViewHolder?.onPlayerErrorBehindInLive(code, name, detail)

                // Tracking
                playerTrackingCallback.onErrorBehindInLive(code, name, detail)
            }

            override fun onPause() {
                super.onPause()
                Timber.tag("tam-moment").e("onPause")
                currentViewHolder?.onPlayerPause()

                // Tracking
                playerTrackingCallback.onPause()
                momentAutoScrollCallback?.onMomentPause(currPosition = currentPage)
                (playerSeekbar as? MomentSeekbar)?.setSeekBarOnPause()

            }

            override fun onPlay() {
                super.onPlay()
                hideLoading()
                Timber.tag("tam-moment").e("onPlay")
                currentViewHolder?.onPlayerPlay()

                // Tracking
                playerTrackingCallback.onPlay()

                momentAutoScrollCallback?.onMomentPlay(currPosition = currentPage)
                if (!isSeekBarDragging) {
                    (playerSeekbar as? MomentSeekbar)?.setSeekBarNormal()
                }
            }

            override fun onPrepare() {
                super.onPrepare()
                Timber.tag("tam-moment").v("onPrepare")
                currentViewHolder?.hideLoading(loadingView)
//                currentViewHolder?.hideThumbnail()
                playerView?.visibility = View.VISIBLE
                currentViewHolder?.onPlayerPrepare()

                // Tracking
                playerTrackingCallback.onPrepare()

                initPlayerTrackChangeListener()
                (playerSeekbar as? MomentSeekbar)?.setSeekBarNormal()
            }

            override fun onReady() {
                super.onReady()
                Timber.tag("tam-moment").v("onReady")
                currentViewHolder?.hideLoading(loadingView)
                currentViewHolder?.hideThumbnail()
                currentViewHolder?.onPlayerReady()

                // Tracking
                playerTrackingCallback.onReady()

                // auto scroll
                momentAutoScrollCallback?.onMomentReady(currPosition = currentPage)
                if (playerSeekbar is MomentSeekbar) {
                    (playerSeekbar as MomentSeekbar).let { seekbar ->
                        player?.let {
                            seekbar.progress = it.currentDuration().toInt()
                            seekbar.max = it.totalDuration().toInt()
                            if(!isSeekBarDragging)
                                seekbar.setSeekBarNormal()
                            currentViewHolder?.bindSeekbarView(seekbar)
                        }

                    }
                }
            }

            override fun onStart() {
                super.onStart()
                Timber.tag("tam-moment").v("onStart")
                currentViewHolder?.onPlayerStart()

                // Tracking
                playerTrackingCallback.onStart()
                momentAutoScrollCallback?.onMomentStart(currPosition = currentPage)
            }

            override fun onStop() {
                super.onStop()
                Timber.tag("tam-moment").v("onStop")
                currentViewHolder?.onPlayerStop()

                // Tracking
                playerTrackingCallback.onStop()
                if (playerSeekbar is MomentSeekbar) {
                    (playerSeekbar as MomentSeekbar).setSeekBarNormal()
                }

            }
        }
    }
    val debugViewData: PlayerDebugViewData by lazy { PlayerDebugViewData() }
    private var playerVideoSize: VideoSize? = null
    fun getVideoSize(): String {
        playerVideoSize?.let {
            return "${it.width}x${it.height}"
        } ?: kotlin.run {
            return ""
        }
    }

    // region Tracking

    private val playerAnalyticsEvents by lazy {
        object : AnalyticsListener {
            override fun onVideoInputFormatChanged(
                eventTime: AnalyticsListener.EventTime,
                format: Format,
                decoderReuseEvaluation: DecoderReuseEvaluation?
            ) {
                try {
                    val fps = if (format.frameRate != Format.NO_VALUE.toFloat()) "${format.frameRate}" else ""
                    val codecs = format.codecs ?: ""
                    val res = "${format.height}"
                    val bitrate = format.bitrate
                    val bitrateInMbps = if (bitrate > 0) String.format("%.2f", (bitrate / 1_000_000.toFloat())) else ""

//                debugViewData.bitrateInMps = bitrateInMbps
                    debugViewData.videoInfo.let { videoInfo ->
                        videoInfo.bitrateInMps = bitrateInMbps
                        videoInfo.bitrateInBitValue = if (bitrate >= 0) bitrate.toString() else ""
                        videoInfo.codec = codecs
                        videoInfo.fps = fps
                        videoInfo.res = res
                    }

                } catch (ex: Exception) {
                    ex.printStackTrace()
                }
            }

            override fun onAudioInputFormatChanged(
                eventTime: AnalyticsListener.EventTime,
                format: Format,
                decoderReuseEvaluation: DecoderReuseEvaluation?
            ) {
                try {
                    val codecs = format.codecs ?: ""
                    val bitrate = format.bitrate
                    val bitrateInMbps = if (bitrate > 0) String.format("%.2f", (bitrate / 1_000_000.toFloat())) else ""

                    debugViewData.audioInfo.let { audioInfo ->
                        audioInfo.bitrateInMps = bitrateInMbps
                        audioInfo.bitrateInBitValue = if (bitrate >= 0) bitrate.toString() else ""
                        audioInfo.codec = codecs
                    }

                } catch (ex: Exception) {
                    ex.printStackTrace()
                }

            }

            override fun onBandwidthEstimate(
                eventTime: AnalyticsListener.EventTime,
                totalLoadTimeMs: Int,
                totalBytesLoaded: Long,
                bitrateEstimate: Long
            ) {
                debugViewData.bitrateInMps = String.format("%.2f Mbps", bitrateEstimate / 1_000_000.toFloat())
            }
        }
    }
    private val playerTrackChangeEvents by lazy {
        object : Player.Listener {
            override fun onTimelineChanged(timeline: Timeline, reason: Int) {
                (player?.internalPlayer() as? ExoPlayer)?.let { exoPlayer ->
                    if (exoPlayer.currentMediaItemIndex in 0 until timeline.windowCount) {
                        val windowCurrent =
                            timeline.getWindow(exoPlayer.currentMediaItemIndex, Timeline.Window())
                        if (windowCurrent.windowStartTimeMs != C.TIME_UNSET && windowCurrent.windowStartTimeMs > 0) {
                            val localTime = windowCurrent.currentUnixTimeMs
                            val nextTime = windowCurrent.windowStartTimeMs + exoPlayer.currentPosition
                            debugViewData.latency =
                                String.format("%.1fs", (localTime - nextTime) / 1000.0)

                        } else {
                            debugViewData.latency = ""
                        }
                    } else {
                        debugViewData.latency = ""
                    }

                }
            }

            override fun onVideoSizeChanged(videoSize: VideoSize) {
                super.onVideoSizeChanged(videoSize)
                playerVideoSize = videoSize
            }
        }
    }


    private val playerTrackingCallback by lazy {
        object : IPlayer.IPlayerCallback {
            override fun onEnd() {
                Timber.tag("tam-moment").v("playerTrackingCallback onEnd")
            }

            override fun onError(code: Int, name: String, detail: String, error403: Boolean, responseCode: Int) {
                Timber.tag("tam-moment").e(" playerTrackingCallback onError $code - $name - $detail")
                logKibana?.sendLogError(desError = detail, errorCode = code.toString(), errorMess = name)

            }

            override fun onErrorBehindInLive(code: Int, name: String, detail: String) {
                Timber.tag("tam-moment").e(" playerTrackingCallback onErrorBehindInLive $code - $name - $detail")
                logKibana?.sendLogError(desError = detail, errorCode = code.toString(), errorMess = name)

            }

            override fun onPause() {
            }

            override fun onPlay() {
            }

            override fun onPrepare() {
                logKibana?.sendLogStart(isInit = true, url = player?.url() ?: "", isRepeat = isRepeat)
            }

            override fun onReady() {
                initTrackingHeartBeat()
                startCountRealTimePlaying()
                player?.let {
                    logKibana?.sendLogStartFirstFrame(
                        totalDuration = (it.totalDuration() / 1000).toString(),
                        currentDuration = (it.currentDuration() / 1000).toString(),
                        realTimePlaying = realTimePlaying.toString()
                    )
                }

                //start
            }

            override fun onStart() {
                player?.let {
                    logKibana?.sendLogSeek(currentDuration = (it.currentDuration() / 1000).toString())
                }
            }

            override fun onStop() {
                player?.let {
                    logKibana?.sendLogPing(
                        currentDuration = (currentDuration / 1000).toString(),
                        realTimePlaying = realTimePlaying.toString(),
                        bandwidth = getTrackingBandWith(),
                        streamBandwidth = debugViewData.videoInfo.getBitrateRawValue(),
                        streamBandwidthAudio = debugViewData.audioInfo.getBitrateRawValue(),
                        totalByteLoaded = currentByteLoaded()?.toString() ?: "",
                        resolution = getVideoSize(),
                        bitrate = getTrackingBitrate()
                    )
                    logKibana?.sendLogStop(
                        currentDuration = (currentDuration / 1000).toString(),
                        realTimePlaying = realTimePlaying.toString()
                    )
                }
                removeTrackingHeartBeat()
                stopCountRealTimePlaying()
            }
        }
    }


    private fun initPlayerTrackChangeListener() {
        (player?.internalPlayer() as? ExoPlayer)?.run {
            removeListener(playerTrackChangeEvents)
            addListener(playerTrackChangeEvents)

            removeAnalyticsListener(playerAnalyticsEvents)
            addAnalyticsListener(playerAnalyticsEvents)
        }
    }

    private var realTimePlaying = 0
    private var timerRealTimePlaying: Timer? = null

    //check is call when onstop
    private var isCallStop = false

    //
    private var logKibana: ShortVideosLogKibana? = null
    fun setShortLogKibana(logKibana: ShortVideosLogKibana) {
        this.logKibana = logKibana
    }

    private fun startCountRealTimePlaying() {
        stopCountRealTimePlaying()
        timerRealTimePlaying = Timer()
        timerRealTimePlaying?.scheduleAtFixedRate(
            object : TimerTask() {
                override fun run() {
                    player?.let {
                        viewLifecycleOwner?.lifecycleScope?.launch(Dispatchers.Main) {
                            if (it.isPlaying()) {
                                realTimePlaying += 1
                            }
                            currentDuration = it.currentDuration()
                        }
                    }

                }
            }, 1000, 1000
        )
    }

    private fun stopCountRealTimePlaying() {
        realTimePlaying = 0
        if (timerRealTimePlaying != null) {
            timerRealTimePlaying?.cancel()
            timerRealTimePlaying = null
        }
    }

    private fun initTrackingHeartBeat() {
        removeTrackingHeartBeat()
        if (handlerTrackingHeartBeat == null) {
            Looper.getMainLooper()?.run {
                handlerTrackingHeartBeat = Handler(this)
            }
        }
        handlerTrackingHeartBeat?.run {
            post(runnableTrackingHeartBeat)
        }
    }

    private fun removeTrackingHeartBeat() {
        handlerTrackingHeartBeat?.removeCallbacks(runnableTrackingHeartBeat)
        handlerTrackingHeartBeat = null
    }

    private fun sendTrackingHeartBeat() {
        if (isPlaying() == true) {
            player?.let {
                logKibana?.sendLogPing(
                    currentDuration = (it.currentDuration() / 1000).toString(),
                    realTimePlaying = realTimePlaying.toString(),
                    bandwidth = getTrackingBandWith(),
                    streamBandwidth = debugViewData.videoInfo.getBitrateRawValue(),
                    streamBandwidthAudio = debugViewData.audioInfo.getBitrateRawValue(),
                    totalByteLoaded = currentByteLoaded()?.toString() ?: "",
                    resolution = getVideoSize(),
                    bitrate = getTrackingBitrate()
                )
            }
        }

        handlerTrackingHeartBeat?.run {
            postDelayed(runnableTrackingHeartBeat, 30000)
        }
    }

    fun getTrackingBandWith(): String {
        return (player as? ExoPlayerProxy)?.getTrackingBandwidth() ?: "0"
    }

    fun getTrackingBitrate(): String {
        return (player as? ExoPlayerProxy)?.getTrackingBitrate() ?: "0"
    }
    // endregion Tracking

    // region View
    private val loadingView by lazy {
        LoadingView(context)
    }

    // endregion View

    // region retry

    private var isPlayerErrorByInternet = false


    private fun bindEventInternetListener(viewLifecycleOwner: LifecycleOwner) {
        MainApplication.INSTANCE.networkDetector.observe(viewLifecycleOwner) {
            it?.let { hasInternet ->
                if (isPlayerErrorByInternet && hasInternet) {
//                    tryRetry()
                    tryRetryV2()
                }
                isPlayerErrorByInternet = false
            }
        }
    }

    private fun handlePlayerError(code: Int, name: String, detail: String) {
        isPlayerErrorByInternet = code == PLAYER_ERROR_CODE_INTERNET
        /**
         * Note : retry player error
         * **/
        //tryRetry()
    }

    /**
     * To Do : try playback with current time
     * */
    private fun tryRetry() {
        if (currentStreamInfo.url?.isNotEmpty() == true) {
            val oldPosition = player?.currentDuration() ?: 0L
            player?.stop()
            player?.run {
                prepare(
                    request = IPlayer.Request(
                        url = IPlayer.Request.Url(url = currentStreamInfo.url ?: ""),
                        startPosition = oldPosition,
                        forceUsingStartPosition = true,
                        clearRequestWhenOnStop = true,
                        headerRequestProperties = Utils.getHeaderForPlayerRequest(currentStreamInfo.streamSession ?: "")
                    )
                )
            }
        } else {
            /**
             * Case when not connected internet connection
             *  switch up /switch down  next to  other stream
             *  url of new stream is empty
             *  To do : callback and get stream info
             * */
            onPageChangeCallback?.retryStreamUrl(currentPage)
        }

    }

    private fun tryRetryV2() {
        val streamMomentDataV2 = currentStreamInfo.streamMomentDataV2
        if (streamMomentDataV2 != null) {
            val oldPosition = player?.currentDuration() ?: 0L
            player?.stop()
            player?.run {
                cacheDataSourceFactory?.let {
                    val linkUrl =
                        streamMomentDataV2.urlDashH265.ifBlank { streamMomentDataV2.urlHlsH265 }
                            .ifBlank { streamMomentDataV2.urlDashH264 }
                            .ifBlank { streamMomentDataV2.urlHlsH264 }
                    player?.prepare(
                        request = IPlayer.Request(
                            createMediaSource = getMediaSource(linkUrl, it),
                            url = IPlayer.Request.Url(
                                h265Url = streamMomentDataV2.urlDashH265.ifBlank { streamMomentDataV2.urlHlsH265 },
                                url = streamMomentDataV2.urlDashH264.ifBlank { streamMomentDataV2.urlHlsH264 },
                            ),
                            startPosition = 0,
                            forceUsingStartPosition = true,
                            clearRequestWhenOnStop = true,
                            headerRequestProperties = Utils.getHeaderForPlayerRequest(currentStreamInfo.streamSession ?: "")
                        )
                    )
                }
            }
        } else {
            /**
             * Case when not connected internet connection
             *  switch up /switch down  next to  other stream
             *  url of new stream is empty
             *  To do : callback and get stream info
             * */
            onPageChangeCallback?.retryStreamUrl(currentPage)
        }

    }
    // endregion retry

    private val errorTime: Long
        get() = if (player is ExoPlayerProxy) C.TIME_UNSET else -1

    // region Initialize

    fun init(viewLifecycleOwner: LifecycleOwner) {
        this.viewLifecycleOwner = viewLifecycleOwner
        bindComponent()
        bindEvent()
        initPlayer()
        initPlayerView()
        bindEventInternetListener(viewLifecycleOwner)

    }

    private fun bindComponent() {
//        Timber.tag("tam-moment").w("playerRecyclerView bindComponent: started $started")
        layoutManager = LinearLayoutManager(context)
        pagerSnapHelper.attachToRecyclerView(this)
    }

    private fun bindEvent() {
        viewLifecycleOwner?.lifecycle?.addObserver(this)
        addOnScrollListener(scrollListener)

    }

    private fun initPlayer() {
        player = ExoPlayerProxy(
            context = context,
            useCronetForNetworking = true,
            requireMinimumResolutionH265 = "",
            requireMinimumResolutionH265HDR = "",
            requireMinimumResolutionAV1 = "",
            requireMinimumResolutionVP9 = "",
            requireMinimumResolutionDolbyVision = "",

            ).apply {
            addPlayerCallback(playerCallback)
        }

        cacheDataSourceFactory = DataSourceUtils.buildCacheDataSourceFactory(
            cache = MainApplication.simpleCache,
            applicationContext = MainApplication.INSTANCE.applicationContext,
            listener = object : CacheDataSource.EventListener {
                override fun onCachedBytesRead(cacheSizeBytes: Long, cachedBytesRead: Long) {
                    Logger.d("PlayerRecyclerView -> onCachedBytesRead. cacheSizeBytes:$cacheSizeBytes, cachedBytesRead: $cachedBytesRead")
                }

                override fun onCacheIgnored(reason: Int) {
                    Logger.d("PlayerRecyclerView -> onCacheIgnored. reason:$reason")
                }
            }
        )

        viewLifecycleOwner?.lifecycle?.addObserver(player as DefaultLifecycleObserver)
    }

    private fun initPlayerView() {
        playerView = ExoPlayerView(context).apply {
            isMobileView(true)
//            setResizeMode(AspectRatioFrameLayout.RESIZE_MODE_FIT)
            setResizeMode(AspectRatioFrameLayout.RESIZE_MODE_FIXED_HEIGHT)
            useControl(false)
        }
    }

    // endregion Initialize

    // region Override

    // region Override DefaultLifecycleObserver
    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        if ((currentStreamInfo?.url?.isNotBlank() == true || currentStreamInfo?.streamMomentDataV2 != null) && !currentStreamInfo.streamUnavailable) {
            post(updateProgressRunnable)
        }
    }

    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)
        Timber.tag("tam-moment").i("onPause")
        removeCallbacks(updateProgressRunnable)
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        Timber.tag("tam-moment-v2").i("${this.javaClass.simpleName} onStop")
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        Timber.tag("tam-moment-v2").i("${this.javaClass.simpleName} onDestroy")
        stopCountRealTimePlaying()
        removeTrackingHeartBeat()
    }

    // endregion Override DefaultLifecycleObserver

    // endregion Override

    // region Get Set
    // set
    fun setAdapter(adapter: PlayerRvAdapter<*, *>) {
        super.setAdapter(adapter)
        playerRvAdapter = adapter
    }

    fun setSeekbar(seekbar: AppCompatSeekBar?) {
        playerSeekbar = seekbar
        playerSeekbar?.setOnSeekBarChangeListener(onSeekBarChangeListener)
    }

    fun setMomentStreamLocalBookmarkInfo(momentStreamLocalBookmarkInfo: MomentStreamLocalBookmarkInfo) {
        this.momentStreamLocalBookmarkInfo = momentStreamLocalBookmarkInfo
    }

    //set
    fun currentDuration(): Long = player?.currentDuration() ?: 0L
    fun totalDuration(): Long = player?.totalDuration() ?: 0L

    fun isPlaying(): Boolean? = player?.isPlaying()
    fun currentByteLoaded(): Long? = player?.currentByteLoaded()

    // endregion Get Set

    private fun removePlayerView() {
        playerView?.apply {
            currentViewHolder?.removePlayerView(this)

        }
    }

    private fun removeSeekbar() {
        playerSeekbar?.apply {
            val parentVG = parent as? ViewGroup ?: return
            parentVG.removeView(this)
        }
    }

    private fun startPlayer(streamUrl: String, h265StreamUrl: String, streamSession: String, itemId: String) {
        if (player == null) {
            initPlayer()
        }
        playerView?.apply {
            player?.setInternalPlayerView(this)
        }

        var startPosition = 0L
        var playWhenReady = true
        momentStreamLocalBookmarkInfo?.let {
            if (it.itemId == itemId) {
                Timber.tag("tam-moment").i("startPlayer SEEK!!!!!!!")
                startPosition = it.startPosition
                playWhenReady = it.isPlayerPlaying

            }
        }
        momentStreamLocalBookmarkInfo = null
        player?.run {
            cacheDataSourceFactory?.let {
                val linkUrl = h265StreamUrl.ifBlank { streamUrl }
                player?.prepare(
                    request = IPlayer.Request(
                        createMediaSource = getMediaSource(linkUrl, it),
                        url = IPlayer.Request.Url(
                            h265Url = h265StreamUrl,
                            url = streamUrl
                        ),
                        startPosition = startPosition,
                        forceUsingStartPosition = true,
                        clearRequestWhenOnStop = true,
                        headerRequestProperties = Utils.getHeaderForPlayerRequest(streamSession),
                        autoPlay = playWhenReady
                    )
                )
            }
        }

    }

    private fun stopPlayer() {
        player?.stop(force = true)
        removePlayerView()
        removeSeekbar()
        currentViewHolder?.unbind()
        currentViewHolder = null
    }

    private fun showLoading() {
        Timber.tag("tam-moment").d("showLoading")
        currentViewHolder?.showLoading(loadingView)
    }

    private fun hideLoading() {
//        Timber.tag("tam-moment").d("hideLoading")
        currentViewHolder?.hideLoading(loadingView)

    }

    private fun showError(title: String, description: String) {
        (currentViewHolder as? MomentAdapter.MomentViewHolder)?.showErrorView(title = title, description = description, showTitleOnly = false)

    }

    private fun hideError() {
        (currentViewHolder as? MomentAdapter.MomentViewHolder)?.hideErrorView()
    }

    fun updateAutoScrollCountDown(progress: Int) {
        (currentViewHolder as? MomentAdapter.MomentViewHolder)?.updateAutoScrollCountDown(progress)

    }

    fun hideAutoScrollCountDown() {
        (currentViewHolder as? MomentAdapter.MomentViewHolder)?.hideAutoScrollCountDown()
    }

    private fun loopPlayer() {
        player?.let {
            it.seek(0)
            Timber.tag("tam-moment").v(" playerTrackingCallback loopPlayer")
            isRepeat = true
            onPageChangeCallback?.onMomentRepeated(currentPage)

        }
    }

    // region Algorithm
    private fun detectPositionAndStart(scrollState: Int) {
        Timber.tag("tam-moment").v("detectPositionAndStart")
        if (scrollState == SCROLL_STATE_IDLE) {
            startPlayItem()
        }
    }

    private fun startPlayItem() {
        val llm = (layoutManager as? LinearLayoutManager)
        val activeItemPosition = llm?.findFirstCompletelyVisibleItemPosition() ?: -1
        Timber.tag("tam-moment").v("detectPositionAndStart $activeItemPosition  - $currentPage")
        if (activeItemPosition < 0) {
            Timber.tag("tam-moment").e("activeItemPosition < 0")
            return
        }

        // if page not changed, not doing anything
        if (currentPage == activeItemPosition) {
            Timber.tag("tam-moment").e("currentPage $currentPage == activeItemPosition $activeItemPosition")
            return
        }

        Timber.tag("tam-moment").i("currentPage $currentPage != activeItemPosition $activeItemPosition")
        //send log next before stop old video and start new video
        player?.let {
            logKibana?.sendLogNext(
                currentDuration = (it.currentDuration() / 1000).toString(),
                realTimePlaying = realTimePlaying.toString()
            )
        }
        // stop old video
        stopPlayer()

        currentPage = activeItemPosition
        currentItemId = playerRvAdapter?.itemId(activeItemPosition)
//            currentObject = playerRvAdapter?.item(activeItemPosition)
        val viewHolder = findViewHolderForAdapterPosition(currentPage)
        if (viewHolder is PlayerRvItemViewHolder) {
            currentViewHolder = viewHolder
        }
        onPageChangeCallback?.onPageSelected(activeItemPosition)
        if (autoPlayOnSelect) {
            showLoading()
            onPageChangeCallback?.requestStreamUrl(activeItemPosition)
            preloadStream(currentPage)
        }
        started = true
    }

    fun checkLoadMore() {
        playerRvAdapter?.let {
            val llm = (layoutManager as? LinearLayoutManager)
            val activeItemPosition = llm?.findFirstCompletelyVisibleItemPosition() ?: -1
            Timber.tag("tam-moment").d("${javaClass.simpleName} activeItemPosition: $activeItemPosition ")
            loadMoreHandler?.run {
                if (activeItemPosition >= it.itemCount - this.preloadOffset || (loadMoreHandler is MultipleLoadMorePreloadHandler && activeItemPosition <= this.preloadOffset)) {
                    Timber.tag("tam-moment").d("${javaClass.simpleName} Load more")
                    val canLoadmore = loadMoreHandler?.canScroll(activeItemPosition)
                    Timber.tag("tam-moment").i("${javaClass.simpleName} Load more canLoadmore $canLoadmore")
                }
            }

        }
    }
    // endregion Algorithm

    fun start(position: Int = 0, force: Boolean = false) {
        Timber.tag("tam-moment").v("starts $position $started")
        if (!started || force) {
            detectPositionAndStart(scrollState)
        }

    }

    /*fun handleStreamUrl(streamInfo: MomentStreamInfo?, moment: MomentDetail? = null, playlistId:String = "") { //??
        Timber.tag("tam-moment").i("handleStreamUrl: ${streamInfo?.itemId} - ${streamInfo?.url}")
        if (currentItemId == null || streamInfo?.itemId != currentItemId) {
            return
        }
        if (streamInfo?.itemId.isNullOrEmpty() || streamInfo?.url.isNullOrBlank()) {
            currentStreamInfo = currentStreamInfo.copy(url = streamInfo?.url ?: "")
            removePlayerView()
            removeSeekbar()
//            hideLoading()
            showLoading() // flow product: if get stream error -> show loading on top thumbnail
            currentViewHolder?.unbind()
            return
        }
        moment?.let {
            logKibana?.setDataMomentCur(it, playlistId)
        }
        currentViewHolder?.apply {
            currentStreamInfo = currentStreamInfo.copy(url = streamInfo?.url ?: "")
//            hideLoading()
            hideError()
            startPlayer(streamInfo?.url ?: "", "", streamInfo?.streamSession ?: "", streamInfo?.itemId ?: "")
            playerView?.let {
                bindPlayerView(it)
            }
//            playerSeekbar?.let {
//                bindSeekbarView(it)
//            }
            player?.let {
                bindPlayer(it)
            }
            post(updateProgressRunnable)

            // Tracking
            isRepeat = false

        }
    }*/

    fun handleStreamUrlV2(streamInfo: MomentStreamInfo?, moment: MomentDetail? = null, playlistId:String = "") {
//        Timber.tag("tam-moment").i("handleStreamUrlV2: ${streamInfo?.itemId} - ${streamInfo?.streamMomentDataV2}")

        if (currentItemId == null || streamInfo?.itemId != currentItemId) {
            return
        }

        if (streamInfo?.itemId.isNullOrEmpty() || streamInfo?.streamMomentDataV2 == null) {
            currentStreamInfo = currentStreamInfo.copy(streamMomentDataV2 = streamInfo?.streamMomentDataV2)
            removePlayerView()
            removeSeekbar()
//            hideLoading()
            showLoading() // flow product: if get stream error -> show loading on top thumbnail
            currentViewHolder?.unbind()
            return
        }

        if (streamInfo.streamUnavailable) {
            // flow product: if streamUnavailable (have data but stream url blank) -> show loading on top thumbnail
            currentStreamInfo = currentStreamInfo.copy(streamMomentDataV2 = streamInfo.streamMomentDataV2)
            removePlayerView()
//            removeSeekbar()
            playerSeekbar?.let {
                //currentViewHolder?.bindSeekbarView(it)
                it.progress = 0
                removeCallbacks(updateProgressRunnable)
            }
            player?.stop(force = true)
            player?.request = null
            hideLoading()
            showError(
                title = MainApplication.INSTANCE.sharedPreferences.getMomentNotfoundMsgTitle(),
                description = MainApplication.INSTANCE.sharedPreferences.getMomentNotfoundMsgDesc()
            )
            currentViewHolder?.unbind()
            return
        }
        val streamMomentDataV2 = streamInfo.streamMomentDataV2
        currentViewHolder?.apply {
            currentStreamInfo = currentStreamInfo.copy(streamMomentDataV2 = streamMomentDataV2)
//            hideLoading()
            hideError()
            moment?.let {
                logKibana?.setDataMomentCur(it, playlistId)
            }
            startPlayer(
                streamUrl = streamMomentDataV2.urlDashH264.ifBlank { streamMomentDataV2.urlHlsH264 } ?: "",
                h265StreamUrl = streamMomentDataV2.urlDashH265.ifBlank { streamMomentDataV2.urlHlsH265 } ?: "",
                streamSession = streamInfo.streamSession ?: "",
                itemId = streamInfo.itemId
            )
            playerView?.let {
                bindPlayerView(it)
            }
//            playerSeekbar?.let {
//                bindSeekbarView(it)
//            }
            player?.let {
                bindPlayer(it)
            }
            post(updateProgressRunnable)

            // Tracking
            isRepeat = false
        }
    }

    private fun preloadStream(currentPosition: Int) {
        if (preloadStreamOffsetBottom <= 0) return
        val size = playerRvAdapter?.size() ?: return
        var offset = 1
        var preloadPage = currentPosition + offset
        while (offset <= preloadStreamOffsetBottom && preloadPage < size) {
            Timber.tag("tam-moment").i("preloadStream pos: $preloadPage")
            onPageChangeCallback?.requestStreamUrl(preloadPage)
            offset++
            preloadPage = currentPosition + offset
        }
    }

    fun executeWhenOnPause() {
        if (player?.isPlaying() == true || isPausingManual) {
            pauseVideo()
        } else {
            isCallStop = true
            player?.stop()
            player?.release()
        }
        momentStreamLocalBookmarkInfo = null
    }

    fun executeWhenOnStop() {
        isCallStop = true
        player?.stop()
        player?.release()
    }

    fun isPauseVideo(): Boolean {
        return player?.isPause() == true && !isCallStop
    }

    fun restart() {
        onPageChangeCallback?.retryStreamUrl(currentPage)
        isCallStop = false
    }

    private fun pauseVideo() {
        player?.pause()
        // Tracking
        Timber.tag("tam-moment").v(" playerTrackingCallback Pause")
    }

    fun playVideo() {
        isCallStop = false
        player?.play()
        isPausingManual = false
        Timber.tag("tam-moment").v(" playerTrackingCallback Play")
    }

    fun togglePlayPause() {
        if (player?.isPlaying() == true) {
            Timber.tag("tam-moment").v(" playerTrackingCallback togglePlayPause Pause")
            isPausingManual = true
            pauseVideo()
            player?.let {
                logKibana?.sendLogPause(
                    currentDuration = (it.currentDuration() / 1000).toString(),
                    realTimePlaying = realTimePlaying.toString()
                )
            }

        } else {
            Timber.tag("tam-moment").v(" playerTrackingCallback togglePlayPause Play")
            isPausingManual = false
            playVideo()
            player?.let {
                logKibana?.sendLogResume(
                    currentDuration = (it.currentDuration() / 1000).toString(),
                    realTimePlaying = realTimePlaying.toString()
                )
            }
        }
    }

    // region Progress Bar
    private val delayTimeToUpdateProgress: Long = 1000
    private val updateProgressRunnable: Runnable by lazy { Runnable { updateProgress() } }

    private fun updateProgress() {
        player?.let {
//            currentViewHolder?.updateProgress(it.currentDuration(), it.bufferDuration(), it.totalDuration())
            val currentDur = it.currentDuration()
            val bufferDur = it.bufferDuration()
            val totalDur = it.totalDuration()
            if (!isSeekBarDragging) {
                val currentTime = currentDur ?: errorTime
                val bufferTime = bufferDur ?: errorTime
                val durationTime = totalDur ?: errorTime
                playerSeekbar?.apply {
                    if (currentTime != errorTime) progress = currentTime.toInt()
                    if (bufferTime != errorTime) secondaryProgress =
                        bufferTime.toInt()
                    if (durationTime != errorTime) max = durationTime.toInt()
                    show()
                }
                removeCallbacks(updateProgressRunnable)
                postDelayed(updateProgressRunnable, delayTimeToUpdateProgress)
            }
        }
    }

    // endregion Progress Bar


    //region Pre Caching

    private fun getMediaSource(
        url: String,
        cacheDataSourceFactory: DataSource.Factory
    ): MediaSourceFactory {
        return when {
            url.contains(".m3u8") -> {
                HlsMediaSource.Factory(cacheDataSourceFactory)
            }

            url.contains(".mpd") -> {
                DashMediaSource.Factory(cacheDataSourceFactory)
            }

            url.contains(".mp4") -> {
                ProgressiveMediaSource.Factory(cacheDataSourceFactory)
            }

            else -> HlsMediaSource.Factory(cacheDataSourceFactory)
        }
    }

    fun updateCurrentPage(size: Int, addToFirst: Boolean) {
        currentPage = if (addToFirst) size + currentPage else currentPage
    }

    //endregion

    interface OnPageChangeCallback {
        fun onPageSelected(position: Int)
        fun onMomentRepeated(position: Int)
        fun requestStreamUrl(position: Int)
        fun retryStreamUrl(position: Int)
    }


    companion object {
        const val PLAYER_ERROR_CODE_INTERNET = 2001
    }
}