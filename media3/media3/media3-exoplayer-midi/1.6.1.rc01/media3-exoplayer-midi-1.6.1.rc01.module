{"formatVersion": "1.1", "component": {"group": "androidx.media3", "module": "media3-exoplayer-midi", "version": "1.6.1.rc01", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.media3", "module": "media3-exoplayer", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-decoder", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-extractor", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-common", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-exoplayer-midi-1.6.1.rc01.aar", "url": "media3-exoplayer-midi-1.6.1.rc01.aar", "size": 280772, "sha512": "212a4b99a0174ae80f2b7ca39124a7fb12f6b417bc96b672daa03c65a1e9046f6d5c01e2a75b228362420eacca5e72c200cf82dd40072ea525cdf47ed193d8da", "sha256": "27dd0e0eb051a830e4a6a82cf612c579eb4e3dc00adf4dbb4c7da0509007c9a8", "sha1": "3434d4d2d665226b1253fb387705fded55db5426", "md5": "1b85ccf6a5b02018a8f0ba9906c63373"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.6.0"}}, {"group": "com.github.philburk", "module": "jsyn", "version": {"requires": "40a41092cbab558d7d410ec43d93bb1e4121e86a"}}, {"group": "androidx.media3", "module": "media3-exoplayer", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-decoder", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-extractor", "version": {"requires": "1.6.1.rc01"}}, {"group": "androidx.media3", "module": "media3-common", "version": {"requires": "1.6.1.rc01"}}], "files": [{"name": "media3-exoplayer-midi-1.6.1.rc01.aar", "url": "media3-exoplayer-midi-1.6.1.rc01.aar", "size": 280772, "sha512": "212a4b99a0174ae80f2b7ca39124a7fb12f6b417bc96b672daa03c65a1e9046f6d5c01e2a75b228362420eacca5e72c200cf82dd40072ea525cdf47ed193d8da", "sha256": "27dd0e0eb051a830e4a6a82cf612c579eb4e3dc00adf4dbb4c7da0509007c9a8", "sha1": "3434d4d2d665226b1253fb387705fded55db5426", "md5": "1b85ccf6a5b02018a8f0ba9906c63373"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "media3-exoplayer-midi-1.6.1.rc01-sources.jar", "url": "media3-exoplayer-midi-1.6.1.rc01-sources.jar", "size": 35024, "sha512": "12789bd7ff89fc70c5813ee2cef32980dfa10b8e0aeda16eef261495ec2e565605e864524cb01c57d7338394dac39f65779cf13c84a05c942ff16071b37bcaf6", "sha256": "fa2e613ebb9c95197727eb5bc3c29f3e465a6ea977863d9e54b3493ef878afe2", "sha1": "553fc0f4718b676e0a515f3d0f40408f36fa7b16", "md5": "b4958a9e24aa3a4f1347d1eefd26de4c"}]}]}