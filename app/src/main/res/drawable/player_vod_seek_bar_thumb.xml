<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <layer-list>
            <item android:gravity="center">
                <shape android:shape="oval">
                    <solid android:color="@color/white" />
                    <size android:width="@dimen/player_timeline_progress_thumb_focus_size" android:height="@dimen/player_timeline_progress_thumb_focus_size" />
                </shape>
            </item>
        </layer-list>
    </item>

    <item android:state_focused="true">
        <layer-list>
            <item android:gravity="center">
                <shape android:shape="oval">
                    <solid android:color="@color/white" />
                    <size android:width="@dimen/player_timeline_progress_thumb_focus_size" android:height="@dimen/player_timeline_progress_thumb_focus_size" />
                </shape>
            </item>
        </layer-list>
    </item>

    <!-- Normal state -->
    <item>
        <layer-list>
            <item android:gravity="center">
                <shape android:shape="oval">
                    <solid android:color="@color/white" />
                    <size android:width="@dimen/player_timeline_progress_thumb_size" android:height="@dimen/player_timeline_progress_thumb_size" />
                </shape>
            </item>
        </layer-list>
    </item>
</selector>