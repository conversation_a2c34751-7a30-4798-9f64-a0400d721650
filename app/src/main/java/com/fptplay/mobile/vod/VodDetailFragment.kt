package com.fptplay.mobile.vod

import android.content.Intent
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.widget.ImageView
import android.widget.Toast
import androidx.annotation.DrawableRes
import androidx.annotation.UiThread
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import coil.load
import com.fplay.module.downloader.VideoDownloadManager
import com.fplay.module.downloader.listener.DownloadListener
import com.fplay.module.downloader.model.VideoTaskItem
import com.fplay.module.downloader.model.VideoTaskState
import com.fplay.module.downloader.utils.VideoDownloadUtils
import com.fplay.module.downloader.utils.VideoStorageUtils
import com.fptplay.dial.connection.models.transfer_model.ExceptionEventType
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavAirlineDirections
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.NavVodChildDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.block.BlockItemAdapter
import com.fptplay.mobile.common.extensions.*
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.models.NextActionEvent
import com.fptplay.mobile.common.ui.bases.BaseActivity
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.ui.view.GlobalSnackbarManager
import com.fptplay.mobile.common.ui.view.GlobalSnackbarManager.withCurrent
import com.fptplay.mobile.common.ui.view.SnackbarManager
import com.fptplay.mobile.common.ui.view.PlayerVodSeekbarView
import com.fptplay.mobile.common.utils.*
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.databinding.AdsInteractiveOmniProductViewLayoutBinding
import com.fptplay.mobile.databinding.GameEmojiLayoutBinding
import com.fptplay.mobile.databinding.VodDetailFragmentBinding
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.adjust.Source
import com.fptplay.mobile.features.adjust.SourcePage
import com.fptplay.mobile.features.adjust.WatchType
import com.fptplay.mobile.features.comment_v2.CommentV2ViewModel
import com.fptplay.mobile.features.comment_v2.data.CommentTrackingData
import com.fptplay.mobile.features.download.DownloadUtils
import com.fptplay.mobile.features.game_emoji.utils.GameEmojiUtils
import com.fptplay.mobile.features.game_emoji.view.GameEmojiView
import com.fptplay.mobile.features.mega.view.OmniProductView
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.features.payment.PaymentViewModel
import com.fptplay.mobile.features.payment.google_billing.BillingUtils
import com.fptplay.mobile.features.payment.util.PaymentTrackingUtil
import com.fptplay.mobile.features.pladio.util.context
import com.fptplay.mobile.features.tracking_ga4.TrackingGA4Proxy
import com.fptplay.mobile.player.PlayerUtils
import com.fptplay.mobile.player.PlayerView
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.visible
import com.fptplay.mobile.vod.data.BuyPackageGuide
import com.fptplay.mobile.vod.data.VodCommentInfo
import com.fptplay.mobile.vod.dialog.addViewRating
import com.fptplay.mobile.vod.views.VodCommentTypeView
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.RequiredLogin
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.game.gamevod.GameVOD
import com.xhbadxx.projects.module.domain.entity.fplay.vod.Details
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class VodDetailFragment : BaseFragment<VodDetailViewModel.VodDetailState, VodDetailViewModel.VodDetailIntent>() {
    override val viewModel: VodDetailViewModel by activityViewModels()
    private val viewModelCommentV2: CommentV2ViewModel by activityViewModels()
    private val vodAdsViewModel: VodAdsViewModel by activityViewModels()
    private val paymentViewModel by activityViewModels<PaymentViewModel>()
    private var vodId: String = ""

    @Inject
    lateinit var sharedPreferences: SharedPreferences
    private val safeArgs: VodDetailFragmentArgs by navArgs()

    private var _binding: VodDetailFragmentBinding? = null
    private val binding get() = _binding!!

    private var adsStubBinding: AdsInteractiveOmniProductViewLayoutBinding? = null

    private var gameEmojiStubBinding: GameEmojiLayoutBinding? = null

    private val momentAdapter by lazy{ BlockItemAdapter(binding.root.context, BlockItemAdapter.Type.VerticalSliderVideo) }

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    private var isFollow = false

    private var downloadEpisode: Details.Episode? = null
    private var downloadEpisodeIdx: Int = -1

    var isUsageOptionInternal : String? = ""

    private var previewTimeUpDialog: AlertDialog? = null
    private val vodPlayerFragment get() = try {
        childFragmentManager.findFragmentById(R.id.f_player) as? VodPlayerFragment
    } catch (ex: Exception) {
        null
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        VideoDownloadManager.instance.setGlobalDownloadListener(mDownloadListener)
        _binding = VodDetailFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }


    override fun onDestroyView() {
        hideAdsInteractiveView(destroy = true)
        adsStubBinding = null
        hideGameEmojiView(destroy = true)
        gameEmojiStubBinding = null
        viewModel.triggerPlayRequiredVipTrailer(null)
        viewModel.triggerPlayPreview(null)
        viewModel.triggerPlayerChanged(null)
        viewModelCommentV2.clearCommentData()
        _binding = null
        TrackingUtil.idRelated = ""
        TrackingUtil.itemIndex = ""
        super.onDestroyView()
    }

    override fun onResume() {
        super.onResume()
//        Timber.v("*** resume ${Constants.FIREBASE_NOTIFICATION_FILED_TYPE_VOD}  ${viewModel.getId()}")
        sharedPreferences.saveCurrentContentType(Constants.FIREBASE_NOTIFICATION_FILED_TYPE_VOD)
        sharedPreferences.saveCurrentContentId(viewModel.getId())
    }

    override fun onPause() {
        super.onPause()
//        Timber.w("*** pause ${Constants.FIREBASE_NOTIFICATION_FILED_TYPE_VOD} ")
        sharedPreferences.removeCurrentContentId()
        sharedPreferences.removeCurrentContentType()
        //
        previewTimeUpDialog?.dismissAllowingStateLoss()
    }

    override fun initData() {
        if (vodId.isNotEmpty()) {
            viewModel.saveId(vodId)
            viewModelCommentV2.saveContentId(vodId)
        } else {
            viewModel.saveId(id = safeArgs.id)
            viewModelCommentV2.saveContentId(safeArgs.id)
        }
        Timber.d("******vodid: $vodId safeargs: ${safeArgs.id} id: ${viewModel.getId()}")

        viewModel.saveIsPlaylistContent(isPlaylist = safeArgs.isPlaylist)
        viewModel.saveLoginRequired(isRequired = false, requiredLogin = null)
        viewModel.saveVipRequired(isRequired = false, requiredVip = null)
        viewModel.saveScreenProvider(screenProvider = safeArgs.screenProvider)
        viewModel.triggerInitPlayer()
    }

    override fun bindData() {
        Logger.d("trangtest vao bindata ==== ${viewModel.getDataDetail()}")
        if (context.isTablet()) {
            if (viewModel.getDataDetail() != null) {
                updateComment(
                    viewModelCommentV2.getCommentTypeList()
                ) {
                    lifecycleScope.launch(Dispatchers.IO) {
                        delay(100)
                        binding.vCommentType.post {
                            binding.vCommentType.scrollToPosition(viewModelCommentV2.getLastCommentTypeClickPosition())
                            viewModelCommentV2.saveLastCommentTypeClickPosition(0)
                        }
                    }
                }
                viewModel.getDataDetail()?.let { updateDetailUI(data = it) }
                updateMomentUI()
            }
        }
        viewModel.getDataDetail()?.let {
            updateDetailBuyPackage()
        }

    }

    override fun bindEvent() {
        observeData()
        handlePaymentState()
        bindEventForBuyPackage()
        // Tablet
        if (context.isTablet()) {
            binding.vCommentType.setTrackingInfo(trackingInfo)
            binding.vCommentType.setTooltipParentView(binding.clVodInfo)
            binding.vCommentType.setViewListener(object : VodCommentTypeView.VodCommentTypeViewListener {
                override fun onViewTouch() {
                    viewModelCommentV2.dispatchIntent(CommentV2ViewModel.CommentIntent.CancelCommentSlideAnimation)
                }

                override fun onPageSelected(position: Int) {
                    viewModelCommentV2.saveCommentPagePosition(position)
                }
            })

            binding.vCommentType.apply {
                setItemClickListener(
                    object : IEventListener<VodCommentInfo> {
                        override fun onClickView(position: Int, view: View?, data: VodCommentInfo) {
                            Logger.d("VodCommentTypeView(${binding.vCommentType}) onClickView: $position")
                            when (data.commentType) {
                                VodCommentInfo.VodCommentType.VOD_COMMENT_TYPE_NORMAL -> {
                                    when(view?.id) {
                                        R.id.tv_retry -> {
                                            viewModelCommentV2.dispatchIntent(
                                                CommentV2ViewModel.CommentIntent.GetCommentInfo(
                                                    viewModel.getId(),
                                                    isRetry = true
                                                )
                                            )
                                        }
                                        else -> {
                                            if (data.commentData.statusInfo.status == VodCommentInfo.Status.Success) {
                                                if(NetworkUtils.isNetworkAvailable()){
                                                    navigateToCommentFragment()
                                                } else {
                                                    showWarningDialog(<EMAIL>(R.string.error_no_intent))
                                                }
                                            }
                                        }
                                    }
                                }
                                VodCommentInfo.VodCommentType.VOD_COMMENT_TYPE_AI -> {
                                    when(view?.id) {
                                        R.id.ic_question_mark -> {
                                            binding.vCommentType.showTooltipView(position)
                                        }

                                        else -> {
                                            if (sharedPreferences.userLogin()) {
                                                if(NetworkUtils.isNetworkAvailable()){
                                                    navigateToAICommentFragment()
                                                } else {
                                                    showWarningDialog(<EMAIL>(R.string.error_no_intent))
                                                }
                                            } else {
                                                parentFragment?.navigateToLoginWithParams()
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                )
            }

            binding.clTitleInfo.onClickDelay { navigateToVodPeopleFragment() }
            binding.vDownTitleTablet.onClickDelay { navigateToVodPeopleFragment() }
            binding.llFollowTablet.onClickDelay {
                if (isFollow) {
                    //if (viewModel.getDataDetail()?.blockContent?.isComingSoon != true) {
                    viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.GetVodDeleteFollowSuggest(viewModel.getId(),"vod"))
                    //}
                } else {
                    viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.GetVodAddFollowSuggest(viewModel.getId(), "vod"))
                }
            }
            binding.llShareTablet.onClick(delayBetweenClick = if(context.isTablet()) 500L else 200L){
                viewModel.getDataDetail()?.blockContent?.webUrl?.let {
                    onShareLink(it)
                }
            }
            binding.llDownloadTablet.onClickDelay {
                if (context.isTablet()) {
                    if(sharedPreferences.userLogin()){
                        viewModel.currentEpisode()?.let { episode ->
                            findEpisodeIndex(episode)?.let { episodeIndex ->
                                handleOnChooseDownload(
                                    status = this.tag,
                                    episode = episode,
                                    episodeIndex = episodeIndex
                                )
                            }
                        }
                    } else {
                        val extendsArgs:Bundle = bundleOf(Constants.VOD_DOWNLOAD_NAVIGATE_LOGIN_REQUEST_KEY to true)
                        parentFragment?.navigateToLoginWithParams(extendsArgs = extendsArgs)
                    }
                }
            }

            updateFollowButton()
            momentAdapter.eventListener = object : IEventListener<BaseObject> {
                override fun onClickedItem(position: Int, data: BaseObject) {
                    when (PlayerUtils.getPlayingType()) {
                        is PlayerView.PlayingType.Cast -> {
                            MainApplication.INSTANCE.pairingConnectionHelper.showNotSupportDialog(
                                navHostFragment = activity?.findNavHostFragment(),
                                onStopCastAndNavigate = {
                                    MainApplication.INSTANCE.pairingConnectionHelper.disconnect()
                                    Timber.d("thien test click item moment")
//                                    checkBeforePlayUtil.navigateToSelectedContent(data, CheckBeforePlayUtil.SourceScreen.VodDetail)
                                    checkBeforePlayUtil.navigateToSelectedContent(data, CheckBeforePlayUtil.NavExtraData.NavMomentData(
                                        sourceScreen = CheckBeforePlayUtil.SourceScreen.VodDetail,
                                        relatedId = viewModel.getId()
                                    ))
                                },
                                onNavigate = {
                                    Timber.d("thien test click item moment")
//                                    checkBeforePlayUtil.navigateToSelectedContent(data, CheckBeforePlayUtil.SourceScreen.VodDetail)
                                    checkBeforePlayUtil.navigateToSelectedContent(data, CheckBeforePlayUtil.NavExtraData.NavMomentData(
                                        sourceScreen = CheckBeforePlayUtil.SourceScreen.VodDetail,
                                        relatedId = viewModel.getId()
                                    ))
                                },
                                onCancel = {}
                            )
                        }
                        else -> {
                            Timber.d("thien test click item moment")
//                            checkBeforePlayUtil.navigateToSelectedContent(data, CheckBeforePlayUtil.SourceScreen.VodDetail)
                            checkBeforePlayUtil.navigateToSelectedContent(data, CheckBeforePlayUtil.NavExtraData.NavMomentData(
                                sourceScreen = CheckBeforePlayUtil.SourceScreen.VodDetail,
                                relatedId = viewModel.getId()
                            ))
                        }
                    }
                }
            }
        }
    }

    private fun bindEventForBuyPackage() {
        binding.layoutBuyPackageDetail.btnBuyPackage.onClickDelay {
            handleNavigateToBuyPackage()
        }
    }

    override fun bindComponent() {
        binding.rcvMoment.apply {
            adapter = momentAdapter
            layoutManager = LinearLayoutManager(binding.root.context, LinearLayoutManager.HORIZONTAL, false)
        }
        bindEventFragmentResult()
        
        // Set up custom seekbar for the player
         setupCustomSeekbar(binding.playerCustomSeekbar)
    }
    
    private fun setupCustomSeekbar(seekBar: PlayerVodSeekbarView?) {
        // Get the VodPlayerFragment and set the custom seekbar
        try {
            seekBar?.let {
                if(binding.playerCustomSeekbar.isSeekBarControlVisible() && vodPlayerFragment?.getPlayerRequest() != null) it.showSeekbarControl() else it.hideSeekbarControl()
            }
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
        vodPlayerFragment?.setCustomSeekBarView(seekBar)
    }


    override fun VodDetailViewModel.VodDetailState.toUI() {
        when (this) {
            is VodDetailViewModel.VodDetailState.ResultDetail -> {
                //region save data for log Adjust
                AdjustAllEvent.dataCur.contentId = data.blockContent.id
                AdjustAllEvent.dataCur.contentName = data.blockContent.titleVietnam
                AdjustAllEvent.dataCur.contentType = data.blockContent.tracking.contentType
                AdjustAllEvent.dataCur.genre = data.maturityRating.advisories
                AdjustAllEvent.dataCur.watchType = if (data.blockContent.isVip == 1) WatchType.paid else WatchType.free
                AdjustAllEvent.dataCur.sourcePage = SourcePage.detail_module
                AdjustAllEvent.dataCur.source = Source.package_recommend
                //endregion
                vodId = data.blockContent.id
                if (context.isTablet()) {
                    updateFollowButton(data)
                    updateDetailUI(data = data, isPlayerCalled = this.intent is VodDetailViewModel.VodDetailIntent.GetDetail && this.intent.isPlayerCalled)
                }
                if (this.intent is VodDetailViewModel.VodDetailIntent.GetDetail && this.intent.isPlayerCalled) {
                    updateDetailBuyPackage()
                }
                if(this.intent is VodDetailViewModel.VodDetailIntent.GetDetail && !this.intent.isPlayerCalled){
                    Logger.d("trangtest get rating === in reult detail")
                    viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.GetRating(itemId = data.blockContent.id, refId = data.blockContent.refId))
                }
                viewModel.saveDataDetail(this.data)

                if (this.data.blockContent.isComment) {
                    viewModelCommentV2.dispatchIntent(
                        CommentV2ViewModel.CommentIntent.GetCommentInfo(
                            viewModel.getId()
                        )
                    )
                } else {
                    viewModelCommentV2.dispatchIntent(
                        CommentV2ViewModel.CommentIntent.UpdateNormalCommentData(
                            isSuccess = false,
                            vodId = viewModel.getDataDetail()?.blockContent?.id ?: "",
                            vodCommentInfo = null
                        )
                    )
                }
            }
            is VodDetailViewModel.VodDetailState.ResultUserRating->{

            }
            is VodDetailViewModel.VodDetailState.ResultRatingData->{
                Logger.d("trangtest get rating === $data")
                if (context.isTablet()) {
                    loadDataRating()
                }
            }
            is VodDetailViewModel.VodDetailState.ResultStream -> {
                hideBuyPackageView()
            }
            is VodDetailViewModel.VodDetailState.ResultOnPlayerChanged -> {
                if (context.isTablet()) {
                    if (viewModel.isPlaylistContent()) { // Playlist
                        binding.tvTitleTablet.text = viewModel.getDataDetail()?.blockContent?.titleVietnam ?:""
                    } else {
                        if(viewModel.getDataDetail()?.blockContent?.episodeType ==0){
                            binding.tvTitleTablet.text = viewModel.getDataDetail()?.blockContent?.titleVietnam ?:""
                        }else{
                            binding.tvTitleTablet.text = <EMAIL>(R.string.vod_title_pattern, viewModel.getDataDetail()?.blockContent?.titleVietnam ?:"", viewModel.currentEpisode()?.titleVietnam?:"")
                        }
                    }
                    viewModel.dispatchIntent(
                        VodDetailViewModel.VodDetailIntent.GetVodCheckFollowSuggest(
                            viewModel.getId(),
                            "vod"
                        )
                    )
                    viewModel.currentEpisode()?.let {
                        viewModel.getDataDetail()?.let { detail ->
                            viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.GetDownloadStage(detail.blockContent.id, it))
                        }
                    }
                }
            }
            is VodDetailViewModel.VodDetailState.ResultVodCheckFollowSuggest -> {
                if (context.isTablet()) {
                    isFollow = data.status == 1
                    updateFollowButton()
                }
            }
            is VodDetailViewModel.VodDetailState.ResultVodAddFollowSuggest -> {
                if (context.isTablet()) {
                    isFollow = data.status == 1
                    updateFollowButton()
                }
            }
            is VodDetailViewModel.VodDetailState.ResultVodDeleteFollowSuggest -> {
                if (context.isTablet()) {
                    isFollow = data.status != 1
                    updateFollowButton()
                }
            }
            is VodDetailViewModel.VodDetailState.ErrorRequiredLogin -> {
                when (data){
                    is VodDetailViewModel.VodDetailIntent.UserRating->{
                        findNavController().navigateUp()
                        viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.TriggerPlayerState(true)) //play continue
                        parentFragment?.navigateToLoginWithParams()
                    }
                    is VodDetailViewModel.VodDetailIntent.CheckForDownload,
                    is VodDetailViewModel.VodDetailIntent.GetDetail -> {
                        handleErrorRequireLogin(requiredLogin, message)
                        val extendsArgs:Bundle = bundleOf(Constants.VOD_DOWNLOAD_NAVIGATE_LOGIN_REQUEST_KEY to true)
                        parentFragment?.navigateToLoginWithParams(title = this.message, extendsArgs =extendsArgs)
                    }
                    is VodDetailViewModel.VodDetailIntent.GetStream -> {
                        handleErrorRequireLogin(requiredLogin, message)
                        if (viewModel.hasPreview()) {
                            requiredLogin?.let {
                                if (viewModel.hasPreviewLink(it)) {
//                                    viewModel.triggerPlayPreview(url265 = it.urlDashH265, url = it.urlDash)
                                    // Update UI
                                    showBuyPackageView()
                                } else {
                                    hideBuyPackageView()
                                    //
                                    val extendsArgs:Bundle = bundleOf(Constants.VOD_DOWNLOAD_NAVIGATE_LOGIN_REQUEST_KEY to true)
                                    parentFragment?.navigateToLoginWithParams(title = this.message, extendsArgs =extendsArgs)
                                }
                            } ?: kotlin.run {
                                hideBuyPackageView()
                                val extendsArgs:Bundle = bundleOf(Constants.VOD_DOWNLOAD_NAVIGATE_LOGIN_REQUEST_KEY to true)
                                parentFragment?.navigateToLoginWithParams(title = this.message, extendsArgs =extendsArgs)
                            }
                        } else {
                            hideBuyPackageView()
                            val extendsArgs:Bundle = bundleOf(Constants.VOD_DOWNLOAD_NAVIGATE_LOGIN_REQUEST_KEY to true)
                            parentFragment?.navigateToLoginWithParams(title = this.message, extendsArgs =extendsArgs)
                        }
                    }
//                    is VodDetailViewModel.VodDetailIntent.GetVodAddFollowSuggest->{
//                        val extendsArgs:Bundle = bundleOf(
//                            Constants.VOD_FOLLOW_NAVIGATE_LOGIN_REQUEST_KEY to true,
//                            Constants.VOD_FOLLOW_NAVIGATE_LOGIN_VALUE to true,
//                        )
//                        parentFragment?.parentFragment?.navigateToLoginWithParams(title = this.message,extendsArgs = extendsArgs)
//                    }
//                    is VodDetailViewModel.VodDetailIntent.GetVodDeleteFollowSuggest -> {
//                        val extendsArgs:Bundle = bundleOf(
//                            Constants.VOD_FOLLOW_NAVIGATE_LOGIN_REQUEST_KEY to true,
//                            Constants.VOD_FOLLOW_NAVIGATE_LOGIN_VALUE to false,
//                        )
//                        parentFragment?.parentFragment?.navigateToLoginWithParams(title = this.message,extendsArgs = extendsArgs)
//                    }
                    else -> {}
                }
            }
            is VodDetailViewModel.VodDetailState.ErrorRequiredVip -> {
                if (data is VodDetailViewModel.VodDetailIntent.GetStream) {
                    viewModel.saveVipRequired(isRequired = true, requiredVip = requiredVip)
                    viewModel.getVipRequired()?.let {
                        if (it.first) {
                            it.second?.run {
                                if (viewModel.hasPreview()
                                    && (viewModel.hasPreviewLink(this))
                                ) {
//                                    val isAutoPlay = if (data.nextActionEvent != null) {
//                                        if (data.nextActionEvent.action == NextActionEvent.Action.PREVIEW) {
//                                            val isNavigateToPayment = data.nextActionEvent.data.getBoolean(Constants.PREVIEW_SHOULD_NAVIGATE_TO_PAYMENT_VALUE, false)
//                                            !isNavigateToPayment
//                                        } else true
//                                    } else true
//                                    viewModel.triggerPlayPreview(url265 = urlDashH265, url = urlDash, isAutoPlay = isAutoPlay)

                                } else {
                                    viewModel.triggerPlayRequiredVipTrailer(url = this.trailerUrl)
                                }
                                showBuyPackageView()
                            }
                        } else {
                            hideBuyPackageView()
                        }
                    } ?: run { hideBuyPackageView() }

                    // PairingControl
                    if (MainApplication.INSTANCE.pairingConnectionHelper.isConnected) {
                        MainApplication.INSTANCE.pairingConnectionHelper.saveProcessingData(type = "vod", id = viewModel.getId() ?:"", actionType = ExceptionEventType.REQUIRE_PAYMENT)
                    }

                    //
                    data.nextActionEvent?.let {
                        if (it.action == NextActionEvent.Action.GO_TO_PAYMENT) {
                            val isNavigateToPayment = it.data.getBoolean(Constants.PREVIEW_SHOULD_NAVIGATE_TO_PAYMENT_VALUE, false)
                            if (isNavigateToPayment) {
                                handleClickBuyPackageForPreview()
                            }
                        }
                    }
                }
            }
            is VodDetailViewModel.VodDetailState.ResultDownloadStage -> {
                if (context.isTablet()) {
                    checkDownload(this.episode, this.downloadStage,if(this.percent >= 1.0) VideoDownloadUtils.getPercent(this.percent) else "")
                }
            }
            is VodDetailViewModel.VodDetailState.ResultTriggerPlayerLayout -> {
                adjustPlayerConstraint(isScale = this.isScale)
            }
            is VodDetailViewModel.VodDetailState.ResultGameVod -> {
                updateRankingButton(
                    if(GameEmojiUtils.validGameEmojiV2(viewModel,data) ) {
                        data.data
                    } else {
                        null
                    }
                )
            }

            is VodDetailViewModel.VodDetailState.StartGameEmoji -> {
                startGameEmoji(url = url, jsonData = jsonData)
            }
            is VodDetailViewModel.VodDetailState.ShowRankGameEmoji -> {
                showRankGameEmoji(url = url)
            }
            is VodDetailViewModel.VodDetailState.CloseGameEmoji -> {
                hideGameEmojiView(destroy = false)
            }
            is VodDetailViewModel.VodDetailState.DownloadByChapterResult -> {
                DownloadUtils.bindEventFragmentResult(this.data.chapterId,this@VodDetailFragment, eventDelete = {
                    showDownloadLoading()
                    lifecycleScope.launch(Dispatchers.IO) {
                        VideoDownloadManager.instance.deleteVideoTask(data.chapterId, true)
                    }
                }, eventList = {
                    parentFragment?.findNavController()
                        ?.navigate(NavHomeMainDirections.actionGlobalToDownloadV2())
//                    findNavController().navigate(VodDetailFragmentDirections.actionVodDetailFragmentToNavDowloadV2())
                }, eventExtend = {
                    viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.CheckExtendForDownload(data))
                }, checkInternet = {
                    if(NetworkUtils.isNetworkAvailable()) {
                        val alwaysDownloadByWifi = sharedPreferences.alwaysDownloadByWifi()
                        if (alwaysDownloadByWifi && Utils.useMobileData(activity)){
                            showWarningAlwaysUseWifiDialog()
                            false
                        } else {
                            true
                        }
                    } else {
                        showWarningDialog(<EMAIL>(R.string.error_no_intent))
                        false
                    }
                })
                if(data.taskState == VideoTaskState.SUCCESS) {
                    if (DownloadUtils.calculateTimeLeft(data.lastUpdateTime,data.expiredTime) <= 0) {
                        viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_DOWNLOADED_BUT_EXPIRED)
                    }
                }
                findNavController().navigateSafe(VodDetailFragmentDirections.actionVodDetailFragmentToVodOptionDialogFragment(hasEdgeToEdge = hasEdgeToEdge))
            }
            is VodDetailViewModel.VodDetailState.ResultCheckExtendForDownload -> {
                if (this.vodDownloadInfo.downloadUrl.isNotBlank()) {
                    val taskItem = this.item
                    taskItem.lastUpdateTime = System.currentTimeMillis()
                    DownloadUtils.extendDownloadTaskItem(item = this.item, info = this.vodDownloadInfo)
                    Toast.makeText(requireContext(),resources.getString(R.string.noti_extent_success),Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(requireContext(),resources.getString(R.string.noti_pakage_unavailable),Toast.LENGTH_SHORT).show()
                }
            }
            is VodDetailViewModel.VodDetailState.ResultFetchDownloadLink -> {
                if (this.vodDownloadInfo.downloadUrl.isNotBlank()) {
                    DownloadUtils.startDownloadWithNewUrl(this.vodDownloadInfo.downloadUrl,this.item,this.vodDownloadInfo.d2gTime, this.vodDownloadInfo.streamSession)
                } else {
                    showWarningDialog(resources.getString(R.string.noti_pakage_unavailable))
                }
            }
            is VodDetailViewModel.VodDetailState.ResultCheckEpisodeFailed -> {
                showWarningAlreadyDownloadedDialog()
            }
            is VodDetailViewModel.VodDetailState.ResultCheckForDownload -> {
                if (this.vodDownloadInfo.downloadUrl.isNullOrBlank()) {
                    if(this.vodDownloadInfo.requiredPackage.equals("0")) {
                        Toast.makeText(requireContext(),this.vodDownloadInfo.msg, Toast.LENGTH_SHORT).show()
                    } else {
                        showNotiPaymentDialog(textDesCription = this.vodDownloadInfo.msg,onConfirm = {
                            PaymentTrackingUtil.showPackageFrom = PaymentTrackingUtil.PackageFrom.Detail
                            if (isAirlineLayout()) {
                                parentFragment?.findNavController()?.navigate(NavAirlineDirections.actionGlobalToPayment())
                            } else {
                                parentFragment?.findNavController()?.navigate(NavHomeMainDirections.actionGlobalToPayment())
                            }
                        })
                    }
                    return
                }
                DownloadUtils.startNewDownload(
                    vodDownloadInfo = this.vodDownloadInfo,
                    vod = this.vod,
                    episode = this.episode,
                    episodeIndex = this.episodeIndex,
                    hasCollectionInDb = this.hasCollectionInDb,
                    isAirline = isAirlineLayout()
                )
            }
            is VodDetailViewModel.VodDetailState.ResultListMoment -> {
                viewModel.saveMomentDetail(this.data)
                updateMomentUI()
            }

            is VodDetailViewModel.VodDetailState.ShowBuyPackageGuide -> {
                showBuyPackageGuide(buyPackageGuide)
            }

            is VodDetailViewModel.VodDetailState.ResultTriggerBuyPackage -> {
                handleNavigateToBuyPackage()
                viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.TriggerConsumeBuyPackageAction)
            }

            is VodDetailViewModel.VodDetailState.Error -> {
                when (data){
                    is VodDetailViewModel.VodDetailIntent.GetListMoment -> {
                        Timber.d("thien test api moment error")
                        viewModel.saveMomentDetail(null)
                        updateMomentUI()
                    }
                    else -> {}
                }
            }
            is VodDetailViewModel.VodDetailState.ResultTriggerMsgUserReport->{
                if (context.isTablet()) {
                    showSnackbar(this.message)
                } else {
                    binding.clRoot.showSnackBar(this.message)
                }
            }
            is VodDetailViewModel.VodDetailState.ResultPreviewPlayCompleted -> {
                if (viewModel.isFullScreen.value?.first == true) {
                    viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.SwitchPlayerMode(modeFullscreen = false))
                }

                showPreviewTimeUp()
            }
            else -> {}
        }
    }

    private fun handleNavigateToBuyPackage() {
        if (viewModel.hasPreview() && hasPreviewLink()) {
            handleClickBuyPackageForPreview()
        } else {
            var isHandleRequiredVipPackage = false
            viewModel.getVipRequired()?.let {
                it.second?.let { requireVip ->
                    if (binding.navHostFragment.findNavController().currentDestination?.id != R.id.vod_child_main_fragment) {
                        binding.navHostFragment.findNavController().popBackStack(R.id.vod_child_main_fragment, false)
                    }

                    if (requireVip.isTvod) {
                        paymentViewModel.dispatchIntent(PaymentViewModel.PaymentViewIntent.GetPackagePlan(viewModel.getVipRequired()?.second?.requireVipPlan ?: "", BillingUtils.isEnableGoogleBilling(), PaymentViewModel.FromSource.PLAY.rawValue))
                    } else {
                        parentFragment?.findNavController()?.navigate(
                            NavHomeMainDirections.actionGlobalToPaymentDetail(packageType = viewModel.getVipRequired()?.second?.requireVipPlan ?: "", continueWatch = true, requestFromCast = PlayerUtils.getPlayingType() == PlayerView.PlayingType.Cast))
                        AdjustAllEvent.sendPackageRecommendClickEvent(packageId = requireVip.requireVipPlan?: "", packageName = requireVip.requireVipName?: "")
                        TrackingGA4Proxy.saveTrackingTypeClickAndContentName(TrackingGA4Proxy.TypeCallDetail.button, binding.tvTitleTablet.text.toString())
                    }
                    isHandleRequiredVipPackage = true
                }
            }
            if(!isHandleRequiredVipPackage) {
                viewModel.getBuyPackageGuide()?.let { guide ->
                    DeeplinkUtils.parseDeepLinkAndExecute(
                        deeplink = guide.deepLink,
                        trackingInfo = trackingInfo,
                        isDeeplinkCalledInApp = true
                    )
                }
            } else {}
        }
    }

    private fun hasPreviewLink(): Boolean {
        return (viewModel.getVipRequired()?.second?.let {
            viewModel.hasPreviewLink(it)
        } ?: false) || (viewModel.getLoginRequired()?.second?.let {
            viewModel.hasPreviewLink(it)
        } ?: false)
    }

    private fun loadDataRating() {
        Logger.d("trangtest ============================ loadDataRating")
        binding.llRatingGroupTablet.removeAllViews()
        viewModel.ratingData?.let { data ->
            if (data.status == 1) {//success
                data.data.content.forEach { item ->
                    binding.llRatingGroupTablet.addViewRating(
                        data = item, userRate = data.data.rateOfUser,
                        sharedPreferences = sharedPreferences, userLogin = sharedPreferences.userLogin()
                    )
                    {
                        if(sharedPreferences.userLogin()) {
                            findNavController().navigateSafe(
                                VodDetailFragmentDirections.actionVodDetailFragmentToRatingBottomSheetDialogFragment(
                                    userRating = data.data.rateOfUser,
                                    countDes = item.countDescription,
                                    rateValue = item.avgRate
                                )
                            )
                        }else {
                            parentFragment?.navigateToLoginWithParams()
                        }
                    }
                }
            } else {
                //khong get duoc data rating, khong hien thi
            }
        }
    }

    private fun handleErrorRequireLogin(requiredLogin: RequiredLogin?, message: String) {
        viewModel.saveLoginRequired(isRequired = true, requiredLogin = requiredLogin, message = message)
        // PairingControl
        if (MainApplication.INSTANCE.pairingConnectionHelper.isConnected) {
            MainApplication.INSTANCE.pairingConnectionHelper.saveProcessingData(type = "vod", id = viewModel.getId() ?:"", actionType = ExceptionEventType.REQUIRE_LOGIN)
        }
    }

    override fun observeState() {
        super.observeState()
        viewModel.playVod.observe(viewLifecycleOwner) {
            Logger.d("VODDetailFragment >> LogDetail observe play vod")

            it?.let { vod ->
                viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.GetDetail(it.first, isPlayerCalled = false))
                viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.GetVodCheckFollowSuggest(it.first, "vod"))
            }
        }
        viewModel.idChange.observe(viewLifecycleOwner) { idToUpdate ->
            if (binding.navHostFragment.findNavController().currentDestination?.id != R.id.vod_child_main_fragment) {
                binding.navHostFragment.findNavController().navigateUp()
            }
            if (idToUpdate == viewModel.getId()) { // The idChange keep the old value when first observe -> Call api with old vod id -> fix
                viewModelCommentV2.dispatchIntent(CommentV2ViewModel.CommentIntent.RefreshCommentData(vodId = idToUpdate))
                viewModel.getDataDetail()?.let {
                    if (it.blockContent.isComment) {
                        viewModelCommentV2.dispatchIntent(CommentV2ViewModel.CommentIntent.GetCommentInfo(idToUpdate))
                    }
                }
            }
        }
        vodAdsViewModel.state.observe(viewLifecycleOwner) { it.toAdsUI() }
    }

    private fun observeData() {
        viewModelCommentV2.state.observe(viewLifecycleOwner) {
            when (it) {
                is CommentV2ViewModel.CommentState.Loading -> {
                    when (it.intent) {
                        is CommentV2ViewModel.CommentIntent.GetCommentInfo -> {
                            if(it.intent.isRetry) {
                                updateNormalComment(VodCommentInfo.Status.Loading)
                            }
                        }
                        else -> {}
                    }
                }
                is CommentV2ViewModel.CommentState.Error -> {
                    when (it.intent) {
                        is CommentV2ViewModel.CommentIntent.GetCommentInfo -> {
                            updateNormalComment(status = VodCommentInfo.Status.Error, message = it.message)
                        }
                        else -> {}
                    }
                }
                is CommentV2ViewModel.CommentState.ResultCommentInfo -> {
                    viewModelCommentV2.saveCommentInfo(it.data)
                    updateNormalComment(VodCommentInfo.Status.Success)
                }
                is CommentV2ViewModel.CommentState.ResultCommentTypeLoadDone -> {
                    Logger.d("VODDetailFragment >> LogDetail observe comment type child")
                    updateComment(
                        viewModelCommentV2.getCommentTypeList()
                    )
                }
                is CommentV2ViewModel.CommentState.CommentTypeAutoSlideAnimation -> {
                    if(_binding != null) {
                        binding.vCommentType.scrollToPosition(position = it.targetPosition, isSmoothScroll = true)
                    }
                }

                is CommentV2ViewModel.CommentState.CloseSummaryCommentAI -> {
                    if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.vod_summary_comment_fragment) {
                        binding.navHostFragment.findNavController().navigateUp()
                    }
                }
                is CommentV2ViewModel.CommentState.ResultUpdateCommentInfo -> {
                    viewModelCommentV2.saveCommentInfo(it.commentInfo)
                    updateNormalComment(VodCommentInfo.Status.Success, forceUpdate = true)
                }
                is CommentV2ViewModel.CommentState.UpdateTrackingData -> {
                    val commentTrackingData = updateCommentTrackingData(it.data)
                    if (it.sendTrackingAfterUpdate) {
                        viewModelCommentV2.dispatchIntent(CommentV2ViewModel.CommentIntent.TriggerSendTrackingLog(commentTrackingData))
                    } else {
                        viewModelCommentV2.dispatchIntent(CommentV2ViewModel.CommentIntent.UpdateAndSendTrackingComplete)
                    }
                }
                else -> {}
            }
        }
        viewModel.isFullScreen.observe(viewLifecycleOwner) {
            it?.run {

                if(this.first) {
                    binding.playerCustomSeekbar.gone()
                     setupCustomSeekbar(null)
                } else {
                    if(vodPlayerFragment?.getPlayerRequest() != null) {
                        binding.playerCustomSeekbar.visible()
                    }
                    setupCustomSeekbar(binding.playerCustomSeekbar)
                }

                // isFullscreen, isLandscape
                if (context.isTablet()) {
                    handleTabletLayout(isFullscreen = it.first, isLandscapeMode = it.second)
                    binding.navHostFragment.isVisible = !it.first
                    if (!it.second) {
                        adjustPlayerConstraint(isScale = false)
                    }
                    // todo: add in next sprint
                    constraintAdsInteractiveTablet(it.second)

                    constraintEmojiPopupTablet(it.first, it.second)
                    if (gameEmojiStubBinding?.gameEmojiPopupView?.visibility == View.VISIBLE) {
                        viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.TriggerPlayerLayout(isScale = viewModel.isPlayerScalableOnGame()))
                    }
                } else {
                    binding.navHostFragment.isVisible = !it.first
                    if (!it.first) {
                        adjustPlayerConstraint(isScale = false)
                    } else binding.root.hideKeyboard()
                    constraintEmojiPopup(it.second)
                    if (gameEmojiStubBinding?.gameEmojiPopupView?.visibility == View.VISIBLE) {
                        viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.TriggerPlayerLayout(isScale = viewModel.isPlayerScalableOnGame()))
                    }
                }

                /***
                 *
                 * See docs[AdsSdkDiagramChart] (https://app.diagrams.net/#G1AkQg13Jr3qvobQKtWV0snv0_OZqEnpQX)
                 *
                 */

                if(it.first && vodAdsViewModel.isInteractiveOpened()) {
                    // if switch to fullscreen and interactive opened -> close interactive ads
                    hideAdsInteractiveView(destroy = false)
                }

                //
                // handle position when switching to fullscreen or back to normal mode
                handlePositionSnackBar(it.first,it.second)
                //


                viewModelCommentV2.getCommentPagePosition()?.let { position ->
                    binding.vCommentType.selectPage(position)

                }
            }
        }
        viewModel.downloadClick.observe(viewLifecycleOwner) {
            it?.let { onDownloadClick ->
                if(sharedPreferences.userLogin()){
                    handleOnChooseDownload(
                        status = onDownloadClick.currentStatus,
                        episode = onDownloadClick.episode,
                        episodeIndex = onDownloadClick.episodeIndex
                    )
                } else {
                    val extendsArgs:Bundle = bundleOf(Constants.VOD_DOWNLOAD_NAVIGATE_LOGIN_REQUEST_KEY to true)
                    parentFragment?.navigateToLoginWithParams(extendsArgs = extendsArgs)
                }
            }
        }
    }

    private fun updateCommentTrackingData(data: CommentTrackingData): CommentTrackingData {
         val trackingData = data.apply {
            viewModel.getDataDetail()?.let {
                appSource = it.blockContent.appId
                itemName = it.blockContent.titleVietnam
                isLinkDrm = if(it.blockContent.isVerimatrix) "1" else "0"
                businessPlan = it.blockContent.payment?.id ?: ""
            }
            viewModel.currentEpisode()?.let {
                chapterId = it.id
                episodeId = it.realEpisodeId
            }
            playerName = TrackingUtil.playerName
        }
        viewModelCommentV2.trackingData = trackingData
        return trackingData
    }

    // region Snackbar
    private fun handlePositionSnackBar(isFullscreen: Boolean,isLandscape: Boolean){
        GlobalSnackbarManager.withCurrent()?.position =  if (isFullscreen) SnackbarManager.Position.TOP_CENTER else SnackbarManager.Position.BOTTOM_CENTER
    }

    fun VodAdsViewModel.VodAdsState.toAdsUI() {
        when (this) {
            is VodAdsViewModel.VodAdsState.OpenInteractiveAdsPopup -> {
                startAdsInteractiveView(jsonData = jsonData, source = source, userInteract = userInteract)
            }
            is VodAdsViewModel.VodAdsState.CloseInteractiveAdsPopupOnAdsDone -> {
                hideAdsInteractiveViewAutomatically(destroy = false)
            }
            else -> {}
        }
    }

    private fun inflateAdsInteractiveView() {
        if (binding.vsOmniPopUpView.parent != null) {
            adsStubBinding = AdsInteractiveOmniProductViewLayoutBinding.bind(binding.vsOmniPopUpView.inflate())
            adsStubBinding?.apply {

                omniProductView.omniPopupListener = object : OmniProductView.OmniPopupListener {
                    @UiThread
                    override fun closePopup(reason: String?) {
                        hideAdsInteractiveView()
                    }

                    @UiThread
                    override fun showWarningDialog(message: String?) {
                        // not show warning, just close popup
                        hideAdsInteractiveView()
//                        if(message != null) {
//                            showWarning(message)
//                        } else {
//                            hideAdsInteractiveView()
//                        }
                    }

                    @UiThread
                    override fun onLoaded(source: OmniProductView.Source) {
                        showAdsInteractiveView(source)
                    }

                    override fun showRetryPage() {
                        hideAdsInteractiveView()
                    }
                }
                btnClose.setOnClickListener {
                    hideAdsInteractiveView()
                }


            }
        }
    }

    private fun startAdsInteractiveView(jsonData: String, source: OmniProductView.Source = OmniProductView.Source.UNKNOWN, userInteract: Boolean = false) {
        inflateAdsInteractiveView()
        adsStubBinding?.apply {
            val url = Utils.parseOmniShopUrl(jsonData)
            omniProductView.start(
                OmniProductView.OmniInitData(
                    url = url  ?: "",
                    jsonData = jsonData,
                    userId = sharedPreferences.userId(),
                    userPhone = sharedPreferences.userPhone(),
                    userToken = sharedPreferences.accessToken(),
                    userName = sharedPreferences.displayName(),
                    source = source
                ),
                userInteract = userInteract
            )
        }
    }

    private fun showAdsInteractiveView(source: OmniProductView.Source) {
        adsStubBinding?.apply {
            omniProductView.show()
            if (ctlOmniPopupView.visibility != View.VISIBLE) {
                val animSlideUp = AnimationUtils.loadAnimation(
                    context, R.anim.slide_up
                )
                animSlideUp.duration = 600
                ctlOmniPopupView.startAnimation(animSlideUp)
                ctlOmniPopupView.visibility = View.VISIBLE
            }
            vodAdsViewModel.saveInteractiveOpened(true)
            vodAdsViewModel.dispatchIntent(VodAdsViewModel.VodAdsIntent.NotifyInteractivePopupShow(source))
        }
    }
    private fun constraintAdsInteractiveTablet(isLandscape: Boolean) {
        if (binding.vsOmniPopUpView.parent != null) {
            binding.vsOmniPopUpView.apply {
                val lp = ConstraintLayout.LayoutParams(
                    0,
                    0
                )
                if (isLandscape) {
                    lp.startToEnd = R.id.f_player
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                }
                else{
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToBottom = R.id.f_player
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                }
                layoutParams = lp
            }
        } else {
            adsStubBinding?.ctlOmniPopupView?.apply {
                val lp = ConstraintLayout.LayoutParams(
                    0,
                    0
                )
                if (isLandscape) {
                    lp.startToEnd = R.id.f_player
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                } else {
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToBottom = R.id.f_player
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID

                }
                layoutParams = lp
            }
        }
    }

    private fun hideAdsInteractiveView(destroy: Boolean = false) {
        if(vodAdsViewModel.isInteractiveOpened()) {
            // only close and trigger restart ads if interactive opened
            adsStubBinding?.omniProductView?.close(destroy = destroy)
            adsStubBinding?.ctlOmniPopupView?.hide()
            vodAdsViewModel.saveInteractiveOpened(false)
            if(!destroy) {
                vodAdsViewModel.dispatchIntent(VodAdsViewModel.VodAdsIntent.TriggerRestartAds)
            }
        }
    }

    private fun hideAdsInteractiveViewAutomatically(destroy: Boolean = false) {
        if(vodAdsViewModel.isInteractiveOpened()) {
            // only close and trigger restart ads if interactive opened
            if (adsStubBinding?.omniProductView?.closeIfNotInteract(destroy = destroy) == true) {
                adsStubBinding?.ctlOmniPopupView?.hide()

                vodAdsViewModel.saveInteractiveOpened(false)
                vodAdsViewModel.dispatchIntent(VodAdsViewModel.VodAdsIntent.TriggerRestartAds)

            }
        }

    }
    // endregion Ads

    //region Tablet
    private fun navigateToVodPeopleFragment() {
        if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.vod_child_main_fragment) {
            binding.navHostFragment.findNavController().navigateSafe(VodDetailChildMainFragmentDirections.actionVodDetailFragmentToVodPeopleFragment())
        } else {
            binding.navHostFragment.findNavController().popBackStack()
        }
    }

    private fun navigateToCommentFragment() {
        if(binding.navHostFragment.findNavController().currentDestination?.id != R.id.vod_child_main_fragment
            && binding.navHostFragment.findNavController().currentDestination?.id != R.id.comment_v2_fragment) {
            binding.navHostFragment.findNavController().popBackStack()
        }
        if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.vod_child_main_fragment) {
            binding.navHostFragment.findNavController().navigateSafe(
                NavVodChildDirections.actionGlobalDetailToCommentV2Fragment(contentId = viewModel.getId())
            )
        }
    }

    private fun navigateToAICommentFragment() {
        if(binding.navHostFragment.findNavController().currentDestination?.id != R.id.vod_child_main_fragment
            && binding.navHostFragment.findNavController().currentDestination?.id != R.id.vod_summary_comment_fragment) {
            binding.navHostFragment.findNavController().popBackStack()
        }
        if (binding.navHostFragment.findNavController().currentDestination?.id == R.id.vod_child_main_fragment) {
            binding.navHostFragment.findNavController().navigateSafe(
                VodDetailChildMainFragmentDirections.actionVodChildMainFragmentToSummaryCommentFragment(
                    vodId = viewModel.getId(),
                    firstLoad = viewModelCommentV2.getAiContent(
                        viewModel.getDataDetail()?.blockContent?.id ?: "",
                    ).isEmpty()
                )
            )
        }
    }

    private fun updateFollowButton(details: Details? = null) {
        binding.ivFollowTablet.isSelected = isFollow
        val data = details ?: viewModel.getDataDetail()
        if (data?.blockContent?.isComingSoon == true) {
            binding.ivFollowTablet.isActivated = true
            binding.tvFollowTablet.text = if (isFollow) { getString(R.string.vod_scheduled_premiere) } else getString(R.string.vod_schedule_premiere)
        } else {
            binding.ivFollowTablet.isActivated = false
            binding.tvFollowTablet.text = if (isFollow) { getString(R.string.vod_unfollow) } else getString(R.string.vod_follow)
        }
    }

    private fun updateComment(
        commentData: List<VodCommentInfo>,
        callback: () -> Unit = {}
    ) {
        if (commentData.isNotEmpty()) {
            binding.vCommentType.show()
            binding.vCommentType.updateCommentData(
                commentTypeList = commentData,
            ) {
                callback.invoke()
            }
        } else {
            binding.vCommentType.hide()
        }
    }

    private fun updateMomentUI() {
        viewModel.getMomentDetail().let {
            momentAdapter.bind(it)
            binding.grMoment.isVisible = !it.isNullOrEmpty()
            binding.tvMoment.text = viewModel.getDataDetail()?.blockMoment?.name ?: ""
        }
    }

    private fun updateDetailUI(data: Details, isPlayerCalled: Boolean = false) {
        Logger.d("VODDetailFragment >> updateDetailUI >> isPlayerCalled: $isPlayerCalled data: $data")
        data.let { vod ->
            if (!isPlayerCalled) {
                binding.scrollViewVodInfo.show()
                if (viewModel.isPlaylistContent()) {
                    binding.tvTitleTablet.text = vod.blockContent.titleVietnam
                } else {
                    if (vod.blockContent.episodeType == 0) {
                        binding.tvTitleTablet.text = vod.blockContent.titleVietnam
                    } else {
                        binding.tvTitleTablet.text = if (viewModel.currentEpisode() == null) vod.blockContent.<NAME_EMAIL>(R.string.vod_title_pattern, vod.blockContent.titleVietnam, viewModel.currentEpisode()?.titleVietnam ?: "")
                    }
                }
                loadDataRating()
                val text = StringUtils.getMetaText(
                    context = binding.root.context,
                    data = vod.blockContent
                )
                Logger.d("VODDetailFragment >> updateDetailUI >> update text metadata: $text")
                if (text.isNullOrBlank()) {
                    binding.tvVideoInfoTablet.isVisible = false
                } else {
                    binding.tvVideoInfoTablet.isVisible = true
                    binding.tvVideoInfoTablet.text = text
                }
                if (vod.maturityRating.advisories.isBlank()) {
                    binding.tvAgeRestrictionTablet.gone()
                } else {
                    binding.tvAgeRestrictionTablet.text = vod.maturityRating.advisories
                    binding.tvAgeRestrictionTablet.show()
                }
                binding.tvShortDescription.apply {
                    isVisible = vod.blockContent.shortDescription.isNotBlank()
                    setText(vod.blockContent.shortDescription)
                }
            }
        }
    }

    private fun updateDetailBuyPackage() {
        viewModel.getVipRequired()?.let {
            if (it.first) {
                it.second?.let { _ ->
                    showBuyPackageView()
                } ?: run {
                    hideBuyPackageView()
                }
            } else {
                hideBuyPackageView()
            }
        } ?: run {
            hideBuyPackageView()
        }
    }

    private fun showBuyPackageView() {
        viewModel.getDataDetail()?.let {
            //show -> log event adjust
            AdjustAllEvent.sendPackageRecommendDisplayEvent(!it.blockContent.isTvod)
            binding.layoutBuyPackageDetail.apply {
                root.setBackgroundResource(R.drawable.background_buy_package_detail_full)
                ctlBuyPackageButton.setBackgroundResource(R.drawable.background_buy_package_detail)
                tvBuyPackageDescription.setTextColor(requireContext().getColor(R.color.detail_preview_buy_package_description))
                ivLogo.hide()
            }

            binding.layoutBuyPackageDetail.tvBuyPackage.text = BillingUtils.getTextBuyPackage(binding.root.context, it.blockContent.isTvod)
            val isPreview = viewModel.hasPreview() && hasPreviewLink()
            binding.layoutBuyPackageDetail.tvBuyPackageDescription.text = BillingUtils.getTextDescriptionBuyPackage(binding.root.context, it.blockContent.isTvod, isPreview = isPreview)
            binding.layoutBuyPackageDetail.root.show()
        }
    }

    private fun hideBuyPackageView() {
        binding.layoutBuyPackageDetail.root.hide()
    }


    private fun handleTabletLayout(isFullscreen: Boolean, isLandscapeMode: Boolean) {
        if (_binding == null) return
        try {
            if (isLandscapeMode) {
                if (isFullscreen) {
                    ConstraintSet().apply {
                        connect(R.id.nav_host_fragment, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
                        connect(R.id.nav_host_fragment, ConstraintSet.START, R.id.f_player, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clRoot)
                } else {
                    ConstraintSet().apply {
                        connect(R.id.nav_host_fragment, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
                        connect(R.id.nav_host_fragment, ConstraintSet.START, R.id.f_player, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clRoot)
                }
            } else {
                if (isFullscreen) {
                    ConstraintSet().apply {
                        connect(R.id.nav_host_fragment, ConstraintSet.TOP, R.id.scrollViewVodInfo, ConstraintSet.BOTTOM)
                        connect(R.id.nav_host_fragment, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
                        connect(R.id.nav_host_fragment, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clRoot)
                } else {
                    ConstraintSet().apply {
                        connect(R.id.nav_host_fragment, ConstraintSet.TOP, R.id.scrollViewVodInfo, ConstraintSet.BOTTOM)
                        connect(R.id.nav_host_fragment, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
                        connect(R.id.nav_host_fragment, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                        connect(R.id.nav_host_fragment, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    }.applyTo(binding.clRoot)
                }
            }
            //
            handleSubDetailLayout(isFullscreen, isLandscapeMode)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun handlePaymentState() {
        if (paymentViewModel.state.hasObservers()) paymentViewModel.resetState()
        paymentViewModel.state.observe(viewLifecycleOwner) { state ->
            when (state) {
                is PaymentViewModel.PaymentViewState.ResultVerifyGoogleBillingTransaction -> {
                    if (state.data.status == 1) {
                        hideBuyPackageView()
                    }
                }
                else -> {}
            }
        }
    }

    private fun handleSubDetailLayout(isFullscreen: Boolean, isLandscapeMode: Boolean) {
        if (isLandscapeMode) {
            //
            ConstraintSet().apply {
                clone(binding.clRoot)
                connect(R.id.scrollViewVodInfo, ConstraintSet.TOP, R.id.f_player, ConstraintSet.BOTTOM)
                connect(R.id.scrollViewVodInfo, ConstraintSet.START, R.id.f_player, ConstraintSet.START)
                connect(R.id.scrollViewVodInfo, ConstraintSet.END, R.id.f_player, ConstraintSet.END)
                connect(R.id.scrollViewVodInfo, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
            }.applyTo(binding.clRoot)
            //
            binding.clVodInfo.show()
            //
            val params = binding.scrollViewVodInfo.layoutParams
            params.width = 0
            params.height = 0
            binding.scrollViewVodInfo.layoutParams = params
        } else {

            //
            ConstraintSet().apply {
                clone(binding.clRoot)
                connect(R.id.scrollViewVodInfo, ConstraintSet.TOP, R.id.f_player, ConstraintSet.BOTTOM)
                connect(R.id.scrollViewVodInfo, ConstraintSet.START, R.id.f_player, ConstraintSet.START)
                connect(R.id.scrollViewVodInfo, ConstraintSet.END, R.id.f_player, ConstraintSet.END)
                connect(R.id.scrollViewVodInfo, ConstraintSet.BOTTOM, R.id.nav_host_fragment, ConstraintSet.TOP)
            }.applyTo(binding.clRoot)
            //
            binding.clVodInfo.hide()
            //
            val params = binding.scrollViewVodInfo.layoutParams
            params.width = ConstraintLayout.LayoutParams.MATCH_PARENT
            params.height = if (isFullscreen) 0 else ConstraintLayout.LayoutParams.WRAP_CONTENT
            binding.scrollViewVodInfo.layoutParams = params
        }
    }

    private fun onShareLink(url: String) {
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, url)
            type = "text/plain"
        }
        val shareIntent = Intent.createChooser(sendIntent, getString(R.string.share))
        context?.startActivity(shareIntent)
        sendTrackingShare()
    }
    private fun sendTrackingShare() {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "516",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = TrackingUtil.screen,
                event = "Share",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                itemId = viewModel.getId(),
                itemName = viewModel.getDataDetail()?.blockContent?.titleVietnam?:"",
                EpisodeID = viewModel.currentEpisode()?.realEpisodeId?: "",
                chapterId = viewModel.currentEpisode()?.id?: "",
                businessPlan = viewModel.getDataDetail()?.blockContent?.payment?.id ?: "",
                refItemId = TrackingUtil.contentPlayingInfo.refId,
                refPlaylistID = TrackingUtil.contentPlayingInfo.refPlaylistID,
                refEpisodeID = TrackingUtil.contentPlayingInfo.refEpisodeId,
                isRecommend = TrackingUtil.isRecommend
            )
        )
    }

    private fun checkDownload(episode: Details.Episode, downloadStage: Int,percentage: String = "") {
        if(DownloadUtils.canDownload(episode, isAirlineLayout(), viewModel.isPlaylistContent(), profileEnableDownload = MultiProfileUtils.profileEnabledDownload(sharedPreferences))) {
            updateDownloadButtonStatus(downloadStage,percentage)
        } else {
            updateDownloadButton(status = VideoTaskState.ERROR)
        }
    }


    @UiThread
    private fun updateDownloadButtonStatus(status: Int?, percentage: String = "") {
        if(status == null) {
            updateDownloadButton(status = VideoTaskState.ERROR)
            return
        }
        when(status) {
            VideoTaskState.DEFAULT -> {
                updateDownloadButton(R.drawable.ic_download, getString(R.string.download_button_download_text), status)
            }
            VideoTaskState.PREPARE,
            VideoTaskState.PENDING,
            VideoTaskState.START,
            VideoTaskState.DOWNLOADING -> {
                updateDownloadButton(
                    R.drawable.ic_downloading,
                    if (percentage.isNotEmpty()) // percentage > 0
                        getString(R.string.download_button_downloading_with_percentage_text, percentage)
                    else
                        getString(R.string.download_button_downloading_text),
                    status
                )
            }
            VideoTaskState.PAUSE -> {
                updateDownloadButton(R.drawable.ic_download, getString(R.string.download_button_pause_text), status)
            }
            VideoTaskState.ERROR -> {
                updateDownloadButton(R.drawable.ic_download_error, getString(R.string.download_button_error_text), status)
            }
            VideoTaskState.SUCCESS -> {
                updateDownloadButton(R.drawable.ic_downloaded, getString(R.string.download_button_downloaded_text), status)
            }
            else -> {
                updateDownloadButton(R.drawable.ic_download, getString(R.string.download_button_download_text), status)
            }
        }
    }

    @UiThread
    private fun updateDownloadButton(@DrawableRes icon: Int? = null, text: String? = null, status: Int) {
        _binding?.apply {
            if(icon == null || text == null) {
                llDownloadTablet.hide()
                return
            }
            if (!isAirlineLayout()) {
                if (status == VideoTaskState.DOWNLOADING || status == VideoTaskState.PREPARE || status == VideoTaskState.START || status == VideoTaskState.PENDING) {
                    animTabletDownloading.show()
                    ivDownloadTablet.hide()
                    if (!animTabletDownloading.isAnimating) {
                        animTabletDownloading.playAnimation()
                    }
                } else {
                    animTabletDownloading.hide()
                    ivDownloadTablet.show()
                    animTabletDownloading.cancelAnimation()
                }
            }
            ivDownloadTablet.load(icon)
            tvDownloadTablet.text = text
            llDownloadTablet.tag = status
            llDownloadTablet.show()
        }
    }

    //region Handle -> Player Layout
    private fun adjustPlayerConstraint(isScale: Boolean) {
        Timber.tag("tamlog-emoji").d("adjustPlayerConstraint $isScale")

        if (_binding == null) return
        if (isScale) {
            val condition = if (context.isTablet()) {
                viewModel.isFullScreen.value?.first == true && viewModel.isFullScreen.value?.second == true
            } else {
                MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
            }
            if (condition) {
                ConstraintSet().apply {
                    // Set constraint for player
                    clone(binding.clRoot)
                    connect(R.id.f_player, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
                    connect(R.id.f_player, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                }.applyTo(binding.clRoot)
            }
        } else {
            ConstraintSet().apply {
                // Set constraint for player
                clone(binding.clRoot)
                connect(R.id.f_player, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
                connect(R.id.f_player, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
            }.applyTo(binding.clRoot)
        }
    }

    //endregion
    // region Game Emoji
    private fun updateRankingButton(data: GameVOD.GameVODData? = null) {
        //https://fptplay.vn/doandaidi/bang-xep-hang?idEvent=************************&fptplayId=97140135
//        val data = GameVOD.GameVODData(isLeaderBoard = true, leaderBoardLink = "https://fptplay.vn/doandaidi/bang-xep-hang")
        if (data != null && data.isLeaderBoard && data.leaderBoardLink.isNotBlank()) {

            binding.llRankGameTablet.show()

            binding.llRankGameTablet.onClickDelay {
                viewModel.dispatchIntent(
                    VodDetailViewModel.VodDetailIntent.TriggerShowRankGame(
                        url = data.leaderBoardLink.toString()
                    )
                )
            }
        } else {
            binding.llRankGameTablet.hide()
            binding.llRankGameTablet.onClickDelay { /* do nothing */ }
        }
    }
    private fun startGameEmoji(url:String, jsonData: String) {
        inflateGameEmojiView()
        gameEmojiStubBinding?.apply {
            gameEmojiPopupView.showWithAnimation()
            gameEmojiPopupView.start(url = url, jsonData = jsonData, userId = sharedPreferences.userId(), userPhone = sharedPreferences.userPhone(), accessToken = sharedPreferences.accessToken())
        }
    }

    private fun showRankGameEmoji(url: String) {
        inflateGameEmojiView()
        gameEmojiStubBinding?.apply {
            gameEmojiPopupView.showWithAnimation()
            gameEmojiPopupView.showRank(url = url)
        }
    }

    private fun constraintEmojiPopup(isLandscape: Boolean) {
        Timber.tag("tamlog-emoji").d("constraintEmojiPopup")
        if (binding.vsGameEmojiPopupView.parent != null) {
            Timber.tag("tamlog-emoji").e("constraintEmojiPopup not inflate")

            binding.vsGameEmojiPopupView.apply {
                val lp = ConstraintLayout.LayoutParams(
                    0,
                    0
                )
                if (isLandscape) {
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.matchConstraintPercentWidth = 0.35f

                } else {
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToBottom = R.id.f_player
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID

                }
                layoutParams = lp
            }
        } else {
            Timber.tag("tamlog-emoji").i("constraintEmojiPopup inflate")

            gameEmojiStubBinding?.gameEmojiPopupView?.apply {
                val lp = ConstraintLayout.LayoutParams(
                    0,
                    0
                )
                if (isLandscape) {
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.matchConstraintPercentWidth = 0.35f

                } else {
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToBottom = R.id.f_player
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID

                }
                layoutParams = lp
            }
        }
    }
    private fun constraintEmojiPopupTablet(isFullscreen: Boolean, isLandscape: Boolean) {
        Timber.tag("tamlog-emoji").d("constraintEmojiPopupTablet")
        if (binding.vsGameEmojiPopupView.parent != null) {
            Timber.tag("tamlog-emoji").e("constraintEmojiPopupTablet not inflate")

            binding.vsGameEmojiPopupView.apply {
                val lp = ConstraintLayout.LayoutParams(
                    0,
                    0
                )
                if (isLandscape) {
                    if(isFullscreen) {
                        lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                        lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                        lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                        lp.matchConstraintPercentWidth = 0.35f
                    } else {
                        lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                        lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                        lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                        lp.startToEnd = R.id.f_player
                    }

                } else {
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToBottom = R.id.f_player
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID

                }
                layoutParams = lp
            }
        } else {
            Timber.tag("tamlog-emoji").i("constraintEmojiPopupTablet inflate")

            gameEmojiStubBinding?.gameEmojiPopupView?.apply {
                val lp = ConstraintLayout.LayoutParams(
                    0,
                    0
                )
                if (isLandscape) {
                    if(isFullscreen) {
                        lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                        lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                        lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                        lp.matchConstraintPercentWidth = 0.35f
                    } else {
                        lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                        lp.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                        lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                        lp.startToEnd = R.id.f_player
                    }

                } else {
                    lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    lp.topToBottom = R.id.f_player
                    lp.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID

                }
                layoutParams = lp
            }
        }
    }

    private fun inflateGameEmojiView() {
        if (binding.vsGameEmojiPopupView.parent != null) {
            gameEmojiStubBinding = GameEmojiLayoutBinding.bind(binding.vsGameEmojiPopupView.inflate())
            gameEmojiStubBinding?.apply {
                gameEmojiPopupView.gameEmojiListener = object : GameEmojiView.GameEmojiListener {
                    override fun onClose(reason: String?) {
                        viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.TriggerPlayerLayout(isScale = false))
                        viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.SetPlayerControlVisibility(isVisible = true))
                        toggleEnableOnGameEmoji(enabled = true)
                    }

                    override fun onShowWarningDialog(message: String?) {}

                    @UiThread
                    override fun onShow() {
                        viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.TriggerPlayerLayout(isScale = viewModel.isPlayerScalableOnGame()))
                        viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.SetPlayerControlVisibility(isVisible = false))
                        toggleEnableOnGameEmoji(enabled = false)
                    }
                }
                gameEmojiPopupView.lifecycleOwner = this@VodDetailFragment
            }
        }
    }

    private fun hideGameEmojiView(destroy: Boolean = false) {
        gameEmojiStubBinding?.gameEmojiPopupView?.close(destroy = destroy)
    }

    private fun toggleEnableOnGameEmoji(enabled: Boolean) {
        binding.flVodInfoOverlay?.apply {
            if(enabled) {
                hide()
            } else {
                show()

            }
        }

    }

    // endregion Game Emoji


   private fun View.toggleEnableView(enabled: Boolean) {
        isEnabled = enabled
        if(this is ViewGroup) {
            for (i in 0 until childCount) {
                getChildAt(i).toggleEnableView(enabled)
            }
        }
    }

    private fun findEpisodeIndex(episode: Details.Episode?) : Int? {
        if(episode == null) return null
        val listEpisode = viewModel.getDataDetail()?.blockEpisode?.episodes ?: listOf()
        val episode = listEpisode.firstOrNull { it.id == episode.id }
        episode?.let {
            return it.id.toIntOrNull()
        }
        return null
    }

    private fun handleOnChooseDownload(status: Any, episode: Details.Episode, episodeIndex: Int) {
        // check episode allow download
        Timber.tag("tamlog").d("---handleOnChooseDownload $status - ${episode.titleVietnam}")

        // check current download stage
        when (status) {
            VideoTaskState.PREPARE,
            VideoTaskState.PENDING,
            VideoTaskState.START,
            VideoTaskState.DOWNLOADING -> {
                /**
                 * If Download Stage = PROCESSING
                 * cancel Download and update button download
                 */
//                if(isAirlineLayout()){
//                    viewModel.dispatchIntent(
//                        VodDetailViewModel.VodDetailIntent.CancelDownloadByChapterId(
//                            "${viewModel.getId()}-${episode.id}"
//                        )
//                    )
//                } else {
                if (isAirlineLayout()){
                    viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_DOWNLOADING)
                } else {
                    viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_DOWNLOADING_FOR_VOD)
                }
                viewModel.dispatchIntent(
                    VodDetailViewModel.VodDetailIntent.DownloadByChapterId(
                        "${viewModel.getId()}-${episode.id}"
                    )
                )
//                }
            }
            VideoTaskState.PAUSE -> {
                if (isAirlineLayout()){
                    viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_DOWNLOAD_PAUSE)
                } else {
                    viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_DOWNLOAD_PAUSE_FOR_VOD)
                }
                viewModel.dispatchIntent(
                    VodDetailViewModel.VodDetailIntent.DownloadByChapterId(
                        "${viewModel.getId()}-${episode.id}"
                    )
                )
            }
            VideoTaskState.ERROR -> {
                if (isAirlineLayout()){
                    viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_ERROR)
                } else {
                    viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_ERROR_FOR_VOD)
                }
                viewModel.dispatchIntent(
                    VodDetailViewModel.VodDetailIntent.DownloadByChapterId(
                        "${viewModel.getId()}-${episode.id}"
                    )
                )
            }
            VideoTaskState.SUCCESS -> {
                if (isAirlineLayout()){
                    viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_DELETE_FROM_DOWNLOAD)
                } else {
                    viewModel.savePlayerOptionsType(type = Utils.OPTION_DIALOG_DOWNLOADED)
                }
                viewModel.dispatchIntent(
                    VodDetailViewModel.VodDetailIntent.DownloadByChapterId(
                        "${viewModel.getId()}-${episode.id}"
                    )
                )
            }
            else -> {
                if (isAirlineLayout()){
                    if (Utils.useMobileData(activity)) {
                        showWarning3GDownloadingDialog {
                            checkLicensedStorageForDownload(
                                episode,
                                episodeIndex
                            )
                        }
                    } else {
                        checkLicensedStorageForDownload(
                            episode,
                            episodeIndex
                        )
                    }
                } else {
                    isUsageOptionInternal = sharedPreferences.storageUsageForDowload()
                    val alwaysDownloadByWifi = sharedPreferences.alwaysDownloadByWifi()
                    if (alwaysDownloadByWifi && Utils.useMobileData(activity)){
                        showWarningAlwaysUseWifiDialog()
                    } else {
                        checkLicensedStorageForDownload(
                            episode,
                            episodeIndex
                        )
                    }
                }
            }
        }
    }

    private fun checkLicensedStorageForDownload(episode: Details.Episode? = null, episodeIndex: Int = -1) {
        if (isAirlineLayout()){
            checkLicensedStorageForAirLine(episode, episodeIndex)
        } else {
            checkLicensedStorage(episode, episodeIndex)
        }
    }

    // region check permission
    private fun checkLicensedStorageForAirLine(episode: Details.Episode? = null, episodeIndex: Int = -1) {
        val isEnoughSpaceInternal = true
        val isEnoughSpaceExternal = true
        when {
            isEnoughSpaceExternal && isEnoughSpaceInternal -> {
                episode?.let {
                    downloadEpisode = it
                }
                if (episodeIndex > -1) {
                    downloadEpisodeIdx = episodeIndex
                }
                showChooseStorageDialog()
            }
            isEnoughSpaceExternal -> {
                if(VideoStorageUtils.checkSDCardAvailable(requireContext())) {
                    isUsageOptionInternal = Utils.USE_OPTION_EXTERNAL_STORAGE
                    getStreamForDownload(
                        vod = viewModel.getDataDetail(),
                        episode = episode,
                        episodeIndex = episodeIndex,
                        useExternalStorage = true
                    )
                }
            }
            isEnoughSpaceInternal -> {
                isUsageOptionInternal = Utils.USE_OPTION_INTERNAL_STORAGE
                getStreamForDownload(
                    vod = viewModel.getDataDetail(),
                    episode = episode,
                    episodeIndex = episodeIndex,
                    useExternalStorage = false
                )
            }
            else -> {
                showWarningFullStorageDialog({
                    if (episode != null) {
                        VideoDownloadManager.instance.deleteVideoTask(episode.id,true)
                    }
                })
            }
        }
    }

    private fun checkLicensedStorage(episode: Details.Episode? = null, episodeIndex: Int = -1) {
        when(isUsageOptionInternal) {
            Utils.USE_OPTION_INTERNAL_STORAGE -> {
                setStorageUsageOption(Utils.USE_OPTION_INTERNAL_STORAGE)
                getStreamForDownload(
                    vod = viewModel.getDataDetail(),
                    episode = episode,
                    episodeIndex = episodeIndex,
                    useExternalStorage = false
                )
            }
            else -> {
                if(VideoStorageUtils.checkSDCardAvailable(requireContext())) {
                    setStorageUsageOption(Utils.USE_OPTION_EXTERNAL_STORAGE)
                    getStreamForDownload(
                        vod = viewModel.getDataDetail(),
                        episode = episode,
                        episodeIndex = episodeIndex,
                        useExternalStorage = true
                    )
                } else {
                    setStorageUsageOption(Utils.USE_OPTION_INTERNAL_STORAGE)
                    getStreamForDownload(
                        vod = viewModel.getDataDetail(),
                        episode = episode,
                        episodeIndex = episodeIndex,
                        useExternalStorage = false
                    )
                }
            }
        }
    }

//    private fun getStreamForDownload(vod: Details?, episode: Details.Episode?, episodeIndex: Int, useExternalStorage: Boolean = false) {
//        if (vod == null || episode == null) return
//        val listQualityDownload: MutableList<String> = ArrayList()
//        for (i in 0 until episode.bitrates.size) {
//            if (episode.bitrates[i].name != "Auto") {
//                listQualityDownload.add(episode.bitrates[i].name)
//            }
//        }
//        viewModel.saveListQualityDownload(listQualityDownload.toMutableList())
//        var downloadId = ""
//        if (!MainApplication.INSTANCE.sharedPreferences.d2gProfile().toString().isNullOrEmpty()) {
//            if (episode.bitrates.toString().contains(MainApplication.INSTANCE.sharedPreferences.d2gProfile().toString())) {
//                downloadId = MainApplication.INSTANCE.sharedPreferences.d2gProfile().toString()
//                startDownloadStream(vod, episode, episodeIndex, useExternalStorage, downloadId)
//            } else {
//                findNavController().navigate(VodDetailFragmentDirections.actionVodDetailFragmentToDownloadQualityDialogFragmentV2(screen = Constants.SCREEN_VOD_TO_POP_UP_QUALITY))
//                setFragmentResultListener(Constants.SELECT_QUALITY_DOWNLOAD) { _, bundle ->
//                    bundle.getString(Constants.SELECT_QUALITY_DOWNLOAD_KEY)?.let {
//                        downloadId = it
//                        startDownloadStream(vod, episode, episodeIndex, useExternalStorage, it)
//                    }
//                }
//            }
//        } else {
//            findNavController().navigate(VodDetailFragmentDirections.actionVodDetailFragmentToDownloadQualityDialogFragmentV2(screen = Constants.SCREEN_VOD_TO_POP_UP_QUALITY))
//            setFragmentResultListener(Constants.SELECT_QUALITY_DOWNLOAD) { _, bundle ->
//                bundle.getString(Constants.SELECT_QUALITY_DOWNLOAD_KEY)?.let {
//                    downloadId = it
//                    startDownloadStream(vod, episode, episodeIndex, useExternalStorage, it)
//                }
//            }
//        }
//    }

    private fun getStreamForDownload(vod: Details?, episode: Details.Episode?, episodeIndex: Int, useExternalStorage: Boolean = false) {
        if (vod == null || episode == null) return
        if(!NetworkUtils.isNetworkAvailable()) {
            showWarningDialog(<EMAIL>(R.string.error_no_intent))
            return
        }
        Timber.tag("tamlog").i("getStreamForDownload ${vod.blockContent.id}")
        viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.CheckForDownload(
            details = vod,
            episode = episode,
            episodeIndex = episodeIndex,
            useExternalStorage = useExternalStorage))
    }

    private fun setStorageUsageOption(option: String) {
        sharedPreferences.setStorageUsageForDowload(option)
        VideoDownloadManager.instance.setCacheRoot(MainApplication.INSTANCE.setStorageUsage(option).absolutePath)
    }

    private fun showChooseWacthOrDownloadDialog() {
        findNavController().navigate(
            VodDetailFragmentDirections.actionVodDetailFragmentToDownloadOptionBottomSheetDialogFragment(
                type = Utils.DOWNLOAD_OPTION_DIALOG_DOWNLOAD_TYPE,
                title = getString(R.string.download_choose_dialog_title),
                optionOneText = getString(R.string.download_choose_dialog_option_play),
                optionOneValue = Utils.DOWNLOAD_OPTION_DIALOG_OPTION_WATCH_NOW_ID,
                optionTwoText = getString(R.string.download_choose_dialog_option_down),
                optionTwoValue = Utils.DOWNLOAD_OPTION_DIALOG_OPTION_DOWNLOAD_ID,
            )
        )
    }

    private fun showFinishDownloadDialog(chapterIdx: Int) {
        findNavController().navigate(
            VodDetailFragmentDirections.actionVodDetailFragmentToDownloadOptionBottomSheetDialogFragment(
                type = Utils.DOWNLOAD_OPTION_DIALOG_DELETE_TYPE,
                title = <EMAIL>(R.string.download_warning_dialog_cannot_play_after_offline),
                optionOneText = <EMAIL>(R.string.delete),
                optionOneValue = viewModel.getId() + "-" + chapterIdx,
                optionTwoText = <EMAIL>(R.string.warning_dialog_button_negative_text),
                optionTwoValue = "",
            )
        )
    }

    private fun showWarning3GDownloadingDialog(onConfirm: () -> Unit) {
        AlertDialog().apply {
            setMessage(<EMAIL>(R.string.download_warning_dialog_mobile_data_title))
            setTextConfirm(<EMAIL>(R.string.download_warning_dialog_confirm_button))
            setTextExit(<EMAIL>(R.string.download_warning_dialog_cancel_button))
            setOnlyConfirmButton(false)
            setListener(object : AlertDialogListener {
                override fun onConfirm() {
                    onConfirm()
                }
            })
        }.show(childFragmentManager, "DownloadWarning")
    }

    private fun showWarningAlwaysUseWifiDialog() {
        AlertDialog().apply {
            setShowTitle(true)
            setTextTitle("Thông báo")
            setMessage(<EMAIL>(R.string.download_warning_dialog_always_use_wifi))
            setTextConfirm(<EMAIL>(R.string.download_warning_dialog_Setting_button))
            setTextExit(<EMAIL>(R.string.download_warning_dialog_Back_button))
            setListener(object : AlertDialogListener {
                override fun onConfirm() {
                    when (context.isTablet()) {
                        true -> findNavController().navigate(VodDetailFragmentDirections.actionGlobalFragmentToNavSettingDownloadV2Tablet())
                        false -> findNavController().navigate(VodDetailFragmentDirections.actionGlobalFragmentToNavSettingDownloadV2())
                    }
                }
            })
        }.show(childFragmentManager, "DownloadWarning")
    }

    private fun showWarningStoragePermissionDenied(onConfirm: (() -> Unit)) {
        AlertDialog().apply {
            setMessage(<EMAIL>(R.string.download_warning_dialog_no_permission_read_write_storage))
            setTextConfirm(<EMAIL>(R.string.download_warning_dialog_confirm_button))
            setTextExit(<EMAIL>(R.string.download_warning_dialog_cancel_button))
            setOnlyConfirmButton(false)
            setListener(object : AlertDialogListener {
                override fun onConfirm() {
                    onConfirm()
                }
            })
        }.show(childFragmentManager, "DownloadWarning")
    }

    private fun showChooseStorageDialog() {
        findNavController().navigateSafe(VodDetailFragmentDirections.actionVodDetailFragmentToDownloadOptionBottomSheetDialogFragment(
            type = Utils.DOWNLOAD_OPTION_DIALOG_STORAGE_TYPE,
            title = <EMAIL>(R.string.download_choose_storage_dialog_title),
            optionOneText = <EMAIL>(
                R.string.download_choose_storage_dialog_option_internal,
                Utils.convertByteToGigabyte(VideoStorageUtils.getInternalAvailableSpaceInBytes(requireContext()))
            ),
            optionOneValue = Utils.DOWNLOAD_OPTION_DIALOG_OPTION_INTERNAL_STORAGE_ID,
            optionTwoText = <EMAIL>(
                R.string.download_choose_storage_dialog_option_external,
                Utils.convertByteToGigabyte(VideoStorageUtils.getExternalAvailableSpaceInBytes(requireContext()))
            ),
            optionTwoValue = Utils.DOWNLOAD_OPTION_DIALOG_OPTION_EXTERNAL_STORAGE_ID,
        ))
    }

    private fun showWarningFullStorageDialog(onConfirm: (() -> Unit)) {
        val alertTag = "DownloadStorageWarning"
        val alertDialog = childFragmentManager.findFragmentByTag(alertTag)
        if (alertDialog == null) {
            AlertDialog().apply {
                setMessage(<EMAIL>(R.string.download_warning_dialog_full_storage_title))
                setTextConfirm(<EMAIL>(R.string.download_warning_dialog_confirm_button))
                setOnlyConfirmButton(true)
                setListener(object :  AlertDialogListener {
                    override fun onConfirm() {
                        onConfirm()
                    }
                })
            }.show(childFragmentManager, alertTag)
        }
    }

    private fun showWarningAlreadyDownloadedDialog() {
        AlertDialog().apply {
            setMessage(<EMAIL>(R.string.download_warning_dialog_movie_already_downloaded))
            setTextConfirm(<EMAIL>(R.string.download_warning_dialog_confirm_button))
            setOnlyConfirmButton(true)
        }.show(childFragmentManager, "DownloadWarning")
    }
    private fun reloadDataRating(){
        Logger.d("trangtest get rating === reload in voddetail")
        viewModel.getDataDetail()?.let {
            viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.GetRating(itemId = it.blockContent.id, refId = it.blockContent.refId))
        }
    }

    private fun bindEventFragmentResult() {
        run {
            setFragmentResultListener(Utils.RATING_RESULT_EVENT){ _, bundle ->
                when(bundle.getString(Utils.RATING_EVENT, "")){
                    Utils.RATING_NEED_RELOAD->{
                        //reloadDataRating()
                        loadDataRating()
                    }
                    Utils.RATING_EXIT->{}
                    else ->{}
                }
            }
            setFragmentResultListener(Utils.DOWNLOAD_OPTION_DIALOG_DOWNLOAD_TYPE) { _, bundle ->
                val message =
                    when (bundle.getString(Utils.DOWNLOAD_OPTION_DIALOG_CHOSEN_OPTION_ID_KEY, "")) {
                        Utils.DOWNLOAD_OPTION_DIALOG_OPTION_WATCH_NOW_ID -> {
                            // Watch VOD
                            "Choose Watch now"
                        }
                        Utils.DOWNLOAD_OPTION_DIALOG_OPTION_DOWNLOAD_ID -> {
                            // Download VOD
                            binding.llDownloadTablet.performClick()
                            "Choose Download"
                        }
                        else -> {
                            "Choose something else"
                        }
                    }
                Timber.d(message)
            }

            setFragmentResultListener(Utils.DOWNLOAD_OPTION_DIALOG_STORAGE_TYPE) { _, bundle ->
                val message: String
                val useExternalStorage =
                    when (bundle.getString(Utils.DOWNLOAD_OPTION_DIALOG_CHOSEN_OPTION_ID_KEY, "")) {
                        Utils.DOWNLOAD_OPTION_DIALOG_OPTION_EXTERNAL_STORAGE_ID -> {
                            isUsageOptionInternal = Utils.USE_OPTION_EXTERNAL_STORAGE
                            message = "Choose External Storage"
                            true
                        }
                        Utils.DOWNLOAD_OPTION_DIALOG_OPTION_INTERNAL_STORAGE_ID -> {
                            isUsageOptionInternal = Utils.USE_OPTION_INTERNAL_STORAGE
                            message = "Choose Internal Storage"
                            false
                        }
                        else -> {
                            isUsageOptionInternal = Utils.USE_OPTION_INTERNAL_STORAGE
                            message = "Choose something else"
                            false
                        }
                    }

                findEpisodeIndex(downloadEpisode)?.let {
                    var file = MainApplication.INSTANCE.setStorageUsage(isUsageOptionInternal)
                    VideoDownloadManager.instance.setCacheRoot(file.absolutePath)
                    getStreamForDownload(
                        vod = viewModel.getDataDetail(),
                        episode = downloadEpisode,
                        episodeIndex = it,
                        useExternalStorage = useExternalStorage
                    )
                }

                Timber.d(message)
            }

            setFragmentResultListener(Utils.DOWNLOAD_OPTION_DIALOG_DELETE_TYPE) { _ , bundle ->
                val chapterId = bundle.getString(Utils.DOWNLOAD_OPTION_DIALOG_CHOSEN_OPTION_ID_KEY, "")
                if (chapterId.isNotEmpty()) {
                    VideoDownloadManager.instance.deleteVideoTask(chapterId, true)
                }
            }

        }
    }

    private fun showDownloadLoading() {
        binding.pbLoading.root.show()

    }
    private fun hideDownloadLoading(){
        binding.pbLoading.root.hide()
    }

    //region Download Video
    private val mDownloadListener: DownloadListener = object : DownloadListener() {
        private var mLastProgressTimeStamp: Long = 0

        override fun onDownloadDefault(oldState: Int, item: VideoTaskItem) {
            if (item.movieId != viewModel.getId()) return
            if(!MultiProfileUtils.profileEnabledDownload(sharedPreferences)) return
            Timber.d("onDownloadDefault: $item")
            runOnUiThread {
                hideDownloadLoading()
                hideLoading()
                viewModel.triggerUpdateDownloadState(
                    chapterIdx = item.chapterIdx.toString(),
                    state = item.taskState)
                viewModel.currentEpisode()?.let {
                    findEpisodeIndex(it)?.let { index ->
                        if(item.chapterIdx == index) {
                            updateDownloadButtonStatus(item.taskState)
                        }
                    }
                }
            }
        }

        override fun onDownloadPending(item: VideoTaskItem) {
            if (item.movieId != viewModel.getId()) return
            if(!MultiProfileUtils.profileEnabledDownload(sharedPreferences)) return
            Timber.d("onDownloadPending: $item")
            viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.NotifyAddDownloadItem(item))
            runOnUiThread {
                viewModel.triggerUpdateDownloadState(
                    chapterIdx = item.chapterIdx.toString(),
                    state = item.taskState)
                viewModel.currentEpisode()?.let {
                    findEpisodeIndex(it)?.let { index ->
                        if(item.chapterIdx == index) {
                            updateDownloadButtonStatus(item.taskState)
                        }
                    }
                }
            }
        }

        override fun onDownloadPrepare(item: VideoTaskItem) {
            if (item.movieId != viewModel.getId()) return
            if(!MultiProfileUtils.profileEnabledDownload(sharedPreferences)) return
            Timber.d("onDownloadPrepare: $item")
            runOnUiThread {
                viewModel.triggerUpdateDownloadState(
                    chapterIdx = item.chapterIdx.toString(),
                    state = item.taskState)
                viewModel.currentEpisode()?.let {
                    findEpisodeIndex(it)?.let { index ->
                        if(item.chapterIdx == index) {
                            updateDownloadButtonStatus(item.taskState)
                        }
                    }
                }
            }
        }

        override fun onDownloadStart(item: VideoTaskItem) {
            if (item.movieId != viewModel.getId()) return
            if(!MultiProfileUtils.profileEnabledDownload(sharedPreferences)) return
            Timber.d("onDownloadStart: $item")
            runOnUiThread {
                viewModel.triggerUpdateDownloadState(
                    chapterIdx = item.chapterIdx.toString(),
                    state = item.taskState)
                viewModel.currentEpisode()?.let {
                    findEpisodeIndex(it)?.let { index ->
                        if(item.chapterIdx == index) {
                            updateDownloadButtonStatus(item.taskState)
                        }
                    }
                }
            }
        }

        override fun onDownloadProgress(item: VideoTaskItem) {
            if (item.movieId != viewModel.getId()) return
            if(!MultiProfileUtils.profileEnabledDownload(sharedPreferences)) return
            val currentTimeStamp = System.currentTimeMillis()
            if (currentTimeStamp - mLastProgressTimeStamp > 1000) {
                runOnUiThread {
                    viewModel.triggerUpdateDownloadProcess(
                        chapterIdx = item.chapterIdx.toString(),
                        progress = item.percentString,
                        size = item.downloadSizeString,
                        state = item.taskState)
                    viewModel.currentEpisode()?.let {
                        findEpisodeIndex(it)?.let { index ->
                            if(item.chapterIdx == index) {
                                updateDownloadButtonStatus(item.taskState,item.percentString)
                            }
                        }
                    }
                }
                mLastProgressTimeStamp = currentTimeStamp
            }
        }

        override fun onDownloadPause(item: VideoTaskItem) {
            if (item.movieId != viewModel.getId()) return
            if(!MultiProfileUtils.profileEnabledDownload(sharedPreferences)) return
            Timber.d("onDownloadPause: %s", item.url)
            runOnUiThread {
                viewModel.triggerUpdateDownloadState(
                    chapterIdx = item.chapterIdx.toString(),
                    state = item.taskState)
                viewModel.currentEpisode()?.let {
                    findEpisodeIndex(it)?.let { index ->
                        if(item.chapterIdx == index) {
                            updateDownloadButtonStatus(item.taskState)
                        }
                    }
                }
            }
        }

        override fun onDownloadError(item: VideoTaskItem) {
            if (item.movieId != viewModel.getId()) return
            if(!MultiProfileUtils.profileEnabledDownload(sharedPreferences)) return
            Timber.d("onDownloadError: %s", item.url)
            if (item.hasLinkRefetch) {
                viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.FetchDownloadLink(item))
            } else {
                runOnUiThread {
                    viewModel.triggerUpdateDownloadState(
                        chapterIdx = item.chapterIdx.toString(),
                        state = item.taskState)
                    viewModel.currentEpisode()?.let {
                        findEpisodeIndex(it)?.let { index ->
                            if(item.chapterIdx == index) {
                                updateDownloadButtonStatus(item.taskState)
                            }
                        }
                    }
                }            }
        }

        override fun onDownloadSuccess(item: VideoTaskItem) {
            if (item.movieId != viewModel.getId()) return
            if(!MultiProfileUtils.profileEnabledDownload(sharedPreferences)) return
            Timber.d("onDownloadSuccess: $item")
            runOnUiThread {
                viewModel.triggerUpdateDownloadState(
                    chapterIdx = item.chapterIdx.toString(),
                    state = item.taskState)
                viewModel.currentEpisode()?.let {
                    findEpisodeIndex(it)?.let { index ->
                        if(item.chapterIdx == index) {
                            updateDownloadButtonStatus(item.taskState)
                        }
                    }
                }
            }
        }

        override fun onDownloadable(item: VideoTaskItem, estimateSizeToBytes: Long) : Boolean {
            val availableStorageToBytes : Long
            when(isUsageOptionInternal) {
                Utils.USE_OPTION_INTERNAL_STORAGE -> {
                    availableStorageToBytes = VideoStorageUtils.getInternalAvailableSpaceInBytes(requireContext())
                }
                else -> {
                    if (VideoStorageUtils.checkSDCardAvailable(requireContext())) {
                        availableStorageToBytes =
                            VideoStorageUtils.getExternalAvailableSpaceInBytes(requireContext())
                    } else {
                        availableStorageToBytes = VideoStorageUtils.getInternalAvailableSpaceInBytes(requireContext())
                        sharedPreferences.setStorageUsageForDowload(Utils.USE_OPTION_INTERNAL_STORAGE)
                    }
                }
            }
            val minimumSizeToBytes = if (item.estimateSizeToBytes > 0) {
                item.estimateSizeToBytes + VideoStorageUtils.EXTENDED_SIZE_TO_BYTES
            } else {
                estimateSizeToBytes + VideoStorageUtils.EXTENDED_SIZE_TO_BYTES
            }
            if (minimumSizeToBytes < availableStorageToBytes) {
                return true
            } else {
                if(MultiProfileUtils.profileEnabledDownload(sharedPreferences)) {
                    showWarningFullStorageDialog {}
                }
                return false
            }
        }
    }



    //region Preview
    private fun showPreviewTimeUp() {
        try {
            binding.root.hideKeyboard()
        } catch (ex: Exception) {
            ex.printStackTrace()
        }

        previewTimeUpDialog?.dismissAllowingStateLoss()
        val message = BillingUtils.getPreviewTimeUpDescription(binding.root.context, viewModel.getDataDetail()?.blockContent?.isTvod ?: false)
        previewTimeUpDialog = AlertDialog().apply {
            setShowTitle(true)
            setTextTitle(BillingUtils.getPreviewTimeUpTitle(binding.root.context, viewModel.getDataDetail()?.blockContent?.isTvod ?: false))
            setMessage(message)
            setTextConfirm(BillingUtils.getTextBuyPackage(binding.root.context, viewModel.getDataDetail()?.blockContent?.isTvod ?: false))
            setTextExit(<EMAIL>(R.string.close))
            setOnlyConfirmButton(false)
            setListener(object : AlertDialogListener {
                override fun onExit() {
                    viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.TriggerPreviewTimeUpPopupClose)
                }
                override fun onConfirm() {
                    handleClickBuyPackageForPreview()
                    viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.TriggerPreviewTimeUpPopupClose)
                }
            })
            isCancelable = false
        }
        previewTimeUpDialog?.show(childFragmentManager, "PreviewTimeUp")
        sendTrackingShowPopup(message)
    }

    private fun handleClickBuyPackageForPreview() {
        //
        viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.TriggerPausePreview)
        viewModel.dispatchIntent(VodDetailViewModel.VodDetailIntent.TriggerSaveLocalPreviewHistory)
        //
        TrackingUtil.savePreviewProcessItem(vodId = viewModel.getId(), episodeId = viewModel.currentEpisode()?.id ?: "")
        //
        viewModel.getDataDetail()?.let {
            if (binding.navHostFragment.findNavController().currentDestination?.id != R.id.vod_child_main_fragment) {
//                binding.navHostFragment.findNavController().navigateUp()
                binding.navHostFragment.findNavController().popBackStack(R.id.vod_child_main_fragment, false)
            }

            if (it.blockContent.isTvod) { // TVOD
                val packagePlan = viewModel.getVipRequired()?.second?.requireVipPlan ?: ""
                paymentViewModel.dispatchIntent(
                    PaymentViewModel.PaymentViewIntent.GetPackagePlan(
                        packageType =  packagePlan,
                        isEnableGooglePay = BillingUtils.isEnableGoogleBilling(),
                        fromSource = PaymentViewModel.FromSource.PLAY.rawValue,
                        actionFromPreview = true
                    )
                )
            } else { // SVOD

                val packagePlan = viewModel.getLoginRequired()?.second?.requiredVipPlan
                    ?: viewModel.getVipRequired()?.second?.requireVipPlan ?: ""
                val packageName = viewModel.getVipRequired()?.second?.requireVipName ?: ""

                AdjustAllEvent.sendPackageRecommendClickEvent(packageId = packagePlan, packageName = packageName)


                parentFragment?.findNavController()?.navigate(
                    NavHomeMainDirections.actionGlobalToPayment(
                        idToPlay = viewModel.getId(),
                        extraId = viewModel.currentEpisode()?.id ?: "",
                        bitrateId = viewModel.currentEpisode()?.autoProfile ?:"",
                        type = "vod",
                        viewType = BillingUtils.PACKAGE_VIEW_TYPE_PREVIEW,
                        packageType = packagePlan,
                        popupToId = R.id.nav_vod
                    )
                )
            }
        }
    }
    //endregion

    private fun sendTrackingShowPopup(message: String = "") {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "191",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "Popup",
                event = "ShowPopup",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                itemId = viewModel.getId(),
                itemName = message
            )
        )
    }
    private fun showBuyPackageGuide(buyPackageGuide: BuyPackageGuide) {
        binding.layoutBuyPackageDetail.apply {

            tvBuyPackage.text = buyPackageGuide.button
            ctlBuyPackageButton.setBackgroundColor(Color.TRANSPARENT)

            tvBuyPackageDescription.text = buyPackageGuide.description
            tvBuyPackageDescription.setTextColor(requireContext().getColor(R.color.buy_package_guide_description_color))

            ivLogo.show()
            val placeholderId = R.drawable.image_placeholder_guide
            if (buyPackageGuide.imageUrl.isNotBlank()) {
                ImageProxy.load(
                    context = binding.root.context,
                    url = buyPackageGuide.imageUrl,
                    width = binding.context.resources.getDimensionPixelSize(R.dimen.buy_package_guide_logo_width),
                    height = binding.context.resources.getDimensionPixelSize(R.dimen.buy_package_guide_logo_height),
                    target = ivLogo,
                    placeHolderId = placeholderId,
                    errorDrawableId = placeholderId
                )
            } else {
                ImageProxy.loadLocal(
                    context = binding.root.context,
                    data = placeholderId,
                    width = binding.context.resources.getDimensionPixelSize(R.dimen.buy_package_guide_logo_width),
                    height = binding.context.resources.getDimensionPixelSize(R.dimen.buy_package_guide_logo_height),
                    target = ivLogo,
                    placeHolderId = placeholderId,
                    errorDrawableId = placeholderId,
                )
            }

            root.setGradientDrawableFromHexColors(
                hexColors = buyPackageGuide.gradientHexColors,
                defaultHexColor = BuyPackageGuide.DEFAULT_COLOR
            )
            root.show()
        }
    }

    private fun updateNormalComment(status: VodCommentInfo.Status, message: String = "", forceUpdate: Boolean = false) {
        if (status == VodCommentInfo.Status.Success) {
            viewModelCommentV2.dispatchIntent(
                CommentV2ViewModel.CommentIntent.UpdateNormalCommentData(
                    isSuccess = true,
                    vodId = viewModel.getId(),
                    VodCommentInfo(
                        userAvatar = sharedPreferences.profileAvatar(),
                        commentType = VodCommentInfo.VodCommentType.VOD_COMMENT_TYPE_NORMAL,
                        commentData = VodCommentInfo.VodCommentData(
                            commentMetaData = viewModelCommentV2.getMeta(),
                            commentPageData = viewModelCommentV2.getDataCom(),
                            statusInfo = VodCommentInfo.CommentStatus(
                                message = message,
                                status = status
                            )
                        )
                    ),
                    forceUpdate = forceUpdate
                )
            )
        } else {
            viewModelCommentV2.dispatchIntent(
                CommentV2ViewModel.CommentIntent.UpdateNormalCommentData(
                    isSuccess = false,
                    vodId = viewModel.getDataDetail()?.blockContent?.id ?: "",
                    vodCommentInfo = VodCommentInfo(
                        userAvatar = sharedPreferences.profileAvatar(),
                        commentType = VodCommentInfo.VodCommentType.VOD_COMMENT_TYPE_NORMAL,
                        commentData = VodCommentInfo.VodCommentData(
                            statusInfo = VodCommentInfo.CommentStatus(
                                message = message,
                                status = status
                            )
                        )
                    ),
                    forceUpdate = forceUpdate
                )
            )
        }
    }
}