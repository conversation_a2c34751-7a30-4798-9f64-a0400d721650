<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <style name="Theme.FPTPlay" parent="Theme.MaterialComponents.NoActionBar">
        <item name="android:windowLayoutInDisplayCutoutMode">
            shortEdges <!-- default, shortEdges, or never -->
        </item>
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/accent</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:navigationBarColor">@android:color/transparent</item>

        <!-- Optional, if your app is drawing behind the status bar also. -->
        <item name="android:statusBarColor">@color/status_bar_color</item>
        <!-- Customize your theme here. -->
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar.FullWidth</item>
        <item name="android:ambientShadowAlpha">1</item>
        <item name="android:outlineAmbientShadowColor">#0A000000</item>
        <item name="android:spotShadowAlpha">1</item>
        <item name="android:outlineSpotShadowColor">#30000000</item>

        <!--Disable Force Dark Allowed feature -->
        <item name="android:forceDarkAllowed">false</item>

        <!--Ignore Edge To Edge Default for Android 35 and above-->
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:ignore="NewApi">true</item>

    </style>
</resources>