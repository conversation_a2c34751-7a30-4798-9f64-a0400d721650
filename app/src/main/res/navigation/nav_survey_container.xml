<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_survey_container"
    app:startDestination="@id/survey_start_fragment">
    <fragment
        android:id="@+id/survey_start_fragment"
        android:name="com.fptplay.mobile.features.survey.SurveyStartFragment"
        android:label="SurveyStartFragment"
        tools:layout="@layout/survey_start_end_fragment">
        <action
            android:id="@+id/action_survey_start_fragment_to_survey_question_fragment"
            app:destination="@id/survey_question_fragment" />
    </fragment>
    <fragment
        android:id="@+id/survey_question_fragment"
        android:name="com.fptplay.mobile.features.survey.SurveyQuestionFragment"
        android:label="SurveyQuestionFragment"
        tools:layout="@layout/survey_question_fragment">
        <action
            android:id="@+id/action_survey_question_fragment_to_survey_end_fragment"
            app:destination="@id/survey_end_fragment" />
    </fragment>
    <fragment
        android:id="@+id/survey_end_fragment"
        android:name="com.fptplay.mobile.features.survey.SurveyEndFragment"
        android:label="SurveyEndFragment"
        tools:layout="@layout/survey_start_end_fragment"/>
</navigation>