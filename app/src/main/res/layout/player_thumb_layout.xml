<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    tools:ignore="ContentDescription, UselessParent">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_thumbnail"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="fitXY"
        tools:visibility="gone"
        android:src="@drawable/placeholder_vertical_medium"
        app:layout_constraintDimensionRatio="16:9"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/ImageViewCorner" />

    <LinearLayout
        android:id="@+id/ll_time_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/_5sdp"
        android:paddingVertical="@dimen/_1sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_thumbnail"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center_horizontal"
            android:gravity="bottom"
            android:textColor="@color/white_87"
            tools:text="01:12:00" />

        <TextView
            android:id="@+id/tv_total_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center_horizontal"
            android:gravity="bottom"
            android:textColor="@color/white_60"
            tools:text=" / 01:12:00" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>