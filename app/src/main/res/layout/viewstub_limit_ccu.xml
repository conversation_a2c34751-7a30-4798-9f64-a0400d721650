<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/rl_show_matrix_ip"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:visibility="visible"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusable="true"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#CC000000"/>
    <ImageButton
        android:id="@+id/ib_back"
        android:layout_width="@dimen/limit_ccu_button_size"
        android:layout_height="@dimen/limit_ccu_button_size"
        android:background="@drawable/all_background_circle_ripple"
        android:src="@drawable/ic_arrow_back"
        android:layout_margin="@dimen/limit_ccu_button_margin"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tv_desc"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintWidth_max="@dimen/limit_ccu_title_max_width"
        android:textSize="@dimen/limit_ccu_title_size"
        android:textColor="@color/white_87"
        android:fontFamily="@font/sf_pro_display_semibold"
        android:maxLines="2"
        android:ellipsize="end"
        android:letterSpacing="0.02"
        android:layout_marginHorizontal="@dimen/limit_ccu_margin_horizontal"
        android:gravity="center"
        tools:text="@string/msg_player_error"/>
    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintBottom_toTopOf="@+id/btn_retry"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintWidth_max="@dimen/limit_ccu_title_max_width"
        android:textSize="@dimen/limit_ccu_desc_size"
        android:textColor="@color/white_87"
        android:fontFamily="@font/sf_pro_display_regular"
        android:maxLines="2"
        android:ellipsize="end"
        android:letterSpacing="0.02"
        android:layout_marginTop="@dimen/limit_ccu_desc_margin"
        android:layout_marginHorizontal="@dimen/limit_ccu_margin_horizontal"
        android:gravity="center"
        tools:text="@string/msg_player_error"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/btn_retry"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/limit_ccu_btn_margin"
        app:layout_constraintTop_toBottomOf="@id/tv_desc"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/bg_btn_retry_ccu">
        <TextView
            android:id="@+id/tv_retry"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:textSize="@dimen/limit_ccu_desc_size"
            android:textColor="@color/white_87"
            android:fontFamily="@font/sf_pro_display_semibold"
            android:maxLines="2"
            android:ellipsize="end"
            android:letterSpacing="0.02"
            android:layout_marginHorizontal="@dimen/limit_ccu_button_padding_horizontal"
            android:layout_marginVertical="@dimen/limit_ccu_button_padding_vertical"
            android:gravity="center"
            android:includeFontPadding="false"
            android:visibility="visible"
            tools:text="Thử lại"/>
        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/v_loading"
            android:layout_width="@dimen/_20sdp"
            android:layout_height="@dimen/_20sdp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_gravity="center"
            android:visibility="gone"
            android:scaleType="fitXY"
            app:lottie_autoPlay="true"
            app:lottie_fileName="loading.json"
            app:lottie_progress="0.7"
            app:lottie_loop="true"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>