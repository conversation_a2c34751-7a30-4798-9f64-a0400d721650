<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <LinearLayout
        android:id="@+id/ll_logo"
        android:layout_width="@dimen/_130sdp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/app_margin"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@id/barrier_bottom"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/barrier_top">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/_16sdp"
            android:layout_height="@dimen/_32sdp"
            android:paddingVertical="@dimen/_10sdp"
            android:scaleType="fitStart"
            android:src="@drawable/back_icon"
            android:visibility="gone"
            android:contentDescription="@string/talkback_icon_back"/>
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/white_87"
            android:textSize="@dimen/_16ssp"
            android:fontFamily="@font/sf_pro_display_semibold"
            android:text="@string/_new"
            android:layout_gravity="center_vertical"
            android:padding="0dp"
            android:visibility="gone" />
        <ImageView
            android:id="@+id/iv_logo"
            android:layout_width="@dimen/home_fpt_logo"
            android:layout_height="match_parent"
            android:src="@drawable/fpt_play_logo" />

    </LinearLayout>
    <ImageView
        android:id="@+id/iv_payment"
        android:layout_width="@dimen/app_bar_icon"
        android:layout_height="@dimen/app_bar_icon"
        android:padding="@dimen/app_bar_icon_padding"
        android:src="@drawable/payment_ic"
        android:layout_marginEnd="@dimen/mask_margin"
        tools:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:contentDescription="@string/talkback_icon_payment"/>

    <ImageView
        android:id="@+id/iv_notification"
        android:layout_width="@dimen/app_bar_icon"
        android:layout_height="@dimen/app_bar_icon"
        android:padding="@dimen/app_bar_icon_padding"
        tools:visibility="gone"
        android:src="@drawable/notification_icon_state"
        app:layout_constraintEnd_toStartOf="@id/iv_payment"
        app:layout_constraintTop_toTopOf="parent"
        android:contentDescription="@string/talkback_icon_notification"/>


    <ImageView
        android:id="@+id/iv_cast"
        android:layout_width="@dimen/app_bar_icon"
        android:layout_height="@dimen/app_bar_icon"
        android:padding="@dimen/app_bar_icon_padding_subtract_icon_padding"
        android:src="@drawable/player_cast_button_seletor"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toStartOf="@id/barrier_guideline_search"
        app:layout_constraintTop_toTopOf="parent"
        android:contentDescription="@string/talkback_icon_cast"/>

    <ImageView
        android:id="@+id/iv_search"
        android:layout_width="@dimen/app_bar_icon"
        android:layout_height="@dimen/app_bar_icon"
        android:padding="@dimen/app_bar_icon_padding"
        android:src="@drawable/search_icon"
        tools:visibility="visible"
        app:layout_constraintEnd_toStartOf="@id/iv_cast"
        app:layout_constraintTop_toTopOf="parent"
        android:contentDescription="@string/talkback_icon_search"/>

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="top"
        app:constraint_referenced_ids="iv_search, iv_profile_avatar"
        />


    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="iv_search, iv_profile_avatar"
        />
    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_guideline_search"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="start"
        app:constraint_referenced_ids="iv_notification, iv_profile_avatar"
        />

    <com.fptplay.mobile.features.multi_profile.views.MultiProfileAvatarView
        android:id="@+id/iv_profile_avatar"
        android:layout_width="@dimen/app_bar_icon"
        android:layout_height="@dimen/app_bar_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:padding="@dimen/app_bar_icon_profile_avatar_padding"
        android:layout_marginEnd="@dimen/mask_margin"
        app:showRibbonKid="false"
        app:showRibbonPrivate="false"
        app:cornerRadius="@dimen/_5sdp"
        app:strokeColor="@color/white_87"
        app:strokeWidth="0.5dp"
        android:visibility="gone"
        tools:visibility="visible"/>
</merge>