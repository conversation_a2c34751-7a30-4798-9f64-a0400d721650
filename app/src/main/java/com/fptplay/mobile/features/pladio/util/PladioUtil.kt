package com.fptplay.mobile.features.pladio.util

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import android.content.SharedPreferences.OnSharedPreferenceChangeListener
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.annotation.AttrRes
import androidx.annotation.CheckResult
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.annotation.IdRes
import androidx.annotation.OptIn
import androidx.core.content.ContextCompat
import androidx.core.content.res.use
import androidx.core.graphics.drawable.DrawableCompat
import androidx.core.graphics.drawable.toBitmap
import androidx.core.view.forEach
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import androidx.media3.common.util.UnstableApi
import androidx.navigation.ActivityNavigator
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavOptions
import androidx.palette.graphics.Palette
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.fptplay.mobile.R
import com.fptplay.mobile.common.utils.GlideApp
import com.fptplay.mobile.common.utils.PageId
import com.fptplay.mobile.features.pladio.data.PladioBottomNavigationInfo
import com.fptplay.mobile.features.pladio.data.Song
import com.fptplay.mobile.features.pladio.helper.PladioHomeTabProvider
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.navigation.NavigationBarView
import com.google.android.material.navigationrail.NavigationRailView
import com.xhbadxx.projects.module.domain.entity.fplay.pladio.PladioDetail
import com.xhbadxx.projects.module.domain.entity.fplay.pladio.PladioType
import com.xhbadxx.projects.module.domain.entity.fplay.common.AlarmTimeConfig
import com.xhbadxx.projects.module.domain.entity.fplay.common.AlarmType
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.ItemType
import com.xhbadxx.projects.module.domain.entity.fplay.home_os4.TabMenu
import com.xhbadxx.projects.module.domain.entity.fplay.pladio.PladioEvent
import com.xhbadxx.projects.module.domain.entity.fplay.pladio.PladioEventType
import com.xhbadxx.projects.module.domain.entity.fplay.pladio.PladioPlaylist
import java.lang.Exception
import java.lang.ref.WeakReference
import java.util.Locale

@OptIn(UnstableApi::class)
@SuppressLint("RestrictedApi")
object PladioUtil {

    fun getDefaultAlarmTimeConfig(): List<AlarmTimeConfig> {
        return listOf(
            AlarmTimeConfig(time = "5 phút", duration = "300", typeAlarm = AlarmType.Normal),
            AlarmTimeConfig(time = "10 phút", duration = "600", typeAlarm = AlarmType.Normal),
            AlarmTimeConfig(time = "15 phút", duration = "900", typeAlarm = AlarmType.Normal),
            AlarmTimeConfig(time = "30 phút", duration = "1800", typeAlarm = AlarmType.Normal),
            AlarmTimeConfig(time = "45 phút", duration = "2700", typeAlarm = AlarmType.Normal),
            AlarmTimeConfig(time = "60 phút", duration = "3600", typeAlarm = AlarmType.Normal),
            AlarmTimeConfig(time = "Hết tập", duration = "", typeAlarm = AlarmType.End),
            AlarmTimeConfig(time = "Tắt hẹn giờ", duration = "", typeAlarm = AlarmType.Off),
        )
    }

    fun List<TabMenu>.mapToPladioBottomNavigationInfo(context: Context, provider: PladioHomeTabProvider): List<PladioBottomNavigationInfo> {
        val list = mutableListOf<PladioBottomNavigationInfo>()
        forEachIndexed { index, tabMenu ->
            val pageId = PageId.OtherPageId(tabMenu.pageId)
            val id = if (tabMenu.pageId == "m-search") R.id.pladio_search_fragment else R.id.pladio_home_fragment
            list.add(PladioBottomNavigationInfo(pageId, id, tabMenu.name, provider.getTabIconDefault(context, index)))
        }
       return list
    }

    fun ArrayList<Song>.toMediaSessionQueue(): List<MediaItem> {
        return mapIndexed { index, song ->
            val mediaMetadata = MediaMetadata.Builder()
                .setTitle(song.title)
                .build()
            MediaItem.Builder()
                .setMediaId(song.id)
                .setMediaMetadata(mediaMetadata)
                .build()
        }
    }

    fun createLeftToRightGradient(startColor: Int, endColor: Int): GradientDrawable {
        return GradientDrawable(
            GradientDrawable.Orientation.LEFT_RIGHT,
            intArrayOf(startColor, endColor)
        ).apply {
            shape = GradientDrawable.RECTANGLE
        }
    }

    fun createTopToBottomGradient(startColor: Int, endColor: Int): GradientDrawable {
        return GradientDrawable(
            GradientDrawable.Orientation.TOP_BOTTOM,
            intArrayOf(startColor, endColor)
        ).apply {
            shape = GradientDrawable.RECTANGLE
        }
    }

    fun makeShuffleList(listToShuffle: MutableList<Song>, current: Int) {
        if (listToShuffle.isEmpty()) return
        if (current >= 0) {
            val song = listToShuffle.removeAt(current)
            listToShuffle.shuffle()
            listToShuffle.add(0, song)
        } else {
            listToShuffle.shuffle()
        }
    }

    fun SharedPreferences.registerOnSharedPreferenceChangedListener(
        listener: OnSharedPreferenceChangeListener,
    ) = this.registerOnSharedPreferenceChangeListener(listener)


    fun SharedPreferences.unregisterOnSharedPreferenceChangedListener(
        changeListener: OnSharedPreferenceChangeListener,
    ) = this.unregisterOnSharedPreferenceChangeListener(changeListener)


    fun getReadableDurationString(songDurationMillis: Long): String {
        var minutes = songDurationMillis / 1000 / 60
        val seconds = songDurationMillis / 1000 % 60
        return if (minutes < 60) {
            String.format(
                Locale.getDefault(),
                "${if (minutes < 10) "%d" else "%02d"}:%02d",
                minutes,
                seconds
            )
        } else {
            val hours = minutes / 60
            minutes %= 60
            String.format(
                Locale.getDefault(),
                "${if (hours < 10) "%d" else "%02d"}:%02d:%02d",
                hours,
                minutes,
                seconds
            )
        }
    }

    fun Context.loadBitmapPalette(url: String, onCompleted: (Int, Bitmap) -> Unit, onLoadFailed: (errorDrawable: Drawable?) -> Unit = {}, onLoadCleared: (placeholder: Drawable?) -> Unit = {}, isCenterCrop: Boolean = false) {
        if (isCenterCrop) {
            GlideApp.with(this)
                .asBitmap()
                .load(url)
                .centerCrop()
                .into(object : CustomTarget<Bitmap>(){
                    override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                        val builder = Palette.Builder(resource)
                        builder.generate { palette: Palette? ->
                            if (palette != null) {
                                val dominantColor = palette.getDominantColor(ContextCompat.getColor(this@loadBitmapPalette, R.color.accent_60))
                                onCompleted.invoke(dominantColor, resource)
                            }
                        }
                    }

                    override fun onLoadFailed(errorDrawable: Drawable?) {
                        onLoadFailed.invoke(errorDrawable)
                    }
                    override fun onLoadCleared(placeholder: Drawable?) {
                        onLoadCleared.invoke(placeholder)
                    }
                })
        } else {
            GlideApp.with(this)
                .asBitmap()
                .load(url)
                .into(object : CustomTarget<Bitmap>(){
                    override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                        val builder = Palette.Builder(resource)
                        builder.generate { palette: Palette? ->
                            if (palette != null) {
                                val dominantColor = palette.getDominantColor(ContextCompat.getColor(this@loadBitmapPalette, R.color.accent_60))
                                onCompleted.invoke(dominantColor, resource)
                            }
                        }
                    }
                    override fun onLoadFailed(errorDrawable: Drawable?) {
                        onLoadFailed.invoke(errorDrawable)
                    }
                    override fun onLoadCleared(placeholder: Drawable?) {
                        onLoadCleared.invoke(placeholder)
                    }
                })
        }
    }

    @JvmOverloads
    fun resolveColor(context: Context, @AttrRes attr: Int, fallback: Int = 0): Int {
        context.theme.obtainStyledAttributes(intArrayOf(attr)).use {
            return try {
                it.getColor(0, fallback);
            } catch (e: Exception) {
                Color.BLACK
            }
        }
    }

    fun isLightColor(@ColorInt color: Int): Boolean {
        val darkness =
            1 - (0.299 * Color.red(color) + 0.587 * Color.green(color) + 0.114 * Color.blue(color)) / 255
        return darkness < 0.4
    }

    inline val @receiver:ColorInt Int.isColorLight
        get() = isLightColor(this)


    fun Context.getTintedDrawable(@DrawableRes id: Int, @ColorInt color: Int): Drawable {
        return ContextCompat.getDrawable(this, id)?.tint(color)!!
    }

    @CheckResult
    fun Drawable.tint(@ColorInt color: Int): Drawable {
        val tintedDrawable = DrawableCompat.wrap(this).mutate()
        setTint(color)
        return tintedDrawable
    }

    fun Drawable.toBitmap(scaleFactor: Float, config: Bitmap.Config? = null): Bitmap {
        return toBitmap((intrinsicHeight*scaleFactor).toInt(), (intrinsicWidth*scaleFactor).toInt(), config)
    }

    @SuppressLint("PrivateResource")
    @JvmStatic
    @ColorInt
    fun getPrimaryTextColor(context: Context?, dark: Boolean): Int {
        return if (dark) {
            ContextCompat.getColor(context!!, androidx.appcompat.R.color.primary_text_default_material_light)
        } else ContextCompat.getColor(context!!, androidx.appcompat.R.color.primary_text_default_material_dark)
    }

    @SuppressLint("PrivateResource")
    @JvmStatic
    @ColorInt
    fun getSecondaryTextColor(context: Context?, dark: Boolean): Int {
        return if (dark) {
            ContextCompat.getColor(context!!, androidx.appcompat.R.color.secondary_text_default_material_light)
        } else ContextCompat.getColor(context!!, androidx.appcompat.R.color.secondary_text_default_material_dark)
    }

    @SuppressLint("PrivateResource")
    @JvmStatic
    @ColorInt
    fun getPrimaryDisabledTextColor(context: Context?, dark: Boolean): Int {
        return if (dark) {
            ContextCompat.getColor(context!!, androidx.appcompat.R.color.primary_text_disabled_material_light)
        } else ContextCompat.getColor(context!!, androidx.appcompat.R.color.primary_text_disabled_material_dark)
    }

    @SuppressLint("PrivateResource")
    @JvmStatic
    @ColorInt
    fun getSecondaryDisabledTextColor(context: Context?, dark: Boolean): Int {
        return if (dark) {
            ContextCompat.getColor(context!!, androidx.appcompat.R.color.secondary_text_disabled_material_light)
        } else ContextCompat.getColor(context!!, androidx.appcompat.R.color.secondary_text_disabled_material_dark)
    }

    fun Song.isTheSame(other: Song): Boolean {
        return id == other.id && playlistId == other.playlistId && streamRequestDataExtra?.episodeId == other.streamRequestDataExtra?.episodeId
    }

    fun PladioDetail.mapToSong(playlistTitle: String = "", songType: Song.PladioType?, playState: Song.PlayState, contentType: Song.PladioContentType): Song {
        return Song(
            id = this.id,
            title = this.title,
            playlistId = this.playlistId,
            playlistTitle = playlistTitle,
            artist = this.artist,
            description = this.description,
            posterUrl = this.img,
            shareUrl = this.deepLink,
            dominantColor = this.bgColor,
            contentType = contentType,
            songType = songType,
            audioOnly = true,
            stream = null,
            duration = this.episodes?.firstOrNull()?.duration ?: "",
            playState = playState,
            streamRequestDataExtra = Song.StreamRequestDataExtra(
                episodeId = this.episodes?.firstOrNull()?.id ?: "",
                autoProfile = this.episodes?.firstOrNull()?.autoProfile ?: "",
                realEpisodeId = this.episodes?.firstOrNull()?.realEpisodeId ?: ""
            )
        )
    }

    fun PladioEvent.mapToSong(id: String, songType: Song.PladioType?, contentType: Song.PladioContentType): Song {
        return Song(
            id = id,
            title = this.title,
            playlistId = "",
            playlistTitle = "",
            artist = this.artist,
            description = "",
            posterUrl = this.poster, // Get Event Detail Poster
            shareUrl = this.deepLink,
            dominantColor = this.bgColor,
            contentType = contentType,
            songType = songType,
            audioOnly = false,
            stream = null,
            duration = "",
            playState = Song.PlayState.NonPlay,
            eventDetail = Song.EventDetail(
                eventId = this.id,
                beginTime = this.beginTime,
                endTime = this.endTime,
                labelEvent = this.labelEvent,
                posterUrl = this.poster,
                isWhitelist = checkIsWhitelist(this.isWhiteList),
                type = this.getEventContentType()
            ),
            streamRequestDataExtra = Song.StreamRequestDataExtra(
                episodeId = "",
                autoProfile = this.autoProfile
            )
        )
    }

    fun getPlayState(isCurrentItemPlay: Boolean, isPlaying: Boolean): Song.PlayState {
        return if(isCurrentItemPlay) {
            if (isPlaying) {
                Song.PlayState.Playing
            } else {
                Song.PlayState.Pause
            }
        } else {
            Song.PlayState.NonPlay
        }
    }

    /**
     * @param whiteList : Preview status
     * @value 0: no preview / 1: preview
     */
    private fun checkIsWhitelist(whiteList: String): Boolean {
        return (whiteList.toIntOrNull() ?: 0) == 1
    }

    fun PladioEvent.getEventContentType(isPremiere: Boolean = false): Song.PladioEventType {
        return when (pladioEventType) {
            is PladioEventType.Event -> {
                if (isPremiere) Song.PladioEventType.Premiere else Song.PladioEventType.Event
            }
            is PladioEventType.EventTv -> Song.PladioEventType.EventTV
            else -> Song.PladioEventType.Event
        }
    }

    fun PladioType.toSongType(): Song.PladioType {
        return when(this) {
            is PladioType.Music -> Song.PladioType.Music
            is PladioType.Podcast -> Song.PladioType.Podcast
            else -> Song.PladioType.Event
        }
    }


    fun convertPladioDetailToPladioPlaylist(detail: PladioDetail): PladioPlaylist? {
        try {
            val pladioPlaylist = PladioPlaylist(
                id = "",
                artists = detail.artist,
                bgColor = detail.bgColor,
                deepLink = detail.deepLink,
                enableReport = detail.enableReport,
                img = detail.img,
                pladioType = detail.pladioType,
                listChapter = detail.episodes?.map { episode ->
                    PladioDetail(
                        id = detail.id,
                        title = episode.title,
                        artist = episode.artist,
                        autoProfile = episode.autoProfile,
                        description = detail.description,
                        duration = episode.duration,
                        episodes = listOf(episode),
                        enableReport = detail.enableReport,
                        episodeType = detail.episodeType,
                        bgColor = detail.bgColor,
                        deepLink = episode.deepLink,
                        img = episode.img,
                        pladioType = detail.pladioType,
                        playlistId = detail.id,
                        tracking = detail.tracking
                    )
                } ?: listOf(),
                title = detail.title,
                tracking = detail.tracking
            )
            return pladioPlaylist
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }


    // region deeplink helper
    fun String?.convertToItemType(): ItemType {
        return when (this) {
            ItemType.VOD.id -> ItemType.VOD
            ItemType.PLAYLIST.id -> ItemType.PLAYLIST
            ItemType.Event.id -> ItemType.Event
            ItemType.EventTV.id -> ItemType.EventTV
            else -> ItemType.Unknown
        }
    }

    fun Song.PladioContentType?.convertToItemType(): ItemType {
        return when (this) {
            Song.PladioContentType.Single -> ItemType.VOD
            Song.PladioContentType.Playlist -> ItemType.PLAYLIST
            Song.PladioContentType.Series -> ItemType.VOD
            else -> ItemType.Unknown
        }
    }

    // endregion


    // region Navigation

    /**
     * Sets up a [NavigationBarView] for use with a [NavController]. This will call
     * [onNavDestinationSelected] when a menu item is selected. The
     * selected item in the NavigationBarView will automatically be updated when the destination
     * changes.
     *
     * @param navigationBarView The NavigationBarView ([BottomNavigationView] or
     * [NavigationRailView])
     * that should be kept in sync with changes to the NavController.
     * @param navController The NavController that supplies the primary menu.
     * Navigation actions on this NavController will be reflected in the
     * selected item in the NavigationBarView.
     */
    @JvmStatic
    fun setupWithNavController(
        navigationBarView: NavigationBarView,
        navController: NavController
    ) {
        navigationBarView.setOnItemSelectedListener { item ->
            onNavDestinationSelected(
                item,
                navController
            )
        }
        val weakReference = WeakReference(navigationBarView)
        navController.addOnDestinationChangedListener(
            object : NavController.OnDestinationChangedListener {
                override fun onDestinationChanged(
                    controller: NavController,
                    destination: NavDestination,
                    arguments: Bundle?
                ) {
                    val view = weakReference.get()
                    if (view == null) {
                        navController.removeOnDestinationChangedListener(this)
                        return
                    }
                    view.menu.forEach { item ->
                        if (destination.matchDestination(item.itemId)) {
                            item.isChecked = true
                        }
                    }
                }
            })
    }

    /**
     * Attempt to navigate to the [NavDestination] associated with the given MenuItem. This
     * MenuItem should have been added via one of the helper methods in this class.
     *
     * Importantly, it assumes the [menu item id][MenuItem.getItemId] matches a valid
     * [action id][NavDestination.getAction] or [destination id][NavDestination.id] to be
     * navigated to.
     *
     * By default, the back stack will be popped back to the navigation graph's start destination.
     * Menu items that have `android:menuCategory="secondary"` will not pop the back
     * stack.
     *
     * @param item The selected MenuItem.
     * @param navController The NavController that hosts the destination.
     * @return True if the [NavController] was able to navigate to the destination
     * associated with the given MenuItem.
     */
    @JvmStatic
    fun onNavDestinationSelected(item: MenuItem, navController: NavController): Boolean {
        val builder = NavOptions.Builder().setLaunchSingleTop(true).setRestoreState(false)
        if (
            navController.currentDestination!!.parent!!.findNode(item.itemId)
                    is ActivityNavigator.Destination
        ) {
            builder.setEnterAnim(R.anim.nav_default_enter_anim)
                .setExitAnim(R.anim.nav_default_exit_anim)
                .setPopEnterAnim(R.anim.nav_default_pop_enter_anim)
                .setPopExitAnim(R.anim.nav_default_pop_exit_anim)
        } else {
            builder.setEnterAnim(R.animator.nav_default_enter_anim)
                .setExitAnim(R.animator.nav_default_exit_anim)
                .setPopEnterAnim(R.animator.nav_default_pop_enter_anim)
                .setPopExitAnim(R.animator.nav_default_pop_exit_anim)
        }
        if (item.order and Menu.CATEGORY_SECONDARY == 0) {
            builder.setPopUpTo(
                navController.graph.findStartDestination().id,
                inclusive = false,
                saveState = false
            )
        }
        val options = builder.build()
        return try {
            // TODO provide proper API instead of using Exceptions as Control-Flow.
            navController.navigate(item.itemId, null, options)
            // Return true only if the destination we've navigated to matches the MenuItem
            navController.currentDestination?.matchDestination(item.itemId) == true
        } catch (e: IllegalArgumentException) {
            false
        }
    }

    /**
     * Determines whether the given `destId` matches the NavDestination. This handles
     * both the default case (the destination's id matches the given id) and the nested case where
     * the given id is a parent/grandparent/etc of the destination.
     */
    @JvmStatic
    internal fun NavDestination.matchDestination(@IdRes destId: Int): Boolean =
        hierarchy.any { it.id == destId }
    // endregion
}

