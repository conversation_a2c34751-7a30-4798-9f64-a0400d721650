package com.fptplay.mobile.features.mega.adapter

import android.content.Context
import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import coil.load
import com.fplay.module.downloader.model.CollectionVideoTaskItem
import com.fptplay.mobile.BuildConfig
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.adapter.BaseAdapter
import com.fptplay.mobile.common.extensions.onClick
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.AllItemUnknownBinding
import com.fptplay.mobile.databinding.MegaAccountLogoutBlockBinding
import com.fptplay.mobile.databinding.MegaAppVersionBlockBinding
import com.fptplay.mobile.databinding.MegaBlockDownloadBinding
import com.fptplay.mobile.databinding.MegaMenuV3BlockBinding
import com.fptplay.mobile.databinding.MegaMultiProfileItemV2Binding
import com.fptplay.mobile.databinding.MegaProfileItemV2Binding
import com.fptplay.mobile.features.mega.data.BlockAccountLogout
import com.fptplay.mobile.features.mega.data.BlockAppVersion
import com.fptplay.mobile.features.mega.data.UpdateDownloadBlockItemRemove
import com.fptplay.mobile.features.mega.data.UpdateFeatureGridViewMoreState
import com.fptplay.mobile.features.mega.util.MegaAppGridItemDecoration
import com.fptplay.mobile.features.mega.util.MegaMenuItemDecoration
import com.fptplay.mobile.features.pladio.util.setFullWidthLookup
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenu
import com.xhbadxx.projects.module.domain.entity.fplay.common.MegaMenuItem
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.Util.checkToShowContent
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import timber.log.Timber

class MegaMenuAdapter(private val context: Context, private val sharedPreferences: SharedPreferences) :
    BaseAdapter<MegaMenu.Block, RecyclerView.ViewHolder>() {

    var megaMenuItemClickListener: IEventListener<MegaMenuItem>? = null

    //download
    var downloadData = arrayListOf<CollectionVideoTaskItem>()
    var downloadItemClickListener: IEventListener<CollectionVideoTaskItem>? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            0 -> ProfileViewHolder(
                MegaProfileItemV2Binding.inflate(LayoutInflater.from(parent.context), parent, false)
            )

            1 -> MultiProfileViewHolder(
                MegaMultiProfileItemV2Binding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )

            2 -> BlockLogoutHolder(
                MegaAccountLogoutBlockBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )

            3 -> BlockGridSquareSmallHolder(
                MegaMenuV3BlockBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )

            4 -> BlockNavigationMenuHolder(
                MegaMenuV3BlockBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                ),
                isolated = false
            )

            5 -> BlockBannerHolder(
                MegaMenuV3BlockBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )

            6 -> BlockNavigationMenuHolder(
                MegaMenuV3BlockBinding.inflate(LayoutInflater.from(parent.context), parent, false),
                isolated = true
            )

            7 -> BlockAppVersionHolder(
                MegaAppVersionBlockBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )

            8 -> BlockFeatureGridHolder(
                MegaMenuV3BlockBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )

            9 -> BlockDownloadHolder(
                MegaBlockDownloadBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )

            else -> MegaMenuUnknownBlockViewHolder(
                AllItemUnknownBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )
        }

    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val data = differ.currentList[position]
        when (holder) {
            is ProfileViewHolder -> holder.bind(data)
            is BlockNavigationMenuHolder -> holder.bind(data)
            is BlockGridSquareSmallHolder -> holder.bind(data)
            is MultiProfileViewHolder -> holder.bind(data)
            is BlockBannerHolder -> holder.bind(data)
            is BlockLogoutHolder -> holder.bind(data)
            is BlockAppVersionHolder -> holder.bind(data)
            is BlockFeatureGridHolder -> holder.bind(data)
            is MegaMenuUnknownBlockViewHolder -> holder.bind(data)
            is BlockDownloadHolder -> holder.bind(data)
        }
    }


    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        super.onBindViewHolder(holder, position, payloads)
        if (holder is BlockFeatureGridHolder) {
            holder.bind(differ.currentList[position], payloads)
        } else if (holder is BlockDownloadHolder) {

        }
    }

    override fun getItemViewType(position: Int): Int {
        val item = differ.currentList[position]
        return when (item.blockStyle) {
            MegaMenu.BlockStyle.SquareGridSmall -> 3
            MegaMenu.BlockStyle.NavigationMenu -> 4
            MegaMenu.BlockStyle.Banner -> 5
            MegaMenu.BlockStyle.Unknown -> {
                when (item) {
                    is MegaMenu.BlockProfile,
                    is MegaMenu.BlockProfileLogin -> 0
                    is MegaMenu.BlockMultiProfile -> 1
                    is BlockAccountLogout -> 2
                    is BlockAppVersion -> 7
                    else -> -1
                }

            }

            MegaMenu.BlockStyle.IsolateNavigationMenu -> 6
            MegaMenu.BlockStyle.FeatureGrid -> 8
            MegaMenu.BlockStyle.HorizontalSliderD2g -> 9
        }
    }

    inner class ProfileViewHolder(private val binding: MegaProfileItemV2Binding) :
        RecyclerView.ViewHolder(binding.root) {

        init {

            binding.ivHamburger.onClick(500) {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickView(
                        absoluteAdapterPosition,
                        view = this,
                        data = it
                    )
                }
            }
            binding.ivQrCode.onClick(500) {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickView(
                        absoluteAdapterPosition,
                        view = this,
                        data = it
                    )
                }
            }
            binding.ctlProfile.onClick(500) {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        it
                    )
                }
            }
        }

        fun bind(megaMenuBlock: MegaMenu.Block) {

            when (megaMenuBlock) {
                is MegaMenu.BlockProfile -> showProfileLayout(megaMenuBlock)
                is MegaMenu.BlockProfileLogin -> showLoginLayout()
                else -> {}
            }
        }

        private fun showLoginLayout() {
            binding.apply {
                ivAvatar.setImageResource(R.drawable.ic_user_default_avatar)
                tvTitleLogin.show()
                tvSubtitleLogin.hide()

                tvUsername.hide()
                tvLabelSubscription.hide()
            }
        }

        private fun showProfileLayout(menu: MegaMenu.BlockProfile) {
            binding.apply {
//                ivAvatar.setImageResource(R.drawable.ic_user_default_avatar)
                ivAvatar.load(menu.userAvatar)
                tvUsername.checkToShowContent(menu.userName, goneViewWhenNoText = true)
                tvUsername.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0)
                tvLabelSubscription.hide()

                tvTitleLogin.hide()
                tvSubtitleLogin.hide()
            }
        }
    }

    inner class MultiProfileViewHolder(private val binding: MegaMultiProfileItemV2Binding) :
        RecyclerView.ViewHolder(binding.root) {
        private val avatarSize by lazy {
            Utils.getSizeInPixel(
                context = binding.root.context,
                resId = R.dimen.mega_multi_profile_item_ava_size
            )
        }

        init {
            binding.avatarView.showRibbonKid = false
            binding.avatarView.showRibbonPrivate = false
            binding.ctlProfile.onClick(500) {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        it
                    )
                }
            }

            binding.ivHamburger.onClick(500) {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickView(
                        absoluteAdapterPosition,
                        view = this,
                        data = it
                    )
                }
            }
            binding.ivQrCode.onClick(500) {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickView(
                        absoluteAdapterPosition,
                        view = this,
                        data = it
                    )
                }
            }
        }

        fun bind(megaMenuBlock: MegaMenu.Block) {
            if (megaMenuBlock is MegaMenu.BlockMultiProfile) {
                binding.apply {
//                ivAvatar.setImageResource(R.drawable.ic_user_default_avatar)
                    avatarView.bindProfile(
                        url = megaMenuBlock.profileAvatar,
                        width = avatarSize,
                        height = avatarSize
                    )
                    tvUsername.checkToShowContent(
                        megaMenuBlock.profileName,
                        goneViewWhenNoText = true
                    )
                }
            }
        }


    }

    inner class BlockNavigationMenuHolder(private val binding: MegaMenuV3BlockBinding, private val isolated: Boolean) :
        RecyclerView.ViewHolder(binding.root) {

        private val menuAdapter by lazy {
            NavigationMenuAdapter(binding.root.context, isolated = isolated, sharedPreferences = sharedPreferences).apply {
                eventListener = megaMenuItemClickListener
            }
        }

        private val menuMarginTop by lazy {
            context.resources.getDimensionPixelSize(R.dimen.mega_block_item_margin_top)
        }
        init {
            binding.rvMenu.apply {
                adapter = menuAdapter
                layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
                if(isolated) {
                    addItemDecoration(MegaMenuItemDecoration(menuMarginTop))

                } else {
                    background =
                        ContextCompat.getDrawable(
                            binding.root.context,
                            R.drawable.mega_menu_background
                        )
                }
            }

        }

        fun bind(megaMenuBlock: MegaMenu.Block) {
            binding.apply {
                tvTitle.checkToShowContent(megaMenuBlock.title, goneViewWhenNoText = true)
                tvSubTitle.checkToShowContent(megaMenuBlock.subTitle, goneViewWhenNoText = true)
                menuAdapter.bind(megaMenuBlock.megaMenus)
                tvViewAll.hide()
            }
        }

    }

    inner class BlockGridSquareSmallHolder(private val binding: MegaMenuV3BlockBinding) :
        RecyclerView.ViewHolder(binding.root) {

        private val marginBetweenBlock by lazy {
            context.resources.getDimensionPixelSize(R.dimen.mega_app_margin_between)
        }
        private val appsAdapter by lazy {
            MegaAppV2Adapter(context).apply {
                eventListener = megaMenuItemClickListener
            }
        }

        init {
            binding.apply {
                rvMenu.apply {
                    adapter = appsAdapter
                    layoutManager = GridLayoutManager(
                        context,
                        MegaAppV2Adapter.APP_ITEMS_PER_ROW,
                        RecyclerView.VERTICAL,
                        false
                    )
                    addItemDecoration(
                        MegaAppGridItemDecoration(
                            MegaAppV2Adapter.APP_ITEMS_PER_ROW, marginBetweenBlock, includeEdge = false, includeHeader = true
                        )
                    )
                }

                tvViewAll.onClickDelay {
                    item(absoluteAdapterPosition)?.let { data ->
                        eventListener?.onClickView(absoluteAdapterPosition, this, data)

                    }
                }
            }

        }

        fun bind(menu: MegaMenu.Block) {
            binding.apply {
                tvTitle.checkToShowContent(menu.title, goneViewWhenNoText = true)
                tvSubTitle.checkToShowContent(menu.subTitle, goneViewWhenNoText = true)
                appsAdapter.bind(menu.megaMenus)
                // Currently hide view all
                tvViewAll.hide()
            }
        }

    }

    inner class BlockFeatureGridHolder(private val binding: MegaMenuV3BlockBinding) : RecyclerView.ViewHolder(binding.root) {

        private var isShowMore = false
        private val marginBetweenBlock by lazy {
            context.resources.getDimensionPixelSize(R.dimen.mega_feature_grid_item_margin_between)
        }
        private val featureGridAdapter by lazy {
            FeatureGridMenuAdapter(context).apply {
                eventListener = object: IEventListener<MegaMenuItem> {
                    override fun onClickedItem(position: Int, data: MegaMenuItem) {
                        if(data.id == FeatureGridMenuAdapter.VIEW_MORE_ITEM_ID) {
                            isShowMore = !isShowMore
                            <EMAIL>(absoluteAdapterPosition, UpdateFeatureGridViewMoreState(isShowMore))
//                            <EMAIL>()
                        } else {
                            megaMenuItemClickListener?.onClickedItem(position, data)
                        }
                    }
                }
            }
        }

        init {
            binding.apply {
                rvMenu.apply {
                    adapter = featureGridAdapter
                    layoutManager = GridLayoutManager(
                        context,
                        FeatureGridMenuAdapter.ITEMS_PER_ROW,
                        RecyclerView.VERTICAL,
                        false
                    )
                    addItemDecoration(
                        MegaAppGridItemDecoration(
                            FeatureGridMenuAdapter.ITEMS_PER_ROW,
                            marginBetweenBlock,
                            includeEdge = false,
                            includeHeader = false
                        )
                    )
                }

                tvViewAll.onClickDelay {
                    item(absoluteAdapterPosition)?.let { data ->
                        eventListener?.onClickView(absoluteAdapterPosition, this, data)

                    }
                }
            }


        }

        fun bind(menu: MegaMenu.Block) {

            binding.apply {
                tvTitle.checkToShowContent(menu.title, goneViewWhenNoText = true)
                tvSubTitle.checkToShowContent(menu.subTitle, goneViewWhenNoText = true)
                if (menu.megaMenus.size <= FeatureGridMenuAdapter.MAXIMUM_ITEM_SHOWN) {
                    featureGridAdapter.bind(menu.megaMenus)
                } else {
                    if (isShowMore) {
                        bindShowMoreItem(menu)

                    } else {
                        bindShowLessItem(menu)
                    }

                }

                // Currently hide view all
                tvViewAll.hide()
            }
        }
        fun bind(menu: MegaMenu.Block, payloads: MutableList<Any>) {
            for (obj in payloads) {
                when (obj) {
                    is UpdateFeatureGridViewMoreState -> {
                        if(obj.isShowMore) bindShowMoreItem(menu) else bindShowLessItem(menu)
                    }
                }
            }
        }

        private fun bindShowLessItem(menu: MegaMenu.Block) {
            if (menu.megaMenus.size <= FeatureGridMenuAdapter.MAXIMUM_ITEM_SHOWN) {
                featureGridAdapter.bind(menu.megaMenus)
            } else {
                val menus = ArrayList(menu.megaMenus).subList(
                    0,
                    FeatureGridMenuAdapter.MAXIMUM_ITEM_SHOWN
                ).apply{
                    add(
                        MegaMenuItem(
                            id = FeatureGridMenuAdapter.VIEW_MORE_ITEM_ID,
                            title = context.getString(R.string.mega_feature_grid_block_view_more),
                            actionType = MegaMenuItem.ActionType.Unknown(context.getString(R.string.mega_feature_grid_block_view_more)),
                            blockType = MegaMenu.Type.Unknown,
                        )
                    )
                }
                (binding.rvMenu.layoutManager as? GridLayoutManager)?.setFullWidthLookup { index ->
                    index == menus.lastIndex
                }
                featureGridAdapter.bind(menus)

            }

        }
        private fun bindShowMoreItem(menu: MegaMenu.Block) {
            if (menu.megaMenus.size <= FeatureGridMenuAdapter.MAXIMUM_ITEM_SHOWN) {
                featureGridAdapter.bind(menu.megaMenus)
            } else {
                val menus = ArrayList(menu.megaMenus).apply{
                    add(
                        MegaMenuItem(
                            id = FeatureGridMenuAdapter.VIEW_MORE_ITEM_ID,
                            title = context.getString(R.string.mega_feature_grid_block_hide),
                            actionType = MegaMenuItem.ActionType.Unknown(context.getString(R.string.mega_feature_grid_block_view_more)),
                            blockType = MegaMenu.Type.Unknown,

                            )
                    )
                }
                (binding.rvMenu.layoutManager as? GridLayoutManager)?.setFullWidthLookup { index ->
                    index == menus.lastIndex
                }

                featureGridAdapter.bind(menus)

            }

        }

    }

    inner class BlockBannerHolder(private val binding: MegaMenuV3BlockBinding) :
        RecyclerView.ViewHolder(binding.root) {

        private val bannerAdapter by lazy {
            MegaBannerAdapter(context).apply {
                eventListener = megaMenuItemClickListener
            }
        }

        init {
            binding.apply {
                rvMenu.apply {
                    adapter = bannerAdapter
                    layoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)

                }

                tvViewAll.onClickDelay {
                    item(absoluteAdapterPosition)?.let { data ->
                        eventListener?.onClickView(absoluteAdapterPosition, this, data)

                    }
                }
            }

        }

        fun bind(menu: MegaMenu.Block) {
            Timber.tag("tam-mega").e("${this.javaClass.simpleName} bind $menu")
            binding.apply {
                tvTitle.checkToShowContent(menu.title, goneViewWhenNoText = true)
                tvSubTitle.checkToShowContent(menu.subTitle, goneViewWhenNoText = true)
                bannerAdapter.bind(menu.megaMenus)
                // Currently hide view all
                tvViewAll.hide()
            }
        }

    }

    inner class BlockLogoutHolder(private val binding: MegaAccountLogoutBlockBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.onClick(500) {
                item(absoluteAdapterPosition)?.let {
                    eventListener?.onClickedItem(
                        absoluteAdapterPosition,
                        it
                    )
                }
            }
        }

        fun bind(menu: MegaMenu.Block) {
            Timber.tag("tam-mega").i("${this.javaClass.simpleName} bind $menu")

        }

    }

    inner class BlockAppVersionHolder(private val binding: MegaAppVersionBlockBinding)  :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(menu: MegaMenu.Block) {
            Timber.tag("tam-mega").i("${this.javaClass.simpleName} bind $menu")
            var textContent = String.format(
                binding.root.context.getString(R.string.version_param),
                "${MainApplication.INSTANCE.appConfig.nameOs} ${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})"
            )
            if (sharedPreferences.shouldShowDeviceId()) {
                if(sharedPreferences.deviceIdDisplayLength() > 0) {
                    val deviceId = if(sharedPreferences.deviceIdDisplayLength() < sharedPreferences.androidId().length) {
                        try {
                            sharedPreferences.androidId().substring(
                                sharedPreferences.androidId().length - sharedPreferences.deviceIdDisplayLength(),
                                sharedPreferences.androidId().length
                            )
                        } catch (ex: Exception) {
                            ex.printStackTrace()
                            ""
                        }
                    } else {
                        sharedPreferences.androidId()
                    }
                    textContent += "\n${deviceId}"
                }
            }
            binding.tvAppVersion.text = textContent
        }
    }

    inner class MegaMenuUnknownBlockViewHolder(binding: AllItemUnknownBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(data: BaseObject) {}
    }

    inner class BlockDownloadHolder(private val binding: MegaBlockDownloadBinding) : RecyclerView.ViewHolder(binding.root) {
        private val downloadItemMarginRight by lazy {
            binding.root.context.resources.getDimensionPixelSize(R.dimen.mega_block_download_item_margin_right)
        }

        private val downloadAdapter by lazy {
            MegaBlockDownloadAdapter(context).apply {
                setHasStableIds(true)
                eventListener = object: IEventListener<CollectionVideoTaskItem> {
                    override fun onClickedItem(position: Int, data: CollectionVideoTaskItem) {
                        <EMAIL>(absoluteAdapterPosition)?.let {
                            if(it.megaMenus.isNotEmpty()) {
                                megaMenuItemClickListener?.onSelectedItem(
                                    absoluteAdapterPosition,
                                    it.megaMenus[0]
                                )
                            }

                        }
                        if(data.movieId == MegaBlockDownloadAdapter.DOWNLOADING_ITEM_ID) {

                            // get item download to get title to navigate
                            <EMAIL>(absoluteAdapterPosition)?.let {
                                if(it.megaMenus.isNotEmpty()) {
                                    megaMenuItemClickListener?.onClickedItem(
                                        absoluteAdapterPosition,
                                        it.megaMenus[0]
                                    )
                                }

                            }
                            return
                        }
                        downloadItemClickListener?.onClickedItem(position, data)

                    }
                }
            }
        }

        init {
            binding.apply {
                rvDownload.apply {
                    adapter = downloadAdapter
                    layoutManager = LinearLayoutManager(
                        context,
                        RecyclerView.HORIZONTAL,
                        false
                    )
                    addItemDecoration(object: RecyclerView.ItemDecoration() {
                        override fun getItemOffsets(
                            outRect: Rect,
                            view: View,
                            parent: RecyclerView,
                            state: RecyclerView.State
                        ) {
                            super.getItemOffsets(outRect, view, parent, state)
                            // Apply offset top to all except first item
                            if (parent.getChildAdapterPosition(view) != 0) {
                                outRect.left += downloadItemMarginRight
                            } else {
                                outRect.left = 0
                            }
                        }
                    })
                }

                ivViewAll.onClickDelay {
                    // mặc định block download chỉ có 1 item
                    item(absoluteAdapterPosition)?.let {
                        if(it.megaMenus.isNotEmpty()) {
                            megaMenuItemClickListener?.onClickedItem(
                                absoluteAdapterPosition,
                                it.megaMenus[0]
                            )
                        }

                    }

                }
                binding.ctlEmptyItems.onClick(500) {
                    // mặc định block download chỉ có 1 item
                    item(absoluteAdapterPosition)?.let {
                        if(it.megaMenus.isNotEmpty()) {
                            megaMenuItemClickListener?.onClickedItem(
                                absoluteAdapterPosition,
                                it.megaMenus[0]
                            )
                        }

                    }
                }
            }


        }

        fun bind(menu: MegaMenu.Block) {
            if(downloadData.isEmpty()) {
                bindDownloadEmpty(menu)
            } else {
                bindDownloadData(menu, downloadData)
            }

        }

        fun bind(menu: MegaMenu.Block, payloads: MutableList<Any>) {
            for (obj in payloads) {
                when (obj) {
                    is UpdateDownloadBlockItemRemove -> {
                        val pos = downloadAdapter.data()
                            .indexOfFirst { collectionVideoTaskItem -> collectionVideoTaskItem.movieId == obj.movieId }
                        if(pos != -1) {
                            downloadAdapter.removeIndex(pos) {
                                downloadAdapter.notifyItemRemoved(pos)
                                if(downloadAdapter.size() == 0) {
                                    bindDownloadEmpty(menu)
                                }
                            }
                        }
                    }
                }
            }
        }


        private fun bindDownloadEmpty(menu: MegaMenu.Block) {
            binding.apply {
//                tvTitle.checkToShowContent(menu.title, goneViewWhenNoText = true)
                tvTitle.hide()
                tvSubTitle.hide()
                ivViewAll.hide()
                rvDownload.hide()
                if(menu.megaMenus.isEmpty()) {
                    tvEmptyTitle.hide()
                    tvEmptySubtitle.hide()
                } else {
                    val data = menu.megaMenus[0]
                    tvEmptyTitle.checkToShowContent(data.title, goneViewWhenNoText = true)
                    tvEmptySubtitle.checkToShowContent(data.subTitle, goneViewWhenNoText = true)
                }
                ctlEmptyItems.show()
            }

        }

        private fun bindDownloadData(menu: MegaMenu.Block, downloadData: List<CollectionVideoTaskItem>) {
            binding.apply {
                ctlEmptyItems.hide()
                if(menu.megaMenus.isNotEmpty()) {
                    val data = menu.megaMenus[0]
                    tvTitle.checkToShowContent(data.title, goneViewWhenNoText = true)
                }
                ivViewAll.show()
                tvSubTitle.checkToShowContent(menu.subTitle, goneViewWhenNoText = true)

                downloadAdapter.bind(downloadData.toMutableList())  // copy data, not used real data incase more than 1 block of download
                if(rvDownload.visibility != View.VISIBLE)
                    rvDownload.show()
                // Currently hide view all
//                tvViewAll.hide()
            }

        }
    }


}