<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/transparent"
    android:layout_width="match_parent"
    android:paddingHorizontal="@dimen/_11sdp"
    android:layout_height="match_parent">
    <androidx.core.widget.NestedScrollView
        android:id="@+id/sv_container"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/btn_previous"
        android:layout_marginBottom="@dimen/_11sdp"
        android:layout_marginTop="@dimen/_83sdp"
        android:layout_width="match_parent"
        android:layout_height="0dp">
        <LinearLayout
            android:id="@+id/ll_container"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/tv_survey_title"
                tools:text="FPT Play mời bạn tham gia khảo sát"
                android:gravity="start"
                android:textSize="@dimen/_14ssp"
                android:textColor="@color/white_87"
                android:fontFamily="@font/sf_pro_display_semibold"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/tv_survey_description"
                tools:text="Trả lời câu hỏi để giúp FPT Play phục vụ bạn tốt hơn"
                android:textSize="@dimen/_11ssp"
                android:textColor="@color/white_60"
                android:fontFamily="@font/sf_pro_display_regular"
                android:gravity="start"
                android:layout_marginTop="@dimen/_11sdp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <androidx.recyclerview.widget.RecyclerView
                android:focusable="false"
                android:clickable="false"
                android:id="@+id/rv_list_answers"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_18sdp"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>


    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_previous"
        android:layout_width="0dp"
        android:layout_height="@dimen/_34sdp"
        android:layout_marginBottom="@dimen/_23sdp"
        android:text="@string/survey_back"
        android:textAllCaps="false"
        android:textSize="@dimen/_13ssp"
        android:textColor="@color/white_87"
        android:fontFamily="@font/sf_pro_display_semibold"
        android:layout_marginHorizontal="@dimen/_11sdp"
        android:background="@drawable/login_button_rectangle_disable"
        android:layout_marginEnd="@dimen/_4sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btn_continues"
        app:layout_constraintStart_toStartOf="parent"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_continues"
        android:layout_width="0dp"
        android:layout_height="@dimen/_34sdp"
        android:layout_marginBottom="@dimen/_23sdp"
        android:text="@string/survey_continues"
        android:textAllCaps="false"
        android:textSize="@dimen/_13ssp"
        android:textColor="@color/white_87"
        android:fontFamily="@font/sf_pro_display_semibold"
        android:layout_marginHorizontal="@dimen/_11sdp"
        android:background="@drawable/login_button_background"
        android:layout_marginStart="@dimen/_4sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_previous"/>

</androidx.constraintlayout.widget.ConstraintLayout>
