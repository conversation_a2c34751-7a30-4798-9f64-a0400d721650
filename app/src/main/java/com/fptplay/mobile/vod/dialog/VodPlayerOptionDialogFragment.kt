package com.fptplay.mobile.vod.dialog

import android.app.Dialog
import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseFullDialogFragment
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.DialogPlayerOptionBinding
import com.fptplay.mobile.player.dialog.PlayerOptionAdapter
import com.fptplay.mobile.player.dialog.data.AudioTrackPlayerData
import com.fptplay.mobile.player.dialog.data.BitratePlayerData
import com.fptplay.mobile.player.dialog.data.ExpandPlayerData
import com.fptplay.mobile.player.dialog.data.SubtitlePlayerData
import com.fptplay.mobile.vod.VodDetailViewModel
import com.tear.modules.player.util.PlayerControlView
import com.tear.modules.player.util.TrackType
import com.xhbadxx.projects.module.domain.entity.fplay.BaseObject
import com.xhbadxx.projects.module.domain.entity.fplay.common.PlayerSpeed
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class VodPlayerOptionDialogFragment : BaseFullDialogFragment<VodDetailViewModel.VodDetailState, VodDetailViewModel.VodDetailIntent>() {

    override val hasEdgeToEdge by lazy { if (context.isTablet()) false else MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE }
    override val viewModel: VodDetailViewModel by activityViewModels()

    override val handleBackPressed = true

    private var _binding: DialogPlayerOptionBinding? = null
    private val binding get() = _binding!!

    private val optionAdapter: PlayerOptionAdapter by lazy { PlayerOptionAdapter() }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialogDark)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = LayoutInflater.from(MainApplication.INSTANCE.applicationContext).inflate(R.layout.dialog_player_option, container, false)
        _binding = DialogPlayerOptionBinding.bind(view)
        return binding.root
    }
    
    override fun backHandler() {
        setFragmentResultEmptyData()
        super.backHandler()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        // Update container width immediately on orientation change
        refresh()
    }

    private fun refresh() {
        findNavController().currentDestination?.id?.let {
            findNavController().popBackStack(it, inclusive = true)
            findNavController().navigate(it)
        }
    }

    override fun bindData() {
        when (viewModel.getPlayerOptionsType()) {
            Utils.OPTION_DIALOG_BITRATE -> {
                // 1 recycler (include: bitrate)
                // end setup

                binding.tvTitle.text = getString(R.string.player_resolution)
                val data = viewModel.getCurrentPlayerBitrates() ?: listOf()
                data.reversed().run {
                    optionAdapter.bind(this.map { item ->
                        if (viewModel.getPlayerOptionsCurItem() == item.id) {
                            BitratePlayerData(id = item.id, title = item.name, resId = null, isSelected = true)
                        } else {
                            BitratePlayerData(id = item.id, title = item.name, resId = null)
                        }
                    })
                }

            }
            Utils.OPTION_DIALOG_SUBTITLE -> {
                // 1 or 2 recycler (include: subtitle and track)
                val data = getListTrack()
                val listSubtitle = data.first
                if (listSubtitle.isNotEmpty()) {
                    binding.tvTitle.text = getString(R.string.player_subtitle)
                    //===== Bind data ====
                    listSubtitle.run {
                        optionAdapter.bind(this.map { item ->
                            SubtitlePlayerData(id = item.id, name = item.name, iconVip = item.iconVip, trackGroupIndex = item.trackGroupIndex, trackIndex = item.trackIndex, isSelected = item.isSelected)
                        })
                    }
                    //==== End bind data ====
                }
                // end setup
            }

            Utils.OPTION_DIALOG_AUDIO -> {
                // 1 or 2 recycler (include: subtitle and track)
                val data = getListTrack()
                val listAudio = data.second
                if (listAudio.isNotEmpty()) {
                    binding.tvTitle.text = getString(R.string.player_track)
                    //===== Bind data ====
                    listAudio.run {
                        optionAdapter.bind(this.map { item ->
                            AudioTrackPlayerData(
                                id = item.id,
                                name = item.name,
                                iconVip = item.iconVip,
                                trackGroupIndex = item.trackGroupIndex,
                                trackIndex = item.trackIndex,
                                isSelected = item.isSelected
                            )
                        })
                    }
                }
            }

            Utils.OPTION_DIALOG_EXPAND -> {
                binding.tvTitle.text = getString(R.string.player_expand)
                val data = ExpandPlayerData.getDefaultData().map { item ->
                    if (viewModel.getPlayerOptionsCurItem() == item.resizeModeId.toString()) {
                        ExpandPlayerData(resizeModeId = item.resizeModeId, title = item.title, isVipRequired = item.isVipRequired, vipImage = item.vipImage, isSelected = true)
                    } else {
                        ExpandPlayerData(resizeModeId = item.resizeModeId, title = item.title, isVipRequired = item.isVipRequired, vipImage = item.vipImage, isSelected = false)
                    }
                }
                data[0].isSelected = viewModel.getPlayerOptionsCurItem() == "-1" && data.isNotEmpty()
                optionAdapter.bind(data)
            }
            Utils.OPTION_DIALOG_SPEED -> {
                // end setup
                binding.tvTitle.text = getString(R.string.player_speed)
                val data = viewModel.getListPlayerSpeed()
                optionAdapter.bind(data)
            }
            else -> {

            }
        }
    }


    override fun bindComponent() {
        binding.rvOptions.apply {
            adapter = optionAdapter
            layoutManager = LinearLayoutManager(binding.root.context, LinearLayoutManager.VERTICAL, false)
        }
        
        // Set llContainer width based on device type and orientation
        setContainerWidth()
    }
    
    private fun setContainerWidth() {
        val layoutParams = binding.llContainer.layoutParams
        val isTablet = context.isTablet()
        val isLandscape = MainApplication.INSTANCE.applicationContext.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
        
        when {
            isTablet -> {
                // Tablet + portrait & Tablet + landscape: 640px
                layoutParams.width = resources.getDimensionPixelSize(R.dimen._266sdp)
            }
            isLandscape -> {
                // Phone + landscape: 420px
                layoutParams.width = resources.getDimensionPixelSize(R.dimen._300sdp)
            }
            else -> {
                // Phone + portrait: match parent
                layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
            }
        }
        
        binding.llContainer.layoutParams = layoutParams
    }

    override fun bindEvent() {
        optionAdapter.eventListener = object : IEventListener<BaseObject> {
            override fun onClickedItem(position: Int, data: BaseObject) {
                when (data) {
                    is BitratePlayerData -> {
                        if (data.isSelected) return
                        setFragmentResult(
                            Utils.OPTION_DIALOG_BITRATE_KEY, bundleOf(
                                Utils.OPTION_DIALOG_BITRATE_ID_KEY to data.id,
                                Utils.OPTION_DIALOG_BITRATE_POSITION_KEY to position,
                            )
                        )
                    }
                    is SubtitlePlayerData -> {
                        if (data.isSelected) return
                        setFragmentResult(
                            Utils.OPTION_DIALOG_SUBTITLE_KEY,
                            bundleOf(Utils.OPTION_DIALOG_SUBTITLE_ID_KEY to data.id)
                        )
                    }
                    is AudioTrackPlayerData -> {
                        if (data.isSelected) return
                        setFragmentResult(
                            Utils.OPTION_DIALOG_AUDIO_TRACK_KEY,
                            bundleOf(Utils.OPTION_DIALOG_AUDIO_TRACK_ID_KEY to data.name)
                        )
                    }
                    is ExpandPlayerData -> {
                        if (data.isSelected) return
                        setFragmentResult(Utils.OPTION_DIALOG_EXPAND_KEY, bundleOf(Utils.OPTION_DIALOG_EXPAND_ID_KEY to data.resizeModeId, Utils.OPTION_DIALOG_EXPAND_POS_KEY to position))
                    }

                    is PlayerSpeed -> {
                        if(data.isSelected) return
                        setFragmentResult(
                            Utils.OPTION_DIALOG_SPEED_KEY,
                            bundleOf(Utils.OPTION_DIALOG_SPEED_ID_KEY to data.speed)
                        )
                    }
                }
                findNavController().popBackStack()
            }
        }

        binding.clContainer.setOnClickListener {
            setFragmentResultEmptyData()
            findNavController().popBackStack()
        }
    }


    override fun VodDetailViewModel.VodDetailState.toUI() {

    }

    private fun getListTrack() : Pair<List<PlayerControlView.Data.Track>, List<PlayerControlView.Data.Track>> {
        val tracks = viewModel.getTracks() ?: listOf()
        val listSubtitle = tracks.filter { item -> (item.type == TrackType.TEXT.ordinal || ((item.id == "Tắt phụ đề" || item.name == "Tắt phụ đề") && item.type == 10002))}
        val listAudio = tracks.filter { item -> (item.type == TrackType.AUDIO.ordinal) }
        return Pair(listSubtitle, listAudio)
    }

    private fun setFragmentResultEmptyData() {
        when (viewModel.getPlayerOptionsType()) {
            Utils.OPTION_DIALOG_BITRATE -> setFragmentResult(Utils.OPTION_DIALOG_BITRATE_KEY, bundleOf())
            Utils.OPTION_DIALOG_SUBTITLE -> setFragmentResult(Utils.OPTION_DIALOG_SUBTITLE_KEY, bundleOf())
            Utils.OPTION_DIALOG_AUDIO_TRACK_KEY -> setFragmentResult(Utils.OPTION_DIALOG_AUDIO_TRACK_ID_KEY, bundleOf())
            Utils.OPTION_DIALOG_AUDIO -> setFragmentResult(Utils.OPTION_DIALOG_AUDIO_TRACK_KEY, bundleOf())
            Utils.OPTION_DIALOG_EXPAND -> setFragmentResult(Utils.OPTION_DIALOG_EXPAND_KEY, bundleOf())
            Utils.OPTION_DIALOG_SPEED -> setFragmentResult(Utils.OPTION_DIALOG_SPEED_KEY, bundleOf())
            else -> { }
        }
    }

}