package com.fptplay.mobile.features.moments

import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.database.ContentObserver
import android.graphics.Color
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.SeekBar
import android.widget.Toast
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.work.Data
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.NavMomentDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.extensions.getDisplayHeight
import com.fptplay.mobile.common.extensions.getDisplayWidth
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.global.GlobalEvent
import com.fptplay.mobile.common.ui.bases.BaseActivity
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.view.GlobalSnackbarManager
import com.fptplay.mobile.common.utils.CheckBeforePlayUtil
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.DateTimeUtils
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.*
import com.fptplay.mobile.databinding.MomentsFragmentBinding
import com.fptplay.mobile.databinding.PageMomentErrorViewBinding
import com.fptplay.mobile.features.comment_v2.CommentV2ViewModel
import com.fptplay.mobile.features.comment_v2.data.CommentTrackingData
import com.fptplay.mobile.features.moments.data.Moment
import com.fptplay.mobile.features.moments.data.MomentAutoScrollData
import com.fptplay.mobile.features.moments.data.MomentData
import com.fptplay.mobile.features.moments.data.MomentStreamInfo
import com.fptplay.mobile.features.moments.data.MomentStreamLocalBookmarkInfo
import com.fptplay.mobile.features.moments.utils.*
import com.fptplay.mobile.features.moments.view.MomentSeekbar
import com.fptplay.mobile.features.moments.view.player_recycler_view.PlayerRecyclerView
import com.fptplay.mobile.features.short_video.DataCacheObject
import com.fptplay.mobile.features.short_video.ShortVideosContentFragment
import com.fptplay.mobile.features.short_video.ShortVideosFragment
import com.fptplay.mobile.player.utils.gone
import com.fptplay.mobile.player.utils.isEnableAutoScreenRotationInSettings
import com.fptplay.mobile.player.utils.visible
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.common.AutoScrollConfig
import com.xhbadxx.projects.module.domain.entity.fplay.moment.MomentDetail
import com.xhbadxx.projects.module.domain.entity.fplay.moment.MomentReaction
import com.xhbadxx.projects.module.domain.entity.fplay.moment.PlaylistMomentDetail
import com.xhbadxx.projects.module.domain.entity.fplay.moment.StreamMomentDataV2
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.collections.ArrayList
import kotlin.math.min

@AndroidEntryPoint
class MomentsFragment :
    BaseFragment<MomentsViewModel.MomentsState, MomentsViewModel.MomentsIntent>() {

    override val hasEdgeToEdge: Boolean = true
    override val handleBackPressed: Boolean = true

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    private val safeArgs: MomentsFragmentArgs by navArgs()
    private var momentToProcessLike: MomentDetail? = null
    private var momentToProcessComment: MomentDetail? = null

    private var momentPositionLike: Int = -1
    private var momentPositionComment: Int = -1

    private val screenWidth: Int by lazy { min(MainApplication.INSTANCE.applicationContext.getDisplayWidth(), MainApplication.INSTANCE.applicationContext.getDisplayHeight()) }
    private val screenHeight: Int by lazy { (screenWidth * 1.0 * 16.0 / 9.0).toInt() }

    override val viewModel: MomentsViewModel by activityViewModels()
    private val viewModelCommentV2: CommentV2ViewModel by activityViewModels()
    private var _binding: MomentsFragmentBinding? = null
    private val binding get() = _binding!!
    private val totalItemInPage by lazy { MainApplication.INSTANCE.appConfig.numItemOfMoment }
    private val totalItemInPlaylistPage by lazy { MainApplication.INSTANCE.appConfig.numItemOfPlaylistMoment }
    private val itemInRow: Int by lazy { Constants.MOMENT_ITEM_IN_ROW }
    private val preloadOffset: Int by lazy { Constants.MOMENT_PRELOAD_OFFSET }
    private val momentAdapter by lazy {
        MomentAdapter(binding.root.context, showMetadata = safeArgs.showMetadata, isPlaylistLayout = safeArgs.isPlaylistLayout, clickItem = safeArgs.clickItem).apply {
            triggerAutoPlay = {
                if(_binding != null) {
//                    Timber.tag("tam-moment").i("binding.root.afterMeasured triggerAutoPlay")
                    binding.vpMoments.start()
                }
            }
        }
    }

    private val moments: ArrayList<MomentDetail> = ArrayList()
    private val loadMoreHandler: LoadMorePreloadHandler by lazy {
        LoadMorePreloadHandler(
            totalItem = momentAdapter.size() ,
            totalItemInPage = totalItemInPage,
            totalItemInRow = itemInRow,
            preloadOffset = preloadOffset,
            onScroll = { page ->
                val lastMomentIndex = momentAdapter.size() - 1
                val lastMomentId = momentAdapter.itemId(lastMomentIndex)
                viewModel.dispatchIntent(
                    MomentsViewModel.MomentsIntent.GetListMomentsV2WithBookmark(momentId = shortVideoId, chapterId = shortVideoChapter, page = page, perPage = totalItemInPage, relatedID = safeArgs.relatedId, lastMomentId = lastMomentId, tabId = tabId)
                )
            }
        )
    }
    private val playlistLoadMoreHandler: MultipleLoadMorePreloadHandler by lazy {
        MultipleLoadMorePreloadHandler(
            totalItem = momentAdapter.size() ,
            totalItemInPage = totalItemInPlaylistPage,
            totalItemInRow = itemInRow,
            preloadOffset = preloadOffset,
            onScroll = { page ->
                viewModel.dispatchIntent(
                    MomentsViewModel.MomentsIntent.GetMomentPlaylist(
                        playlistId = shortVideoId,
                        page = page,
                        perPage = totalItemInPlaylistPage,
                        addToFirst = page < viewModel.getPlaylistCurrentPage(),
                        feature = Constants.MOMENT_FEATURE_SHORT_VOD
                    )
                )
                viewModel.savePlaylistCurrentPage(page)
            }
        )
    }

    private val playlistEpisode: ArrayList<PlaylistMomentDetail.PlaylistEpisode> = ArrayList()

    private var isFirstInit = true

    // auto scroll handler

    private var isCountDownStopped = true
    private var countDownTimerAutoScroll : CountDownTimer ?= null
    private val countdownTimeAutoScrollMs by lazy {  TimeUnit.SECONDS.toMillis(COUNT_DOWN_TIME_FOR_AUTO_SCROLL_IN_SECONDS) }
    private var runnableCheckAutoScroll = Runnable {
        checkStartCountdownAutoScroll()
    }
    private var handlerCheckAutoScroll : Handler?= null
    private var autoScrollData: MomentAutoScrollData? = null
    private var curIndex:Int = 0

    private var tabId = Constants.SHORT_VIDEO_TAB_ID_DEFAULT //default
    private var indexTab = 0

    private var pageMomentErrorViewBinding: PageMomentErrorViewBinding? = null

    private var shortVideoId = "0"
    private var shortVideoChapter = "0"

    private var dataSave = MomentData() //case danh cho ko phai tablayout: deeplink, click item home, voddetail,...

    private var logKibana:ShortVideosLogKibana? = null
    private var isSharing:Boolean = false


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = MomentsFragmentBinding.inflate(inflater, container, false)
        binding.root.keepScreenOn = true
        return binding.root
    }


    override fun onDestroyView() {
        super.onDestroyView()
        resetOrientation()
        trackingInfo.updatePlayingSession(time = 0)
        viewModel.playlistMomentDetail = null
        viewModel.savePlaylistCurrentPage(1)
        GlobalEvent.unRegisterEvent(GlobalEvent.LIKE_MOMENT_VIDEO_EVENT)
        momentAdapter.release()
        binding.root.keepScreenOn = false
        _binding = null
        pageMomentErrorViewBinding = null
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycle.addObserver(checkBeforePlayUtil)

    }

    override fun backHandler() {
        if(viewModel.isTabHomeLayout) //detail playlist or not tab Short
            checkExit()
        else{
            super.backHandler()
        }
    }

    private var backCount = 0
    private var lastBackTime = 0L
    private fun checkExit() {
        backCount += 1
        if (backCount == 1) {
            Toast.makeText(context, getString(R.string.press_back_2_times_to_exit_app), Toast.LENGTH_SHORT).show()
            lastBackTime = System.currentTimeMillis()
        } else if (backCount > 1) {
            if (System.currentTimeMillis() - lastBackTime < 1000L) {
                activity?.finish()
            } else {
                Toast.makeText(context, getString(R.string.press_back_2_times_to_exit_app), Toast.LENGTH_SHORT).show()
                lastBackTime = System.currentTimeMillis()
                backCount = 1
            }
        }
    }

    override fun onResume() {
        super.onResume()
        logKibana?.setIsRecommendValue(safeArgs.sourceScreen)
        if (isFirstInit) {
            if(shortVideoId.isEmpty() || shortVideoChapter.isEmpty()) {
                showPageMomentError(
                    errorMessage = getString(R.string.moment_error_missing_params_des),
                    errorTitle = getString(R.string.moment_error_missing_params_title),
                    btRetryText = getString(R.string.go_to_home),
                    onBtnRetry = {
                        parentFragment?.parentFragment?.findNavController()?.navigate(NavHomeMainDirections.actionGlobalToHome())
                    }
                )
                return
            }
            if(safeArgs.isPlaylistLayout) {
                val currentPage = getCurrentPage()
                viewModel.dispatchIntent(
                    MomentsViewModel.MomentsIntent.GetMomentPlaylist(
                        playlistId = shortVideoId,
                        page = currentPage,
                        perPage = totalItemInPlaylistPage,
                        isBind = true,
                        addToFirst = currentPage == 1,
                        feature = Constants.MOMENT_FEATURE_SHORT_VOD
                    )
                )
            } else {
                if(safeArgs.clickItem){ //click item -> load new from api
                    logKibana?.setDataLogSendFirstItem(safeArgs.sourceScreen) //set data first item is click
                    loadListMomentFromApi()
                }else{
                    loadDataCache()
                }

            }
        }else{
            if(!isSharing) { //onresume by sharing do nothing
                if (viewModel.isTabHomeLayout) {
                    if (indexTab == DataCacheObject.dataCache.getCurFocusTabIndex()) {
                        if (binding.vpMoments.isPauseVideo()) {
                            binding.vpMoments.playVideo()
                        } else {
                            val data = DataCacheObject.dataCache.getMomentWithTab(tabId)
                            data?.let {
                                if (data.listMoment.isNotEmpty()) {
                                    //play lai tu bookmark save truoc do
                                    binding.vpMoments.setMomentStreamLocalBookmarkInfo(
                                        MomentStreamLocalBookmarkInfo(
                                            itemId = data.curItemId,
                                            startPosition = data.curTimePlaying,
                                            isPlayerPlaying = true
                                        )
                                    )
                                }
                            }
                            binding.vpMoments.restart()
                        }
                    }
                } else {
                    if (binding.vpMoments.isPauseVideo()) {
                        binding.vpMoments.playVideo()
                    } else {
                        if (dataSave.curItemId.isNotEmpty()) {
                            //play lai tu bookmark save truoc do
                            binding.vpMoments.setMomentStreamLocalBookmarkInfo(
                                MomentStreamLocalBookmarkInfo(
                                    itemId = dataSave.curItemId,
                                    startPosition = dataSave.curTimePlaying,
                                    isPlayerPlaying = true
                                )
                            )
                        }
                        binding.vpMoments.restart()
                    }
                }
            }
        }
        isSharing = false
    }

    private fun getCurrentPage(): Int {
        val currentPage = (safeArgs.indexForPlaylist / totalItemInPlaylistPage) + if (safeArgs.indexForPlaylist % totalItemInPlaylistPage > 0) 1 else 0
        Timber.tag("tam-moment").d("indexNew: ${safeArgs.indexForPlaylist} totalItemInPage: $totalItemInPlaylistPage  getCurrentPage $currentPage")
        viewModel.savePlaylistCurrentPage(currentPage)
        return currentPage
    }

    override fun onDestroy() {
        super.onDestroy()
        lifecycle.removeObserver(checkBeforePlayUtil)

    }

    override fun onPause() {
        if(!viewModel.isReloading) { //if is sharing, and reloading do nothing
            if (viewModel.isTabHomeLayout) {
                DataCacheObject.dataCache.saveListMoment(
                    data = momentAdapter.data(),
                    tabId = tabId,
                    curIndex = curIndex,
                    curItemId = momentAdapter.itemId(curIndex) ?: "",
                    curTimePlaying = if (binding.vpMoments.currentDuration() >= binding.vpMoments.totalDuration()) 0 else binding.vpMoments.currentDuration()
                )
                if (!isSharing) { //if is sharing ko pause
                    binding.vpMoments.executeWhenOnPause()
                }
            } else {
                dataSave = MomentData(
                    curIndex = curIndex,
                    curItemId = momentAdapter.itemId(curIndex) ?: "",
                    curTimePlaying = if (binding.vpMoments.currentDuration() >= binding.vpMoments.totalDuration()) 0 else binding.vpMoments.currentDuration()
                )
            }
        }
        super.onPause()
    }

    override fun onStop() {
        removeHandlerAndCountDownSkipCredits()
        binding.vpMoments.executeWhenOnStop()
        if(isSharing) { //when is sharing -> player still playing (onstop get real currentDuration)
            if(viewModel.isTabHomeLayout) {
                DataCacheObject.dataCache.saveCurTimePlayingWithTabId(tabId, binding.vpMoments.currentDuration)
            }else dataSave.curTimePlaying = binding.vpMoments.currentDuration
            isSharing = false
        }
        super.onStop()
    }
    override fun bindComponent() {
        binding.vpMoments.apply {
            init(viewLifecycleOwner)
            setAdapter(momentAdapter)
            val seekbar = MomentSeekbar(context)
            setSeekbar(seekbar)
            loadMoreHandler = if (safeArgs.isPlaylistLayout) this@MomentsFragment.<NAME_EMAIL>
        }
        if(context.isTablet()) {
          handleScreenRotation()
        }
    }
    override fun bindEvent() {
        observeData()
        binding.vpMoments.apply {
            onPageChangeCallback = object : PlayerRecyclerView.OnPageChangeCallback {
                override fun onPageSelected(position: Int) {
                    momentAdapter.onMomentSelected(position)
                    curIndex = position
                    if(safeArgs.isPlaylistLayout) {
                        val episodeId = momentAdapter.itemId(position)  // in playlistLayout content id is episode id
                        Timber.tag("tam-moment").e("MomentsFragment onPageSelected $position - $episodeId")
                        setFragmentResult(
                            Utils.MOMENT_SELECTED_BUNDLE_KEY,
                            bundleOf(Utils.MOMENT_SELECTED_EPISODE_ID_KEY to episodeId)
                        )
                    }

                }

                override fun onMomentRepeated(position: Int) {
//                    trackingInfo.updatePlayingSession(time = System.currentTimeMillis())
                }

                override fun requestStreamUrl(position: Int) {
                    viewModel.isReloading = false
                    val moment = momentAdapter.item(position)

                    if (moment == null) {
                        binding.vpMoments.handleStreamUrlV2(null)
                        return
                    }
                    val momentId = moment.content.momentId
                    val currentChapterId = moment.content.currentChapterId
                    val streamMomentData = moment.content.currentStreamMomentDataV2
                    if (streamMomentData == null) {
                        if (momentId.isEmpty() || currentChapterId.isNullOrEmpty()) {
                            binding.vpMoments.handleStreamUrlV2(null)
                        } else {
                            viewModel.dispatchIntent(
                                MomentsViewModel.MomentsIntent.GetMomentStreamUrlV2(
                                    momentId = momentId,
                                    moment = moment,
                                    momentPosition = position,
                                    chapterId = currentChapterId,
                                    tabId = tabId
                                )
                            )
                        }
                    } else {
                        binding.vpMoments.handleStreamUrlV2(MomentStreamInfo(
                            itemId = moment.content.id,
                            url = moment.content.streamUrl,
                            streamMomentDataV2 =  streamMomentData,
                            streamSession = moment.content.streamSession,
                            streamUnavailable = streamMomentData.isUnavailable(),
                        ), moment = moment, playlistId = if(safeArgs.isPlaylistLayout) shortVideoId else "")


                        // Preload
                        val url =
                            streamMomentData.urlDashH265.ifBlank { streamMomentData.urlHlsH265 }.ifBlank {
                                streamMomentData.urlDashH264.ifBlank { streamMomentData.urlHlsH264 }
                            }
                        preCacheMoment(moment = moment, urls = arrayOf(url), imgUrls = listOf(moment.content.placeHolder))
                    }

                }
                override fun retryStreamUrl(position: Int) {
                    viewModel.isReloading = false
                    val moment = momentAdapter.item(position)
                    val id = moment?.content?.momentId
                    val currentChapterId = moment?.content?.currentChapterId
                    if (moment == null || id == null || currentChapterId.isNullOrEmpty()) return
                    viewModel.dispatchIntent(
                        MomentsViewModel.MomentsIntent.GetMomentStreamUrlV2(
                            momentId = id,
                            moment = moment,
                            chapterId = currentChapterId,
                            momentPosition = position,
                            tabId = tabId
                        )
                    )
                }
            }

            momentAutoScrollCallback = object : MomentAutoScrollCallback {
                override fun onMomentEnded(currPosition: Int) {
                    autoScrollData = MomentAutoScrollData(currPosition)
                    handleAutoScroll()

                }
                override fun onMomentReady(currPosition: Int) {
                    val moment = momentAdapter.item(currPosition)
                    if(!contentCanScroll(moment)) {
                        initCheckAutoScroll()
                        autoScrollData = MomentAutoScrollData(currPosition)
                        checkStartCountdownAutoScroll()
                    }
                }

                override fun onMomentStart(currPosition: Int) {
                    val moment = momentAdapter.item(currPosition)
                    if(!contentCanScroll(moment)) {
                        autoScrollData = MomentAutoScrollData(currPosition)
                        checkStartCountdownAutoScroll()
                    }

                }

                override fun onMomentPlay(currPosition: Int) {
                    val moment = momentAdapter.item(currPosition)
                    if(!contentCanScroll(moment)) {
                        autoScrollData = MomentAutoScrollData(currPosition)
                        checkStartCountdownAutoScroll()
                    }

                }

                override fun onSeekbarStartTrackingTouch(seekBar: SeekBar?) {
                    stopCountDownTimerAutoScroll()
                }

                override fun onMomentPause(currPosition: Int) {
                    stopCountDownTimerAutoScroll(hideCountDown = false)
                }
            }
        }
        momentAdapter.eventListener = object : IEventListener<MomentDetail> {
            override fun onClickView(position: Int, view: View?, data: MomentDetail) {
                if (_binding == null) {
                    Timber.tag("tam-moment").e("MomentsFragment onClickView binding null ")
                    return
                }
                when (view?.id) {
                    R.id.ctl_metadata -> {
                        if(!safeArgs.showMetadata) return //case vod detail
                        if(!onRelatedMomentClick(data)){ //link to vod
                            if(safeArgs.isPlaylistLayout) { //detail playlist
                                findNavController().navigateSafe(
                                    MomentsFragmentDirections.actionMomentsFragmentToMomentPlaylistBottomSheetFragment(
                                        playlistId = data.content.momentId,
                                        currPlaylistEpisodeId = data.content.currentChapterId
                                    )
                                )

                            } else {
                                if(data.content.typeContent == MomentDetail.Content.MomentContentType.Series) { //là playlist
                                    var startPosition = 0L
                                    var isPlaying = true
                                    try {
                                        startPosition = binding.vpMoments.currentDuration()
                                        isPlaying = binding.vpMoments.isPlaying() ?: true   // if isPlaying null -> player not initalized -> default true

                                    } catch (e: Exception) {

                                    }
                                    navigateToPlaylist(data, startPosition, isPlaying, startNextEpisode = false, openListEpisodeDialog = true)
                                }
                            }
                        }
                    }
                    R.id.ib_back -> {
                        backHandler()
                    }
                    R.id.ll_share-> {
                        logKibana?.sendLogReaction(ShortVideosLogKibana.ShortVideosReaction.Share)
                        val link = "${getString(R.string.base_url_deep_link_moment_share)}/${data.content.momentId}/${data.content.currentChapterId}"
                        onShareLink(link)
                    }
                    R.id.playerLayoutTouch,
                    R.id.ib_play -> {
                        binding.vpMoments.togglePlayPause()
                    }
                    R.id.ll_like -> {
                        logKibana?.sendLogReaction(ShortVideosLogKibana.ShortVideosReaction.Like)
                        saveDataLike(data, position, false)
                        processLikeMoment(data, position, isLocalUpdate = true, isDoubleTap = false)
                    }
                    R.id.ll_comment -> {
                        momentToProcessComment = data
                        momentPositionComment = position
                        openComment(momentId = data.content.momentId, chapterId = data.content.currentChapterId)
                    }
                }
            }
        }
        momentAdapter.momentEventListener = object : MomentEventListener<MomentDetail> {
            override fun onDoubleTap(position: Int, data: MomentDetail) {
                Timber.d("thien test double tap here")
                saveDataLike(data, position, true)
                processLikeMoment(data, position, isLocalUpdate = true, isDoubleTap = true)
                logKibana?.sendLogReaction(ShortVideosLogKibana.ShortVideosReaction.Like)
            }
        }
        if(viewModel.isTabHomeLayout){
            setFragmentListener(parentFragment?.parentFragment?.parentFragment?.parentFragment) //nav host, shortvideocontent, shortvideo, home
        }else{
            setFragmentListener(this)
        }

    }

    private fun observeData() {
        viewModelCommentV2.state.observe(viewLifecycleOwner) {
            when (it) {
                is CommentV2ViewModel.CommentState.UpdateTrackingData -> {
                    val commentTrackingData = updateCommentTrackingData(it.data)
                    if (it.sendTrackingAfterUpdate) {
                        viewModelCommentV2.dispatchIntent(CommentV2ViewModel.CommentIntent.TriggerSendTrackingLog(commentTrackingData))
                    } else {
                        viewModelCommentV2.dispatchIntent(CommentV2ViewModel.CommentIntent.UpdateAndSendTrackingComplete)
                    }
                }
                else -> {}
            }
        }
    }

    private fun updateCommentTrackingData(data: CommentTrackingData): CommentTrackingData {
        var trackingData = data
        logKibana?.let {
            trackingData = it.updateCommentTrackingData(data)
            viewModelCommentV2.trackingData = trackingData
        }
        return trackingData
    }

    private fun setFragmentListener(fragment:Fragment?){
//not use
//        fragment?.setFragmentResultListener(Constants.CHECK_REQUIRE_VIP) { key, bundle ->
//            checkBeforePlayUtil.onFragmentResult(key, bundle)
//        }

        fragment?.setFragmentResultListener(Constants.WARNING_REQUEST_KEY){ _, bundle ->
            if(bundle.getBoolean(Constants.WARNING_RESULT)){
//                binding.vpMoments.pauseVideo()
            }else{ //back from dialog
                DataCacheObject.dataLikeCommentCache.resetData()
            }
        }
        setFragmentResultListener(Utils.MOMENT_COMMENT_BUNDLE_KEY) { _, bundle ->
            val momentId = bundle.getString(Utils.MOMENT_COMMENT_BUNDLE_ID_KEY)
            val totalComment = bundle.getString(Utils.MOMENT_COMMENT_BUNDLE_TOTAL_KEY)
            Timber.tag("tulog-moment").d("momentId: $momentId  && total comment : $totalComment")
            totalComment?.let {
                momentAdapter.notifyItemChanged(momentPositionComment, UpdateCommentStatus(it))
                momentPositionComment = -1
            }
        }

        setFragmentResultListener(Utils.MOMENT_SEND_COMMENT_BUNDLE_KEY) { _, bundle ->
            val momentId = bundle.getString(Utils.MOMENT_SEND_COMMENT_STATUS_BUNDLE_KEY)
            if (momentId == momentToProcessComment?.content?.id) {
                logKibana?.sendLogReaction(ShortVideosLogKibana.ShortVideosReaction.Comment)
            }
            momentToProcessComment = null
        }


        setFragmentResultListener(Utils.MOMENT_SELECT_PLAYLIST_EPISODE_BUNDLE_KEY) { _, bundle ->
            val episodeId = bundle.getString(Utils.MOMENT_SELECT_PLAYLIST_EPISODE_ID_KEY)
            var position = -1
            for((index, momentDetail) in momentAdapter.data().withIndex()) {
                if(momentDetail.content.id == episodeId) {
                    position = index
                    break
                }
            }

            Timber.tag("tam-moment").w("MOMENT_SELECT_PLAYLIST_EPISODE $position")
            scrollToMoment(position)

        }

        // to like when login success on tablet
        fragment?.setFragmentResultListener(Constants.LOGIN_SUCCESS) { _, bundle ->
            val isSuccess = bundle.getBoolean(Constants.LOGIN_SUCCESS_KEY, false)
            if (isSuccess) {
                momentToProcessLike?.let {
                    processLikeMoment(
                        it,
                        momentPositionLike,
                        isLocalUpdate = true,
                        isDoubleTap = false
                    )
                }
            }
            else{
                momentToProcessComment?.let { process ->
                    openComment(momentId = process.content.momentId, chapterId = process.content.currentChapterId)
                }
                DataCacheObject.dataLikeCommentCache.resetData()
            }
        }

    }

    private fun processLikeCommentCache(){
        if(DataCacheObject.dataLikeCommentCache.dataActive){
            if (DataCacheObject.dataLikeCommentCache.actionStatus == 0) { //like
                viewModel.dispatchIntent(MomentsViewModel.MomentsIntent.UpdateMomentLikeStatusV2Cache(
                    DataCacheObject.dataLikeCommentCache.itemId,
                    DataCacheObject.dataLikeCommentCache.chapterId,
                    DataCacheObject.dataLikeCommentCache.statusLikeLocal,
                    tabId = tabId))
            } else if (DataCacheObject.dataLikeCommentCache.actionStatus == 1) { //comment
                //khong xu ly gi het
            }
            DataCacheObject.dataLikeCommentCache.dataActive = false
        }

    }
    private fun checkHaveItemCache(moment:MomentDetail):Boolean{
        if (moment.content.momentId == DataCacheObject.dataLikeCommentCache.itemId &&
            moment.content.chapterId == DataCacheObject.dataLikeCommentCache.chapterId
        ) return true
        return false
    }

    private fun saveDataLike(moment: MomentDetail, position: Int, isDoubleTap: Boolean){
        if(NetworkUtils.isNetworkAvailable()) {
            momentToProcessLike = moment //save lai vi chua chac da login - co the bi da thiet bi
            momentPositionLike = position
            DataCacheObject.dataLikeCommentCache = ShortVideoLikeCommentCache(
                dataActive = false,
                tabId = DataCacheObject.dataCache.getCurTabId() ?: "",
                actionStatus = 0, //like
                itemId = moment.content.momentId,
                chapterId = moment.content.chapterId,
                statusLikeLocal = if (isDoubleTap) "1" else if (moment.like.statusLikeLocal == "1") "0" else "1"
            )
        }
    }
    private fun processLikeMoment(moment: MomentDetail, position: Int, isLocalUpdate: Boolean, isDoubleTap: Boolean) {
        if(NetworkUtils.isNetworkAvailable()){
            if(sharedPreferences.userLogin()) {
                if(isLocalUpdate){
                    if (isDoubleTap && moment.like.statusLikeLocal == "1") {
                        // do nothing
                    }
                    else {
                        if(isDoubleTap) moment.like.statusLikeLocal = "1"
                        else moment.like.statusLikeLocal = if (moment.like.statusLikeLocal == "1") "0" else "1"
                        try {
                            if(moment.like.statusLikeLocal == "1") moment.like.countLocal = (moment.like.countLocal.toInt() + 1).toString()
                            else moment.like.countLocal = (moment.like.countLocal.toInt() - 1).toString()
                        } catch(e: NumberFormatException) { }
                        viewModel.dispatchIntent(MomentsViewModel.MomentsIntent.UpdateMomentLikeStatusV2(moment.content.momentId,
                            moment.content.currentChapterId, moment.like.statusLikeLocal, moment, tabId = tabId))
                    }
                }
                momentAdapter.notifyItemChanged(position, UpdateLikeStatus(moment.like.countLocal))
            }
            else {
                if(viewModel.isTabHomeLayout)
                    parentFragment?.parentFragment?.navigateToLoginWithParams(isDirect = false)
                else
                    navigateToLoginWithParams(isDirect = false)
            }

        } else {
            showWarningDialog(getString(R.string.error_no_internet), textConfirm = getString(R.string.understood))
        }
    }

    // region handle screen rotation
    private fun handleScreenRotation() {
        activity?.run {
            if (isEnableAutoScreenRotationInSettings()) {
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR
            }
        }
        context?.contentResolver?.registerContentObserver(Settings.System.getUriFor(Settings.System.ACCELEROMETER_ROTATION), true, rotationObserver)
    }

    private var isLandscapeMode = false
    private val rotationObserver = object : ContentObserver(Handler(Looper.getMainLooper())) {
        override fun onChange(selfChange: Boolean) {
            activity?.run {
                if (isEnableAutoScreenRotationInSettings()) {
                    requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR
                } else {
                    if (isLandscapeMode) {
                        activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
                    } else {
                        activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                    }
                }
            }
        }
    }

    private fun resetOrientation() {
        if (context.isTablet()) {
            activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        } else {
            activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
    }
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        when (newConfig.orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> {
                if (context.isTablet()) {
                    isLandscapeMode = true
                }
            }
            Configuration.ORIENTATION_PORTRAIT -> {
                if (context.isTablet()) {
                    isLandscapeMode = false
                }
            }
            else -> {}
        }
    }

    // endregion handle screen rotation

    private fun onShareLink(url: String) {
        isSharing = true
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, url)
            type = "text/plain"
        }
        val shareIntent = Intent.createChooser(sendIntent, getString(R.string.share))
        context?.startActivity(shareIntent)
    }

    override fun retryLoadPage() {
        if(safeArgs.isPlaylistLayout) {
            val currentPage = getCurrentPage()
            viewModel.dispatchIntent(
                MomentsViewModel.MomentsIntent.GetMomentPlaylist(
                    playlistId = shortVideoId,
                    page = currentPage,
                    perPage = totalItemInPage,
                    isBind = true,
                    addToFirst = currentPage == 1,
                    feature = Constants.MOMENT_FEATURE_SHORT_VOD
                )
            )
        } else {
            viewModel.dispatchIntent(
                MomentsViewModel.MomentsIntent.GetListMomentsV2WithBookmark(
                    momentId = shortVideoId,
                    chapterId = shortVideoChapter,
                    page = 1,
                    perPage = totalItemInPage,
                    relatedID = safeArgs.relatedId,
                    fromDeeplink = safeArgs.isFromDeeplink,
                    tabId = tabId
                )
            )
        }
    }

    override fun initData() {
        logKibana = ShortVideosLogKibana(trackingProxy, trackingInfo)
        logKibana?.let {
            binding.vpMoments.setShortLogKibana(it)
        }

        if(safeArgs.isPlaylistLayout || safeArgs.clickItem){
            viewModel.isTabHomeLayout = false
            shortVideoId = safeArgs.momentId
            shortVideoChapter = safeArgs.chapterId

            if(safeArgs.isPlaylistLayout) {
                // only set bookmark local if in playlist
                if(safeArgs.startNextEpisode) logKibana?.setAutoScroll()
                binding.vpMoments.setMomentStreamLocalBookmarkInfo(
                    MomentStreamLocalBookmarkInfo(
                        itemId = shortVideoChapter,
                        startPosition = safeArgs.startPosition,
                        isPlayerPlaying = safeArgs.isPlaying
                    )
                )
            }
        }else {
            viewModel.isTabHomeLayout = true
            if(DataCacheObject.dataLikeCommentCache.dataActive){
                shortVideoId = DataCacheObject.dataLikeCommentCache.itemId
                shortVideoChapter = DataCacheObject.dataLikeCommentCache.chapterId
                processLikeCommentCache()
            }else{
                resetShortVideoIdAndChapter()
            }

            if(parentFragment?.parentFragment is ShortVideosContentFragment){
                indexTab = (parentFragment?.parentFragment as ShortVideosContentFragment).getPosition()
                tabId = DataCacheObject.dataCache.getTabWithIndex(indexTab)?.id?: ""
            }
        }
    }

    override fun MomentsViewModel.MomentsState.toUI() {
        //case nay danh cho call api chua finish ma chuyen tab khac -> khong nhan data process va reset state loadmore
        if((viewModel.isTabHomeLayout && indexTab != DataCacheObject.dataCache.getCurFocusTabIndex())){
            hideLoading()
            loadMoreHandler.endLoading()
            return
        }
        when(this) {
            is MomentsViewModel.MomentsState.Loading -> {
                when(this.intent) {
                    is MomentsViewModel.MomentsIntent.GetListMomentsV2WithBookmark->{
                        if(tabId != intent.tabId) return
                        showLoading()
                    }
                    is MomentsViewModel.MomentsIntent.GetMomentPlaylist -> {
                        showLoading()
                    }
                    else -> {}
                }
            }
            is MomentsViewModel.MomentsState.ResultListMoments -> {
                if(tabId != tabIdCall) return
                hideLoading()
                if (this.data.isNotEmpty()) {
                    Timber.tag("tam-moment").i("Load more refresh with ${data[0].content.momentId} adapter size: ${momentAdapter.size()} isBind: $isBind")
                    moments.addAll(this.data)
                    val realList = this.data.validateAndFilter()
                    Timber.tag("tam-moment").i("Load more api list size ${this.data.size} - realList size: ${realList.size}")
                    if(realList.isNotEmpty()) {
                        momentAdapter.add(data = realList, isBind = this.isBind) {
                            loadMoreHandler.refresh(
                                totalItem = momentAdapter.size(),
                                endPage = this.data.size < totalItemInPage // check if data from api is enough for another page, not the filter list
    //                            endPage = false
                            )
                        }
                    } else {
                        loadMoreHandler.refresh(
                            totalItem = momentAdapter.size(),
                            endPage = true
                        )

                        if(isBind) {
                            showPageMomentError(
                                errorMessage = getString(R.string.moment_error_api_des),
                                errorTitle = getString(R.string.moment_error_api_title)
                            )
                        }
                    }

                } else {
                    loadMoreHandler.refresh(
                        totalItem = momentAdapter.size(),
                        endPage = true
                    )

                    if(isBind) {
                        // empty page 1
                        showPageMomentError(
                            errorMessage = getString(R.string.moment_error_api_des),
                            errorTitle = getString(R.string.moment_error_api_title)
                        )
//                        showPageMomentError(binding.flError,getString(R.string.server_error))
                    }
                }
            }
            is MomentsViewModel.MomentsState.ResultListMomentsWithBookmark -> {
                if(tabId != tabIdCall) return
                hideLoading()
                if (this.data.isNotEmpty()) {
                    Timber.tag("tam-moment").i("Load more refresh with ${data[0].content.momentId} adapter size: ${momentAdapter.size()} isBind: $isBind")
                    moments.addAll(this.data)
                    val realList = this.data.validateAndFilter()
                    Timber.tag("tam-moment").i("Load more api list size ${this.data.size} - realList size: ${realList.size}")
                    if(realList.isNotEmpty()) {
                        momentAdapter.add(data = realList, isBind = this.isBind) {
                            loadMoreHandler.refresh(
                                totalItem = momentAdapter.size(),
                                endPage = this.data.size < totalItemInPage // check if data from api is enough for another page, not the filter list
    //                            endPage = false
                            )
                        }

                        if(listMomentHaveBookmark.isNotEmpty()) {
                            viewModel.dispatchIntent(
                                MomentsViewModel.MomentsIntent.GetMomentReaction(
                                    listMomentsNeedToFetchReaction = this.listMomentHaveBookmark,
                                    tabId = tabId
                                )
                            )
                        }
                        isFirstInit = false
                    } else {
                        loadMoreHandler.refresh(
                            totalItem = momentAdapter.size(),
                            endPage = true
                        )

                        if(isBind) {
                            // empty page 1
                            showPageMomentError(
                                errorMessage = getString(R.string.moment_error_api_des),
                                errorTitle = getString(R.string.moment_error_api_title)
                            )
//                            showPageMomentError(binding.flError,getString(R.string.server_error))
                        }
                    }

                } else {
                    loadMoreHandler.refresh(
                        totalItem = momentAdapter.size(),
                        endPage = true
                    )

                    if(isBind) {
                        // empty page 1
                        showPageMomentError(
                            errorMessage = getString(R.string.moment_error_api_des),
                            errorTitle = getString(R.string.moment_error_api_title)
                        )
//                        showPageMomentError(binding.flError,getString(R.string.server_error))
                    }
                }
            }
//            is MomentsViewModel.MomentsState.ResultMomentStreamUrl -> {
//                if(tabId != tabIdCall) return
//                moment.content.streamUrl = data.url
//                moment.content.streamSession = data.streamSession
//                binding.vpMoments.handleStreamUrl(MomentStreamInfo(
//                    itemId = moment.content.id,
//                    url = data.url,
//                    streamSession = data.streamSession
//                ), moment = moment, playlistId = if(safeArgs.isPlaylistLayout) shortVideoId else "")
//            }
            is MomentsViewModel.MomentsState.ResultMomentStreamUrlV2 -> {
                if(tabId != tabIdCall) return
                moment.content.streamMomentDataV2 = data
                moment.content.streamSession = ""
                moment.content.currentChapterId = this.chapterId
//                Timber.tag("tam-moment").i("ResultMomentStreamUrlV2 ${moment.content}")
                binding.vpMoments.handleStreamUrlV2(MomentStreamInfo(
                    itemId = moment.content.id,
                    streamMomentDataV2 = data,
                    streamSession = "",
                    streamUnavailable = data.isUnavailable()
                ), moment = moment, playlistId = if(safeArgs.isPlaylistLayout) shortVideoId else "")

                // PreCache
                val url = data.urlDashH265.ifBlank { data.urlHlsH265 }.ifBlank {
                    data.urlDashH264.ifBlank { data.urlHlsH264 }
                }
                preCacheMoment(moment = moment, urls = arrayOf(url), imgUrls = listOf(moment.content.placeHolder))
                //

            }
            is MomentsViewModel.MomentsState.ResultMomentPlaylist -> {
                hideLoading()
                isFirstInit = false
                if(!safeArgs.isPlaylistLayout) return
                val currentMomentPlaylist = viewModel.currentMomentPlaylist
                if (currentMomentPlaylist == null) {
                    showWarningDialog(getString(R.string.moment_error_missing_params_des), textConfirm = getString(R.string.understood))
                    return
                }
                Timber.tag("tam-moment").w("ResultMomentPlaylist")
                val moment = data.episodes.map { it.toMomentDetail(titlePlaylist = data.title, momentPlaylist = currentMomentPlaylist) }
                if (moment.isNotEmpty()) {
                    Timber.tag("tam-moment").i("Load more refresh with ${moment[0].id} adapter size: ${momentAdapter.size()}")
                    if (addToFirst) {
                        moments.addAll(0, moment)
                    } else {
                        moments.addAll(moment)
                    }
                    val realList = moment.validateAndFilter()
                    Timber.tag("tam-moment").i("Load more api list size ${moment.size} - realList size: ${realList.size} - totalItemInPage: $totalItemInPlaylistPage")
                    if(realList.isNotEmpty()) {
                        savePlaylistMomentDetail(data, addToFirst)
                        val pageEnd = moment.size < totalItemInPlaylistPage || page == 1
                        momentAdapter.addWithLocation(data = realList, isBind = isBind, addToFirst = addToFirst) {
                            Timber.tag("tam-moment").i("Load more success")
                            if (!isBind) {
                                binding.vpMoments.updateCurrentPage(moment.size, addToFirst)
                            }
                            playlistLoadMoreHandler.refresh(
                                totalItem = momentAdapter.size(),
                                endPage = pageEnd,
                                addToFirst = addToFirst,
                                page = page,
                                isBind = isBind
                            )
                        }
                        if (isBind) {
                            if(shortVideoChapter.isNotEmpty()) {
                                var currEpisodeIdIndex = momentAdapter.findIndexByEpisodeIndex(safeArgs.indexForPlaylist)
                                if(currEpisodeIdIndex == -1) currEpisodeIdIndex = 0
                                momentAdapter.itemId(currEpisodeIdIndex)?.let {
                                    shortVideoChapter = it
                                }
                                if(currEpisodeIdIndex > 0) {
                                    scrollToMoment(currEpisodeIdIndex)
                                }
                                Timber.tag("tam-moment").e("ResultMomentPlaylist currEpisodeIdIndex $currEpisodeIdIndex")

                            }
                            // open playlist bottom sheet fragment when first open playlist layout and require open
                            if(safeArgs.openListEpisodeDialog) {
                                findNavController().navigateSafe(
                                    MomentsFragmentDirections.actionMomentsFragmentToMomentPlaylistBottomSheetFragment(
                                        playlistId = shortVideoId,
                                        currPlaylistEpisodeId = shortVideoChapter
                                    )
                                )
                            }
                        }

                    } else {
//                        showPageMomentError(binding.flError,getString(R.string.server_error))
                        if (isBind) {
                            showWarningDialog(
                                getString(R.string.moment_error_api_title),
                                textConfirm = getString(R.string.understood)
                            )
                        }

//                        showPageMomentError(
//                            parentView = binding.flError,
//                            errorMessage = getString(R.string.moment_error_api_des),
//                            icon = R.drawable.iv_mega_error,
//                            errorTitle = getString(R.string.moment_error_api_title)
//                        )
                        playlistLoadMoreHandler.refresh(
                            totalItem = momentAdapter.size(),
                            endPage = true,
                            addToFirst = addToFirst,
                            page = page,
                            isBind = isBind
                        )
                    }
                } else {
//                    showPageMomentError(
//                        parentView = binding.flError,
//                        errorMessage = getString(R.string.moment_error_api_des),
//                        icon = R.drawable.iv_mega_error,
//                        errorTitle = getString(R.string.moment_error_api_title)
//                    )
//                    showPageMomentError(binding.flError,getString(R.string.server_error))
                    if (isBind) {
                        showWarningDialog(
                            getString(R.string.moment_error_api_title),
                            textConfirm = getString(R.string.understood)
                        )
                    }

                    playlistLoadMoreHandler.refresh(
                        totalItem = momentAdapter.size(),
                        endPage = true,
                        addToFirst = addToFirst,
                        page = page,
                        isBind = isBind
                    )
                }


            }

            is MomentsViewModel.MomentsState.CanScrollForPlaylist -> {
                val canLoadmore = playlistLoadMoreHandler.canScroll(position)
                Timber.tag("tam-moment").i("${javaClass.simpleName} Load more canLoadmore $canLoadmore")
            }

            is MomentsViewModel.MomentsState.Error-> {
                if(this.intent is MomentsViewModel.MomentsIntent.GetListMomentsV2WithBookmark){
                    if(tabId != intent.tabId) return
                    hideLoading()
                    if(intent.page == 1) {
                        showPageMomentError(
                            errorMessage = getString(R.string.moment_error_api_des),
                            errorTitle = getString(R.string.moment_error_api_title)
                        )
                    }
                    loadMoreHandler.refresh(
                        totalItem = momentAdapter.size(),
                        endPage = false
                    )
                }
                if(this.intent is MomentsViewModel.MomentsIntent.GetMomentPlaylist){
                    hideLoading()
                    if(intent.page == 1) {
                        showWarningDialog(getString(R.string.moment_error_api_title), textConfirm = getString(R.string.understood))

                    }
                }
//                if(this.intent is MomentsViewModel.MomentsIntent.GetMomentStreamUrl){
//                    if(tabId != intent.tabId) return
//                    hideLoading()
//                    binding.vpMoments.handleStreamUrl(MomentStreamInfo(
//                        this.intent.momentId, url = "", this.intent.moment.content.streamSession
//                    ))
//                }
                if(this.intent is MomentsViewModel.MomentsIntent.GetMomentStreamUrlV2){
                    if(tabId != intent.tabId) return
                    hideLoading()
                    binding.vpMoments.handleStreamUrlV2(MomentStreamInfo(
                        itemId = this.intent.momentId, streamMomentDataV2 = null, streamSession = this.intent.moment.content.streamSession
                    ))
                }
                if(this.intent is MomentsViewModel.MomentsIntent.UpdateMomentLikeStatusV2){
                    if(tabId != intent.tabId) return
                    showWarningDialog(textTitle = getString(R.string.moment_error_like_moment_title), message = getString(R.string.moment_error_like_moment_des), textConfirm = getString(R.string.understood))
                    momentToProcessLike = null
                    DataCacheObject.dataLikeCommentCache.resetData()
                }

            }
            is MomentsViewModel.MomentsState.ErrorNoInternet-> {
                if(this.intent is MomentsViewModel.MomentsIntent.GetListMomentsV2WithBookmark) {
                    if(tabId != intent.tabId) return
                    hideLoading()
                    if(intent.page == 1) {
                        showPageMomentError(
                            errorMessage = getString(R.string.moment_error_api_des),
                            errorTitle = getString(R.string.moment_error_api_title)
                        )
                    }
                    loadMoreHandler.refresh(
                        totalItem = momentAdapter.size(),
                        endPage = false
                    )
                }


                if(this.intent is MomentsViewModel.MomentsIntent.GetMomentPlaylist){
                    hideLoading()
                    if(intent.page == 1) {
                        showWarningDialog(getString(R.string.error_no_intent), textConfirm = getString(R.string.understood))
                    }
                }
//                if(this.intent is MomentsViewModel.MomentsIntent.GetMomentStreamUrl){
//                    if(tabId != intent.tabId) return
//                    hideLoading()
//                    binding.vpMoments.handleStreamUrl(MomentStreamInfo(
//                        this.intent.momentId, url = "", this.intent.moment.content.streamSession
//                    ))
//                }
                if(this.intent is MomentsViewModel.MomentsIntent.GetMomentStreamUrlV2){
                    if(tabId != intent.tabId) return
                    hideLoading()
                    binding.vpMoments.handleStreamUrlV2(MomentStreamInfo(
                        itemId = this.intent.momentId, streamMomentDataV2 = null, streamSession = this.intent.moment.content.streamSession
                    ))
                }
                if(this.intent is MomentsViewModel.MomentsIntent.UpdateMomentLikeStatusV2){
                    if(tabId != intent.tabId) return
                    momentToProcessLike = null
                    DataCacheObject.dataLikeCommentCache.resetData()
                }
            }
            is MomentsViewModel.MomentsState.ErrorRequiredLogin -> {
                if(this.intent is MomentsViewModel.MomentsIntent.GetMomentReaction) return // not handle for api GetMomentReaction
                if(viewModel.isTabHomeLayout)
                    parentFragment?.parentFragment?.navigateToLoginWithParams(title = message, isDirect = false)
                else
                    navigateToLoginWithParams(title = message, isDirect = false)

            }

            is MomentsViewModel.MomentsState.ResultUpdateLikeStatus -> {
                if(tabId != tabIdCall) return
                momentToProcessLike = null
                DataCacheObject.dataLikeCommentCache.resetData()
                //this.moment.like.countLocal = data.totalLike
                Logger.d("ResultUpdateLikeStatus = $data")
                processLikeMoment(this.moment, momentPositionLike, isLocalUpdate = false, isDoubleTap = false)
            }
            is MomentsViewModel.MomentsState.ResultListMomentsReaction -> {
                if(tabId != tabIdCall) return
                processMomentReaction(listMomentReaction = this.data)
            }
            else -> {}
        }
    }
    private fun savePlaylistMomentDetail(data: PlaylistMomentDetail, addToFirst: Boolean) {
        viewModel.playlistMomentDetail?.let {
            val newItems = it.episodes.toMutableList()
            if(addToFirst) {
                newItems.addAll(0, data.episodes)
            } else {
                newItems.addAll(data.episodes)
            }
            viewModel.playlistMomentDetail = data.copy(episodes = newItems)
        } ?: run {
            viewModel.playlistMomentDetail = data
        }
    }

    private fun onRelatedMomentClick(moment: MomentDetail): Boolean {
        var navigated = false
        val related = moment.related ?: return false

        when(related.type_related) {
            is MomentDetail.Related.MomentRelatedType.VOD -> {
                TrackingUtil.idRelated = moment.content.momentId
                TrackingUtil.screen = TrackingUtil.screenMomentRelated
                if(related.id.isNotBlank()) {
                    checkBeforePlayUtil.navigateToSelectedContent(
                        data = related
                    )
//                    findNavController().navigate(NavHomeMainDirections.actionGlobalToVod(
//                        id = related.id
//                    ))
                    navigated = true
                }
            }

            is MomentDetail.Related.MomentRelatedType.Unknown -> {
                Timber.tag("tam-moment").d("*** momentRelatedType Unknown ${related.type_related.id}")
            }
        }
        if(navigated) {
//            binding.vpMoments.pauseVideo()
        }
        return navigated
    }

    private fun scrollToMoment(position: Int) {
        if(position >= 0) {
//                binding.vpMoments.layoutManager?.smoothScrollToPosition(binding.vpMoments, null, position)
            binding.vpMoments.layoutManager?.scrollToPosition(position)
            binding.vpMoments.postDelayed({
                if(_binding != null) {
                    binding.vpMoments.start(force = true)
                    binding.vpMoments.checkLoadMore()
                }
            }, 100)

        }

    }

    // region AutoScroll
    private fun initCheckAutoScroll() {

        if (handlerCheckAutoScroll == null) {
            Looper.getMainLooper()?.run {
                handlerCheckAutoScroll = Handler(this)
            }
        }
        handlerCheckAutoScroll?.run {
            post(runnableCheckAutoScroll)
        }
    }
    private fun runHandlerAutoScroll() {
        Timber.tag("tam-moment").d(" runHandlerAutoScroll")
        val timeToCheckAutoScroll = binding.vpMoments.totalDuration() - countdownTimeAutoScrollMs - binding.vpMoments.currentDuration()
        handlerCheckAutoScroll?.run {
            postDelayed(runnableCheckAutoScroll, if (timeToCheckAutoScroll > 0) timeToCheckAutoScroll else 100)
        }
    }
    private fun removeHandlerAndCountDownSkipCredits() {
        stopCountDownTimerAutoScroll()
        binding.vpMoments.hideAutoScrollCountDown()
        handlerCheckAutoScroll?.removeCallbacks(runnableCheckAutoScroll)
    }
    private fun checkStartCountdownAutoScroll() {
        if(Utils.getCurrentAutoScrollStatus(sharedPreferences) != AutoScrollConfig.On) return
        if(binding.vpMoments.isPlaying() != true) return

        //remove handler and countdown timer
        removeHandlerAndCountDownSkipCredits()
        //check have skip credits
        val currentDuration = binding.vpMoments.currentDuration()
        val totalDuration = binding.vpMoments.totalDuration()
        val timeToCheckAutoScroll = binding.vpMoments.totalDuration() - countdownTimeAutoScrollMs - 1000L
        if (currentDuration < totalDuration) {
            if (currentDuration > timeToCheckAutoScroll) {
                startCountDownTimerAutoScroll(totalDuration - currentDuration)
            } else {
                binding.vpMoments.hideAutoScrollCountDown()
                runHandlerAutoScroll()
            }
        } else { // Sometime, player callback currentDuration > totalDuration, so we need to start countdown again in order to  auto scroll
            if ((totalDuration != 0L)) {
                startCountDownTimerAutoScroll(countdownTimeAutoScrollMs)
            }
        }
    }

    private fun startCountDownTimerAutoScroll(countdownTime: Long) {
        if (countDownTimerAutoScroll == null) {
            countDownTimerAutoScroll = object : CountDownTimer(countdownTime, 1000) {
                @SuppressLint("SetTextI18n")
                override fun onTick(millisUntilFinished: Long) {
                    isCountDownStopped = false
                    binding.vpMoments.updateAutoScrollCountDown(progress = TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished).toInt())

                }

                override fun onFinish() {
                    isCountDownStopped = true
                    binding.vpMoments.hideAutoScrollCountDown()
                    stopCountDownTimerAutoScroll()
                    removeHandlerAndCountDownSkipCredits()
                }
            }
            countDownTimerAutoScroll?.start()
        }
    }
    private fun stopCountDownTimerAutoScroll(hideCountDown: Boolean = true) {
        if (countDownTimerAutoScroll != null) {
            isCountDownStopped = true
            if(hideCountDown) binding.vpMoments.hideAutoScrollCountDown()
            countDownTimerAutoScroll?.cancel()
            countDownTimerAutoScroll = null
        }
    }
    private fun handleAutoScroll() {
        if(Utils.getCurrentAutoScrollStatus(sharedPreferences, forceOffAutoScroll = hasCommentPopupIsOpen()) != AutoScrollConfig.On) return
        autoScrollData?.let {
            momentAdapter.item(it.position)?.let { moment ->
                if(contentCanScroll(moment)) {// if in layout playlist or single moment
                    closePopupMomentComment()
                    scrollToMoment(it.position + 1)
                    logKibana?.setAutoScroll()
                } else {
                    navigateToPlaylist(
                        data = moment,
                        startPosition = 0,
                        isPlaying = true,
                        startNextEpisode = moment.content.isLastest != "1",
                        openListEpisodeDialog = false
                    )

                }
            }

        }
    }

    // return true if inPlaylistLayout OR moment is not a series (single)
    private fun contentCanScroll(moment: MomentDetail?) =
        safeArgs.isPlaylistLayout || moment?.content?.typeContent != MomentDetail.Content.MomentContentType.Series

    private fun navigateToPlaylist(data: MomentDetail, startPosition: Long, isPlaying: Boolean = true, startNextEpisode: Boolean = false, openListEpisodeDialog: Boolean = false) {
        viewModel.currentMomentPlaylist = data
        closePopupMomentComment()
        if(NetworkUtils.isNetworkAvailable()){
            var indexForPlaylist = data.content.currentChapterIndex
            if(startNextEpisode) indexForPlaylist += 1
            checkBeforePlayUtil.navigateToMomentPlaylist(
                    momentId = data.content.momentId,
                    chapterId = data.content.currentChapterId,
                    showMetadata = safeArgs.showMetadata,
                    sourceScreen = safeArgs.sourceScreen,
                    relatedId = safeArgs.relatedId,
                    isPlaylistLayout = true,
                    indexForPlaylist = indexForPlaylist,
                    startPosition = startPosition,
                    isPlaying = isPlaying,
                    startNextEpisode = startNextEpisode,
                    openListEpisodeDialog = openListEpisodeDialog
                )

        } else {
            showWarningDialog(getString(R.string.error_no_internet), textConfirm = getString(R.string.understood))
        }

    }

    private fun closePopupMomentComment() {
        if (findNavController().currentDestination?.id == R.id.commentBottomSheetDialogFragment || findNavController().currentDestination?.id == R.id.comment_v2_fragment) {
            findNavController().popBackStack()
        }
    }

    private fun hasCommentPopupIsOpen() : Boolean {
        return findNavController().currentDestination?.id == R.id.commentBottomSheetDialogFragment || findNavController().currentDestination?.id == R.id.comment_v2_fragment
    }

    // endregion


    private fun processStreamLikeAndComment(moment: MomentDetail, momentPosition: Int, streamLike: MomentDetail.Like, streamComment: MomentDetail.Comment) {
        moment.like = streamLike
        moment.comment = streamComment
        momentAdapter.notifyItemChanged(momentPosition, UpdateLikeStatus(moment.like.countLocal))
        momentAdapter.notifyItemChanged(momentPosition, UpdateCommentStatus(moment.comment.countLocal))
    }
    /*private fun updateLikeLocalForCache(){
        if(DataCacheObject.dataLikeCommentCache.actionStatus == 3 && !DataCacheObject.dataLikeCommentCache.dataActive){
            val position = momentAdapter.itemPosition(DataCacheObject.dataLikeCommentCache.itemId, DataCacheObject.dataLikeCommentCache.chapterId)
            if(position < 0) return
            val moment = momentAdapter.item(position)
            if(moment == null){
                DataCacheObject.dataLikeCommentCache.resetData()
                return
            }
            moment.like.statusLikeLocal = DataCacheObject.dataLikeCommentCache.statusLikeLocal
            try {
                if (moment.like.statusLikeLocal == "1") moment.like.countLocal = (moment.like.countLocal.toInt() + 1).toString()
                else moment.like.countLocal = (moment.like.countLocal.toInt() - 1).toString()
            } catch (e: NumberFormatException) {
            }
            momentAdapter.notifyItemChanged(position, UpdateLikeStatus(moment.like.countLocal))
            momentAdapter.notifyItemChanged(position, UpdateCommentStatus(moment.comment.countLocal))
            DataCacheObject.dataLikeCommentCache.resetData()

        }
    }*/

    private fun processMomentReaction(listMomentReaction: List<MomentReaction>) {
        listMomentReaction.forEach {
            val position = momentAdapter.itemPosition(it.momentId, it.chapterId)
            if(position < 0) return
            val moment = momentAdapter.item(position) ?: return
            processStreamLikeAndComment(moment = moment, momentPosition = position, streamLike = it.like, streamComment = it.comment)
        }
        //updateLikeLocalForCache()
    }
    private fun List<MomentDetail>.validateAndFilter(): List<MomentDetail> {
        return filter {
            if (it.content.id.isBlank()) {
                return@filter false
            }
            return@filter when (it.content.typeContent) {
                is MomentDetail.Content.MomentContentType.Moment -> {
                    false
                }
                is MomentDetail.Content.MomentContentType.Unknown -> {
                    false
                }

                MomentDetail.Content.MomentContentType.Series -> true
                MomentDetail.Content.MomentContentType.Single -> true
            }
        }
    }

    private fun PlaylistMomentDetail.PlaylistEpisode.toMomentDetail(titlePlaylist: String, momentPlaylist: MomentDetail): MomentDetail {
        return momentPlaylist.copy(
            like = this.like,
            comment = this.comment,
            content = MomentDetail.Content(
                id = this.id,
                title = titlePlaylist,
                caption = this.caption,
                thumb = this.thumb,
                typeContent = this.typeContent,
                chapterId = this.id,
                momentId = shortVideoId,
                logo = momentPlaylist.content.logo,
                //
                streamMomentDataV2 = this.streamMomentDataV2,
                placeHolder = this.placeHolder,
                index = Utils.convertStringToInt(this.index, 0),
                appId = this.appId,
                isLastest = this.isLastest
            ).apply {
                currentChapterId = this.id
                currentChapterIndex = this.index
                currentStreamMomentDataV2 = this.streamMomentDataV2
            }
        )
    }

    private fun StreamMomentDataV2.isUnavailable(): Boolean {
        return urlHlsH264.isBlank() && urlHlsH265.isBlank()
    }


    // region Precaching
    private fun preCacheMoment(moment: MomentDetail, urls: Array<String>, imgUrls: List<String>) {
        if (!moment.isCached) {
            moment.isCached = true
            preCachingUrl(urls = urls)
            preloadPlaceHolder(urls = imgUrls)
        }
    }

    private fun preCachingUrl(urls: Array<String>) {
        val inputData = Data.Builder().putStringArray(Constants.PRE_CACHING_KEY_LIST_DATA, urls).build()
        val preCachingWork = OneTimeWorkRequestBuilder<PreCachingService>().setInputData(inputData)
            .build()
        WorkManager.getInstance(requireContext())
            .enqueue(preCachingWork)
    }

    private fun preloadPlaceHolder(urls: List<String>) {
        urls.forEach { url ->
            MomentGlideUtils.preload(
                context = MainApplication.INSTANCE.applicationContext,
                url = url,
                width = screenWidth,
                height = screenHeight
            )
        }
    }
    // endregion

    private fun loadDataCache(){
        hideLoading()
        val data = DataCacheObject.dataCache.getMomentWithTab(tabId)
        data?.let {
            if(data.listMoment.isNotEmpty()) {
                binding.vpMoments.setMomentStreamLocalBookmarkInfo(
                    MomentStreamLocalBookmarkInfo(
                        itemId = data.curItemId,
                        startPosition = data.curTimePlaying,
                        isPlayerPlaying = true
                    )
                )
                moments.addAll(data.listMoment)
                val realList = data.listMoment.validateAndFilter()
                if (realList.isNotEmpty()) {
                    momentAdapter.add(data = realList, isBind = true) {
                        loadMoreHandler.refresh(
                            totalItem = momentAdapter.size(),
                            endPage = data.listMoment.size < totalItemInPage // check if data from api is enough for another page, not the filter list
                        )
                    }
                    scrollToMoment(data.curIndex)
                    isFirstInit = false
                } else {
                    loadListMomentFromApi()
                }
            }else{
                loadListMomentFromApi()
            }
        }?:run {
            loadListMomentFromApi()
        }

    }

    private fun openComment(momentId: String, chapterId: String) {
        if(NetworkUtils.isNetworkAvailable()){
            findNavController().navigateSafe(
                NavMomentDirections.actionGlobalMomentToCommentV2Fragment(
                    contentId = momentId,
                    episodeId = chapterId
                )
            )
        } else {
            showSnackbarMessage(text = sharedPreferences.msgNoInternet()?.takeIf { it.isNotEmpty() }
                ?: getString(R.string.error_no_internet_text), drawableId = R.drawable.ic_wifi_off)
        }
    }

    private fun loadListMomentFromApi(){
        viewModel.dispatchIntent(
            MomentsViewModel.MomentsIntent.GetListMomentsV2WithBookmark(
                momentId = shortVideoId,
                chapterId = shortVideoChapter,
                page = 1,
                perPage = totalItemInPage,
                relatedID = safeArgs.relatedId,
                fromDeeplink = safeArgs.isFromDeeplink,
                tabId = tabId
            )
        )
//        shortVideoId = "0"
//        shortVideoChapter = "0"
    }

    private fun showPageMomentError(errorMessage: String, errorTitle: String, btRetryText: String? = null, onBtnRetry: (() -> Unit)? = null) {
        viewModel.isReloading = false
        if (pageMomentErrorViewBinding == null) {
            pageMomentErrorViewBinding = PageMomentErrorViewBinding.inflate(layoutInflater, binding.flError, false)
        }
        pageMomentErrorViewBinding?.let {
            binding.flError.removeAllViews()
            binding.flError.addView(it.root)
            if(viewModel.isTabHomeLayout) it.ibBack.gone() else it.ibBack.visible()
            it.tvDes.text = errorMessage
            it.tvTitle.text = errorTitle
            it.btRetry.text = btRetryText?: getString(R.string.all_retry)
            it.btRetry.onClickDelay {
                binding.flError.removeView(it.root)
                if(onBtnRetry != null) {
                    onBtnRetry()
                } else {
                    retryLoadPage()
                }
            }
            it.ibBack.onClickDelay {
                backHandler()
            }
        }
    }
    private fun resetShortVideoIdAndChapter(){
        shortVideoId = "0"
        shortVideoChapter = "0"
    }

    private fun showSnackbarMessage(
        text: String,
        drawableId: Int
    ) {
        GlobalSnackbarManager.showMessageWithDrawable(message = text, drawableId = drawableId)
    }

    companion object {
        const val COUNT_DOWN_TIME_FOR_AUTO_SCROLL_IN_SECONDS = 3L
    }
}