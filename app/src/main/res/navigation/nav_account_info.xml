<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_account_info"
    app:startDestination="@id/account_info_fragment">

    <include app:graph="@navigation/nav_account_otp_action" />

    <fragment
        android:id="@+id/account_info_fragment"
        android:name="com.fptplay.mobile.features.mega.account.AccountInfoFragment"
        android:label="AccountInfoFragment"
        tools:layout="@layout/account_info_fragment">
        <action
            android:id="@+id/account_info_fragment_to_update_email_fragment"
            app:destination="@id/account_info_update_email_fragment">
            <argument
                android:name="user_email"
                android:defaultValue=""
                app:argType="string" />
        </action>

    </fragment>

    <fragment android:id="@+id/account_info_update_email_fragment"
        android:name="com.fptplay.mobile.features.mega.account.AccountInfoUpdateEmailFragment"
        tools:layout="@layout/account_info_update_email_fragment"
        android:label="AccountInfoUpdateEmailFragment">
        <argument
            android:name="user_email"
            android:defaultValue=""
            app:argType="string" />

        <action
            android:id="@+id/action_account_info_update_email_fragment_to_account_otp_v2_fragment"
            app:destination="@id/nav_account_otp_action" >
            <argument
                android:name="isPackage"
                android:defaultValue="false"
                app:argType="boolean" />
            <argument
                android:name="verifyToken"
                app:argType="string"
                android:defaultValue="" />
            <argument
                android:name="email"
                app:argType="string"
                android:defaultValue="" />
            <argument
                android:name="targetScreen"
                app:argType="com.fptplay.mobile.features.mega.account.model.AccountOtpTargetScreen" />

        </action>
    </fragment>
    <action
        android:id="@+id/action_global_to_account_info"
        app:popUpTo="@id/account_info_fragment"
        app:popUpToInclusive="false" />
    <action
        android:id="@+id/account_info_fragment_to_account_otp_delete_v2_fragment"
        app:destination="@id/nav_account_otp_action" >
        <argument
            android:name="isPackage"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="verifyToken"
            app:argType="string"
            android:defaultValue=""
            />
        <argument
            android:name="targetScreen"
            app:argType="com.fptplay.mobile.features.mega.account.model.AccountOtpTargetScreen" />

    </action>
</navigation>