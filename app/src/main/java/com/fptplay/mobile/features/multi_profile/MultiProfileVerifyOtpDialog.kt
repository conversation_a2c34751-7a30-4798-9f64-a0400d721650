package com.fptplay.mobile.features.multi_profile

import android.annotation.SuppressLint
import android.content.IntentFilter
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.os.CountDownTimer
import android.text.InputFilter
import android.text.InputFilter.LengthFilter
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.EditText
import androidx.core.content.ContextCompat
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.activityViewModels
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.hideKeyboard
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.extensions.showSnackBarTop
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.ui.bases.BaseFullDialogFragment
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.StringUtils.encodePhoneNumber
import com.fptplay.mobile.common.utils.StringUtils.getText
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.MultiProfileVerifyOtpDialogBinding
import com.fptplay.mobile.features.login.LoginViewModel
import com.fptplay.mobile.features.login.gmsotp.SMSReceiver
import com.fptplay.mobile.features.login.utils.LoginUtils
import com.fptplay.mobile.features.login.utils.formatToLong
import com.fptplay.mobile.features.mega.account.AccountVerifyOtpFragment.Companion.IS_NOT_ERROR
import com.fptplay.mobile.features.mega.account.AccountVerifyOtpFragment.Companion.IS_SUCCESS
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.xhbadxx.projects.module.domain.entity.fplay.otp.UserOtpType
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject


@AndroidEntryPoint
class MultiProfileVerifyOtpDialog :
    BaseFullDialogFragment<MultiProfileViewModel.MultiProfileState, MultiProfileViewModel.MultiProfileIntent>() {

    override val hasEdgeToEdge = true

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor
    private val safeArgs: MultiProfileVerifyOtpDialogArgs by navArgs()

    override val viewModel: MultiProfileViewModel by activityViewModels()

    // login view model
    private val loginViewModel by activityViewModels<LoginViewModel>()
    private val loginUtils by lazy { LoginUtils(loginViewModel, parentFragmentManager) }

    private var _binding: MultiProfileVerifyOtpDialogBinding? = null
    private val binding get() = _binding!!

    private var globalLayoutListener: ViewTreeObserver.OnGlobalLayoutListener? = null


    private val requiredOtpLength by lazy { 4 }
    private var otpLength = requiredOtpLength

    private var edtLayout: EditText? = null

    private var smsReceiver: SMSReceiver? = null

    private val otpListener by lazy {
        object : SMSReceiver.OTPReceiveListener {
            override fun onOTPReceived(otp: String?) {
                otp?.apply {
                    val otpString = getOTP(otp)
                    Timber.tag("tam-multiProfile").d("getOTP $otpString")
                    binding.edtOtp.setText(otpString)

                    checkOtp(otpString)

                }
            }

            override fun onOTPTimeOut() {
                Timber.tag("tam-multiProfile").d("OTP Time out")
            }

            override fun onOTPReceivedError(error: String?) {
                Timber.tag("tam-multiProfile").d("onOTPReceivedError")
            }

        }
    }

    private var countDownTimer: CountDownTimer? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.MultiProfilePinProfileBottomSheetDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = MultiProfileVerifyOtpDialogBinding.inflate(inflater, container, false)
        startSMSListener()
        return binding.root
    }


    override fun onStop() {
        super.onStop()
        binding.root.viewTreeObserver.removeOnGlobalLayoutListener(globalLayoutListener)

    }

    override fun onDestroy() {
        super.onDestroy()
        if (smsReceiver != null) {
            requireActivity().unregisterReceiver(smsReceiver)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        edtLayout?.hideKeyboard()
        countDownTimer?.cancel()
        _binding = null
        edtLayout = null
    }

    override fun observeState() {
        super.observeState()
        Timber.tag("tam-multiProfile").d("loginViewModel observeState")

        loginViewModel.state.observe(viewLifecycleOwner) { it.toUI() }

    }

    override fun bindComponent() {
//        binding.edtOtp.setMaxLength(requiredOtpLength)
//        binding.edtOtp.setTypeInputEditText(InputType.TYPE_CLASS_NUMBER)
        binding.edtOtp.filters = arrayOf<InputFilter>(LengthFilter(otpLength))
        setEdittextError(null)


//        binding.edtOtp.filters = arrayOf(InputFilter.LengthFilter(requiredOtpLength))



        binding.apply {
//            tvErrorPin.gone()
            tvHeader.setText(R.string.multi_profile_reset_password_verify_otp_title)
            tvDes.text = getText(
                R.string.multi_profile_reset_password_verify_otp_des,
                safeArgs.userPhone.encodePhoneNumber()
            )

        }

//        edtLayout = binding.edtOtp
        edtLayout = binding.edtOtp

    }


    override fun bindData() {
        loginViewModel.dispatchIntent(
//            LoginViewModel.LoginIntent.SendOTPV1(
//                RequestOtp.FORGOT_PASS,
//                safeArgs.countryCode,
//                safeArgs.userPhone
//            )
//        )
            LoginViewModel.LoginIntent.RequestOtpV2(
                phone = safeArgs.userPhone,
                verifyToken = safeArgs.verifyToken,
                otpType = UserOtpType.LoginForgotPass
            )
        )
        SmsRetriever.getClient(requireContext()).startSmsUserConsent(null)

    }

    override fun bindEvent() {
        binding.vOutside.onClickDelay {
            handleBack()
        }
        binding.ivClose.onClickDelay {
            handleBack()
        }

        binding.flContain.onClickDelay {
            edtLayout?.hideKeyboard()
        }

        binding.tvResendOtp.setOnClickListener {
            startSMSListener()
            loginViewModel.dispatchIntent(
//                LoginViewModel.LoginIntent.ResendOTPV1(
//                    safeArgs.userPhone,
//                    RequestOtp.FORGOT_PASS
//                )
                LoginViewModel.LoginIntent.RequestResendOtpV2(
                    phone = safeArgs.userPhone,
                    verifyToken = safeArgs.verifyToken,
                    otpType = UserOtpType.LoginForgotPass
                )
            )

        }
        binding.edtOtp.apply {
            doAfterTextChanged { editable ->
                setEdittextError(null)
                editable?.toString()?.let {
                    if (it.length == otpLength) {
                        hideKeyboard()
                        checkOtp(otpValue = text.toString())
                    }
                }
            }

        }

    }

    private fun setEdittextError(error: String?) {
        if (error.isNullOrBlank()) {
            binding.edtOtp.setBackgroundResource(R.drawable.multi_profile_password_view_bg)
            binding.tvError.hide()
            binding.tvError.text = ""
        } else {
            binding.edtOtp.setBackgroundResource(R.drawable.multi_profile_password_view_error_bg)
            binding.tvError.show()
            binding.tvError.text = error
        }

    }
    private fun checkOtp(otpValue: String) {
        val pushRegId = sharedPreferences.fcmToken()
        loginViewModel.dispatchIntent(
//            LoginViewModel.LoginIntent.VerifyOTPV1(
//                RequestOtp.FORGOT_PASS,
//                otpValue,
//                safeArgs.userPhone,
//                pushRegId
//            )
            LoginViewModel.LoginIntent.VerifyOtpV2(
                phone = safeArgs.userPhone,
                otpCode = otpValue,
                otpType = UserOtpType.LoginForgotPass
            )
        )
    }

    fun handleBack() {
        findNavController().navigateUp()
    }

    private fun startSMSListener() {
        try {
//            val appSignatureHashHelper = AppSignatureHashHelper(requireContext())
//            Logger.d("trangtest HashKey: " + appSignatureHashHelper.appSignatures[0])
            smsReceiver?.apply {
                requireActivity().unregisterReceiver(smsReceiver)
                smsReceiver = null
            }

            smsReceiver = SMSReceiver()
            smsReceiver?.setOTPListener(otpListener)
            val intentFilter = IntentFilter()
            intentFilter.addAction(SmsRetriever.SMS_RETRIEVED_ACTION)
            activity?.let { requiredActivity ->
                ContextCompat.registerReceiver(requiredActivity, smsReceiver, intentFilter, ContextCompat.RECEIVER_EXPORTED)
            }
            val client = SmsRetriever.getClient(requireActivity())
            val task = client.startSmsRetriever()
            task.addOnSuccessListener {
                // API successfully started
                Logger.d("addOnSuccessListener")
            }
            task.addOnFailureListener {
                // Fail to start API
                Logger.d("addOnFailureListener")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    private fun getOTP(sms: String): String {
        //11 is hashkey length
        return sms.substring(0, sms.length - 11).filter { it.isDigit() }
    }

    @SuppressLint("SetTextI18n")
    private fun startCountdown(seconds: Int) {
        binding.tvResendOtp.visibility = View.GONE
        binding.tvResendDes.text =
            getString(R.string.login_resend_otp_des,"(${seconds})")
        var timeCountdown = seconds
        countDownTimer?.cancel()
        countDownTimer = object : CountDownTimer(timeCountdown * 1000L, 1000) {
            @SuppressLint("SetTextI18n")
            override fun onTick(millisUntilFinished: Long) {
                timeCountdown--
                Logger.d("trangtest countdown = $timeCountdown")
                binding.tvResendDes.text =
                    getString(R.string.login_resend_otp_des,"(${timeCountdown})")
            }

            override fun onFinish() {
                enableResendButton()
            }
        }.start()
    }

    private fun enableResendButton(){
        binding.tvResendDes.text = getString(R.string.login_not_have_otp)
        binding.tvResendOtp.visibility = View.VISIBLE
    }

    private fun handleManyRequestSendOtp(seconds: String, message: String) {
        val time = seconds.formatToInt()
        if (time <= 0) {
            enableResendButton()
        } else {
            startCountdown(time)
        }
        binding.root.showSnackBarTop(message.ifBlank { getString(R.string.new_otp_rate_limit) })
    }


    private fun handleErrorRequestOtp(message: String) {
        binding.root.showSnackBarTop(message)
//        binding.tvDes.text = getText(
//            R.string.delete_account_input_otp_error_des,
//            safeArgs.userPhone.encodePhoneNumber()
//        )
        Utils.fromHtml(binding.tvDes, getString(
            R.string.delete_account_input_otp_error_des,
            safeArgs.userPhone.encodePhoneNumber()
        ))

    }
    private fun handleManyRequestVerifyOTP(message: String) {
        setEdittextError(error = message.ifBlank { getString(R.string.new_otp_many_request_verify)})
    }

    private fun showPopUpTokenExpire(title: String, description: String){
        showAlertDialog(
            title = title.ifBlank { getString(R.string.login_title_token_expire)},
            showTitle = true,
            textConfirm = getString(R.string.close),
            onlyConfirm = true,
            message =  description.ifBlank { getString(R.string.login_des_token_expire) },
            isCancelled = true,
            onConfirm = {
                handleBack()
            })
    }

    override fun MultiProfileViewModel.MultiProfileState.toUI() {
        when (this) {
            is MultiProfileViewModel.MultiProfileState.Loading -> {
                if(isIntentCalledInLayout(intent)) showLoadingView()
            }

            is MultiProfileViewModel.MultiProfileState.ErrorNoInternet -> { }

            is MultiProfileViewModel.MultiProfileState.ErrorRequiredLogin -> {}

            is MultiProfileViewModel.MultiProfileState.Error -> {}

            is MultiProfileViewModel.MultiProfileState.Done -> {
                if(isIntentCalledInLayout(intent)) hideLoadingView()
            }

            else -> {}
        }
    }


    private fun LoginViewModel.LoginState.toUI() {
        when (this) {
            is LoginViewModel.LoginState.Loading -> {
                if(isIntentCalledInLayout(data)) showLoadingView()

            }
            is LoginViewModel.LoginState.ResultSendOtpV1 -> {
                when (data.status) {
                    LoginViewModel.Status.IS_SUCCESS -> { //success -> show otp view
                        if(data.errorCode == 0) {
                            startCountdown(30)
                        }else {
                            loginUtils.checkErrorRequest(requireContext(), data.errorCode, data.message, data.seconds)

                        }
                    }
                    else -> {
                        when(intent) {
                            is LoginViewModel.LoginIntent.SendOTPV1 -> loginUtils.checkErrorRequest(requireContext(), data.errorCode, data.message, data.seconds)
                            is LoginViewModel.LoginIntent.ResendOTPV1 -> {
                                when (data.errorCode) {
                                    LoginViewModel.Status.ERROR_MANY_REQUEST_21,
                                    LoginViewModel.Status.ERROR_MANY_REQUEST_22,
                                    LoginViewModel.Status.ERROR_MANY_REQUEST_429 -> {
                                        startCountdown(data.seconds)
                                    }
                                    else -> {
                                        loginUtils.showNotifyDialog(
                                            getString(R.string.login_notify_title),
                                            data.message,
                                            getString(R.string.login_close)
                                        )
                                    }
                                }
                            }
                            else -> {}
                        }
                    }
                }
            }

            is LoginViewModel.LoginState.ResultVerifyOTP -> {
                when (data.status) {
                    LoginViewModel.Status.IS_SUCCESS -> {//success
                        if(data.errorCode == 0) {
                            //success and switch to reset password.
                            val navOptions = NavOptions.Builder()
                                .setPopUpTo(safeArgs.popUpToId, safeArgs.popUpToInclusive)
                                .build()

                            findNavController().navigate(
                                MultiProfileVerifyOtpDialogDirections.actionProfileVerifyOtpDialogToProfileResetPasswordDialog(
                                    targetScreen = safeArgs.targetScreen,
                                    verifyToken = data.verifyToken,
                                    createPinSetting = false,
                                    popUpToInclusive = safeArgs.popUpToInclusive,
                                    popUpToId = safeArgs.popUpToId
                                ), navOptions
                            )

                        } else {
                            loginUtils.checkErrorRequest(
                                requireContext(),
                                data.errorCode,
                                data.message,
                                data.timeToBlock
                            )
                        }
                    }

                    else -> { //error
                        loginUtils.checkErrorRequest(
                            requireContext(),
                            data.errorCode,
                            data.message,
                            data.timeToBlock
                        )

                    }
                }
            }

            is LoginViewModel.LoginState.ErrorManyRequest -> {
                hideLoadingView()
                when (data) {
                    is LoginViewModel.LoginIntent.VerifyOtpV2-> {
                        Timber.tag("tam-fid").e("ManyRequest $this")
                        handleManyRequestVerifyOTP(message)
                    }
                    is LoginViewModel.LoginIntent.RequestOtpV2,
                    is LoginViewModel.LoginIntent.RequestResendOtpV2-> {
                        handleManyRequestSendOtp(seconds = seconds.toString(), message = message)
                    }
                    else -> {

                    }
                }
            }
            is LoginViewModel.LoginState.ErrorNoInternet-> {
                hideLoadingView()
                when (intent) {
                    is LoginViewModel.LoginIntent.RequestOtpV2,
                    is LoginViewModel.LoginIntent.RequestResendOtpV2-> {
//                        showSnackbar(getString(R.string.new_otp_error_no_internet_text))
                        showWarningDialog(message = message)

                    }
                    else -> {
                        showWarningDialog(message = message)
                    }
                }
            }


            is LoginViewModel.LoginState.Error -> {
                if(data is LoginViewModel.LoginIntent.RequestOtpV2 ||
                    data is LoginViewModel.LoginIntent.RequestResendOtpV2
                ) {
//                    binding.root.showSnackBarTop(getString(R.string.new_otp_error_no_internet_text))
                    handleErrorRequestOtp(message)

                    return
                }
                if(isIntentCalledInLayout(data))
                    loginUtils.showNotifyDialog(
                        getString(R.string.login_notify_title),
                        message,
                        getString(R.string.login_close)
                    )
            }
            is LoginViewModel.LoginState.ResultRequestOtpV2 -> {
                hideLoadingView()
                if (data.status.formatToInt() == LoginViewModel.Status.IS_SUCCESS) {
                    startSMSListener()
                    binding.tvDes.text = boldTextFormatSpannableString(des = data.message, textFormat = data.textFormat)
                    otpLength = data.otpLength.toInt()
                    //set max length for otp
                    val lengthFilter = InputFilter.LengthFilter(otpLength)
                    binding.edtOtp.filters = arrayOf(lengthFilter)
                    val time = data.seconds.formatToLong()
                    if (time <= 0L) {
                        enableResendButton()
                    } else {
                        startCountdown(data.seconds.formatToInt())
                    }
                } else {
                    when (data.errorCode) {
                        LoginViewModel.Status.ERROR_LIMIT_CALL_API -> {
                            handleManyRequestSendOtp(seconds = data.seconds, message = data.message)
                        }

                        LoginViewModel.Status.ERROR_TOKEN_EXPIRE -> {
                            showPopUpTokenExpire(data.title, data.message)
                        }

                        else -> {
                            handleErrorRequestOtp(data.message)
//                            showWarningDialog(message = data.message)
                        }
                    }
                }
            }
            is LoginViewModel.LoginState.ResultVerifyOtpV2 ->{
                hideLoadingView()
                if(data.status == IS_SUCCESS && data.errorCode == IS_NOT_ERROR) {
                    setEdittextError(null)
                    val navOptions = NavOptions.Builder()
                        .setPopUpTo(safeArgs.popUpToId, safeArgs.popUpToInclusive)
                        .build()

                    findNavController().navigate(
                        MultiProfileVerifyOtpDialogDirections.actionProfileVerifyOtpDialogToProfileResetPasswordDialog(
                            targetScreen = safeArgs.targetScreen,
                            verifyToken = data.verifyToken,
                            createPinSetting = false,
                            popUpToId = safeArgs.popUpToId,
                            popUpToInclusive = safeArgs.popUpToInclusive
                        ), navOptions
                    )
                } else {
                    setEdittextError(error = data.message)
                }
            }
            is LoginViewModel.LoginState.Done -> {
                if(isIntentCalledInLayout(data)) hideLoadingView()
            }
            is LoginViewModel.LoginState.ErrorRequiredLogin -> {
                if(isIntentCalledInLayout(intent)){
                    navigateToLoginWithParams(
                        isDirect = false,
                        requestRestartApp = true
                    )
                }
            }
            else -> {}
        }
    }

    private fun isIntentCalledInLayout(intent: ViewIntent?) =
        (intent is LoginViewModel.LoginIntent.SendOTPV1 ||
                intent is LoginViewModel.LoginIntent.ResendOTPV1||
                intent is LoginViewModel.LoginIntent.VerifyOTPV1 ||
                intent is LoginViewModel.LoginIntent.RequestOtpV2 ||
                intent is LoginViewModel.LoginIntent.RequestResendOtpV2 ||
                intent is LoginViewModel.LoginIntent.VerifyOtpV2)



    private fun errorMessage(message: String?): String {
        return if (message.isNullOrBlank()) {
            getString(R.string.multi_profile_login_profile_error)
        } else {
            message
        }
    }

    private fun boldTextFormatSpannableString(
        des: String,
        textFormat: List<String>
    ): SpannableString {
        if (textFormat.isEmpty()) return SpannableString(des)
        var spannableString = SpannableString(des)
        for (textFormatItems in textFormat) {
            spannableString = boldTextFormatItemsSpannableString(des = spannableString.toString(), format = textFormatItems)
        }
        return spannableString
    }

    private fun boldTextFormatItemsSpannableString(des: String, format:String): SpannableString {
        val spannableString = SpannableString(des)
        val start = des.indexOf(format)
        val end = start + format.length
        spannableString.setSpan(
            StyleSpan(Typeface.BOLD),
            start,
            end,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableString.setSpan(
            ForegroundColorSpan(Color.WHITE),
            start,
            end,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        return spannableString
    }



    private fun String.formatToInt(defaultValue: Int = 0): Int {
        return try {
            this.toInt()
        } catch (e: Exception) {
            defaultValue
        }
    }
}