<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>androidx.media3</groupId>
  <artifactId>media3-common</artifactId>
  <version>1.6.1.rc01</version>
  <packaging>aar</packaging>
  <name>Media3 common module</name>
  <description>Media3 common module</description>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>The Android Open Source Project</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/androidx/media.git</connection>
    <url>https://github.com/androidx/media</url>
  </scm>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-cast</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-common-ktx</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-container</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-database</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-datasource</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-datasource-cronet</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-datasource-okhttp</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-datasource-rtmp</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-decoder</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-exoplayer-midi</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-effect</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-exoplayer</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-exoplayer-dash</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-exoplayer-hls</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-exoplayer-ima</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-exoplayer-rtsp</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-exoplayer-smoothstreaming</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-exoplayer-workmanager</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-extractor</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-muxer</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-session</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-transformer</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-ui</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-ui-compose</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-ui-leanback</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-test-utils</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>androidx.media3</groupId>
        <artifactId>media3-test-utils-robolectric</artifactId>
        <version>1.6.1.rc01</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-bom</artifactId>
        <version>1.8.0</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>33.3.1-android</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.j2objc</groupId>
          <artifactId>j2objc-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.checkerframework</groupId>
          <artifactId>checker-compat-qual</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.checkerframework</groupId>
          <artifactId>checker-qual</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>androidx.annotation</groupId>
      <artifactId>annotation-experimental</artifactId>
      <version>1.3.1</version>
      <scope>compile</scope>
      <type>aar</type>
    </dependency>
    <dependency>
      <groupId>androidx.annotation</groupId>
      <artifactId>annotation</artifactId>
      <version>1.6.0</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
</project>
